# 任务：OKR进度历史组件按月筛选模式实现

## 任务描述
实现进度历史组件的按月筛选模式，展示整月进度记录并按时间倒序排列。

## 所属功能模块
OKR进度历史组件

## 技术实现详情

### 按月筛选模式UI实现
1. 完善按月筛选模式下的记录列表展示：
   ```html
   <!-- 按月模式下的记录列表 -->
   <view class="records-list month-mode" v-if="filterMode === 'month'">
     <!-- 日期分组标题 -->
     <block v-for="(group, groupIndex) in groupedRecords" :key="groupIndex">
       <view class="date-group-title">
         <text>{{ formatGroupTitle(group.date) }}</text>
       </view>
       
       <!-- 当天的记录列表 -->
       <view class="record-item" v-for="(item, itemIndex) in group.records" :key="itemIndex">
         <view class="record-time">{{ formatTime(item.createTime) }}</view>
         <view class="record-content">
           <view class="progress-value">
             <text>{{ item.progress }}%</text>
             <text class="progress-change" :class="getChangeClass(item.progressChange)">
               {{ formatProgressChange(item.progressChange) }}
             </text>
           </view>
           <view class="record-desc" v-if="item.desc">{{ item.desc }}</view>
           
           <!-- 操作按钮 -->
           <view class="record-actions">
             <view class="action-btn edit" @click="editRecord(item)">
               <text class="iconfont icon-edit"></text>
             </view>
             <view class="action-btn delete" @click="deleteRecord(item)">
               <text class="iconfont icon-delete"></text>
             </view>
           </view>
         </view>
       </view>
     </block>
     
     <!-- 空状态 -->
     <view class="empty-state" v-if="filteredRecords.length === 0 && !loading">
       <text>{{ filterMonth }}没有进度记录</text>
     </view>
   </view>
   ```

### 按月分组数据处理
1. 添加数据分组计算属性：
   ```javascript
   computed: {
     // 已有计算属性...
     
     /**
      * 按日期分组的记录
      */
     groupedRecords() {
       if (this.filterMode !== 'month' || !this.filteredRecords.length) {
         return [];
       }
       
       // 按日期分组
       const groups = {};
       this.filteredRecords.forEach(record => {
         const dateKey = dayjs(record.createTime).format('YYYY-MM-DD');
         if (!groups[dateKey]) {
           groups[dateKey] = {
             date: dateKey,
             records: []
           };
         }
         groups[dateKey].records.push(record);
       });
       
       // 转换为数组并按日期倒序排序
       return Object.values(groups).sort((a, b) => {
         return new Date(b.date).getTime() - new Date(a.date).getTime();
       });
     }
   }
   ```

### 格式化和辅助方法
1. 添加日期和进度变化格式化方法：
   ```javascript
   methods: {
     // 已有方法...
     
     /**
      * 格式化分组标题（日期）
      * @param {String} date YYYY-MM-DD格式的日期
      * @return {String} 格式化后的日期标题
      */
     formatGroupTitle(date) {
       const recordDate = dayjs(date);
       const today = dayjs();
       const yesterday = dayjs().subtract(1, 'day');
       
       if (recordDate.format('YYYY-MM-DD') === today.format('YYYY-MM-DD')) {
         return '今天';
       } else if (recordDate.format('YYYY-MM-DD') === yesterday.format('YYYY-MM-DD')) {
         return '昨天';
       } else {
         return recordDate.format('MM月DD日');
       }
     },
     
     /**
      * 格式化时间
      * @param {String} time ISO格式的时间字符串
      * @return {String} 格式化后的时间（HH:mm）
      */
     formatTime(time) {
       return dayjs(time).format('HH:mm');
     },
     
     /**
      * 格式化进度变化值
      * @param {Number} change 进度变化值
      * @return {String} 格式化后的变化值字符串
      */
     formatProgressChange(change) {
       if (!change) return '';
       return change > 0 ? `+${change}%` : `${change}%`;
     },
     
     /**
      * 获取进度变化的CSS类名
      * @param {Number} change 进度变化值
      * @return {String} CSS类名
      */
     getChangeClass(change) {
       if (!change) return '';
       return change > 0 ? 'increase' : 'decrease';
     }
   }
   ```

### 记录操作方法
1. 实现编辑和删除记录的方法：
   ```javascript
   /**
    * 编辑记录
    * @param {Object} record 要编辑的记录
    */
   editRecord(record) {
     // 触发编辑事件，由父组件处理
     this.$emit('edit-record', record);
   },
   
   /**
    * 删除记录
    * @param {Object} record 要删除的记录
    */
   deleteRecord(record) {
     uni.showModal({
       title: '确认删除',
       content: '确定要删除这条进度记录吗？',
       success: (res) => {
         if (res.confirm) {
           // 触发删除事件，由父组件处理
           this.$emit('delete-record', record);
         }
       }
     });
   }
   ```

### 样式实现
1. 添加按月模式下的记录列表样式：
   ```css
   .records-list.month-mode {
     padding: 0 16px;
   }
   
   .date-group-title {
     padding: 12px 0 8px;
     font-size: 16px;
     font-weight: bold;
     color: #333;
   }
   
   .record-item {
     margin-bottom: 12px;
     padding-bottom: 12px;
     border-bottom: 1px solid #f0f0f0;
   }
   
   .record-time {
     font-size: 12px;
     color: #999;
     margin-bottom: 4px;
   }
   
   .record-content {
     display: flex;
     flex-direction: column;
   }
   
   .progress-value {
     font-size: 16px;
     margin-bottom: 4px;
     display: flex;
     align-items: center;
   }
   
   .progress-change {
     margin-left: 8px;
     font-size: 14px;
   }
   
   .progress-change.increase {
     color: #52c41a;
   }
   
   .progress-change.decrease {
     color: #f5222d;
   }
   
   .record-desc {
     font-size: 14px;
     color: #666;
     margin-bottom: 8px;
   }
   
   .record-actions {
     display: flex;
     justify-content: flex-end;
   }
   
   .action-btn {
     padding: 6px;
     margin-left: 12px;
   }
   
   .action-btn.edit {
     color: #1890ff;
   }
   
   .action-btn.delete {
     color: #f5222d;
   }
   
   .empty-state {
     padding: 40px 0;
     text-align: center;
     color: #999;
   }
   ```

## 验收标准
1. 按月筛选模式下能正确显示当前选中月份的所有进度记录
2. 记录按日期分组并倒序排列，日期分组标题格式正确（今天、昨天、MM月DD日）
3. 每条记录正确显示时间、进度值、变化值和备注内容
4. 进度变化值根据正负值有不同的颜色标识
5. 支持编辑和删除操作，交互流畅
6. 空状态提示信息正确显示

## 依赖关系
- 上游依赖：task-progress-history-01.md, task-progress-history-02.md
- 下游依赖：task-progress-history-05.md

## 优先级
中

## 估计工作量
1.5人日 
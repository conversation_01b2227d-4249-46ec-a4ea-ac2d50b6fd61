/**
 * 执行上下文管理模块
 *
 * 主要功能：
 * 1. 管理任务执行过程中的所有数据和状态信息
 * 2. 提供步骤结果的存储和检索功能
 * 3. 自动提取和分析上下文数据
 * 4. 支持智能项目匹配和关键词提取
 * 5. 维护执行过程的元数据信息
 *
 * 核心特性：
 * - 会话隔离：每个执行会话都有独立的上下文
 * - 智能提取：自动从步骤结果中提取有用信息
 * - 项目匹配：基于用户输入智能匹配目标项目
 * - 数据关联：建立步骤间的数据依赖关系
 *
 * 使用场景：
 * - 多步骤任务执行时的数据传递
 * - 动态参数解析时的数据源
 * - 执行结果的统计和分析
 * - 用户意图的上下文理解
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

/**
 * 执行上下文管理器
 * 用于管理任务执行过程中的所有数据和状态信息
 *
 * 数据结构：
 * - stepResults: Map<stepId, {result, metadata}> 存储步骤执行结果
 * - contextData: Map<key, value> 存储提取的上下文数据
 * - metadata: Object 存储执行过程的元数据信息
 *
 * 核心功能：
 * - 步骤结果管理：存储和检索每个步骤的执行结果
 * - 上下文数据提取：根据 AI 提取的实体，从结果中提取关键信息
 * - 项目智能匹配：基于 AI 提取的项目名称，匹配最相关的项目
 */
const {
    IntelligentExecutionPlanner
} = require('./modules/planner')
const {
    executeRobustPlan
} = require('./modules/executor')
const {
    globalPerformanceMonitor
} = require('./modules/performance')
const {
    OpenAI
} = require('openai')
const { DEFAULT_SYSTEM_PROMPT, doubaoParams } = require('./modules/config.js')
const {
    ExecutionContextManager
} = require('./modules/context')


module.exports = {
    async chatStreamSSE({
        channel,
        message,
        messages: history_records
    }) {

        // 调试日志：记录接收到的用户消息
        console.log('SSE 流式聊天消息：', message)

        // 参数验证阶段：确保必需参数存在，避免后续处理出错
        if (!message) {
            return {
                errCode: 'PARAM_IS_NULL',
                errMsg: '消息内容不能为空',
            }
        }

        if (!channel) {
            return {
                errCode: 'PARAM_IS_NULL',
                errMsg: 'SSE Channel 不能为空',
            }
        }

        try {
            // 反序列化 SSE Channel，将前端传递的 channel 对象转换为可用的推送通道
            const sseChannel = uniCloud.deserializeSSEChannel(channel)
            
            // 初始化豆包 AI 客户端，使用配置文件中的参数
            const openai = new OpenAI(doubaoParams)

            // 构建对话消息数组：系统提示词 + 历史记录 + 当前用户消息
            const messages = [
              {
                role: 'system',
                content: DEFAULT_SYSTEM_PROMPT,
              },
              ...history_records,
            ]
            if (message) messages.push({
                role: 'user',
                content: message
            })

            // 推送开始消息，通知前端开始处理用户请求
            await sseChannel.write({
                type: 'start',
                message: '开始生成回复...',
                timestamp: Date.now(),
            })

            // 创建流式 AI 响应，启用实时数据传输
            const streamResponse = await openai.chat.completions.create({
              messages, // 对话上下文
              model: 'doubao-seed-1-6-250615', // 使用的 AI 模型
              stream: true, // 启用流式响应，关键设置
              timeout: 300000, // 5 分钟超时（毫秒），防止长时间等待
            })

            // 初始化流式处理相关变量
            let fullContent = '' // 累积的完整 AI 响应内容，用于最终的意图解析
            let chunkCount = 0 // 推送的数据块计数，用于统计和调试
            let intentType = null // 识别的意图类型：task|chat
            let isChatReplyStarted = false // 是否开始推送闲聊回复的标志位
            let chatReply = '' // 提取的闲聊回复内容，仅用于 chat 类型

            // 正则表达式：匹配 AI 返回的意图类型和闲聊回复
            // task 类型只有意图类型，chat 类型有意图类型和闲聊回复
            const intentTypeRegex = /「意图类型」：(task|chat)/
            const chatReplyRegex = /「闲聊回复」：([\s\S]*)/

            // 流式处理 AI 响应数据
            for await (const chunk of streamResponse) {
                // 提取当前数据块的内容，处理可能的空值情况
                const content = chunk.choices[0]?.delta?.content || ''

                if (content) {
                    fullContent += content // 累积完整内容，用于后续的正则匹配
                    chunkCount++ // 增加数据块计数，用于统计和调试

                    // 第一阶段：检测意图类型
                    // 在累积的内容中查找意图类型标识，一旦找到就立即处理
                    if (!intentType) {
                        const typeMatch = intentTypeRegex.exec(fullContent)
                        if (typeMatch) {
                            intentType = typeMatch[1] // 提取意图类型：task|chat
                            console.log(`检测到意图类型：${intentType}`)

                            // 立即推送意图类型到前端，让用户知道 AI 已经理解了请求类型
                            await sseChannel.write({
                                type: 'intent_type',
                                intentType: intentType,
                                timestamp: Date.now(),
                            })
                            continue // 跳过当前块的推送，避免重复发送相同信息
                        }
                    }

                    // 第二阶段：根据意图类型处理内容推送
                    if (intentType === 'chat') {
                        // chat 类型：检测闲聊回复开始标识
                        if (!isChatReplyStarted) {
                            const replyMatch = chatReplyRegex.exec(fullContent)
                            if (replyMatch) {
                                isChatReplyStarted = true
                                chatReply = replyMatch[1] // 提取已有的闲聊回复内容
                                console.log('检测到闲聊回复开始')

                                // 推送闲聊回复开始标识
                                await sseChannel.write({
                                    type: 'content_chunk',
                                    content: chatReply,
                                    timestamp: Date.now(),
                                })
                                continue // 跳过当前块的推送，避免重复发送
                            }
                        } else {
                            // 持续推送闲聊回复内容块
                            await sseChannel.write({
                                type: 'content_chunk',
                                content: content,
                                timestamp: Date.now(),
                            })
                            chatReply += content // 累积闲聊回复内容
                            console.log(`推送闲聊回复块：${content}`)
                        }
                    } else if (intentType === 'task') {
                        // task 类型：不需要推送额外内容，直接进入任务执行阶段
                        console.log('task 类型无需推送内容，等待任务执行')
                    } else {
                        // 尚未检测到意图类型，继续累积内容
                        console.log(`累积内容等待意图识别：${fullContent}`)
                    }
                }
            }

            // 调试输出：打印完整的处理结果，便于开发和调试
            console.log('AI 完整返回内容：', fullContent)
            console.log('提取的意图类型：', intentType)
            console.log('提取的闲聊回复：', chatReply)

            // 任务执行阶段：task 类型触发智能任务执行
            // 只有当识别到任务意图时才执行任务，chat 类型直接返回对话结果
            if (intentType === 'task') {
              // 创建执行上下文管理器，用于管理执行过程中的数据和状态
              const context = new ExecutionContextManager(IntelligentExecutionPlanner.generateUUID())

              // 使用智能执行计划生成器创建执行计划
              // AI 会分析用户意图，并返回包含步骤和提取实体的计划
              const executionPlan = await IntelligentExecutionPlanner.generatePlan(message, intentType)

              // 如果生成了有效的执行计划，则开始执行任务
              if (executionPlan.totalSteps > 0) {
                // 使用增强的执行引擎执行计划
                // 集成了错误处理、重试机制、性能监控等高级功能
                await executeRobustPlan(executionPlan, context, sseChannel, globalPerformanceMonitor)

                // 返回任务执行结果，包含详细的执行信息和性能报告
                return {
                  errCode: 0,
                  errMsg: 'success',
                  data: {
                    type: 'task_executed', // 标识这是一个任务执行结果
                    intentType: intentType, // 识别的意图类型
                    executionPlan: executionPlan, // 完整的执行计划信息
                    contextData: Array.from(context.contextData.keys()), // 上下文数据键列表
                    executionTime: executionPlan.totalExecutionTime, // 总执行时间
                    performanceReport: globalPerformanceMonitor.getPerformanceReport(), // 性能报告
                    content: fullContent, // AI 完整回复内容
                    totalChunks: chunkCount, // 推送的数据块总数
                  },
                }
              }
            }

            // 推送结束消息，标识流式聊天处理完成
            // 这是正常流程的结束，通知前端可以停止等待更多数据
            await sseChannel.end({
                type: 'end',
                content: intentType === 'chat' ? chatReply : fullContent, // chat 类型返回闲聊回复，task 类型返回完整内容
                intentType: intentType, // 识别的意图类型
                totalChunks: chunkCount, // 总共推送的数据块数量
                timestamp: Date.now(),
            })

            // 调试日志：记录流式聊天的完成情况
            console.log(`SSE 流式聊天完成，共推送${chunkCount}个数据块`)

            // 返回流式聊天完成的结果
            return {
                errCode: 0,
                errMsg: 'success',
                data: {
                    type: 'stream_complete', // 标识这是一个流式聊天完成结果
                    content: intentType === 'chat' ? chatReply : fullContent,
                    intentType: intentType,
                    totalChunks: chunkCount,
                },
            }
        } catch (error) {
            // 错误处理：捕获并处理执行过程中的所有异常
            console.log('SSE 流式聊天错误：', error)

            // 尝试通过 SSE Channel 发送错误消息给前端
            // 这样前端可以知道发生了错误，而不是一直等待
            try {
                if (channel) {
                    const sseChannel = uniCloud.deserializeSSEChannel(channel)
                    await sseChannel.end({
                        type: 'error',
                        error: error.message || '调用 AI 流式接口失败',
                        timestamp: Date.now(),
                    })
                }
            } catch (channelError) {
                // 如果连错误消息都发送失败，记录日志但不抛出异常
                console.log('发送错误消息失败：', channelError)
            }

            // 返回错误结果给调用方
            return {
                errCode: 'API_ERROR',
                errMsg: error.message || '调用 AI 流式接口失败',
            }
        }
    },
}

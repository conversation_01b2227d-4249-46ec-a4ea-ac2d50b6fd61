<template>
  <swiper class="h-170" circular previous-margin="100rpx" next-margin="100rpx">
    <swiper-item v-for="(weeks, i) in calendarList" :key="i"> {{ weeks }}</swiper-item>
  </swiper>
</template>
<script setup>
import { defineProps, defineEmits, watch, ref } from 'vue'
const props = defineProps({
  // day: {
  //   type: String,
  //   default: dayjs().format('YYYY-MM-DD'),
  // },
})
const emits = defineEmits('onChange', 'onShowToday')

const calendarList = ref(['11111', '22222', '33333', '44444'])

// 初始化
const init = () => {}
defineExpose({
  init,
})

onLoad(() => {})
</script>
<style scope lang="scss">
.day-point {
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 10rpx;
    height: 10rpx;
    background-color: red;
    border-radius: 50%;
  }
}
</style>

// 云对象教程：https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc 语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129

/**
 * 滴答清单 API 云对象
 * 提供滴答清单相关的 API 接口调用功能
 */

const { logInfo, logError, handleError, createSuccessResponse, createErrorResponse } = require('./utils')

// 导入模块
const authModule = require('./modules/auth')
const tasksModule = require('./modules/tasks')
const pomodoroModule = require('./modules/pomodoro')
const projectsModule = require('./modules/projects')
const habitsModule = require('./modules/habits')
const statisticsModule = require('./modules/statistics')
const usersModule = require('./modules/users')

// 全局会话存储
let currentSession = {
  auth_token:
    '43A001113F9610FF9E252C1ED3D433881CFCB2622B3FD24309382C31B5D72AD921C79B5495A8076156A702E60FA33037B66834BC5D0FFC9894BE739FD776C7F4804D8EB8613305E973532E9BF4ABD72377FC271889007EB032442C066765836743C882963303311AE0C087D77EF17153151002FFD8A5114164D117B92EA2F106ABCA48E8A52E39F211229411FBAA88B430C018A70CFD503A6AAC03FF0B09EE61C0ABBFBE4C30F683D7CF82F24CA79A3B4C4FFBD3D1FB387E',
}

module.exports = {
  _before: function () {
    // 通用预处理器
    const methodName = this.getMethodName()
    const clientInfo = this.getClientInfo()

    // 记录请求日志
    logInfo('_before', `调用方法：${methodName}, 客户端：${clientInfo.clientIP}`)

    // 权限验证示例（可根据需要扩展）
    const publicMethods = [
      'getWeChatQRCode',
      'pollQRStatus',
      'validateWeChatLogin',
      'passwordLogin',
      'getSessionStatus',
    ]
    if (!publicMethods.includes(methodName) && !currentSession) {
      throw new Error('需要先进行身份认证')
    }
  },

  _after: function (error, result) {
    const methodName = this.getMethodName()

    if (error) {
      // 错误日志记录
      logError('_after', `方法 ${methodName} 执行错误`, error)

      // 返回统一的错误响应格式
      const errorResponse = handleError(error, methodName)
      errorResponse.requestId = this.getUniCloudRequestId()
      return errorResponse
    }

    // 添加通用响应字段
    if (result && typeof result === 'object') {
      result.timestamp = Date.now()
      result.requestId = this.getUniCloudRequestId()
    }

    logInfo('_after', `方法 ${methodName} 执行完成`)
    return result
  },

  /**
   * 获取微信登录二维码
   * @param {string} state - 状态参数，默认为 "Lw=="
   * @returns {object} 包含二维码 URL 和密钥的响应
   */
  async getWeChatQRCode(state = 'Lw==') {
    return await authModule.getWeChatQRCode(state)
  },

  /**
   * 轮询二维码状态
   * @param {string} qrCodeKey - 二维码密钥
   * @param {number} maxAttempts - 最大轮询次数，默认 60 次
   * @returns {object} 登录结果
   */
  async pollQRStatus(qrCodeKey, maxAttempts = 60) {
    return await authModule.pollQRStatus(qrCodeKey, maxAttempts)
  },

  /**
   * 验证微信登录
   * @param {string} code - 扫码后获得的验证码
   * @param {string} state - 状态参数
   * @returns {object} 验证响应
   */
  async validateWeChatLogin(code, state = 'Lw==') {
    return await authModule.validateWeChatLogin(code, state)
  },

  /**
   * 密码登录
   * @param {string} username - 登录账户（邮箱或手机号）
   * @param {string} password - 登录密码
   * @returns {object} 登录结果
   */
  async passwordLogin(username, password) {
    return await authModule.passwordLogin(username, password)
  },

  /**
   * 获取会话状态
   * @returns {object} 会话状态信息
   */
  async getSessionStatus() {
    const methodName = 'getSessionStatus'
    logInfo(methodName, '获取会话状态')

    try {
      return createSuccessResponse('获取会话状态成功', {
        has_session: currentSession !== null,
        session_id: currentSession?.session_id || null,
        is_active: currentSession?.is_active || false,
      })
    } catch (error) {
      throw error
    }
  },

  /**
   * 设置认证会话
   * @param {string} authToken - 认证令牌
   * @param {string} csrfToken - CSRF 令牌
   * @returns {object} 设置结果
   */
  async setAuthSession(authToken, csrfToken) {
    const methodName = 'setAuthSession'
    logInfo(methodName, '设置认证会话')

    try {
      // 生成会话 ID
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      // 设置全局会话
      currentSession = {
        session_id: sessionId,
        auth_token: authToken,
        csrf_token: csrfToken,
        is_active: true,
        created_at: Date.now(),
      }

      return createSuccessResponse('认证会话设置成功', {
        session_id: sessionId,
        status: '已设置认证会话，可以调用其他 API',
      })
    } catch (error) {
      throw error
    }
  },

  // ================================
  // 任务管理相关方法
  // ================================

  /**
   * 获取所有任务
   * @returns {object} 任务列表响应
   */
  async getAllTasks() {
    if (!currentSession) {
      throw new Error('需要先设置认证会话')
    }
    return await tasksModule.getAllTasks(currentSession.auth_token, currentSession.csrf_token)
  },

  /**
   * 获取已完成任务（分页）
   * @param {number} page - 页码，从 0 开始
   * @param {number} limit - 每页数量，默认 50
   * @returns {object} 已完成任务列表响应
   */
  async getCompletedTasks(page = 0, limit = 50) {
    if (!currentSession) {
      throw new Error('需要先设置认证会话')
    }
    return await tasksModule.getCompletedTasks(currentSession.auth_token, currentSession.csrf_token, page, limit)
  },

  /**
   * 获取垃圾桶任务
   * @param {number} page - 页码，从 0 开始
   * @param {number} limit - 每页数量，默认 50
   * @returns {object} 垃圾桶任务列表响应
   */
  async getTrashTasks(page = 0, limit = 50) {
    if (!currentSession) {
      throw new Error('需要先设置认证会话')
    }
    return await tasksModule.getTrashTasks(currentSession.auth_token, currentSession.csrf_token, page, limit)
  },

  /**
   * 搜索任务
   * @param {string} keyword - 搜索关键词
   * @param {number} limit - 结果数量限制，默认 20
   * @returns {object} 搜索结果响应
   */
  async searchTasks(keyword, limit = 20) {
    if (!currentSession) {
      throw new Error('需要先设置认证会话')
    }
    return await tasksModule.searchTasks(currentSession.auth_token, currentSession.csrf_token, keyword, limit)
  },

  // ================================
  // 专注管理相关方法
  // ================================

  /**
   * 获取专注概览
   * @returns {object} 专注概览响应
   */
  async getPomodoroGeneral() {
    if (!currentSession) {
      throw new Error('需要先设置认证会话')
    }
    return await pomodoroModule.getPomodoroGeneral(currentSession.auth_token, currentSession.csrf_token)
  },

  /**
   * 获取专注分布
   * @param {string} startDate - 开始日期，格式：YYYY-MM-DD
   * @param {string} endDate - 结束日期，格式：YYYY-MM-DD
   * @returns {object} 专注分布响应
   */
  async getFocusDistribution(startDate, endDate) {
    if (!currentSession) {
      throw new Error('需要先设置认证会话')
    }
    return await pomodoroModule.getFocusDistribution(
      currentSession.auth_token,
      currentSession.csrf_token,
      startDate,
      endDate
    )
  },

  /**
   * 获取专注时间线
   * @param {number} limit - 结果数量限制，默认 50
   * @returns {object} 专注时间线响应
   */
  async getFocusTimeline(limit = 50) {
    if (!currentSession) {
      throw new Error('需要先设置认证会话')
    }
    return await pomodoroModule.getFocusTimeline(currentSession.auth_token, currentSession.csrf_token, limit)
  },

  /**
   * 获取专注热力图
   * @param {string} startDate - 开始日期，格式：YYYY-MM-DD
   * @param {string} endDate - 结束日期，格式：YYYY-MM-DD
   * @returns {object} 专注热力图响应
   */
  async getFocusHeatmap(startDate, endDate) {
    if (!currentSession) {
      throw new Error('需要先设置认证会话')
    }
    return await pomodoroModule.getFocusHeatmap(
      currentSession.auth_token,
      currentSession.csrf_token,
      startDate,
      endDate
    )
  },

  // ================================
  // 项目管理相关方法
  // ================================

  /**
   * 获取项目列表
   * @returns {object} 项目列表响应
   */
  async getProjects() {
    if (!currentSession) {
      throw new Error('需要先设置认证会话')
    }
    return await projectsModule.getProjects(currentSession.auth_token, currentSession.csrf_token)
  },

  // ================================
  // 习惯管理相关方法
  // ================================

  /**
   * 获取习惯列表
   * @returns {object} 习惯列表响应
   */
  async getHabits() {
    if (!currentSession) {
      throw new Error('需要先设置认证会话')
    }
    return await habitsModule.getHabits(currentSession.auth_token, currentSession.csrf_token)
  },

  /**
   * 获取本周习惯统计
   * @returns {object} 本周习惯统计响应
   */
  async getWeekCurrentStatistics() {
    if (!currentSession) {
      throw new Error('需要先设置认证会话')
    }
    return await habitsModule.getWeekCurrentStatistics(currentSession.auth_token, currentSession.csrf_token)
  },

  // ================================
  // 统计服务相关方法
  // ================================

  /**
   * 获取用户排名统计
   * @returns {object} 用户排名统计响应
   */
  async getUserRanking() {
    if (!currentSession) {
      throw new Error('需要先设置认证会话')
    }
    return await statisticsModule.getUserRanking(currentSession.auth_token, currentSession.csrf_token)
  },

  /**
   * 获取通用统计信息
   * @returns {object} 通用统计信息响应
   */
  async getGeneralStatistics() {
    if (!currentSession) {
      throw new Error('需要先设置认证会话')
    }
    return await statisticsModule.getGeneralStatistics(currentSession.auth_token, currentSession.csrf_token)
  },

  // ================================
  // 用户服务相关方法
  // ================================

  /**
   * 获取用户信息
   * @returns {object} 用户信息响应
   */
  async getUserProfile() {
    if (!currentSession) {
      throw new Error('需要先设置认证会话')
    }
    return await usersModule.getUserProfile(currentSession.auth_token, currentSession.csrf_token)
  },
}

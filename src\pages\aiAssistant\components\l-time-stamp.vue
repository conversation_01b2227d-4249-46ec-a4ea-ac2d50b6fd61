<template>
  <div class="time-stamp">
    {{ formattedTime }}
  </div>
</template>

<script setup>
import { computed } from 'vue'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

const props = defineProps({
  time: {
    type: [String, Date, Number],
    required: true,
  },
})

const formattedTime = computed(() => {
  const now = dayjs()
  const messageTime = dayjs(props.time)

  // 如果是当天，显示具体时间，如 "下午 2:30"
  if (now.isSame(messageTime, 'day')) {
    return messageTime.format('A h:mm').replace('AM', '上午').replace('PM', '下午')
  }

  // 如果是昨天，显示 "昨天" + 具体时间
  if (now.subtract(1, 'day').isSame(messageTime, 'day')) {
    return '昨天 ' + messageTime.format('A h:mm').replace('AM', '上午').replace('PM', '下午')
  }

  // 如果是本周内，显示星期几
  if (now.diff(messageTime, 'day') < 7) {
    return messageTime.format('dddd')
  }

  // 其他情况，显示完整日期
  return messageTime.format('YYYY年 M月 D日')
})
</script>

<style lang="scss" scoped>
.time-stamp {
  text-align: center;
  font-size: 12px;
  color: var(--color-gray-500);
  margin-bottom: 12px;
}
</style> 
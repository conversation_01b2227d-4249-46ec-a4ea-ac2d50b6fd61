<template>
  <uni-swipe-action>
    <uni-swipe-action-item
      v-for="(item, index) in taskList"
      :key="item._id"
      :right-options="rightOption"
      :left-options="leftOption"
      @click="onClickItem($event, item._id, index)"
      @change="onChangeItem"
    >
      <z-task :day="day" :todo="item" :color="color" @on-change="emits('onChange')" @on-add-rec="onAddRec" />
    </uni-swipe-action-item>
  </uni-swipe-action>

  <z-task-popup
    v-model:show="taskVisible"
    :type="taskInfo.type"
    :edit-id="taskInfo.editId"
    :todo="todo"
    @on-submit="emits('onChange')"
  />
</template>
<script setup>
import { defineProps, defineEmits, watch, ref } from 'vue'
const props = defineProps({
  sortKey: {
    type: String,
    default: '',
  },
  taskList: {
    type: Array,
    default: () => [],
  },
  day: {
    type: String,
    default: '',
  },
  color: {
    type: String,
    default: '#ee734a',
  },
})
const emits = defineEmits('update:visible', 'onChange')

// 右侧操作按钮
const rightOption = [
  {
    key: 'toToday',
    text: '顺延',
    style: {
      backgroundColor: '#007aff',
    },
  },
  {
    key: 'del',
    text: '删除',
    style: {
      backgroundColor: '#dd524d',
    },
  },
]
// 左侧操作按钮
const leftOption = [
  {
    key: 'toUp',
    text: '上移',
    style: {
      backgroundColor: '#007aff',
    },
  },
  {
    key: 'toDown',
    text: '下移',
    style: {
      backgroundColor: '#dd524d',
    },
  },
]

// 点击操作按钮
const onClickItem = async (e, _id, index) => {
  const { key } = e.content
  if (key === 'del') {
    // 删除
    delTaskApi(_id).then(() => {
      emits('onChange')
      uni.showToast({
        title: '删除成功',
        icon: 'none',
      })
    })
  } else if (key === 'toToday') {
    // 顺延
    const today = dayjs().format('YYYY-MM-DD')
    updateTaskApi(_id, { startDate: today, endDate: today }).then(() => {
      emits('onChange')
      uni.showToast({
        title: '顺延成功',
        icon: 'none',
      })
    })
  } else if (key === 'toUp' || key === 'toDown') {
    if (!props.sortKey) return
    // 上移
    const todoItem = props.taskList.splice(index, 1)[0]
    // 交换位置
    if (key === 'toUp') {
      props.taskList.splice(index - 1, 0, todoItem)
    } else {
      props.taskList.splice(index + 1, 0, todoItem)
    }
    const orderBy = {}
    props.taskList.forEach((e, i) => {
      orderBy[e._id] = i * 100000
    })
    const config = await getConfigApi(props.sortKey)
    if (config) {
      await updateConfigApi(props.sortKey, orderBy)
    } else {
      await addConfigApi({
        key: props.sortKey,
        value: orderBy,
      })
    }
    emits('onChange')
  }
}
const onChangeItem = (e) => {
  // console.log('点击 onChangeItem 了' + (e.position === 'left' ? '左侧' : '右侧') + e.content.text + '按钮')
}

// 添加量化任务进度
const onAddRec = (data) => {
  taskInfo.value = data
  taskVisible.value = true
}

const taskInfo = ref({
  type: '',
  parentId: '',
  okrId: '',
  editId: '',
})

const taskVisible = ref(false)
</script>
<style lang="scss" scope></style>

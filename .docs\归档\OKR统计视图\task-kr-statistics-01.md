# 任务：KR 详情页统计视图 - UI 框架与 Tab 集成

## 任务描述
此任务作为实现统计视图功能的第一步，重点是搭建基础的 UI 框架。主要工作是在`krDetail.vue`页面上添加 Tab 切换功能，允许用户在"进度历史"和"统计情况"两个视图间切换，并创建新的`l-statistics.vue`组件的基本文件结构。

## 所属功能模块
OKR 统计视图

## 技术实现详情

1.  **修改 `krDetail.vue`**
    -   在 `<script setup>` 中，定义一个 `ref` 变量 `activeTab` 来追踪当前激活的 Tab，默认值为 `'history'`。
    -   在进度记录卡片的 `class="card-header"` 内部，添加 Tab 切换的 HTML 结构，包含"进度历史"和"统计情况"两个可点击的 `div`。
    -   使用 `v-bind:class` 为当前激活的 Tab 添加 `.active` 类。
    -   为每个 Tab `div` 添加 `@click` 事件，用于更新 `activeTab` 的值。
    -   在 `class="card-body"` 内部，使用 `v-if="activeTab === 'history'"` 和 `v-else` 来条件渲染 `<l-progress-history>` 组件和新的 `<l-statistics>` 组件。

2.  **创建 `l-statistics.vue` 组件**
    -   在 `src/pages/okr/components/` 目录下创建新文件 `l-statistics.vue`。
    -   组件需使用 `<script setup>`。
    -   使用 `defineProps` 定义组件接收的属性，至少应包括 `taskDetail` 对象，以便后续任务获取 `repeatFlag` 和 `recList`。
    -   在 `<template>` 中放置一个占位符 `div`，用于后续图表渲染。

3.  **集成组件**
    -   在 `krDetail.vue` 中 `import` 新创建的 `l-statistics.vue` 组件。
    -   在 `v-else` 的区块中正确使用 `<l-statistics>` 组件，并传入必要的 `taskDetail` prop。

4.  **添加样式**
    -   在 `krDetail.vue` 的 `<style>` 部分，为 `.card-tabs` 和 `.card-tab` (包括 `.active` 状态) 添加样式，确保其外观和交互符合 UI 设计。

## 验收标准
-   在关键结果详情页的进度记录卡片中，正确显示"进度历史"和"统计情况"两个 Tab。
-   默认选中的是"进度历史"Tab。
-   点击"统计情况"Tab 时，会切换视图，显示 `l-statistics.vue` 的占位内容。
-   点击"进度历史"Tab 时，能切换回原有的进度历史列表。
-   页面切换流畅，无任何控制台报错。

## 依赖关系
-   **上游依赖**: `.docs/OKR统计视图/kr-detail-statistics-view.md` (需求文档)
-   **下游依赖**: `task-kr-statistics-02.md`

## 优先级
高 
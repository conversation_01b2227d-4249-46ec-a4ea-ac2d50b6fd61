/**
 * 数组类
 */
export class ArrayManager {
  constructor() {}

  /**
   * 数字类型数组排序
   * @param arr 数组
   * @param type  排序方式，asc:升序，desc：降序
   * 
   * @default asc
   */
  sort = (arr, type = "asc") => {
    let arrNew = JSON.parse(JSON.stringify(arr));
    arrNew.sort((a, b) => {
      if (type == "asc") {
        return a - b
      } else {
        return b - a
      }
    })
    return arrNew;
  }

  /**
   * 数组分割
   * @param arr 要处理的数组
   * @param len 分割长度
   */
  split = (arr, len) => {
    if (len == 0) {
      return arr;
    }
    let ret = []
    for (let i = 0; i < arr.length; i += len) {
      ret.push(arr.slice(i, i + len))
    }
    return ret
  }

  /**
   * 判断两个数组的数据是否相同
   * @param arr1  第一个数组
   * @param arr2  第二个数组
   * 
   * @@return {true/false}
   */
  isSame = (arr1, arr2) => {
    let newArr1 = JSON.parse(JSON.stringify(arr1));
    let newArr2 = JSON.parse(JSON.stringify(arr2));
    let arr1Sort = newArr1.sort().toString();
    let arr2Sort = newArr2.sort().toString();
    var c = newArr1.length === newArr2.length && arr1Sort === arr2Sort
    return c;
  }

  /**
   * 移除数组中的元素
   * @param arr 要操作的数组
   * @param value 要移除的值
   * 
   * @@return {Array} 移除元素后的数组
   */
  remove = (arr, value) => {
    let newArr = JSON.parse(JSON.stringify(arr));
    let indexOf = newArr.indexOf(value);
    if (indexOf >= 0) {
      newArr.splice(indexOf, 1)
    }
    return newArr;
  }

  /**
   * 添加新元素到指定位置
   */
  add = (arr, index, value) => {
    let newArr = JSON.parse(JSON.stringify(arr));
    newArr.splice(index, 0, value);
    return newArr;
  }

  /**
   * 数组中的最大值
   */
  max = (arr) => {
    let newArr = JSON.parse(JSON.stringify(arr));
    newArr.sort((a, b) => {
      return b - a
    })
    return newArr[0];
  }

  /**
   * 数组中的最小值
   */
  min = (arr) => {
    let newArr = JSON.parse(JSON.stringify(arr));
    newArr.sort((a, b) => {
      return a - b
    })
    return newArr[0];
  }

  /**
   * 数组打乱
   */
  disruption = (arr) => {
    return arr.sort(() => Math.random() - 0.5);
  }

  /**
   * 随机取值
   * @param arr 数组
   * @param length 取出的长度
   * @param isSame  是否可以相同
   */
  random = (arr, length = 1, isSame = false) => {
    if (arr.length <= length) {
      return arr;
    }
    let copyArr = JSON.parse(JSON.stringify(arr));
    let newArr = []; //创建一个新数组
    for (let i = 0; i < length; i++) {
      let temp = Math.floor(Math.random() * copyArr.length); //取随机下标
      newArr.push(copyArr[temp]); //添加到新数组
      if (!isSame) {
        copyArr.splice(temp, 1) //删除当前的数组元素,避免重复
      }
    }
    return newArr;
  }

  /**
   * 去重
   */
  unique = (arr) => {
    let copyArr = JSON.parse(JSON.stringify(arr));
    if (Array.hasOwnProperty('from')) {
      return Array.from(new Set(copyArr));
    } else {
      var n = {},
        r = [];
      for (var i = 0; i < copyArr.length; i++) {
        if (!n[copyArr[i]]) {
          n[copyArr[i]] = true;
          r.push(copyArr[i]);
        }
      }
      return r;
    }
  }

  /**
   * 合并去重
   */
  union = (arr, arr2) => {
    var newArr = arr.concat(arr2);
    return this.unique(newArr);
  }

  /**
   * 取交集
   */
  intersect = (arr, arr2) => {
    arr = this.unique(arr);
    let newArr = [];
    for (let i = 0; i < arr.length; i++) {
      if (arr2.includes(arr[i])) {
        newArr.push(arr[i])
      }
    }
    return newArr;
  }

  /**
   * 是否是数组
   */
  isArray(value) {
    if (typeof Array.isArray === 'function') {
      return Array.isArray(value)
    }
    return Object.prototype.toString.call(value) === '[object Array]'
  }

  /**
   * 平均值
   * @param {Object} arr
   */
  avg = (arr) => {
    let sum = 0;
    for (let i = 0; i < arr.length; i++) {
      sum += arr[i];
    }
    return sum / arr.length;
  }

  /**
   * 求和
   */
  sum = (arr) => {
    return arr.reduce((pre, cur) => {
      return pre + cur
    })
  }
}
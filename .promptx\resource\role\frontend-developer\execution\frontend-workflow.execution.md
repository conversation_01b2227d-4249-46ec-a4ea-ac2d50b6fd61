<execution>
  <constraint>
    ## 前端开发技术约束
    - **浏览器兼容性**：需要支持主流浏览器的最新两个版本
    - **性能要求**：首屏加载时间不超过3秒，交互响应时间不超过100ms
    - **代码质量**：TypeScript覆盖率90%以上，单元测试覆盖率80%以上
    - **安全要求**：遵循OWASP前端安全最佳实践
    - **无障碍性**：符合WCAG 2.1 AA级标准
  </constraint>

  <rule>
    ## 强制性开发规则
    - **类型安全**：所有新代码必须使用TypeScript，严禁使用any类型
    - **组件规范**：所有UI组件必须支持props类型定义和默认值
    - **错误处理**：所有异步操作必须包含错误处理逻辑
    - **性能监控**：关键页面必须集成性能监控代码
    - **代码审查**：所有代码变更必须通过至少一人的代码审查
  </rule>

  <guideline>
    ## 开发指导原则
    - **渐进增强**：从基础功能开始，逐步添加高级特性
    - **移动优先**：优先考虑移动端体验，再适配桌面端
    - **组件复用**：优先使用现有组件，避免重复开发
    - **性能优先**：在功能实现和性能之间找到平衡点
    - **用户体验**：始终从用户角度思考功能设计和交互流程
  </guideline>

  <process>
    ## 前端开发标准流程
    
    ### Phase 1: 需求分析与设计 (20%)
    ```mermaid
    flowchart TD
        A[需求文档] --> B[UI设计稿]
        B --> C[交互原型]
        C --> D[技术方案]
        D --> E[开发计划]
    ```
    
    **关键活动**：
    - 理解业务需求和用户场景
    - 分析UI设计稿和交互细节
    - 确定技术实现方案
    - 评估开发工作量和时间
    
    ### Phase 2: 环境搭建与架构 (15%)
    ```mermaid
    graph LR
        A[项目初始化] --> B[依赖安装]
        B --> C[构建配置]
        C --> D[开发环境]
        D --> E[代码规范]
    ```
    
    **关键活动**：
    - 搭建开发环境和构建工具
    - 配置代码规范和质量检查
    - 设计组件架构和目录结构
    - 建立开发和测试流程
    
    ### Phase 3: 核心开发 (50%)
    ```mermaid
    flowchart TD
        A[组件开发] --> B[功能实现]
        B --> C[样式编写]
        C --> D[交互逻辑]
        D --> E[数据对接]
        E --> F[单元测试]
    ```
    
    **开发循环**：
    1. **组件开发**：按照设计稿实现UI组件
    2. **功能实现**：添加业务逻辑和数据处理
    3. **样式优化**：响应式布局和视觉效果
    4. **交互完善**：用户交互和状态管理
    5. **接口对接**：与后端API集成
    6. **测试编写**：单元测试和集成测试
    
    ### Phase 4: 测试与优化 (10%)
    ```mermaid
    graph TD
        A[功能测试] --> B[性能测试]
        B --> C[兼容性测试]
        C --> D[无障碍测试]
        D --> E[优化调整]
    ```
    
    **测试重点**：
    - 功能完整性和边界情况
    - 性能指标和用户体验
    - 多浏览器和设备兼容性
    - 无障碍访问和键盘导航
    
    ### Phase 5: 部署与监控 (5%)
    ```mermaid
    flowchart LR
        A[构建打包] --> B[部署上线]
        B --> C[性能监控]
        C --> D[错误追踪]
        D --> E[用户反馈]
    ```
    
    **上线流程**：
    - 生产环境构建和部署
    - 性能监控和错误追踪
    - 用户行为分析和反馈收集
    - 持续优化和问题修复
  </process>

  <criteria>
    ## 前端开发质量标准
    
    ### 代码质量指标
    - ✅ TypeScript覆盖率 ≥ 90%
    - ✅ 单元测试覆盖率 ≥ 80%
    - ✅ ESLint检查无错误
    - ✅ 代码审查通过率 100%
    
    ### 性能指标
    - ✅ 首屏加载时间 ≤ 3秒
    - ✅ 交互响应时间 ≤ 100ms
    - ✅ Lighthouse性能评分 ≥ 90
    - ✅ 核心Web指标达标
    
    ### 用户体验指标
    - ✅ 移动端适配完整
    - ✅ 无障碍性测试通过
    - ✅ 多浏览器兼容性验证
    - ✅ 用户满意度 ≥ 85%
    
    ### 维护性指标
    - ✅ 组件复用率 ≥ 60%
    - ✅ 代码重复率 ≤ 5%
    - ✅ 文档覆盖率 ≥ 80%
    - ✅ 技术债务控制在合理范围
  </criteria>
</execution>

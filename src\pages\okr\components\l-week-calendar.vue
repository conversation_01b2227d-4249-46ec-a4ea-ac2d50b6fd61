<template>
  <view class="calendar-section">
    <view class="calendar-header">
      <view class="month-selector">
        <text class="month-text">{{ monthYearDisplay }}</text>
        <view class="calendar-icon-btn">
          <i class="fas fa-calendar-alt"></i>
        </view>
      </view>
      <view class="calendar-actions">
        <view class="week-overview-btn" @click="$emit('showWeekOverview')">
          <i class="fas fa-chart-pie"></i>
          <text>周概览</text>
        </view>
        <view class="calendar-nav-btn" @click="navigateWeek(-1)">
          <i class="fas fa-chevron-left"></i>
        </view>
        <view class="calendar-nav-btn" @click="navigateWeek(1)">
          <i class="fas fa-chevron-right"></i>
        </view>
      </view>
    </view>

    <view class="calendar-week">
      <view
        v-for="(day, index) in weekDaysData"
        :key="index"
        class="calendar-day"
        :class="{ active: day.isActive, today: day.isToday }"
        @click="selectDay(index)"
      >
        <text class="day-name">{{ day.name }}</text>
        <view class="day-number" :class="{ weekend: day.isWeekend }">{{ day.date }}</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, defineComponent, watch, onMounted } from 'vue'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn' // 导入中文语言包

// 设置 dayjs 为中文
dayjs.locale('zh-cn')

defineComponent({
  name: 'LWeekCalendar',
})

const props = defineProps({
  weekDays: {
    type: Array,
    default: () => [],
  },
  pendingTasksCount: {
    type: Number,
    default: 0,
  },
  monthYearText: {
    type: String,
    default: '',
  },
  currentDate: {
    type: [String, Date, Object],
    default: () => dayjs().format('YYYY-MM-DD'),
  },
})

const emit = defineEmits(['selectDay', 'navigateWeek', 'showWeekOverview', 'dateChange'])

// 当前选中的日期
const selectedDate = ref(dayjs(props.currentDate))
// 当前周的开始日期（周一）
const weekStart = ref(getWeekStart(selectedDate.value))
// 今天的日期
const today = dayjs()

// 获取一周的开始日期（周一）
function getWeekStart(date) {
  const day = date.day() || 7 // 将周日的 0 转为 7
  return date.subtract(day - 1, 'day')
}

// 计算当前周的日期数据
const weekDaysData = computed(() => {
  const days = []
  const startOfWeek = weekStart.value

  for (let i = 0; i < 7; i++) {
    const currentDay = startOfWeek.clone().add(i, 'day')
    days.push({
      name: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][i],
      date: currentDay.date(), // 获取日期数字
      fullDate: currentDay.format('YYYY-MM-DD'),
      isActive: currentDay.format('YYYY-MM-DD') === selectedDate.value.format('YYYY-MM-DD'),
      isWeekend: i >= 5, // 周六日为周末
      isToday: currentDay.format('YYYY-MM-DD') === today.format('YYYY-MM-DD'),
    })
  }

  return days
})

// 计算月份年份显示文本
const monthYearDisplay = computed(() => {
  // 获取本周的第一天和最后一天
  const firstDay = weekStart.value
  const lastDay = weekStart.value.clone().add(6, 'day')

  // 如果当前周跨月，只显示下个月
  if (firstDay.month() !== lastDay.month()) {
    // 显示下个月
    return `${lastDay.year()} 年 ${lastDay.month() + 1} 月`
  } else if (firstDay.year() !== lastDay.year()) {
    // 跨年显示下一年的月份
    return `${lastDay.year()} 年 ${lastDay.month() + 1} 月`
  } else {
    // 同月显示
    return `${firstDay.year()} 年 ${firstDay.month() + 1} 月`
  }
})

// 选择日期
const selectDay = (index) => {
  const newSelectedDate = weekStart.value.add(index, 'day')
  const newDateStr = newSelectedDate.format('YYYY-MM-DD')

  console.log('[调试] l-week-calendar selectDay', {
    index,
    newSelectedDate: newDateStr,
    currentSelectedDate: selectedDate.value.format('YYYY-MM-DD'),
  })

  // 避免重复选择同一天
  if (newDateStr === selectedDate.value.format('YYYY-MM-DD')) {
    console.log('[调试] l-week-calendar 跳过选择相同日期')
    return
  }

  selectedDate.value = newSelectedDate

  emit('selectDay', {
    index,
    date: newDateStr,
    dayjs: newSelectedDate,
    source: 'user-select', // 添加来源标记
  })
}

// 周导航
const navigateWeek = (direction) => {
  console.log('[调试] l-week-calendar navigateWeek', { direction })

  // 计算新的周开始日期
  weekStart.value = weekStart.value.add(direction * 7, 'day')

  // 发送事件通知父组件
  emit('navigateWeek', {
    direction,
    weekStart: weekStart.value.format('YYYY-MM-DD'),
    weekEnd: weekStart.value.clone().add(6, 'day').format('YYYY-MM-DD'),
    monthYear: monthYearDisplay.value,
    source: 'week-navigation', // 添加来源标记
  })

  // 确定要选择的日期：如果新的一周包含今天，则选中今天；否则选中该周的第一天
  let dayToSelect = 0 // 默认选中周一（第一天）
  const weekEndDate = weekStart.value.clone().add(6, 'day')
  const todayStr = today.format('YYYY-MM-DD')
  
  // 检查今天是否在当前周范围内
  const isThisWeek = today.isAfter(weekStart.value.clone().subtract(1, 'day')) && 
                     today.isBefore(weekEndDate.clone().add(1, 'day'))
  
  if (isThisWeek) {
    // 如果今天在当前周内，计算今天是周几（索引）
    const todayIndex = today.day() - 1
    dayToSelect = todayIndex < 0 ? 6 : todayIndex // 周日的索引调整为6
  }
  
  // 自动选择合适的日期
  selectDay(dayToSelect)

  // 触发日期变更事件
  emit('dateChange', {
    weekStart: weekStart.value.format('YYYY-MM-DD'),
    weekEnd: weekStart.value.clone().add(6, 'day').format('YYYY-MM-DD'),
    selectedDate: selectedDate.value.format('YYYY-MM-DD'),
    source: 'week-navigation', // 添加来源标记
  })
}

// 监听外部传入的 currentDate 变化
watch(
  () => props.currentDate,
  (newDate, oldDate) => {
    if (newDate) {
      console.log('[调试] l-week-calendar watch currentDate', {
        newDate,
        oldDate,
        currentSelectedDate: selectedDate.value.format('YYYY-MM-DD'),
      })

      // 避免重复更新：只有当新日期与当前选中日期不同时才更新
      if (newDate !== selectedDate.value.format('YYYY-MM-DD')) {
        console.log('[调试] l-week-calendar 更新 selectedDate', newDate)
        selectedDate.value = dayjs(newDate)
        weekStart.value = getWeekStart(selectedDate.value)
      } else {
        console.log('[调试] l-week-calendar 跳过重复更新', newDate)
      }
    }
  },
  { immediate: true }
)

onMounted(() => {
  console.log('[调试] l-week-calendar onMounted')
  // 初始化时发送当前日期信息
  emit('dateChange', {
    weekStart: weekStart.value.format('YYYY-MM-DD'),
    weekEnd: weekStart.value.clone().add(6, 'day').format('YYYY-MM-DD'),
    selectedDate: selectedDate.value.format('YYYY-MM-DD'),
    source: 'initial-mount', // 添加来源标记
  })
})
</script>

<style lang="scss">
// 日历周视图
.calendar-section {
  background-color: var(--color-white);
  border-radius: var(--rounded-lg);
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: var(--shadow-sm);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.month-selector {
  display: flex;
  align-items: center;
}

.month-text {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--color-gray-800);
}

.calendar-icon-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-gray-100);
  border-radius: var(--rounded-full);
  margin-left: 16rpx;

  .fas {
    font-size: 24rpx;
    color: var(--color-gray-600);
  }
}

.calendar-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.week-overview-btn {
  display: flex;
  align-items: center;
  background-color: var(--color-primary);
  padding: 10rpx 20rpx;
  border-radius: var(--rounded-full);

  .fas {
    font-size: 24rpx;
    color: var(--color-white);
    margin-right: 8rpx;
  }

  text {
    font-size: 24rpx;
    color: var(--color-white);
  }
}

.calendar-nav-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-gray-100);
  border-radius: var(--rounded-full);

  .fas {
    font-size: 24rpx;
    color: var(--color-gray-600);
  }
}

.calendar-week {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
}

.calendar-day {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  border-radius: var(--rounded-md);
  transition: var(--transition-fast);

  &.active {
    .day-number {
      background-color: var(--color-primary);
      color: var(--color-white);
    }
  }

  &.today:not(.active) {
    .day-number {
      border: 2rpx solid var(--color-primary);
    }
  }
}

.day-name {
  font-size: 24rpx;
  font-weight: 500;
  color: var(--color-gray-500);
  margin-bottom: 8rpx;
}

.day-number {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--rounded-full);
  font-size: 28rpx;

  &.weekend {
    color: var(--color-primary-light);
  }
}
</style>

declare namespace API {
  type BaseFields = '_id' | 'createTime' | 'updateTime' | 'deleteTime' | 'isDirty'

  type NewOkr = Omit<DB.Okr, BaseFields>
  type EditOkr = Partial<NewOkr>

  type NewOkrConfig = Omit<DB.OkrConfig, BaseFields>
  type EditOkrConfig = Partial<DB.OkrConfig>

  type NewTask = Omit<DB.Task, BaseFields>
  type EditTask = Partial<DB.Task>

  type NewMemo = Omit<DB.Memo, BaseFields>
  type EditMemo = Partial<DB.Memo>

  type CreateParams<T> = Omit<T, BaseFields>
  type UpdateParams<T> = Partial<CreateParams<T>>

  interface BulkUpdate {
    key: string
    changes: {
      [key: string]: any
    }
  }
} 
# 任务：快捷输入UI开发

- **所属功能模块**: `diary-entry`
- **任务ID**: `task-diary-entry-02`
- **优先级**: 中

## 任务描述
开发一个全局悬浮的快捷输入按钮（`l-quick-input`），使用户能在应用的主要界面快速唤起日记输入功能。

## 技术实现详情
1.  **`l-quick-input` 组件**:
    - **UI**:
        - 设计为一个悬浮在页面右下角的圆形按钮（FAB - Floating Action Button）。
        - 图标应直观，如“+”或“笔”。
        - 点击后可以展开更多选项，如“文字”、“语音”、“图片”。
    - **交互**:
        - 点击按钮时应有平滑的动画效果。
        - 触发点击事件后，需要通知父组件或通过全局事件总线，以唤起 `z-diary-editor` 编辑器。
        - 组件应能适应不同页面，在不需要显示的页面（如登录页）可以隐藏。

## 验收标准
- 快捷输入按钮以悬浮形式在主要页面正确展示。
- 点击按钮能触发预定义的事件。
- 按钮及其展开菜单的 UI 效果符合设计要求。
- 组件在不同页面的显示/隐藏逻辑正常。

## 依赖关系
- `task-diary-entry-03`: 需要与 `z-diary-editor` 组件交互，由本组件触发其显示。

## 状态追踪
- 未开始 
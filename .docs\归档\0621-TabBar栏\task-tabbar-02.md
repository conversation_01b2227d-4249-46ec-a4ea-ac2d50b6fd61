# 任务：创建主页框架

## 任务信息

- **所属功能模块**: TabBar 栏自定义
- **优先级**: 高
- **预估工时**: 2 人日
- **状态**: 待开发

## 任务描述

创建新的主页框架，作为应用的入口页面。主页将包含两个主要部分：上方的内容区域和下方的 TabBar 区域。实现基本的页面结构和样式，为后续的 TabBar 组件和页面切换机制做好准备。

## 技术实现详情

### 1. 创建主页文件

创建`pages/index/index.vue`文件，设计基础页面框架：

```vue
<template>
  <div class="container">
    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 这里后续会加载动态组件 -->
      <div class="placeholder">内容区域占位符（将由后续任务实现的动态组件替换）</div>
    </div>

    <!-- TabBar 区域（将在 task-tabbar-03 中实现） -->
    <div class="tabbar-container">
      <!-- TabBar 组件占位，将在后续任务中完善 -->
      <div class="tabbar-placeholder">TabBar 区域占位符（将由后续任务实现的 TabBar 组件替换）</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 基本状态变量定义
const currentTab = ref('okr') // 默认选中的 tab

// 简单的页面初始化逻辑
onMounted(() => {
  console.log('主页已加载')
})
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
}

.content-area {
  flex: 1;
  overflow-y: auto;
  position: relative;
}

.placeholder {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  font-size: 14px;
}

.tabbar-container {
  height: 50px;
  background-color: #fff;
}

.tabbar-placeholder {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  font-size: 14px;
}
</style>
```

### 2. 创建组件目录

创建 TabBar 组件的目录结构：

```
pages/
  index/
    component/
      tabBar.vue  // 将在task-tabbar-03中实现
    index.vue     // 主页文件
```

### 3. 处理安全区域适配

为确保在不同设备（如 iPhone 带刘海屏）上的显示效果一致，添加安全区适配：

```css
/* 添加到 style 部分 */
.tabbar-container {
  height: 50px;
  background-color: #fff;
  padding-bottom: env(safe-area-inset-bottom); /* 适配底部安全区 */
}
```

### 4. 相关路由配置

确保主页文件已正确配置在`pages.json`中（依赖 task-tabbar-01）：

```json
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "主页",
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none" // 禁用回弹效果
        }
      }
    }
    // ... 其他页面配置
  ]
}
```

## 验收标准

1. 主页文件`pages/index/index.vue`已创建，包含基本的页面结构
2. 主页布局正确：上方为内容区域，下方为 TabBar 区域
3. 页面样式合理，符合项目 UI 风格
4. 适配了不同设备的安全区域
5. 页面能正常加载和显示

## 依赖关系

- 依赖 task-tabbar-01 完成（pages.json 配置调整）
- task-tabbar-03 依赖此任务完成（TabBar 组件实现）
- task-tabbar-04 依赖此任务完成（页面切换机制）

## 注意事项

1. 主页设计需考虑不同屏幕尺寸的适配
2. 布局应考虑未来动态加载组件的性能表现
3. 确保主页框架足够灵活，能容纳所有 tab 页面的内容

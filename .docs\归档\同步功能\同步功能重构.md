# 同步功能重构需求

status: draft

## 背景
当前系统的同步功能代码逻辑混乱，缺乏清晰的架构设计和错误处理机制，导致维护困难且容易出现数据不一致的问题。重构同步功能旨在提高代码质量、增强系统稳定性，并为未来功能扩展提供更好的基础架构。

## 需求

### 功能需求
- 保持现有同步功能的所有能力，包括数据双向同步
- 优化同步流程，减少不必要的网络请求
- 完善冲突检测与解决机制
- 增强同步状态的可视化展示
- 提供更详细的同步日志与错误信息
- 支持选择性同步（按数据类型或时间范围）

### 非功能需求
- 提高同步成功率，目标达到 99.5% 以上
- 减少同步过程中的资源消耗（内存、CPU、网络带宽）
- 改善代码可维护性，提高测试覆盖率
- 优化同步速度，大数据量同步性能提升 30% 以上
- 增强异常场景下的恢复能力

## 当前实现分析

### 现有方案实现思路

当前同步功能的主要实现位于 `src/api/syncServer.ts`，其核心思路如下：

1. **数据同步标记机制**：
   - 使用 `lastSyncTime` 作为同步时间点标记
   - 使用 `isDirty` 标记本地修改过需要同步的数据
   - 维护 `syncStorage` 对象作为同步过程的临时存储

2. **同步流程**：
   - 收集本地未同步数据（标记为 `isDirty=1` 的记录）
   - 将收集的数据上传到服务端
   - 从服务端获取最新数据
   - 合并本地和服务端数据（基于时间戳比较）
   - 保存合并后的数据到本地数据库
   - 更新 `lastSyncTime` 作为新的同步时间点

3. **冲突处理**：
   - 采用"最新胜出"策略，比较本地与服务端数据的更新时间
   - 时间戳较新的数据版本会覆盖较旧的版本

4. **状态管理**：
   - 使用 `isFetching` 全局变量标记是否正在同步
   - 通过 `uni.$emit('updateCloud')` 事件通知机制来触发 UI 更新

### 服务端实现分析 (`sync` 函数)

服务端的核心同步逻辑位于 `okr/index.obj.js` 的 `sync` 函数中，其思路如下：

1.  **身份验证**: 通过 `uniID.checkToken` 校验用户令牌。
2.  **增量数据拉取**: 从服务端拉取 `updateTime` 在客户端上次同步时间 `lastSyncTime` 之后的所有数据，实现向客户端的增量数据下发。
3.  **数据写入**: 遍历客户端上传的 `syncData`，使用 `db.collection().doc().set()` 方法将数据逐条写入或覆盖到数据库。
4.  **冲突解决**: 存在简单的冲突解决逻辑，但仅在准备返回给客户端的数据中进行了处理。写入数据库时，直接使用了客户端上传的 `syncData`，并未应用有效的冲突解决。
5.  **不完善的实现**: 代码中存在大量 `TODO` 注释，表明开发者已意识到在事务、错误处理、删除逻辑、性能优化等方面存在严重不足。

### 现有实现的主要缺点

通过对客户端和服务端代码的综合分析，发现当前同步机制存在以下几个核心问题：

1.  **缺乏原子性和事务支持 (客户端与服务端)**:
    - **客户端**：同步过程中如果中断，没有回滚机制，可能导致数据不一致。
    - **服务端**：使用 `Promise.all` 批量更新数据，但不支持事务。一旦部分数据写入失败，会导致服务端与客户端数据状态不一致。此问题已被开发者标记为"阻塞性的 bug"，可能导致后续同步永久失败。

2.  **高依赖单点**：
    - 完全依赖 `lastSyncTime` 作为同步基准点，一旦该时间点丢失或不准确，整个同步机制将失效。

3.  **脆弱且不完整的错误处理**:
    - **客户端**：错误处理机制不完善，对不同类型的同步错误缺乏针对性处理。
    - **服务端**：多个 `TODO` 表明错误处理缺失，例如 token 验证失败、数据库更新失败等情况没有向客户端抛出明确的、可处理的错误。

4.  **未处理删除操作**:
    - 服务端代码中明确标记 `TODO: 删除逻辑需要处理一下`，表明当前同步逻辑不支持记录的删除同步，这会导致客户端已删除的数据在同步后重新出现。

5.  **性能问题**:
    - **客户端**：数据保存使用 `put` 方式，需要重新查询全量数据，耗费性能。
    - **服务端**：逐条更新数据库，未采用批量操作，在高并发或大数据量时性能较低。

6.  **简单的冲突解决策略**:
    - 客户端和服务端都只采用"最新更新时间胜出"的策略，无法处理更复杂的业务冲突，容易造成数据覆盖丢失。

7.  **类型安全与代码组织问题 (客户端)**:
    - 客户端代码存在多处 TypeScript 类型问题，增加了代码的不稳定性和维护难度。
    - 功能职责不清晰，同步逻辑、网络请求、数据处理混杂在一起，难以测试和扩展。

### 需要优化的方向

基于以上问题，需要从以下方向进行优化：

1. **增强同步基准机制**：
   - 引入数据版本控制，不仅依赖时间戳
   - 使用数据变更记录（changelog）作为补充同步依据

2. **引入事务支持**：
   - 添加同步事务机制，确保同步过程的原子性
   - 实现同步失败时的回滚能力

3. **优化数据处理性能**：
   - 实现真正的增量同步，只传输和处理变更数据
   - 优化本地数据库操作，减少全量查询和更新

4. **完善错误处理**：
   - 分类处理不同类型的同步错误
   - 为每种错误提供对应的恢复机制

5. **增强类型安全**：
   - 完善 TypeScript 类型定义
   - 使用接口和类型保护确保代码的类型安全

6. **改进网络状态管理**：
   - 实现网络状态监听机制，响应实时网络变化
   - 添加离线模式与重连后自动同步功能

7. **增强冲突解决能力**：
   - 设计更智能的冲突检测与解决策略
   - 必要时提供用户介入解决冲突的机制

8. **重构代码组织**：
   - 采用分层架构设计，明确各模块职责
   - 提高代码可测试性和可维护性

## 技术方案

### 实现思路
1. **架构重构**：
   - 采用分层架构设计，清晰分离同步逻辑、网络请求和数据处理
   - 引入同步状态管理模块，统一管理同步过程中的各种状态
   - 设计可扩展的插件式同步适配器，便于未来支持更多同步源

2. **同步流程优化**：
   - 实现增量同步算法，只传输变更数据
   - 引入队列机制处理同步任务，避免并发冲突
   - 优化网络请求策略，合并小请求，减少请求次数

3. **错误处理与恢复**：
   - 完善错误分类与处理策略
   - 实现同步断点续传机制
   - 设计自动重试策略，针对不同错误类型采用不同重试机制

4. **用户体验改进**：
   - 重新设计同步状态 UI，提供更直观的进度展示
   - 增加详细的同步日志查看界面
   - 提供同步设置界面，允许用户配置同步行为

### 架构设计
```mermaid
graph TD
    A[用户界面层] --> B[同步控制器]
    B --> C[同步任务调度器]
    C --> D1[数据比对模块]
    C --> D2[冲突解决模块]
    C --> D3[同步执行模块]
    D1 --> E[数据访问层]
    D2 --> E
    D3 --> E
    E --> F1[本地存储适配器]
    E --> F2[远程API适配器]
    G[同步状态管理器] --> A
    G <--> B
    G <--> C
    H[同步日志服务] <--> C
    H --> A
```

### 技术栈与约束
- 前端框架：Vue.js
- 状态管理：Vuex/Pinia
- 网络请求：统一使用封装后的 request.ts
- 本地存储：继续使用现有的数据库方案
- 代码规范：遵循项目已有的 TypeScript 规范
- 测试要求：单元测试覆盖率不低于 80%

## 风险评估

### 假设与未知因素
- 假设现有 API 接口能够支持增量同步
- 假设用户数据量不会超过系统设计的处理能力
- 未知在极端网络条件下的同步稳定性
- 未知不同设备间的性能差异对同步体验的影响

### 潜在风险
- **数据丢失风险**: 
  - 解决方案：实现同步前数据备份机制，关键操作添加事务支持
  
- **兼容性问题**: 
  - 解决方案：全面的浏览器兼容性测试，针对不同平台制定专门的适配策略
  
- **性能瓶颈**: 
  - 解决方案：进行性能基准测试，识别并优化关键路径代码
  
- **用户习惯改变**: 
  - 解决方案：提供详细的功能介绍和引导，收集用户反馈并及时调整

## 实施计划
1. 需求分析与现有代码评审 (1 周)
2. 架构设计与技术方案确认 (1 周)
3. 核心模块开发 (2 周)
4. 界面重构与集成 (1 周)
5. 测试与性能优化 (1 周)
6. 灰度发布与监控 (1 周)

总计时间：约 7 周 
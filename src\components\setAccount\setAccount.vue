<template>
	<view>
		<!-- 添加账单记录 -->
    <uni-popup ref="pop" type="bottom" background-color="rgba(0,0,0,0)" @change="onChange">
    <view style="background-color: #fff;border-radius: 10px 10px 0 0">
      <view class="p-24r">
        <!-- tab 支出，收入 -->
        <view class="flex mt-10 ml-5 mr-5" style="justify-content: space-between">
          <view>
            <text class="mr-10 px-8 py-3 rounded-4 text-16 "
                  :style="{backgroundColor: systemTheme.outColor,opacity: type === 0 ? 1:0.3,color:'white','font-weight':'bold'}"
                  @click="type=0">支出
            </text>
            <text class="px-8 py-3 rounded-4 text-16"
                  :style="{backgroundColor: systemTheme.inColor,opacity: type === 1 ? 1:0.3,color:'white','font-weight':'bold'}"
                  @click="type=1">收入
            </text>
          </view>
          <view>
            <text @click="visible = false" style="font-size: 20px;font-weight: bold" class="iconfont icon-close"></text>
          </view>
        </view>
        <!-- 金额 -->
        <view class="flex justify-start py-12r items-center" style="border-bottom: 1px solid #eee;">
          <text class="text-48r p-16r" style="color: black;font-weight: bold;">¥</text>
          <text class="flex-auto uni-input p-24r text-64r" :style="{color:money===''?'#999':'#333'}"
                style="font-weight: bold">{{ money === '' ? '0.00' : money }}
          </text>
          <!-- <input class="flex-auto uni-input p-24r text-64r" type="digit" placeholder="0.00" v-model="num"/> -->
          <text class="iconfont p-16r rounded-20" :class="activeTypeName"
                :style="{backgroundColor:type === 1?systemTheme.inColor:systemTheme.outColor}"
                style="color: white;font-size: 30px;"></text>
        </view>
        <!-- 类型 -->
        <scroll-view>
          <view @touchmove.stop @touch.stop class="flex flex-wrap my-10 py-3" style="overflow: scroll;row-gap: 4px;"
                :style="{height:typeList.length>6?'136px':'104px'}">
            <view v-for="item in typeList" class="flex flex-column items-center basis-1/5"
                  @click="chooseType(item)">
              <text class="iconfont rounded-50p p-12 mb-4" style="font-size: 24px"
                    :style="{color:item.id === activeType? 'white':'black',backgroundColor:item.id===activeType?type===1? systemTheme.inColor: systemTheme.outColor:'rgba(238, 238, 238,.4)'}"
                    :class="item.icon"></text>
              <text class="text-12 text-999">{{ item.title }}</text>
            </view>
          </view>
        </scroll-view>
        <!-- 时间，备注，图片 -->
        <view class="flex items-center pt-12">
          <view class="flex flex-column items-center justify-center mr-8" @click="handlePickerShow()">
            <text class="iconfont icon-rili mb-4" style="font-size: 48rpx;"></text>
            <view class="picker-text">{{ showDate }}</view>
          </view>
          <view class="flex-auto flex items-center">
            <!-- <text class="iconfont icon-icon text-60r mr-10" style="color: gold;font-size: 48rpx;"></text> -->
            <input class="uni-input" type="text" placeholder="输入备注内容" v-model="remark"/>
          </view>
          <view class="ml-8">
            <text v-if="base64Str==''" class="iconfont icon-tupian rounded-20p text-30 p-16r"
                  style="background-color: rgba(238, 238, 238,.4); color: #ccc;border: 1px dashed #eee;"
                  @click="chooseImg"></text>
            <view v-else class="relative">
              <image :src="base64Str" class="w-46p h-46p bg rounded-20p" mode="aspectFill"
                     @click="chooseImg"/>
              <text class="iconfont icon-close absolute -right-4 -top-4" @click="delImage"></text>
            </view>
          </view>
        </view>
      </view>
      <!-- 数字键盘 -->
      <view class="basis-2/4 flex flex-wrap">
        <monokeyboard :value="money" :show="true" :btnColor="keyboardColor" :isDecimal="true" confirmText="确定"
                      @change="get_number" @confirm="handleSave()">
        </monokeyboard>
      </view>

      <!-- 年月选择器 -->
      <KDatePicker v-model="showDatePicker"  :defaultValue="datePickerValue" @change="handleConfirm" type="date" :timeValue="timeValueStr" :isShowTime="true"></KDatePicker>
    </view>
    </uni-popup>
	</view>
</template>

<script>
	import {
		useTypeStore
	} from '@/store/type';
	import imageTools from '@/uni_modules/niceui-image-tools/util/image-tools.js'
	import monokeyboard from '@/components/keyboard/mono-keyboard.vue'
  import {useSystem} from "@/store/theme";
  import KDatePicker from "../../components/k-date-picker/KDatePicker.vue";
  import dayjs from 'dayjs'


  export default {
		name: "setAccount",
		components: {
			monokeyboard,
      KDatePicker
		},
		props: {
			// 是否显示popup
			visible: {
				type: Boolean,
				default: false
			},
			// 类型对象，type,icon
			typeObj: {
				type: Object,
				default: () => {},
			},
			// 修改or添加
			isUpdate: {
				type: Boolean,
				default: false
			},
			// 账单记录对象
			record: {
				type: Object,
				default: () => {},
			}
		},
		// 父组件visible置false
		emits: ['close', 'updateConfirm'],
		computed: {
			showDate() {
				return dayjs(this.datePickerValue).format('MM-DD')
			},
			money() {
				const str = this.moneyArr.join('')
				if (str) {
					return str
				}
				return ''
			},
			typeList() {
				return this.type ? this.typeStore.inType : this.typeStore.payType
			},
			keyboardColor() {
				return this.type === 0 ? this.systemTheme.outColor : this.systemTheme.inColor
			}
		},
		data() {
			return {
				activeType: '',
				activeTypeName: '',
				showDatePicker: false,
				datePickerValue: 0,
        timeValueStr:'', // HH:mm:ss
				moneyArr: [],
				remark: '',
				type: 0,
				isShow: false,
				base64Str: '',
        title:'',
        id:null
			};
		},
		watch: {
			visible(val) {
				if (val) {
					this.$refs.pop.open()
				} else {
					this.$refs.pop.close()
				}
			}
		},
		setup() {
			const typeStore = useTypeStore()
      const systemTheme = useSystem()
      return {
				typeStore,systemTheme
			}
		},
		methods: {
			get_number(e) {
				this.moneyArr = [...e]
			},
			// popup弹出初始化内容
			onChange(e) {
				if (e.show) {
          if (this.isUpdate){
            this.updateInit()
          } else {
						this.addInit()
					}
				} else {
          this.showDatePicker = false
					this.$emit('close')
				}
			},
			// 初始化添加内容
			addInit() {
				this.moneyArr.splice(0);
				this.remark = '';

        // 默认取类目的第一个
        if (this.typeStore.payType != null){
          this.activeType = this.typeStore.payType[0].id;
          this.activeTypeName = this.typeStore.payType[0].icon;
          this.type = this.typeStore.payType[0].type
        }
				this.datePickerValue = new Date().getTime()
        this.timeValueStr = dayjs(this.datePickerValue).format('HH:mm:ss')
        this.base64Str = ''
			},
			// 初始化编辑内容
			updateInit() {
				this.moneyArr = this.record.num.toString().split('');
				this.remark = this.record.remark;
				this.activeType = this.record.type_id;
				this.activeTypeName = this.record.icon;
				this.datePickerValue = dayjs(this.record.create_time,'YYYY-MM-DD HH:mm:ss').valueOf()
        this.timeValueStr = this.record.create_time.split(' ')[1]
				this.type = this.record.type;
				this.base64Str = this.record.image;
        this.title = this.record.title
        this.id = this.record.id
			},
			// 保存
			async handleSave() {
				if (this.moneyArr.length === 0) {
					uni.showToast({
						title: '请输入金额',
						icon: 'error'
					})
					return
				}
        const obj = {
          num: parseFloat(this.money),
          type_id: this.activeType,
          remark: this.remark,
          image: this.base64Str,
          create_time: dayjs(this.datePickerValue).format('YYYY-MM-DD HH:mm:ss'),
          icon: this.activeTypeName,
          type: this.type,
          title: this.title
        }

        if (this.id != null){
          obj.id =parseInt( this.id)
        }

        await this.$emit('updateConfirm',obj)
			},
			// 展开时间选择器
			handlePickerShow() {
        this.showDatePicker = true
			},
			// 选择时间
			handleConfirm(e) {
        if(typeof e === 'number' && !isNaN(e)){
          this.datePickerValue = e;
        }
			},
			// 取消选择时间
			handleCancel() {
				this.showDatePicker = false
			},
			// chooseType
			chooseType(obj) {
				this.activeType = obj.id
				this.activeTypeName = obj.icon
        this.title = obj.title
			},
			// chooseImg
			chooseImg() {
				const self = this;
				uni.chooseImage({
					count: 1, //默认9
					// sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
					// sourceType: ['album'], //从相册选择
					success: async function(res) {
						const filePath = res.tempFilePaths[0];
						imageTools.pathToBase64(filePath).then(base64 => {
								self.base64Str = base64
							})
							.catch(error => {
								console.error(error)
							})
					}
				});
			},
			delImage() {
				this.base64Str = ''

			}
		}
	}
</script>

<style lang="scss">

</style>

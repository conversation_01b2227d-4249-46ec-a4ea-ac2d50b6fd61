/**
 * 习惯管理模块
 * 处理习惯相关功能
 */

const {
	DIDA_API_BASE,
	DIDA_HABIT_APIS,
	REQUEST_CONFIG,
	buildDidaApiUrl,
} = require("../config");

const {
	logInfo,
	logError,
	createSuccessResponse,
	createErrorResponse,
	validateRequired,
	validateType,
	buildAuthHeaders,
	buildAuthCookies,
	safeJsonParse,
} = require("../utils");

/**
 * 获取所有习惯列表
 * @param {string} authToken - 认证令牌
 * @param {string} csrfToken - CSRF令牌
 * @returns {object} 习惯列表响应
 */
async function getHabits(authToken, csrfToken) {
	const methodName = "getHabits";
	logInfo(methodName, "开始获取习惯列表");

	try {
		// 参数验证
		const authValidation = validateRequired(authToken, "authToken");
		if (authValidation) return authValidation;

		const csrfValidation = validateRequired(csrfToken, "csrfToken");
		if (csrfValidation) return csrfValidation;

		// 构建请求URL
		const apiUrl = buildDidaApiUrl(DIDA_HABIT_APIS.get_habits);

		// 构建请求头和cookies
		const headers = buildAuthHeaders(authToken, csrfToken);
		const cookies = buildAuthCookies(authToken, csrfToken);

		logInfo(methodName, "发起获取习惯列表请求", { apiUrl });

		// 发起请求
		const response = await uniCloud.httpclient.request(apiUrl, {
			method: "GET",
			headers: headers,
			cookies: cookies,
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`获取习惯列表失败，状态码: ${response.status}`);
		}

		// 解析响应数据
		const responseData = safeJsonParse(response.data, null);

		if (!responseData) {
			throw new Error("无法解析习惯列表数据");
		}

		logInfo(methodName, "成功获取习惯列表", {
			habitCount: responseData.length || 0,
		});

		// 处理习惯数据
		const habits = Array.isArray(responseData) ? responseData : [];
		const processedHabits = habits.map((habit) => ({
			id: habit.id,
			name: habit.name,
			description: habit.description,
			color: habit.color,
			icon: habit.icon,
			is_active: habit.isActive || false,
			is_archived: habit.isArchived || false,
			frequency: habit.frequency || "daily",
			target_count: habit.targetCount || 1,
			current_streak: habit.currentStreak || 0,
			best_streak: habit.bestStreak || 0,
			total_completions: habit.totalCompletions || 0,
			created_time: habit.createdTime,
			modified_time: habit.modifiedTime,
			// 习惯统计信息
			statistics: {
				completion_rate: habit.completionRate || 0,
				current_streak: habit.currentStreak || 0,
				best_streak: habit.bestStreak || 0,
				total_days: habit.totalDays || 0,
				completed_days: habit.completedDays || 0,
			},
		}));

		// 按状态分组习惯
		const groupedHabits = {
			active: processedHabits.filter((h) => h.is_active && !h.is_archived),
			archived: processedHabits.filter((h) => h.is_archived),
			inactive: processedHabits.filter((h) => !h.is_active && !h.is_archived),
		};

		// 计算总体统计
		const totalStatistics = {
			total_habits: processedHabits.length,
			active_habits: groupedHabits.active.length,
			archived_habits: groupedHabits.archived.length,
			total_completions: processedHabits.reduce(
				(sum, h) => sum + h.total_completions,
				0
			),
			average_streak:
				processedHabits.length > 0
					? Math.round(
							processedHabits.reduce((sum, h) => sum + h.current_streak, 0) /
								processedHabits.length
					  )
					: 0,
		};

		return createSuccessResponse("获取习惯列表成功", {
			habits: processedHabits,
			grouped_habits: groupedHabits,
			statistics: totalStatistics,
		});
	} catch (error) {
		logError(methodName, "获取习惯列表失败", error);
		throw error;
	}
}

/**
 * 获取本周习惯打卡统计
 * @param {string} authToken - 认证令牌
 * @param {string} csrfToken - CSRF令牌
 * @returns {object} 本周习惯统计响应
 */
async function getWeekCurrentStatistics(authToken, csrfToken) {
	const methodName = "getWeekCurrentStatistics";
	logInfo(methodName, "开始获取本周习惯统计");

	try {
		// 参数验证
		const authValidation = validateRequired(authToken, "authToken");
		if (authValidation) return authValidation;

		const csrfValidation = validateRequired(csrfToken, "csrfToken");
		if (csrfValidation) return csrfValidation;

		// 构建请求URL
		const apiUrl = buildDidaApiUrl(DIDA_HABIT_APIS.week_current_statistics);

		// 构建请求头和cookies
		const headers = buildAuthHeaders(authToken, csrfToken);
		const cookies = buildAuthCookies(authToken, csrfToken);

		logInfo(methodName, "发起获取本周习惯统计请求", { apiUrl });

		// 发起请求
		const response = await uniCloud.httpclient.request(apiUrl, {
			method: "GET",
			headers: headers,
			cookies: cookies,
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`获取本周习惯统计失败，状态码: ${response.status}`);
		}

		// 解析响应数据
		const responseData = safeJsonParse(response.data, null);

		if (!responseData) {
			throw new Error("无法解析本周习惯统计数据");
		}

		logInfo(methodName, "成功获取本周习惯统计");

		// 处理本周统计数据
		const processedData = {
			week_info: {
				start_date: responseData.startDate,
				end_date: responseData.endDate,
				current_day: responseData.currentDay || 0,
			},
			// 每日完成情况
			daily_completions: responseData.dailyCompletions || [],
			// 习惯完成统计
			habit_statistics: responseData.habitStatistics || [],
			// 总体统计
			summary: {
				total_habits: responseData.totalHabits || 0,
				completed_today: responseData.completedToday || 0,
				completion_rate: responseData.completionRate || 0,
				streak_count: responseData.streakCount || 0,
			},
		};

		return createSuccessResponse("获取本周习惯统计成功", processedData);
	} catch (error) {
		logError(methodName, "获取本周习惯统计失败", error);
		throw error;
	}
}

// 导出习惯管理模块函数
module.exports = {
	getHabits,
	getWeekCurrentStatistics,
};

<template>
  <view>
    <!-- 状态栏占位 -->
    <!-- <div id="status-bar-container"></div> -->

    <!-- 导航栏 -->
    <z-page-navbar :title="pageTitle" rightButtonType="save" @back="goBack" @rightButtonClick="submitForm" />

    <!-- 内容区域 -->
    <view class="content-area">
      <view class="container">
        <form id="objective-form">
          <!-- 目标名称部分 -->
          <view class="form-section">
            <view class="section-label">目标名称</view>
            <view class="input-card">
              <view class="color-picker" id="color-trigger">
                <view
                  class="color-circle"
                  :style="{ backgroundColor: selectedColor }"
                  id="selected-color"
                  @click="openColorModal"
                ></view>
                <view class="input-field">
                  <input
                    type="text"
                    id="title"
                    class="input-text"
                    placeholder='如"身体健康💪"'
                    required
                    v-model="title"
                  />
                </view>
              </view>
            </view>
          </view>

          <!-- 动机部分 -->
          <view class="form-section">
            <view class="section-label">激励动机</view>
            <view class="input-card">
              <view v-for="(item, index) in motivations" :key="index" class="motivation-item">
                <view class="motivation-input-wrapper">
                  <input
                    type="text"
                    class="input-text motivation-input"
                    placeholder="写一句激励自己的话吧！"
                    v-model="item.content"
                  />
                  <view class="motivation-delete" @click="removeMotivation(index)" v-if="motivations.length > 1">
                    <i class="fas fa-times"></i>
                  </view>
                </view>
                <view class="motivation-divider" v-if="index < motivations.length - 1"></view>
              </view>
              <view class="add-motivation" @click="addMotivation">
                <i class="fas fa-plus"></i>
                <text>添加动机</text>
              </view>
            </view>
          </view>

          <!-- 目标时间部分 -->
          <view class="form-section">
            <view class="section-label">目标时间</view>

            <!-- 目标时间 -->
            <view class="date-details">
              <uni-datetime-picker :end="endDate" v-model="startDate" type="date">
                <view class="date-card">
                  <view class="date-label">开始日期</view>
                  <view class="date-value">{{ startDateDisplay }}</view>
                  <view class="date-day">{{ startDayDisplay }}</view>
                </view>
              </uni-datetime-picker>

              <uni-datetime-picker :start="startDate" v-model="endDate" type="date">
                <view class="date-card">
                  <view class="date-label">结束日期</view>
                  <view class="date-value">{{ endDateDisplay }}</view>
                  <view class="date-day">{{ endDayDisplay }}</view>
                </view>
              </uni-datetime-picker>
            </view>

            <!-- 持续时间部分 -->
            <view class="duration-container">
              <view class="duration-label">持续时间</view>
              <view class="duration-control">
                <button type="button" class="duration-btn minus" id="duration-minus" @click="decreaseDuration">
                  <i class="fas fa-minus"></i>
                </button>
                <view class="duration-value" id="duration">{{ durationText }}</view>
                <button type="button" class="duration-btn plus" id="duration-plus" @click="increaseDuration">
                  <i class="fas fa-plus"></i>
                </button>
              </view>
            </view>
          </view>
        </form>
      </view>
    </view>

    <!-- 使用封装的颜色选择弹窗组件 -->
    <l-color-popup
      :show="isColorModalActive"
      :selectedColor="selectedColor"
      @update:show="isColorModalActive = $event"
      @onSubmit="selectColor"
    />

    <!-- 加载状态 -->
    <view class="loading" id="loading" :class="{ active: isLoading }">
      <view class="spinner"></view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { router, getRoute } from '@/utils/tools'
import ZPageNavbar from '@/components/z-page-navbar.vue'
import LColorPopup from './components/l-color-popup.vue'
import { addOkrApi, getOkrApi, updateOkrApi } from '@/api/okr'

// uni-datetime-picker 组件已在项目中全局注册，无需导入

const goBack = () => {
  router.back()
}

const isColorModalActive = ref(false)
const isLoading = ref(false)
const selectedColor = ref('#4361ee') // 默认颜色

// 表单字段 (后续会用 ref 绑定到 input)
const title = ref('')
const startDate = ref('')
const endDate = ref('')
const startDateDisplay = ref('')
const endDateDisplay = ref('')
const startDayDisplay = ref('')
const endDayDisplay = ref('')
const dateRangeDisplay = ref('')
const durationText = ref('持续 0 天')
const isEditMode = ref(false) // 是否为编辑模式
const objectiveId = ref('') // 目标 ID

// 动机数据结构
interface Motivation {
  title: string
  content: string
}

// 动机列表
const motivations = ref<Motivation[]>([{ title: '', content: '' }])

// 添加动机
const addMotivation = () => {
  motivations.value.push({ title: '', content: '' })
}

// 删除动机
const removeMotivation = (index: number) => {
  motivations.value.splice(index, 1)
}

// 根据当前模式返回页面标题
const pageTitle = computed(() => (isEditMode.value ? '编辑目标' : '创建目标'))

// 监听日期变化
watch(
  [startDate, endDate],
  ([newStartDate, newEndDate]) => {
    if (newStartDate && newEndDate) {
      updateDateDisplays()
    }
  },
  { immediate: true }
)

// 颜色选择相关函数
const openColorModal = () => {
  isColorModalActive.value = true
}

const selectColor = (color: string) => {
  selectedColor.value = color
}

// 处理开始日期变化
const onStartDateChange = (e: any) => {
  startDate.value = e.detail.value
  const startDt = new Date(startDate.value)
  const endDt = new Date(endDate.value)
  if (startDt > endDt) {
    endDate.value = startDate.value
  }
  updateDateDisplays()
}

// 处理结束日期变化
const onEndDateChange = (e: any) => {
  endDate.value = e.detail.value
  const startDt = new Date(startDate.value)
  const endDt = new Date(endDate.value)
  if (endDt < startDt) {
    startDate.value = endDate.value
  }
  updateDateDisplays()
}

// 清除日期
const clearDates = () => {
  startDate.value = ''
  endDate.value = ''
  updateDateDisplays()
}

const decreaseDuration = () => {
  console.log('Decrease duration')
  const currentEndDate = new Date(endDate.value)
  const currentStartDate = new Date(startDate.value)
  const diffTime = Math.abs(currentEndDate.getTime() - currentStartDate.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays > 1) {
    currentEndDate.setDate(currentEndDate.getDate() - 1)
    endDate.value = formatDateValue(currentEndDate)
    updateDateDisplays()
  }
}

const increaseDuration = () => {
  console.log('Increase duration')
  const currentEndDate = new Date(endDate.value)
  currentEndDate.setDate(currentEndDate.getDate() + 1)
  endDate.value = formatDateValue(currentEndDate)
  updateDateDisplays()
}

const submitForm = () => {
  // 表单验证 (简单示例)
  if (!title.value) {
    uni.showToast({ title: '请输入目标名称', icon: 'none' })
    return
  }
  if (!startDate.value || !endDate.value) {
    uni.showToast({ title: '请选择目标时间', icon: 'none' })
    return
  }

  isLoading.value = true

  // 过滤掉空的动机
  const validMotivations = motivations.value.filter((item) => item.content.trim() !== '')

  const objectiveData = {
    title: title.value,
    startDate: startDate.value,
    endDate: endDate.value,
    color: selectedColor.value,
    content: '',
    motivation: validMotivations,
    feasibility: [],
  }

  console.log('提交的目标数据：', objectiveData)

  // 根据是否是编辑模式选择不同的 API
  const apiCall = isEditMode.value ? updateOkrApi(objectiveId.value, objectiveData) : addOkrApi(objectiveData)

  apiCall
    .then(() => {
      isLoading.value = false
      uni.showToast({ title: isEditMode.value ? '目标更新成功！' : '目标创建成功！', icon: 'success' })
      // 成功后返回上一页
      setTimeout(() => {
        router.back()
      }, 500)
    })
    .catch((error) => {
      isLoading.value = false
      console.error(isEditMode.value ? '更新目标失败：' : '创建目标失败：', error)
      uni.showToast({ title: isEditMode.value ? '更新失败，请重试' : '创建失败，请重试', icon: 'error' })
    })
}

// 日期格式化辅助函数
const formatDateDisplay = (date: Date): string => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${year}/${month}/${day}`
}

const formatDayOfWeek = (date: Date): string => {
  const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return days[date.getDay()]
}

const formatDateValue = (date: Date): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 更新日期显示
const updateDateDisplays = () => {
  if (!startDate.value || !endDate.value) return

  const startDt = new Date(startDate.value)
  const endDt = new Date(endDate.value)

  startDateDisplay.value = formatDateDisplay(startDt)
  endDateDisplay.value = formatDateDisplay(endDt)
  startDayDisplay.value = formatDayOfWeek(startDt)
  endDayDisplay.value = formatDayOfWeek(endDt)
  dateRangeDisplay.value = `${formatDateDisplay(startDt)}~${formatDateDisplay(endDt)}`

  const diffTime = Math.abs(endDt.getTime() - startDt.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1 // 包含首尾两天
  durationText.value = `持续 ${diffDays} 天`
}

// 处理日期范围变化
const handleDateRangeChange = (e: string[]) => {
  if (e && e.length === 2) {
    startDate.value = e[0]
    endDate.value = e[1]
    updateDateDisplays()
  }
}

// 获取目标详情
const fetchObjectiveData = async (id: string) => {
  try {
    isLoading.value = true
    const data = await getOkrApi(id)
    if (data) {
      // 填充表单数据
      title.value = data.title || ''
      startDate.value = data.startDate || ''
      endDate.value = data.endDate || ''

      // 设置颜色
      if (data.color) {
        selectedColor.value = data.color
      }

      // 获取激励短语数据
      if (data.motivation && Array.isArray(data.motivation) && data.motivation.length > 0) {
        motivations.value = data.motivation
      } else {
        motivations.value = [{ title: '', content: '' }]
      }

      // 更新日期显示
      if (startDate.value && endDate.value) {
        updateDateDisplays()
      }
    }
  } catch (error) {
    console.error('获取目标详情失败：', error)
    uni.showToast({ title: '获取目标详情失败', icon: 'none' })
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  // 检查是否是编辑模式
  const params = getRoute.params() as { id?: string }
  const id = params.id

  if (id) {
    isEditMode.value = true
    objectiveId.value = id
    fetchObjectiveData(id)
  } else {
    // 初始化日期 (新建模式)
    const today = new Date()
    const laterDate = new Date(today)
    laterDate.setDate(today.getDate() + 92) // 确保初始是 93 天

    startDate.value = formatDateValue(today)
    endDate.value = formatDateValue(laterDate)

    updateDateDisplays()
  }
})
</script>

<style scoped lang="scss">
.page-container {
  // 假设 <view class="page-container"> 是根元素
  font-family: var(--font-sans);
  background: var(--color-bg);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  color: var(--text-color-primary, var(--color-gray-800)); // Assuming --text-color-primary or fallback
  line-height: 1.6;
}

.content-area {
  flex: 1;
  overflow-y: auto;
  padding: 32rpx 32rpx 0rpx; // Standardized padding, bottom adjusted for footer
  scroll-behavior: smooth;
  background: var(--color-bg); //确保背景色应用
}

.container {
  margin: 0 auto;
  padding: 30rpx 0 60rpx; // Adjusted padding to rpx, removed side padding as content-area has it
}

/* 表单部分 */
.form-section {
  margin-bottom: 48rpx;
}

.section-label {
  font-size: 13px; // Standardized auxiliary text
  color: var(--text-color-secondary, var(--color-gray-600)); // Use text color var
  margin-bottom: 24rpx;
  margin-left: 10rpx;
  opacity: 0.8; // Consider if this should be a color variable
  font-weight: 500;
}

.input-card {
  background-color: var(--color-white);
  border-radius: 20px; // Standardized card radius
  padding: 40rpx; // Standardized card padding
  box-shadow: var(--shadow-sm);
  margin-bottom: 10rpx;
  border: 1px solid var(--color-gray-300);
  transition: var(--transition-normal);
}

.color-picker {
  display: flex;
  align-items: center;
  margin-bottom: 0;
  position: relative;
}

.color-circle {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: var(--color-primary);
  margin-right: 32rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06); // Consider a shadow variable
  cursor: pointer;
  transition: var(--transition-normal);
  border: 1.5px solid rgba(var(--color-white-rgb, 255, 255, 255), 0.8); // Use var for white-rgb

  i {
    font-size: 12px; // Keep as is, or standardize icon sizes
    color: rgba(var(--color-white-rgb, 255, 255, 255), 0.9);
  }
}

.input-field {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 92rpx;
}

.input-text {
  width: 100%;
  border: none;
  font-size: 16px; // Standardized input text
  color: var(--text-color-primary, var(--color-gray-800));
  background: transparent;
  padding: 10rpx 0;
  margin-bottom: 4rpx;
  height: auto;
  line-height: normal;
  min-height: 1.2em;
}

.input-hint {
  font-size: 13px; // Standardized auxiliary text
  color: var(--color-gray-500);
  margin-top: 8rpx;
}

.input-hint-input {
  width: 100%;
  border: none;
  font-size: 13px;
  color: var(--color-gray-500);
  background: transparent;
  padding: 8rpx 0;
  margin-top: 8rpx;
}

/* 日期部分 */
.date-display {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  background-color: var(--color-white);
  border-radius: 20px; // Standardized
  padding: 40rpx; // Standardized
  box-shadow: var(--shadow-sm);
  cursor: pointer;
}

.date-icon {
  color: var(--color-accent);
  margin-right: 30rpx;
  font-size: 18px; // Or standardize icon font sizes
}

.date-range {
  font-size: 16px; // Standardized primary data display
  font-weight: 500;
  flex: 1;
  color: var(--text-color-primary, var(--color-gray-800));
}

.date-details {
  display: flex;
  gap: 24rpx;
  margin-bottom: 30rpx;
}

.date-card {
  flex: 1;
  background-color: var(--color-white);
  border-radius: 20px; // Standardized
  padding: 40rpx; // Standardized
  box-shadow: var(--shadow-sm);
  position: relative;
}

.date-label {
  font-size: 13px; // Standardized
  color: var(--color-gray-500);
  margin-bottom: 12rpx;
}

.date-value {
  font-size: 16px; // Standardized
  font-weight: 600;
  color: var(--text-color-primary, var(--color-gray-800));
}

.date-day {
  font-size: 12px; // Standardized
  color: var(--color-gray-500);
  margin-top: 8rpx;
}

.date-picker-btn {
  margin-top: 20rpx;
  background-color: var(--color-primary-light, rgba(67, 97, 238, 0.1));
  color: var(--color-primary);
  border: none;
  border-radius: 12rpx;
  padding: 12rpx 20rpx;
  font-size: 12px;
  cursor: pointer;
  width: 100%;
  text-align: center;
}

.date-picker-btn::after {
  border: none;
}

/* 持续时间部分 */
.duration-container {
  margin-top: 30rpx;
  background-color: var(--color-white);
  border-radius: 20px; // Standardized
  padding: 40rpx; // Standardized
  box-shadow: var(--shadow-sm);
}

.duration-label {
  font-size: 13px; // Standardized
  color: var(--color-gray-500);
  margin-bottom: 12rpx;
}

.duration-control {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8rpx;
}

.duration-value {
  font-size: 16px; // Standardized
  font-weight: 600;
  color: var(--text-color-primary, var(--color-gray-800));
  text-align: center;
  flex: 1;
  margin: 0 30rpx;
}

.duration-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%; // Keep as circle
  background-color: var(--color-primary);
  color: var(--color-white);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
  padding: 0;
  line-height: 1;

  i {
    font-size: 12px; // Or standardize
  }

  &::after {
    border: none;
  }
}

/* 底部按钮 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: var(--color-gray-100); // Use a light bg color var
  padding: 32rpx 40rpx 32rpx;
  padding-bottom: 32rpx;
  box-shadow: var(--shadow-lg); // Standard shadow for prominent elements
  z-index: 50;
  border-top: 1px solid var(--color-gray-300);
  background-color: rgba(var(--color-gray-100-rgb, 248, 249, 250), 0.85); // Assuming var for light bg rgb
}

.create-btn {
  width: 100%;
  background: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: 20px; // Standardized button radius (same as cards)
  padding: 32rpx; // Standardized padding
  font-size: 16px; // Standardized
  font-weight: 600; // Standardized
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3); // Standardized shadow
  transition: var(--transition-normal);
  cursor: pointer;
  line-height: 1.5;

  &:active {
    opacity: 0.8;
  }
  &::after {
    border: none;
  }
}

.loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(var(--color-white-rgb, 255, 255, 255), 0.9); // Use var for white-rgb
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
  transition: var(--transition-normal);

  &.active {
    opacity: 1;
    pointer-events: all;
  }
}

.spinner {
  width: 96rpx;
  height: 96rpx;
  border: 4px solid var(--color-primary-transparent-20);
  border-left-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.hidden-date-input {
  position: absolute;
  visibility: hidden;
  pointer-events: none;
  width: 0;
  height: 0;
}

// Font Awesome Class (basic for this example)
// These should ideally be globally available if using FA classes in template
.fas,
.far {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}
.far {
  font-weight: 400;
}

// Specific icons used (if not using a full FA import via CSS)
// Kept for now as template might rely on these SCSS definitions for some icons
.fa-chevron-left:before {
  content: '\f053';
}
.fa-calendar-alt:before {
  content: '\f073';
}
.fa-minus:before {
  content: '\f068';
}
.fa-plus:before {
  content: '\f067';
}
.fa-times:before {
  content: '\f00d';
}

// Placeholder style for uni-app input
::v-deep .input-text .uni-input-placeholder,
::v-deep .input-text input::placeholder {
  color: var(--color-gray-400, #b0b0b0); // Use variable, fallback for safety
  font-size: 16px;
}

.date-range-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  background-color: var(--color-white);
  border-radius: 20px;
  padding: 40rpx;
  box-shadow: var(--shadow-sm);
}

.date-range-text {
  font-size: 16px;
  font-weight: 500;
  flex: 1;
  color: var(--text-color-primary, var(--color-gray-800));
}

.date-range-clear {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: var(--color-gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.date-range-clear i {
  font-size: 12px;
  color: var(--color-gray-600);
}

/* 日期部分 */
.date-display {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  background-color: var(--color-white);
  border-radius: 20px; // Standardized
  padding: 40rpx; // Standardized
  box-shadow: var(--shadow-sm);
  cursor: pointer;
}

.okr_time {
  display: none;
}

.okr_time_title {
  display: none;
}

.okr_time_val {
  display: none;
}

.date-separator {
  display: none;
}

/* 持续时间部分 */
.duration-container {
  margin-top: 30rpx;
  background-color: var(--color-white);
  border-radius: 20px; // Standardized
  padding: 40rpx; // Standardized
  box-shadow: var(--shadow-sm);
}

/* 动机部分 */
.motivation-item {
  position: relative;
  width: 100%;
}

.motivation-input-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

.motivation-input {
  flex: 1;
}

.motivation-delete {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: var(--color-gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 16rpx;

  i {
    font-size: 12px;
    color: var(--color-gray-600);
  }

  &:active {
    opacity: 0.7;
  }
}

.motivation-divider {
  height: 1px;
  background-color: var(--color-gray-200);
  margin: 16rpx 0;
  width: 100%;
}

.add-motivation {
  display: flex;
  align-items: center;
  margin-top: 24rpx;
  padding: 16rpx 0;
  color: var(--color-primary);
  font-size: 14px;
  cursor: pointer;

  i {
    margin-right: 12rpx;
    font-size: 12px;
  }

  &:active {
    opacity: 0.7;
  }
}
</style>

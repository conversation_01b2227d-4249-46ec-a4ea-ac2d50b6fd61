# 任务：静态界面开发

## 所属功能模块
- 普通话训练

## 任务描述
创建普通话训练功能的静态页面布局，包含所有核心 UI 元素的占位和基本样式。此阶段不包含业务逻辑，使用模拟数据填充界面。

## 技术实现详情
1.  **页面结构**:
    -   创建一个新的 Vue 组件作为主页面。
    -   在页面顶部设置一个用于输入单个汉字的输入框。
    -   在输入框下方，创建一个区域用于展示历史记录标签（使用模拟的标签数据）。
    -   创建一个主内容区域，用于展示 AI 生成的内容，包括：
        -   词组区域（使用模拟词组数据）。
        -   短句区域（使用模拟短句数据）。
        -   引导问题区域（使用模拟问题数据）。
    -   在页面底部集成 `l-message-input.vue` 用于录音，并添加 `z-audio-player.vue` 用于播放录音（初始状态为隐藏或禁用）。

2.  **样式与布局**:
    -   使用 Flexbox 或 Grid 布局，确保页面在不同设备上表现良好。
    -   为输入框、历史标签、内容展示区和播放器定义清晰的视觉样式。

3.  **模拟数据**:
    -   在组件的 `script` 部分定义静态的模拟数据对象，用于填充 UI，方便预览效果。

## 验收标准
-   页面能够正常渲染，所有 UI 元素都已按设计稿布局。
-   输入框、历史标签区域、内容展示区、录音和播放器组件都已正确加载。
-   页面布局在 PC 和移动端视图下都能自适应，无明显错位。
-   所有交互元素（如按钮、输入框）在静态状态下可见且样式正常。

## 依赖关系
-   无

## 优先级
-   高

## 状态追踪
-   [ ] 待办
-   [ ] 进行中
-   [x] 已完成   {{ item.text }}
              <rt v-if="showPinyin">{{ item.pinyin }}</rt>
            </ruby>
          </view>
        </view>
      </view>

      <!-- 短句 -->
      <view class="content-block">
        <view class="content-title">
          <i class="fas fa-quote-left"></i>
          <text>相关短句</text>
        </view>
        <view class="sentence-item" v-for="(item, index) in generatedContent.sentences" :key="index">
          <ruby>
            {{ item.text }}
            <rt v-if="showPinyin">{{ item.pinyin }}</rt>
          </ruby>
        </view>
      </view>

      <!-- 引导问题 -->
      <view class="content-block">
        <view class="content-title">
          <i class="fas fa-question-circle"></i>
          <text>引导问题</text>
        </view>
        <view class="question-item">
          {{ generatedContent.question }}
        </view>
      </view>
    </view>
    
    <!-- AI 发音播放器 (静态占位) -->
    <view class="audio-player-placeholder">
       <z-audio-player :src="mockAudioUrl" title="AI发音" :autoplay="false"></z-audio-player>
    </view>

    <!-- 录音区域 -->
    <view class="recording-section">
      <l-message-input placeholder="请跟读上面的句子并录音"></l-message-input>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import LMessageInput from '../components/l-message-input.vue'
import ZAudioPlayer from '@/components/z-audio-player/z-audio-player.vue'

// 模拟数据
const characterInput = ref('')
const showPinyin = ref(true)
const historyTags = ref(['好', '学', '习', '天', '上'])
const mockAudioUrl = ref('https://downsc.chinaz.net/Files/DownLoad/sound1/201906/11582.mp3') // 模拟音频

const generatedContent = ref({
  phrases: [
    { text: '学习', pinyin: 'xué xí' },
    { text: '学生', pinyin: 'xué sheng' },
    { text: '学校', pinyin: 'xué xiào' },
  ],
  sentences: [
    { text: '好好学习，天天向上。', pinyin: 'hǎo hǎo xué xí, tiān tiān xiàng shàng' },
  ],
  question: '请用“学”字说一句话。',
})

</script>

<style lang="scss" scoped>
.training-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.status-bar-placeholder {
  height: var(--status-bar-height, 0);
}

.input-section {
  display: flex;
  padding: 15px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.history-section {
  padding: 10px 15px;
  .history-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }
  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
}

.content-section {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  .pinyin-toggle {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
    text {
      margin-right: 8px;
    }
  }
}

.content-block {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .content-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 12px;
    i {
      color: var(--color-primary);
      margin-right: 8px;
    }
  }

  ruby {
    font-size: 18px;
    line-height: 2.2;
    padding: 5px;
  }
  
  rt {
    font-size: 12px;
    color: #888;
  }
}

.phrase-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.phrase-item, .sentence-item {
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s;
  &:hover {
    background-color: #e9ecf0;
  }
}

.question-item {
  font-size: 15px;
  color: #555;
  line-height: 1.6;
}

.audio-player-placeholder {
  padding: 0 15px 10px;
}

.recording-section {
  border-top: 1px solid #e0e0e0;
}
</style> 
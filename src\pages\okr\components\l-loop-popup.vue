<template>
  <u-popup v-model="showPopup" mode="center" width="80%" borderRadius="10">
    <div class="loop-setting-popup">
      <div class="popup-content">
        <!-- 第一步：循环类型选择 -->
        <div v-if="currentStep === 1" class="step-container">
          <!-- 基础循环类型 -->
          <div class="frequency-category">
            <div class="category-title">基础循环</div>
            <div class="radio-group">
              <div 
                class="radio-option" 
                :class="{ 'radio-selected': frequency === 'none' }" 
                @click="handleFrequencySelect('none')"
              >
                <div class="radio-text">不重复</div>
              </div>
              <div
                class="radio-option"
                :class="{ 'radio-selected': frequency === 'daily' }"
                @click="handleFrequencySelect('daily')"
              >
                <div class="radio-text">每天</div>
              </div>
              <div
                class="radio-option"
                :class="{ 'radio-selected': frequency === 'weekly' }"
                @click="handleFrequencySelect('weekly')"
              >
                <div class="radio-text">每周几</div>
              </div>
              <div
                class="radio-option"
                :class="{ 'radio-selected': frequency === 'monthly' }"
                @click="handleFrequencySelect('monthly')"
              >
                <div class="radio-text">每月几</div>
              </div>
            </div>
          </div>

          <!-- 高级循环类型 -->
          <div class="frequency-category">
            <div class="category-title">高级循环</div>
            <div class="radio-group">
              <div
                class="radio-option"
                :class="{ 'radio-selected': frequency === 'n_days' }"
                @click="handleFrequencySelect('n_days')"
              >
                <div class="radio-text">每几天</div>
              </div>
              <div
                class="radio-option"
                :class="{ 'radio-selected': frequency === 'n_weeks' }"
                @click="handleFrequencySelect('n_weeks')"
              >
                <div class="radio-text">每几周</div>
              </div>
              <div
                class="radio-option"
                :class="{ 'radio-selected': frequency === 'n_months' }"
                @click="handleFrequencySelect('n_months')"
              >
                <div class="radio-text">每几月</div>
              </div>
              <div
                class="radio-option"
                :class="{ 'radio-selected': frequency === 'interval' }"
                @click="handleFrequencySelect('interval')"
              >
                <div class="radio-text">每隔几天</div>
              </div>
              <div
                class="radio-option"
                :class="{ 'radio-selected': frequency === 'weekly_n_times' }"
                @click="handleFrequencySelect('weekly_n_times')"
              >
                <div class="radio-text">每周几天</div>
              </div>
              <div
                class="radio-option"
                :class="{ 'radio-selected': frequency === 'monthly_n_times' }"
                @click="handleFrequencySelect('monthly_n_times')"
              >
                <div class="radio-text">每月几天</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第二步：具体参数配置 -->
        <div v-if="currentStep === 2" class="step-container">
          <!-- 头部：显示已选循环类型和返回按钮 -->
          <div class="step-header">
            <div class="back-button" @click="backToFirstStep">
              <i class="icon-back">←</i>
            </div>
            <div class="current-type">{{ frequencyDisplayName }}</div>
          </div>
          
          <!-- 每周重复设置 -->
          <div class="setting-section" v-if="frequency === 'weekly'">
            <div class="section-title">重复日</div>
            <div class="weekday-selector">
              <div
                v-for="(day, index) in weekdays"
                :key="index"
                class="weekday-item"
                :class="{ 'weekday-selected': selectedWeekdays.includes(index) }"
                @click="toggleWeekday(index)"
              >
                {{ day }}
              </div>
            </div>
          </div>

          <!-- 每周几天设置 -->
          <div class="setting-section" v-if="frequency === 'weekly_n_times'">
            <div class="section-title">每周完成天数</div>
            <div class="interval-selector">
              <div class="interval-input-container">
                <u-input
                  v-model="weeklyTimes"
                  type="number"
                  :focus="true"
                  :border="true"
                  height="80"
                  :selection-start="0"
                  :selection-end="String(weeklyTimes).length"
                  :maxlength="1"
                  input-align="center"
                ></u-input>
                <div class="interval-unit">天</div>
              </div>
              <div class="interval-example" v-if="weeklyTimes">每周任意完成 {{ weeklyTimes }} 天，不固定具体哪几天</div>
            </div>
          </div>

          <!-- 每月几天设置 -->
          <div class="setting-section" v-if="frequency === 'monthly_n_times'">
            <div class="section-title">每月完成天数</div>
            <div class="interval-selector">
              <div class="interval-input-container">
                <u-input
                  v-model="monthlyTimes"
                  type="number"
                  :focus="true"
                  :border="true"
                  height="80"
                  :selection-start="0"
                  :selection-end="String(monthlyTimes).length"
                  :maxlength="2"
                  input-align="center"
                ></u-input>
                <div class="interval-unit">天</div>
              </div>
              <div class="interval-example" v-if="monthlyTimes">每月任意完成 {{ monthlyTimes }} 天，不固定具体哪几号</div>
            </div>
          </div>

          <!-- 每月重复设置 -->
          <div class="setting-section" v-if="frequency === 'monthly'">
            <div class="section-title">每月几号</div>
            <div class="month-day-selector">
              <div
                v-for="day in 31"
                :key="day"
                class="month-day-item"
                :class="{ 'month-day-selected': selectedMonthDays.includes(day) }"
                @click="toggleMonthDay(day)"
              >
                {{ day }}
              </div>
            </div>
          </div>

          <!-- 每几天设置 -->
          <div class="setting-section" v-if="frequency === 'n_days'">
            <div class="section-title">几天为周期</div>
            <div class="interval-selector">
              <div class="interval-input-container">
                <u-input
                  v-model="nDays"
                  type="number"
                  :focus="true"
                  :border="true"
                  height="80"
                  :selection-start="0"
                  :selection-end="String(nDays).length"
                  :maxlength="3"
                  input-align="center"
                ></u-input>
                <div class="interval-unit">天</div>
              </div>
              <div class="interval-example" v-if="nDays">每{{ nDays }}天需要完成 1 次</div>
            </div>
          </div>

          <!-- 每几周设置 -->
          <div class="setting-section" v-if="frequency === 'n_weeks'">
            <div class="section-title">几周为周期</div>
            <div class="interval-selector">
              <div class="interval-input-container">
                <u-input
                  v-model="nWeeks"
                  type="number"
                  :focus="true"
                  :border="true"
                  height="80"
                  :selection-start="0"
                  :selection-end="String(nWeeks).length"
                  :maxlength="2"
                  input-align="center"
                ></u-input>
                <div class="interval-unit">周</div>
              </div>
              <div class="interval-example" v-if="nWeeks">每{{ nWeeks }}周需要完成 1 次</div>
            </div>
          </div>

          <!-- 每几月设置 -->
          <div class="setting-section" v-if="frequency === 'n_months'">
            <div class="section-title">几月为周期</div>
            <div class="interval-selector">
              <div class="interval-input-container">
                <u-input
                  v-model="nMonths"
                  type="number"
                  :focus="true"
                  :border="true"
                  height="80"
                  :selection-start="0"
                  :selection-end="String(nMonths).length"
                  :maxlength="2"
                  input-align="center"
                ></u-input>
                <div class="interval-unit">月</div>
              </div>
              <div class="interval-example" v-if="nMonths">每{{ nMonths }}个月需要完成 1 次</div>
            </div>
          </div>

          <!-- 每隔几天设置 -->
          <div class="setting-section" v-if="frequency === 'interval'">
            <div class="section-title">间隔天数</div>
            <div class="interval-selector">
              <div class="interval-input-container">
                <u-input
                  v-model="intervalDays"
                  type="number"
                  :focus="true"
                  :border="true"
                  height="80"
                  :selection-start="0"
                  :selection-end="String(intervalDays).length"
                  :maxlength="3"
                  input-align="center"
                ></u-input>
                <div class="interval-unit">天</div>
              </div>
              <div class="interval-example" v-if="intervalDays">
                举例：1 月 1 日、1 月{{ intervalDays + 2 }}日、1 月{{ (intervalDays + 1) * 2 + 1 }}日、1 月{{
                  (intervalDays + 1) * 3 + 1
                }}日、1 月{{ (intervalDays + 1) * 4 + 1 }}日...
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="popup-footer">
        <div class="cancel-btn" @click="handleCancel">取消</div>
        <div v-if="currentStep === 2" class="confirm-btn" @click="handleConfirm">确定</div>
      </div>
    </div>
  </u-popup>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import dayjs from 'dayjs'
import * as rrule from '@/utils/rrrrule'
import { numberToWeekday, weekdayToNumber } from '@/utils/rrrrule'

// 定义组件属性
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  initialValue: {
    type: String,
    default: 'type=DAILY',
  },
  startDate: {
    type: String,
    default: '',
  },
  endDate: {
    type: String,
    default: '',
  },
})

// 定义组件事件
const emit = defineEmits(['update:show', 'confirm'])

// 内部状态
const showPopup = ref(false)
const frequency = ref('daily')
const selectedWeekdays = ref([])
const selectedMonthDays = ref([1]) // 默认选中 1 号
const intervalDays = ref(2) // 默认间隔 2 天
const nDays = ref(2) // 默认每 2 天
const nWeeks = ref(2) // 默认每 2 周
const nMonths = ref(2) // 默认每 2 个月
const weeklyTimes = ref(3) // 默认每周 3 天
const monthlyTimes = ref(5) // 默认每月 5 天
// 修改顺序与 rrrrule.ts 中的 weekdayToNumber 保持一致：SU(0), MO(1)...SA(6)
const weekdays = ['日', '一', '二', '三', '四', '五', '六']

// 新增：当前步骤状态 (1=选择循环类型，2=配置具体参数)
const currentStep = ref(1)

// 新增：循环类型显示名称映射
const frequencyDisplayNames = {
  'none': '不重复',
  'daily': '每天',
  'weekly': '每周几',
  'monthly': '每月几',
  'n_days': '每几天',
  'n_weeks': '每几周',
  'n_months': '每几月',
  'interval': '每隔几天',
  'weekly_n_times': '每周几天',
  'monthly_n_times': '每月几天'
}

// 新增：当前选择的循环类型显示名称
const frequencyDisplayName = computed(() => {
  return frequencyDisplayNames[frequency.value] || '设置循环'
})

// 新增：是否需要额外配置
const needsExtraConfig = computed(() => {
  return !['none', 'daily'].includes(frequency.value)
})

// 监听 show 属性变化
watch(
  () => props.show,
  (newVal) => {
    showPopup.value = newVal
    if (newVal) {
      // 重置为第一步并解析初始值
      currentStep.value = 1
      parseInitialValue()
    }
  }
)

// 监听内部 showPopup 变化
watch(showPopup, (newVal) => {
  emit('update:show', newVal)
})

// 监听频率变化，当切换到每周时默认选中周一，每月时默认选中 1 号
watch(frequency, (newVal) => {
  if (newVal === 'weekly' && selectedWeekdays.value.length === 0) {
    selectedWeekdays.value = [1] // 默认选中周一 (索引为 1)
  } else if (newVal === 'monthly' && selectedMonthDays.value.length === 0) {
    selectedMonthDays.value = [1] // 默认选中 1 号
  }
})

// 新增：处理循环类型选择
const handleFrequencySelect = (type) => {
  frequency.value = type
  
  // 判断是否需要额外配置
  if (type === 'none' || type === 'daily') {
    // 直接保存并关闭
    handleConfirm()
  } else {
    // 切换到第二步
    currentStep.value = 2
  }
}

// 新增：返回第一步
const backToFirstStep = () => {
  currentStep.value = 1
}

// 解析初始循环规则值 - 重写为使用新格式
const parseInitialValue = () => {
  try {
    if (!props.initialValue || props.initialValue === '不重复') {
      frequency.value = 'none'
      return
    }

    // 解析新格式的循环规则字符串
    const ruleObj = rrule.parseRule(props.initialValue)

    // 基于解析后的对象设置界面状态
    switch (ruleObj.type) {
      case 'DAILY':
        frequency.value = 'daily'
        break

      case 'WEEKLY':
        frequency.value = 'weekly'
        if (ruleObj.byweekday && ruleObj.byweekday.length > 0) {
          selectedWeekdays.value = []
          ruleObj.byweekday.forEach((day) => {
            if (weekdayToNumber[day] !== undefined) {
              selectedWeekdays.value.push(weekdayToNumber[day])
            }
          })
        }
        break

      case 'MONTHLY':
        frequency.value = 'monthly'
        if (ruleObj.bymonthday && ruleObj.bymonthday.length > 0) {
          selectedMonthDays.value = [...ruleObj.bymonthday]
        }
        break

      case 'INTERVAL_DAILY':
        frequency.value = 'interval'
        if (ruleObj.interval) {
          intervalDays.value = ruleObj.interval
        }
        break

      case 'WEEKLY_N_TIMES':
        frequency.value = 'weekly_n_times'
        if (ruleObj.count) {
          weeklyTimes.value = ruleObj.count
        }
        break

      case 'MONTHLY_N_TIMES':
        frequency.value = 'monthly_n_times'
        if (ruleObj.count) {
          monthlyTimes.value = ruleObj.count
        }
        break

      case 'N_DAYS':
        frequency.value = 'n_days'
        if (ruleObj.interval) {
          nDays.value = ruleObj.interval
        }
        break

      case 'N_WEEKS':
        frequency.value = 'n_weeks'
        if (ruleObj.interval) {
          nWeeks.value = ruleObj.interval
        }
        break

      case 'N_MONTHS':
        frequency.value = 'n_months'
        if (ruleObj.interval) {
          nMonths.value = ruleObj.interval
        }
        break

      default:
        frequency.value = 'daily' // 默认每天
    }
    
    // 如果选择的循环类型需要额外配置，直接切换到第二步
    if (needsExtraConfig.value) {
      currentStep.value = 2
    }
  } catch (error) {
    console.error('解析循环规则出错：', error)
    frequency.value = 'daily' // 出错时默认为每天
    currentStep.value = 1
  }
}

// 切换星期几选择
const toggleWeekday = (index) => {
  const position = selectedWeekdays.value.indexOf(index)
  if (position > -1) {
    // 已选中，则取消选择（但保证至少有一个选中项）
    if (selectedWeekdays.value.length > 1) {
      selectedWeekdays.value.splice(position, 1)
    }
  } else {
    // 未选中，则添加选择
    selectedWeekdays.value.push(index)
  }

  // 确保至少选择一天
  if (selectedWeekdays.value.length === 0) {
    const today = new Date().getDay() // JS 中 0=周日，1=周一
    // 直接使用 JS 的 day 即可，无需转换，因为已按相同顺序排列
    selectedWeekdays.value.push(today)
  }
}

// 切换月份日期选择
const toggleMonthDay = (day) => {
  const position = selectedMonthDays.value.indexOf(day)
  if (position > -1) {
    // 已选中，则取消选择（但保证至少有一个选中项）
    if (selectedMonthDays.value.length > 1) {
      selectedMonthDays.value.splice(position, 1)
    }
  } else {
    // 未选中，则添加选择
    selectedMonthDays.value.push(day)
  }

  // 确保至少选择一天
  if (selectedMonthDays.value.length === 0) {
    selectedMonthDays.value.push(1) // 默认选中 1 号
  }
}

// 验证间隔天数输入
const validateIntervalInput = () => {
  // 确保输入的是有效数字
  if (isNaN(intervalDays.value) || intervalDays.value < 1) {
    intervalDays.value = 1
  } else if (intervalDays.value > 365) {
    intervalDays.value = 365
  }
  // 转换为整数
  intervalDays.value = Math.floor(Number(intervalDays.value))
}

// 验证每周完成天数输入
const validateWeeklyTimesInput = () => {
  // 确保输入的是有效数字
  if (isNaN(weeklyTimes.value) || weeklyTimes.value < 1) {
    weeklyTimes.value = 1
  } else if (weeklyTimes.value > 7) {
    weeklyTimes.value = 7
  }
  // 转换为整数
  weeklyTimes.value = Math.floor(Number(weeklyTimes.value))
}

// 验证每月完成天数输入
const validateMonthlyTimesInput = () => {
  // 确保输入的是有效数字
  if (isNaN(monthlyTimes.value) || monthlyTimes.value < 1) {
    monthlyTimes.value = 1
  } else if (monthlyTimes.value > 31) {
    monthlyTimes.value = 31
  }
  // 转换为整数
  monthlyTimes.value = Math.floor(Number(monthlyTimes.value))
}

// 验证每几周输入
const validateNWeeksInput = () => {
  // 确保输入的是有效数字
  if (isNaN(nWeeks.value) || nWeeks.value < 1) {
    nWeeks.value = 1
  } else if (nWeeks.value > 52) {
    nWeeks.value = 52
  }
  // 转换为整数
  nWeeks.value = Math.floor(Number(nWeeks.value))
}

// 验证每几月输入
const validateNMonthsInput = () => {
  // 确保输入的是有效数字
  if (isNaN(nMonths.value) || nMonths.value < 1) {
    nMonths.value = 1
  } else if (nMonths.value > 12) {
    nMonths.value = 12
  }
  // 转换为整数
  nMonths.value = Math.floor(Number(nMonths.value))
}

// 生成循环规则字符串 - 重写为使用新格式
const generateRuleString = () => {
  if (frequency.value === 'none') {
    return ''
  }

  let ruleObj = {}
  let weekdayCodes = []

  switch (frequency.value) {
    case 'daily':
      ruleObj = {
        type: 'DAILY',
      }
      break

    case 'weekly':
      weekdayCodes = []
      selectedWeekdays.value.forEach((index) => {
        weekdayCodes.push(numberToWeekday[index])
      })

      ruleObj = {
        type: 'WEEKLY',
        byweekday: weekdayCodes,
      }
      break

    case 'monthly':
      ruleObj = {
        type: 'MONTHLY',
        bymonthday: [...selectedMonthDays.value],
      }
      break

    case 'interval':
      ruleObj = {
        type: 'INTERVAL_DAILY',
        interval: intervalDays.value,
        startDate: props.startDate || new Date().toISOString().split('T')[0], // 使用当前日期或提供的开始日期
      }
      break

    case 'weekly_n_times':
      ruleObj = {
        type: 'WEEKLY_N_TIMES',
        count: weeklyTimes.value,
      }
      break

    case 'monthly_n_times':
      ruleObj = {
        type: 'MONTHLY_N_TIMES',
        count: monthlyTimes.value,
      }
      break

    case 'n_days':
      ruleObj = {
        type: 'N_DAYS',
        interval: nDays.value,
        startDate: props.startDate || new Date().toISOString().split('T')[0], // 使用当前日期或提供的开始日期
      }
      break

    case 'n_weeks':
      ruleObj = {
        type: 'N_WEEKS',
        interval: nWeeks.value,
        startDate: props.startDate || new Date().toISOString().split('T')[0], // 使用当前日期或提供的开始日期
      }
      break

    case 'n_months':
      ruleObj = {
        type: 'N_MONTHS',
        interval: nMonths.value,
        startDate: props.startDate || new Date().toISOString().split('T')[0], // 使用当前日期或提供的开始日期
      }
      break
  }

  // 将对象转换为字符串格式
  return objectToRuleString(ruleObj)
}

// 新增：将规则对象转换为字符串的辅助函数
const objectToRuleString = (obj) => {
  if (!obj || !obj.type) return ''

  const parts = [`type=${obj.type}`]

  if (obj.byweekday && obj.byweekday.length > 0) {
    parts.push(`byweekday=${obj.byweekday.join(',')}`)
  }

  if (obj.bymonthday && obj.bymonthday.length > 0) {
    parts.push(`bymonthday=${obj.bymonthday.join(',')}`)
  }

  if (obj.interval) {
    parts.push(`interval=${obj.interval}`)
  }

  if (obj.count) {
    parts.push(`count=${obj.count}`)
  }

  if (obj.startDate) {
    parts.push(`startDate=${obj.startDate}`)
  }

  return parts.join(';')
}

// 取消按钮处理
const handleCancel = () => {
  showPopup.value = false
}

// 确认按钮处理 - 修改为使用新格式
const handleConfirm = () => {
  // 在保存时验证各输入值
  validateInputValues()
  
  const ruleString = frequency.value === 'none' ? '' : generateRuleString()
  console.log('ruleString', ruleString)
  emit('confirm', ruleString)
  showPopup.value = false
}

// 统一验证所有输入值
const validateInputValues = () => {
  // 验证每几天输入
  if (frequency.value === 'n_days') {
    if (isNaN(nDays.value) || nDays.value < 1) {
      nDays.value = 1
    } else if (nDays.value > 365) {
      nDays.value = 365
    }
    nDays.value = Math.floor(Number(nDays.value))
  }
  
  // 验证每几周输入
  if (frequency.value === 'n_weeks') {
    validateNWeeksInput()
  }
  
  // 验证每几月输入
  if (frequency.value === 'n_months') {
    validateNMonthsInput()
  }
  
  // 验证间隔天数输入
  if (frequency.value === 'interval') {
    validateIntervalInput()
  }
  
  // 验证每周完成天数输入
  if (frequency.value === 'weekly_n_times') {
    validateWeeklyTimesInput()
  }
  
  // 验证每月完成天数输入
  if (frequency.value === 'monthly_n_times') {
    validateMonthlyTimesInput()
  }
}

// 初始化
onMounted(() => {
  showPopup.value = props.show
  if (props.show) {
    parseInitialValue()
  }
})
</script>

<style scoped>
/* 弹窗基础样式 */
.loop-setting-popup {
  background-color: var(--color-white);
  border-radius: var(--rounded-lg);
  padding: 20px;
}

/* 弹窗头部 */
.popup-header {
  margin-bottom: 20px;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-gray-800);
  text-align: center;
}

/* 设置区域通用样式 */
.setting-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  color: var(--color-gray-700);
  margin-bottom: 10px;
}

/* 频率分类样式 */
.frequency-category {
  margin-bottom: 15px;
}

.category-title {
  font-size: 14px;
  color: var(--color-gray-600);
  margin-bottom: 8px;
  padding-left: 2px;
}

/* 单选按钮组样式 */
.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.radio-option {
  padding: 8px 12px;
  border-radius: var(--rounded-sm);
  background-color: var(--color-gray-100);
  cursor: pointer;
}

.radio-selected {
  background-color: var(--color-primary-transparent-10);
  color: var(--color-primary);
  font-weight: 500;
}

/* 星期选择器样式 */
.weekday-selector {
  display: flex;
  justify-content: space-between;
}

.weekday-item {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--color-gray-100);
  cursor: pointer;
}

.weekday-selected {
  background-color: var(--color-primary);
  color: var(--color-white);
}

/* 月份日期选择器样式 */
.month-day-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.month-day-item {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--color-gray-100);
  cursor: pointer;
}

.month-day-selected {
  background-color: var(--color-primary);
  color: var(--color-white);
}

/* 输入框相关样式 */
.interval-selector {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.interval-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.interval-unit {
  font-size: 16px;
  color: var(--color-gray-700);
  margin-left: 5px;
}

.interval-example {
  font-size: 14px;
  color: var(--color-gray-500);
  margin-top: 8px;
  line-height: 1.4;
}

/* 底部按钮样式 */
.popup-footer {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 20px;
}

.cancel-btn,
.confirm-btn {
  padding: 10px 20px;
  border-radius: var(--rounded-md);
  text-align: center;
  font-size: 16px;
  cursor: pointer;
}

.cancel-btn {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
}

.confirm-btn {
  background-color: var(--color-primary);
  color: var(--color-white);
}

/* 新增：步骤容器样式 */
.step-container {
  position: relative;
  min-height: 200px;
}

/* 新增：步骤头部样式 */
.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--color-gray-200);
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: var(--color-gray-100);
  cursor: pointer;
  margin-right: 10px;
}

.back-button:hover {
  background-color: var(--color-gray-200);
}

.icon-back {
  font-size: 16px;
  color: var(--color-gray-700);
}

.current-type {
  font-size: 16px;
  font-weight: 500;
  color: var(--color-gray-800);
}
</style>

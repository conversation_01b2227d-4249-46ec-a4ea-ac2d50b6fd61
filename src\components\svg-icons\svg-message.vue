<template>
  <BaseSvgIcon :path="path" :viewBox="viewBox" v-bind="$attrs" />
</template>

<script setup>
import BaseSvgIcon from './BaseSvgIcon.vue'

defineProps({
  viewBox: {
    type: String,
    default: '0 0 1024 1024',
  },
  path: {
    type: String,
    default:
      'M512 64C264.6 64 64 264.6 64 512c0 98.6 31.8 189.6 85.6 263.8l-36.1 134.4c-3.3 12.3 8 23.5 20.3 20.3l134.4-36.1C342.4 948.2 433.4 980 532 980c247.4 0 448-200.6 448-448S759.4 64 512 64zm0 820c-88.4 0-170.2-30.6-234.8-81.9l-23.5-18.6-96.2 25.8 25.8-96.2-18.6-23.5C113.5 624.2 82.8 542.3 82.8 453.8c0-235.6 191.6-427.2 427.2-427.2s427.2 191.6 427.2 427.2-191.5 430.2-425.2 430.2z M341.3 341.3h341.3c10.6 0 19.2 8.6 19.2 19.2s-8.6 19.2-19.2 19.2H341.3c-10.6 0-19.2-8.6-19.2-19.2s8.6-19.2 19.2-19.2zm0 149.3h341.3c10.6 0 19.2 8.6 19.2 19.2s-8.6 19.2-19.2 19.2H341.3c-10.6 0-19.2-8.6-19.2-19.2s8.6-19.2 19.2-19.2zm0 149.4h170.7c10.6 0 19.2 8.6 19.2 19.2s-8.6 19.2-19.2 19.2H341.3c-10.6 0-19.2-8.6-19.2-19.2s8.7-19.2 19.2-19.2z',
  },
})
</script>

<style scoped>
.icon {
  display: inline-block;
  vertical-align: middle;
}
</style>

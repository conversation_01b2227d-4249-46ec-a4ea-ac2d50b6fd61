# App 端录音功能实现

## 任务描述

基于 uni.getRecorderManager()实现 App 端的录音功能，保证与 H5 端实现接口一致，处理 App 端特有的生命周期和事件。

## 所属功能模块

录音功能兼容 App 端

## 技术实现详情

### 1. 创建 App 录音实现文件

创建`src/hooks/useRecordApp.ts`文件，实现与 H5 端一致的接口：

```typescript
import { ref, onUnmounted, Ref } from 'vue'
import { UseRecordOptions, UseRecordReturn, RecordResult } from './types/record'
import { RecordError } from './utils/recordErrors'

/**
 * App环境下的录音Hook实现
 * 基于uni.getRecorderManager()
 *
 * @param options 录音配置选项
 * @returns 录音控制对象
 */
export default function useRecordApp(options?: UseRecordOptions): UseRecordReturn {
  // 默认录音配置
  const defaultOptions: UseRecordOptions = {
    maxDuration: 60000, // 默认 60 秒
    disableLogs: true,
    appOptions: {
      format: 'mp3', // App默认格式
    },
  }

  const mergedOptions = { ...defaultOptions, ...options }

  // 获取录音管理器实例
  const recorderManager = uni.getRecorderManager()

  // 录音状态
  const isRecording = ref<boolean>(false)
  const isPaused = ref<boolean>(false)
  const duration = ref<number>(0)
  const volume = ref<number>(0)
  const recordBlob = ref<string | null>(null) // App端是文件路径
  const recordURL = ref<string>('')

  // 定时器和音量分析
  let durationTimer: number | null = null
  let volumeUpdateTimer: number | null = null
  let waveformDataArray: Uint8Array | null = null

  // 监听录音开始事件
  const setupEventListeners = () => {
    // 录音开始事件
    recorderManager.onStart(() => {
      isRecording.value = true
      isPaused.value = false
      startTimers()

      if (!mergedOptions.disableLogs) {
        console.log('录音开始')
      }
    })

    // 录音暂停事件
    recorderManager.onPause(() => {
      isPaused.value = true
      stopTimers()

      if (!mergedOptions.disableLogs) {
        console.log('录音暂停')
      }
    })

    // 录音继续事件
    recorderManager.onResume(() => {
      isPaused.value = false
      startTimers()

      if (!mergedOptions.disableLogs) {
        console.log('录音继续')
      }
    })

    // 录音结束事件
    recorderManager.onStop((res) => {
      const { tempFilePath, duration: recordDuration } = res

      // 更新状态
      isRecording.value = false
      isPaused.value = false
      recordBlob.value = tempFilePath
      recordURL.value = tempFilePath

      // 如果有精确的录音时长，更新时长状态
      if (recordDuration) {
        duration.value = recordDuration
      }

      // 停止计时器
      stopTimers()

      if (!mergedOptions.disableLogs) {
        console.log('录音结束，文件路径：', tempFilePath)
      }
    })

    // 录音错误事件
    recorderManager.onError((res) => {
      const { errMsg } = res

      // 重置状态
      resetState()

      console.error('录音错误：', errMsg)

      // 触发错误回调
      throw RecordError.recordingFailed(errMsg)
    })
  }

  // 开始录音
  const startRecording = async (): Promise<void> => {
    try {
      // 如果已经在录音，先停止之前的录音
      if (isRecording.value) {
        await stopRecording()
      }

      // 重置状态
      resetState()

      // 确保事件监听已设置
      setupEventListeners()

      // 构建录音选项
      const recordOptions: UniApp.RecorderManagerStartOption = {
        duration: mergedOptions.maxDuration ? mergedOptions.maxDuration / 1000 : 60, // 转换为秒
        format: mergedOptions.appOptions?.format || 'mp3',
        numberOfChannels: mergedOptions.numberOfAudioChannels || 1,
        sampleRate: mergedOptions.sampleRate || 44100,
        frameSize: mergedOptions.appOptions?.frameSize,
      }

      // 检查权限
      try {
        // App端需要先请求录音权限
        await new Promise<void>((resolve, reject) => {
          uni.authorize({
            scope: 'scope.record',
            success: () => resolve(),
            fail: (err) => reject(RecordError.permissionDenied(err.errMsg)),
          })
        })
      } catch (error) {
        // 在某些平台上可能不需要显式授权，继续尝试录音
        if (!mergedOptions.disableLogs) {
          console.warn('录音权限请求可能不支持：', error)
        }
      }

      // 开始录音
      recorderManager.start(recordOptions)

      // 模拟波形数据
      initWaveformData()
    } catch (error) {
      console.error('启动录音失败：', error)
      throw error instanceof RecordError ? error : RecordError.initializationFailed(error.message)
    }
  }

  // 暂停录音
  const pauseRecording = (): void => {
    if (isRecording.value && !isPaused.value) {
      recorderManager.pause()
    }
  }

  // 继续录音
  const resumeRecording = (): void => {
    if (isRecording.value && isPaused.value) {
      recorderManager.resume()
    }
  }

  // 停止录音
  const stopRecording = (): Promise<string> => {
    return new Promise((resolve, reject) => {
      if (!isRecording.value) {
        reject(RecordError.recordingFailed('没有正在进行的录音'))
        return
      }

      // 创建一次性事件监听器来获取结果
      const onStopCallback = (res: UniApp.OnStopCallbackResult) => {
        // 移除一次性监听器
        recorderManager.offStop(onStopCallback)

        resolve(res.tempFilePath)
      }

      // 添加一次性监听器
      recorderManager.onStop(onStopCallback)

      // 停止录音
      recorderManager.stop()
    })
  }

  // 播放录音
  const playRecording = (): void => {
    if (recordURL.value) {
      const innerAudioContext = uni.createInnerAudioContext()
      innerAudioContext.src = recordURL.value
      innerAudioContext.autoplay = true
      innerAudioContext.onError((err) => {
        console.error('播放录音失败：', err)
      })
    }
  }

  // 初始化模拟波形数据
  const initWaveformData = (): void => {
    // 创建一个固定大小的数组用于模拟波形数据
    const size = 128
    waveformDataArray = new Uint8Array(size)

    // 初始化为静音状态
    for (let i = 0; i < size; i++) {
      waveformDataArray[i] = 128 // 静音波形中点值
    }
  }

  // 更新模拟波形数据
  const updateWaveformData = (): void => {
    if (!waveformDataArray || !isRecording.value) return

    // 根据当前音量生成随机波形数据
    const volumeValue = volume.value
    const baseAmplitude = Math.max(5, volumeValue / 2) // 确保即使在静音时也有一些波动

    for (let i = 0; i < waveformDataArray.length; i++) {
      // 添加一些随机波动，音量越大波动越大
      const randomAmplitude = Math.random() * baseAmplitude * 2 - baseAmplitude

      // 波形值范围保持在 0-255 之间，中心在 128
      waveformDataArray[i] = Math.max(0, Math.min(255, 128 + randomAmplitude))
    }
  }

  // 获取音频波形数据 (App端通过模拟生成)
  const getAudioWaveform = (): Uint8Array | null => {
    if (!waveformDataArray || !isRecording.value) return null

    // 更新波形数据
    updateWaveformData()
    return waveformDataArray
  }

  // 取消录音
  const cancelRecording = (): void => {
    if (isRecording.value) {
      // 停止录音
      recorderManager.stop()

      // 重置状态
      resetState()
    }
  }

  // 重置状态
  const resetState = (): void => {
    isRecording.value = false
    isPaused.value = false
    duration.value = 0
    volume.value = 0
    recordBlob.value = null
    recordURL.value = ''
    stopTimers()
  }

  // 开始计时器
  const startTimers = (): void => {
    // 录音时长计时器
    if (!durationTimer) {
      durationTimer = window.setInterval(() => {
        duration.value += 100
      }, 100)
    }

    // 模拟音量更新计时器
    if (!volumeUpdateTimer) {
      volumeUpdateTimer = window.setInterval(() => {
        // 生成0-100之间的随机音量值，但保持一定平滑度
        const newVolume = Math.min(100, Math.max(0, volume.value + (Math.random() * 30 - 15)))
        volume.value = Math.round(newVolume)
      }, 100)
    }
  }

  // 停止计时器
  const stopTimers = (): void => {
    if (durationTimer) {
      clearInterval(durationTimer)
      durationTimer = null
    }

    if (volumeUpdateTimer) {
      clearInterval(volumeUpdateTimer)
      volumeUpdateTimer = null
    }
  }

  // 清理资源
  const destroy = (): void => {
    // 停止录音
    if (isRecording.value) {
      cancelRecording()
    }

    // 停止计时器
    stopTimers()

    // 移除事件监听
    recorderManager.offStart()
    recorderManager.offPause()
    recorderManager.offResume()
    recorderManager.offStop()
    recorderManager.offError()
  }

  // 组件卸载时自动清理
  onUnmounted(() => {
    destroy()
  })

  return {
    // 状态
    isRecording: isRecording as Readonly<Ref<boolean>>,
    isPaused: isPaused as Readonly<Ref<boolean>>,
    duration: duration as Readonly<Ref<number>>,
    volume: volume as Readonly<Ref<number>>,
    recordBlob: recordBlob as Readonly<Ref<RecordResult | null>>,
    recordURL: recordURL as Readonly<Ref<string>>,

    // 方法
    startRecording,
    pauseRecording,
    resumeRecording,
    stopRecording,
    playRecording,
    getAudioWaveform,
    cancelRecording,
    destroy,
  }
}
```

### 2. 添加使用示例和文档

创建 App 端录音功能的使用说明文档：

````markdown
# App 端录音功能使用指南

## 基本用法

```vue
<script setup>
import { useRecordApp } from '@/hooks/useRecordApp'

const {
  isRecording,
  isPaused,
  duration,
  volume,
  recordURL,
  startRecording,
  pauseRecording,
  resumeRecording,
  stopRecording,
  cancelRecording,
} = useRecordApp({
  maxDuration: 30000, // 最大录音时长30秒
  appOptions: {
    format: 'mp3', // 音频格式，支持 mp3/wav/aac
    frameSize: 50, // 指定帧大小，单位KB
  },
})

// 开始录音
const handleStartRecording = async () => {
  try {
    await startRecording()
    console.log('录音已开始')
  } catch (error) {
    console.error('录音启动失败', error)
  }
}

// 停止录音
const handleStopRecording = async () => {
  try {
    const filePath = await stopRecording()
    console.log('录音已停止，文件路径：', filePath)

    // 可以将文件上传到服务器或者进行其他处理
    uni.uploadFile({
      url: 'https://your-server-url/upload',
      filePath: filePath,
      name: 'audio',
      success: (res) => {
        console.log('上传成功', res)
      },
      fail: (err) => {
        console.error('上传失败', err)
      },
    })
  } catch (error) {
    console.error('录音停止失败', error)
  }
}
</script>
```
````

## 音量显示

```vue
<template>
  <view class="volume-meter">
    <view class="volume-bar" :style="{ width: volume + '%' }"></view>
  </view>
</template>

<script setup>
import { useRecordApp } from '@/hooks/useRecordApp'

const { isRecording, volume, startRecording, stopRecording } = useRecordApp()
</script>

<style scoped>
.volume-meter {
  width: 100%;
  height: 20px;
  background-color: #eee;
  border-radius: 10px;
  overflow: hidden;
}
.volume-bar {
  height: 100%;
  background-color: #2196f3;
  transition: width 0.1s ease;
}
</style>
```

## 注意事项

1. App 端的录音功能需要申请相应权限，具体权限要求如下：
   - Android 需要在 manifest.json 中配置以下权限：
     ```json
     "permissions": [
       "android.permission.RECORD_AUDIO"
     ]
     ```
   - iOS 需要在 info.plist 中添加以下描述：
     ```
     <key>NSMicrophoneUsageDescription</key>
     <string>需要使用麦克风进行录音</string>
     ```
2. App 端录音返回的是临时文件路径而非 Blob 对象，如需转换为 Blob，可以通过以下方法：

   ```js
   // 将文件路径转换为blob对象
   const filePathToBlob = async (filePath) => {
     return new Promise((resolve, reject) => {
       plus.io.resolveLocalFileSystemURL(
         filePath,
         (entry) => {
           entry.file((file) => {
             const reader = new plus.io.FileReader()
             reader.onloadend = function () {
               resolve(this.result)
             }
             reader.onerror = function (err) {
               reject(err)
             }
             reader.readAsDataURL(file)
           }, reject)
         },
         reject
       )
     })
   }
   ```

3. 使用录音功能前建议先检查权限：

   ```js
   const checkRecordPermission = () => {
     return new Promise((resolve, reject) => {
       uni.authorize({
         scope: 'scope.record',
         success: () => resolve(true),
         fail: () => resolve(false),
       })
     })
   }

   // 使用示例
   const hasPermission = await checkRecordPermission()
   if (hasPermission) {
     startRecording()
   } else {
     uni.showModal({
       title: '提示',
       content: '需要麦克风权限才能录音',
       confirmText: '去设置',
       success: (res) => {
         if (res.confirm) {
           uni.openSetting()
         }
       },
     })
   }
   ```

````

### 3. 创建示例用例
创建一个简单的App端录音功能示例：

```vue
<!-- src/components/z-record-demo/z-record-demo.vue -->
<template>
  <view class="record-demo">
    <view class="status">
      <text>状态: {{ isRecording ? '录音中' : '就绪' }}</text>
      <text>{{ isPaused ? '(已暂停)' : '' }}</text>
    </view>

    <view class="duration">
      录音时长: {{ formatDuration(duration) }}
    </view>

    <view class="volume-meter">
      <view class="volume-bar" :style="{ width: volume + '%' }"></view>
    </view>

    <view class="controls">
      <button @tap="handleStartRecording" :disabled="isRecording && !isPaused">
        {{ isRecording && !isPaused ? '录音中...' : '开始录音' }}
      </button>

      <button @tap="handlePauseRecording"
              :disabled="!isRecording || isPaused"
              v-if="isRecording && !isPaused">
        暂停
      </button>

      <button @tap="handleResumeRecording"
              :disabled="!isPaused"
              v-if="isRecording && isPaused">
        继续
      </button>

      <button @tap="handleStopRecording"
              :disabled="!isRecording">
        停止录音
      </button>

      <button @tap="handlePlayRecording"
              :disabled="!recordURL">
        播放录音
      </button>
    </view>

    <view class="record-result" v-if="recordURL">
      <text>录音文件: {{ recordURL }}</text>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { useRecordApp } from '@/hooks/useRecordApp'

const {
  isRecording,
  isPaused,
  duration,
  volume,
  recordURL,
  startRecording,
  pauseRecording,
  resumeRecording,
  stopRecording,
  playRecording,
  cancelRecording
} = useRecordApp({
  maxDuration: 60000, // 最大录音时长 60 秒
  appOptions: {
    format: 'mp3'
  }
})

// 格式化时长显示
const formatDuration = (ms) => {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 开始录音
const handleStartRecording = async () => {
  try {
    await startRecording()
    uni.showToast({
      title: '录音已开始',
      icon: 'none'
    })
  } catch (error) {
    uni.showModal({
      title: '录音失败',
      content: error.message || '无法启动录音',
      showCancel: false
    })
  }
}

// 暂停录音
const handlePauseRecording = () => {
  pauseRecording()
  uni.showToast({
    title: '录音已暂停',
    icon: 'none'
  })
}

// 继续录音
const handleResumeRecording = () => {
  resumeRecording()
  uni.showToast({
    title: '录音已继续',
    icon: 'none'
  })
}

// 停止录音
const handleStopRecording = async () => {
  try {
    const result = await stopRecording()
    uni.showToast({
      title: '录音已完成',
      icon: 'success'
    })
    console.log('录音文件:', result)
  } catch (error) {
    uni.showModal({
      title: '录音失败',
      content: error.message || '停止录音时发生错误',
      showCancel: false
    })
  }
}

// 播放录音
const handlePlayRecording = () => {
  playRecording()
  uni.showToast({
    title: '正在播放',
    icon: 'none'
  })
}
</script>

<style lang="scss" scoped>
.record-demo {
  padding: 20px;

  .status {
    font-size: 18px;
    margin-bottom: 10px;
  }

  .duration {
    font-size: 16px;
    margin-bottom: 15px;
  }

  .volume-meter {
    height: 20px;
    background-color: #eee;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;

    .volume-bar {
      height: 100%;
      background-color: #2196F3;
      transition: width 0.1s ease;
    }
  }

  .controls {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;

    button {
      flex: 1;
      min-width: 120px;
      font-size: 16px;
      padding: 10px;
    }
  }

  .record-result {
    margin-top: 20px;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 5px;

    text {
      font-size: 14px;
      word-break: break-all;
    }
  }
}
</style>
````

## 验收标准

1. 实现了 App 端录音功能的完整接口，与 H5 端保持一致
2. 使用 uni.getRecorderManager()正确实现了录音相关的操作
3. 处理了 App 端特有的事件和生命周期
4. 提供了完整的录音状态管理，包括：开始、暂停、继续、停止等
5. 实现了音量监控和波形数据生成（模拟）
6. 提供了清晰的文档和示例代码
7. 代码可在真机环境下正常运行
8. 错误处理完善，包括权限检查和错误提示

## 依赖关系

- 上游依赖: task-record-01 (类型定义与接口设计)
- 下游依赖: task-record-04 (入口文件与条件编译)

## 优先级

中 - 作为主要功能实现之一

## 状态追踪

- [x] 已创建 useRecordApp.ts 文件
- [x] 已实现 App 端录音功能
- [x] 已添加权限检查和错误处理
- [x] 已创建示例组件
- [ ] 已完成文档编写
- [ ] 已通过真机测试
- [ ] 已完成代码审查

<template>
  <view class="pages">
    <unicloud-db
      ref="tdbRef"
      v-slot="{ data, loading, error, options }"
      loadtime="manual"
      :page-size="100"
      where="user_id == $cloudEnv_uid && status == 0"
      orderby="create_date desc"
      collection="todos"
    >
      <view v-if="error">{{ error.message }}</view>
      <view v-else>
        <uni-swipe-action-item v-for="(item, index) in data" :right-options="todoRight" :left-options="todoRight">
          <view class="todo-box">
            <checkbox style="transform: scale(0.7)" @click="onChangeStatus(item)" />
            {{ item.title }}
          </view>
        </uni-swipe-action-item>
      </view>
    </unicloud-db>
    <uni-popup ref="editRef" type="bottom">
      <view class="edit-todo-box">
        <input v-model="curTodo.title" auto-focus type="text" placeholder="准备做点啥？" />
        <view class="operation-panel">
          <view class=""> aaa </view>
          <button class="edit-btn" size="mini" type="primary" @click="onNewTodo">添加</button>
        </view>
      </view>
    </uni-popup>
    <view class="new-todo-btn" @click="showNewTodo"></view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { onPullDownRefresh } from '@dcloudio/uni-app'

const db = uniCloud.database()
const tdbRef = ref()
const editRef = ref()
const curTodo = ref({
  title: '',
})
const showNewTodo = () => {
  editRef.value.open()
}
let isLoading = false

const onChangeStatus = async (value) => {
  console.log(value)
  uni.showLoading()
  if (isLoading) return
  isLoading = true
  await db
    .collection('todos')
    .where(`_id == '${value._id}'`)
    .update({
      status: value.status === 0 ? 1 : 0,
    })
  isLoading = false
  uni.hideLoading()
  uni.startPullDownRefresh()
}

// 添加 todo
const onNewTodo = async () => {
  if (isLoading) return
  isLoading = true
  const { title } = curTodo.value
  uni.showLoading()
  await db.collection('todos').add({
    title,
    status: 0,
  })
  uni.hideLoading()
  isLoading = false
  editRef.value.close()
  uni.startPullDownRefresh()
}

const todoRight = ref([
  {
    text: '取消',
    style: {
      backgroundColor: '#007aff',
    },
  },
  {
    text: '确认',
    style: {
      backgroundColor: '#dd524d',
    },
  },
])

const init = () => {
  curTodo.value = {}
  tdbRef.value.loadData(
    {
      clear: true,
    },
    () => {
      uni.stopPullDownRefresh()
    }
  )
}

// 刷新
onPullDownRefresh(init)
onMounted(() => {
  uni.startPullDownRefresh()
})
</script>

<style scoped lang="scss">
.pages {
  height: 100vh;
  background-color: hsl(205deg, 20%, 94%);
  padding: 10px;
}

.todo-box {
  background-color: #fff;
  padding: 10px;
}

.new-todo-btn {
  position: fixed;
  bottom: 50px;
  right: 50px;
  width: 40px;
  height: 40px;
  background-color: $uni-bg-color-grey;
  border-radius: 50%;
}

.edit-todo-box {
  padding: 10px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  background-color: #fff;

  .operation-panel {
    display: flex;

    .edit-btn {
      margin-left: auto;
      margin-right: 0;
    }
  }
}
</style>

<template>
  <view class="week-edit-container">
    <z-page-navbar :title="pageTitle" :back="true"></z-page-navbar>
    
    <view class="edit-content">
      <textarea 
        v-model="content" 
        class="edit-textarea" 
        :placeholder="placeholderText"
        maxlength="-1"
      ></textarea>
    </view>
    
    <view class="action-bar">
      <view class="save-btn" @click="handleSave" :class="{ 'disabled': loading }">
        <i class="fas fa-check"></i>
        <text>{{ loading ? '保存中...' : '保存' }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getUrlParams, router } from '@/utils/tools'
import { getMemo, addMemo, updateMemo } from '@/api/memo'

// 获取路由参数
const params = ref({})
const type = ref('goal') // 默认为周目标
const weekDate = ref('') // 周日期参数

// 页面标题和提示文本
const pageTitle = ref('')
const placeholderText = ref('')
const content = ref('')
const currentId = ref('') // 当前编辑的记录 ID
const loading = ref(false) // 加载状态

// 初始化路由参数
const initParams = () => {
  const urlParams = getUrlParams()
  params.value = urlParams
  type.value = urlParams.type || 'goal'
  weekDate.value = urlParams.date || ''
}

// 根据类型设置页面标题和提示文本
const setupPageByType = () => {
  if (type.value === 'goal') {
    pageTitle.value = '编辑周目标'
    placeholderText.value = '请输入本周的目标和计划...'
  } else if (type.value === 'review') {
    pageTitle.value = '编辑周复盘'
    placeholderText.value = '请对本周工作进行总结和复盘...'
  }
}


// 工作流 API
const workflowApi = async (workflow_id, params) => {
  try {
    // 获取应用程序实例和 token
    const app = getApp()
    const token = app.globalData?.kzToken || toolGlobalData.get('kzToken')

    if (!token) {
      throw new Error('未找到 kzToken')
    }

    const res = await request.post(
      'https://api.coze.cn/v1/workflow/run',
      {
        workflow_id,
        app_id: '7484161211752431679',
        parameters: params,
      },
      {
        timeout: 300000,
        header: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    )
    return {
      ...res,
      data: JSON.parse(res.data),
    }
  } catch (error) {
    console.error('工作流 API 调用失败', error)
    throw error
  }
}


// 加载数据
const loadData = async () => {
  if (!weekDate.value) {
    console.error('缺少周日期参数')
    return
  }
  
  try {
    loading.value = true
    // 构建查询条件字符串
    const memoType = type.value === 'goal' ? 'weekGoal' : 'weekReview'
    const query = `type == "${memoType}" && date == "${weekDate.value}"`
    
    const result = await getMemo(query)
    
    if (result && result.length > 0) {
      content.value = result[0].content
      currentId.value = result[0]._id
      console.log(`[DEBUG] 加载已有${type.value === 'goal' ? '周目标' : '周复盘'}数据成功`)
    } else {
      content.value = ''
      currentId.value = ''
      console.log(`[DEBUG] 未找到${type.value === 'goal' ? '周目标' : '周复盘'}数据，创建新记录`)
    }
  } catch (error) {
    console.error('加载数据失败：', error)
    uni.showToast({
      title: '加载数据失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 保存内容
const handleSave = async () => {
  if (!content.value.trim()) {
    uni.showToast({
      title: '内容不能为空',
      icon: 'none'
    })
    return
  }
  
  try {
    loading.value = true
    const memoType = type.value === 'goal' ? 'weekGoal' : 'weekReview'
    
    if (currentId.value) {
      // 更新已有记录
      await updateMemo({
        _id: currentId.value,
        content: content.value,
      })
      console.log(`[DEBUG] 更新${type.value === 'goal' ? '周目标' : '周复盘'}数据成功`)
    } else {
      // 创建新记录
      await addMemo({
        type: memoType,
        date: weekDate.value,
        content: content.value,
      })
      console.log(`[DEBUG] 创建${type.value === 'goal' ? '周目标' : '周复盘'}数据成功`)
      
    }
    // 查询本周的周概览记录
    // const weekOverviewQuery = `type == "weekOverview" && date == "${weekDate.value}"`
    //   const overviewResult = await getMemo(weekOverviewQuery)
    // const overviewId = overviewResult && overviewResult.length > 0 ? overviewResult[0]._id : generateUUID()
      
    //   // 调用工作流接口
    //   workflowApi('7519776270865072180', {
    //     content: content.value,
    //     date: weekDate.value,
    //     id: overviewId,
    //     type: 'weekOverview'
    //   })
    
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })
    
    // 返回上一页
    // setTimeout(() => {
    //   router.back()
    // }, 1500)
  } catch (error) {
    console.error('保存失败：', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 页面加载时设置页面类型并加载数据
onMounted(() => {
  initParams()
  setupPageByType()
  console.log(`[DEBUG] 编辑页面类型：${type.value}, 日期：${weekDate.value}`)
  loadData()
})
</script>

<style lang="scss">
.week-edit-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--color-white);
}

.edit-content {
  flex: 1;
  padding: 30rpx;
}

.edit-textarea {
  width: 100%;
  height: 100%;
  background-color: var(--color-gray-50);
  border-radius: var(--rounded-md);
  padding: 20rpx;
  font-size: 28rpx;
  color: var(--color-gray-800);
  box-sizing: border-box;
}

.action-bar {
  padding: 20rpx 30rpx;
  border-top: 1px solid var(--color-gray-200);
  display: flex;
  justify-content: center;
  background-color: var(--color-white);
}

.save-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 60rpx;
  background-color: var(--color-primary);
  border-radius: var(--rounded-md);
  color: var(--color-white);
  font-size: 28rpx;
  font-weight: 500;

  i {
    margin-right: 8rpx;
    font-size: 24rpx;
  }
  
  &.disabled {
    opacity: 0.7;
    pointer-events: none;
  }
}
</style> 
# 任务：开发自定义 TabBar 组件

## 任务信息

- **所属功能模块**: TabBar 栏自定义
- **优先级**: 高
- **预估工时**: 2 人日
- **状态**: 待开发

## 任务描述

开发一个独立的自定义 TabBar 组件，替代原生 tabBar。该组件将提供与原生 TabBar 相似的功能，但具有完全可定制的 UI 和交互效果。组件需要能够显示图标和文本、指示当前选中的 tab，并支持点击切换功能。

## 技术实现详情

### 1. 创建 TabBar 组件

在`pages/index/component/tabBar.vue`文件中实现自定义 TabBar 组件：

```vue
<template>
  <div class="custom-tabbar">
    <div
      v-for="(tab, index) in tabs"
      :key="index"
      class="tab-item"
      :class="{ active: modelValue === tab.id }"
      @click="handleTabClick(tab.id)"
    >
      <div class="tab-icon">
        <i :class="modelValue === tab.id ? tab.activeIcon : tab.icon"></i>
      </div>
      <div class="tab-text">{{ tab.text }}</div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  // 使用v-model绑定当前选中的tab
  modelValue: {
    type: String,
    required: true,
  },
  // 定义tab列表
  tabs: {
    type: Array,
    required: true,
    // 每个tab项包含：id, text, icon, activeIcon
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

// 处理tab点击事件
const handleTabClick = (tabId) => {
  if (tabId !== props.modelValue) {
    emit('update:modelValue', tabId)
    emit('change', tabId)
  }
}
</script>

<style scoped>
.custom-tabbar {
  display: flex;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  padding: 5px 0;
}

.tab-icon {
  font-size: 22px;
  margin-bottom: 2px;
  color: var(--color-gray-500);
  transition: all 0.2s ease;
}

.tab-text {
  font-size: 12px;
  color: var(--color-gray-500);
  transition: all 0.2s ease;
}

.tab-item.active .tab-icon,
.tab-item.active .tab-text {
  color: var(--color-primary, #64b6f7);
  font-weight: 500;
}

/* 点击效果 */
.tab-item:active {
  opacity: 0.7;
}
</style>
```

### 2. 定义 TabBar 数据结构

在主页文件`pages/index/index.vue`中定义 TabBar 数据：

```javascript
// 在<script setup>部分添加
import TabBar from './component/tabBar.vue'

// TabBar数据定义
const tabs = [
  {
    id: 'okr',
    text: '目标',
    icon: 'icon-goal',
    activeIcon: 'icon-goal-fill',
    path: 'pages/okr/okrList',
  },
  {
    id: 'today',
    text: '今天',
    icon: 'icon-task',
    activeIcon: 'icon-task-fill',
    path: 'pages/okr/today',
  },
  {
    id: 'analysis',
    text: '分析',
    icon: 'icon-analysis',
    activeIcon: 'icon-analysis-fill',
    path: 'pages/okr/data-analysis',
  },
  {
    id: 'diary',
    text: '日记',
    icon: 'icon-diary',
    activeIcon: 'icon-diary-fill',
    path: 'pages/memo/diary',
  },
  {
    id: 'setting',
    text: '设置',
    icon: 'icon-setting',
    activeIcon: 'icon-setting-fill',
    path: 'pages/setting/setting',
  },
]
```

### 3. 集成 TabBar 到主页

在主页文件`pages/index/index.vue`中集成 TabBar 组件：

```vue
<!-- 在template部分替换tabbar占位符 -->
<div class="tabbar-container">
  <tabBar v-model="currentTab" :tabs="tabs" @change="handleTabChange" />
</div>

<script setup>
// 添加处理tab变更的方法
const handleTabChange = (tabId) => {
  console.log('Tab changed to:', tabId)
  // 后续任务将在此实现页面切换逻辑
}
</script>
```

### 4. 添加图标支持

确保项目中已有必要的图标支持，可以使用以下方法之一：

1. 使用字体图标（如项目已有 iconfont）
2. 使用 SVG 图标
3. 使用图片图标

如使用 iconfont，确保在主页中引入：

```vue
<!-- 在主页的<template>顶部添加 -->
<head>
  <link rel="stylesheet" href="@/static/iconfont/iconfont.css">
</head>
```

## 验收标准

1. TabBar 组件已创建并完成样式设计
2. 组件能正确显示所有 tab 项（目标、今天、分析、日记、设置）
3. 组件能够正确显示当前选中状态
4. 点击不同 tab 能正确触发 change 事件
5. 组件样式与项目整体设计风格一致
6. 适配不同屏幕尺寸和设备特性

## 依赖关系

- 依赖 task-tabbar-02 完成（主页框架）
- task-tabbar-04 依赖此任务完成（页面切换机制）

## 注意事项

1. 确保 TabBar 组件的样式与原生 TabBar 保持视觉一致性，提供流畅的过渡体验
2. 考虑添加过渡动画，提升用户体验
3. 注意图标资源的正确引入和使用
4. 确保组件在各种屏幕尺寸下都能正确显示

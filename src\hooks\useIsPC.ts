/**
 * @description: 判断是否是 PC 端 TODO:需要判断是否是 APP
 * @param {number} minWidth 最小宽度
 * @param {number} maxWidth 最大宽度
 * @return {object} { isPC, isH5Mobile, isAPP }
 */
import { ref, onMounted, onUnmounted, reactive } from 'vue'

interface Params {
  minWidth?: number
  maxWidth?: number
}

export default function useIsPC(params: Params = {}) {
  const system = reactive({
    isPC: false, // pc 端，宽度在 minWidth 和 maxWidth 之间
    isH5Mobile: false, // web 端的移动端，宽度小于 minWidth
    isAPP: false, // APP 端
  })

  let { minWidth, maxWidth } = params
  if (!minWidth) minWidth = getApp().globalData?.minWidth as number
  if (!maxWidth) maxWidth = getApp().globalData?.maxWidth as number

  const updateState = (res: any) => {
    const width = typeof res === 'number' ? res : res.size.windowWidth

    // TODO 需要兼容平板横屏双列布局
    // #ifdef APP-PLUS
    system.isAPP = true
    // #endif

    // #ifdef WEB
    system.isPC = width >= minWidth && width <= maxWidth
    system.isH5Mobile = !system.isPC
    // #endif
  }

  onMounted(() => {
    uni.onWindowResize(updateState)
    updateState(uni.getWindowInfo().windowWidth)
  })

  onUnmounted(() => {
    uni.offWindowResize(updateState)
  })

  return system
}

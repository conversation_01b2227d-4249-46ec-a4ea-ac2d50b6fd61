# todolist-api 接口文档

## 简介

`todolist-api` 是一个基于 uniCloud 云对象实现的后端服务，用于代理和封装对第三方待办事项服务（如滴答清单）的 API 调用。它提供了完整的任务管理和项目管理功能，包括身份认证、数据获取、创建、更新和删除等操作。

本云对象采用模块化设计，主要包含以下几个部分：

- **`index.obj.js`**: 云对象入口文件，负责整合所有模块并对外提供统一接口。
- **`authManager.js`**: 认证管理模块，处理用户登录、`token` 获取与管理，并封装了底层的 HTTP 请求。
- **`taskManager.js`**: 任务管理模块，提供任务的增、删、改、查以及批量操作等功能。
- **`projectManager.js`**: 项目管理模块，提供项目的增、删、改、查等功能。

## 初始化与认证

在使用任何功能之前，必须先对 API 进行初始化认证。支持两种认证方式：

1.  **用户名密码登录**: 通过调用 `login` 方法获取 `token`。
2.  **使用已有 Token**: 如果你已经有了一个有效的 `token`，可以调用 `initWithToken` 方法直接初始化。

### `login(options)`

通过用户名和密码进行登录认证。

- **参数**: `options` (object)
  - `username` (string, **required**): 用户的邮箱或手机号。
  - `password` (string, **required**): 用户的密码。
  - `isPhone` (boolean, *optional*, default: `true`): 指明 `username` 是否为手机号。
- **成功返回**:
  ```json
  {
    "errCode": 0,
    "errMsg": "登录成功",
    "data": {
      "token": "YOUR_AUTH_TOKEN",
      "loginType": "手机号"
    }
  }
  ```
- **调用示例**:
  ```javascript
  const todoApi = uniCloud.importObject('todolist-api');
  try {
    const res = await todoApi.login({
      username: 'your_phone_or_email',
      password: 'your_password'
    });
    if (res.errCode === 0) {
      console.log('登录成功, token:', res.data.token);
    } else {
      console.error('登录失败:', res.errMsg);
    }
  } catch (e) {
    console.error('调用 login 异常:', e);
  }
  ```

### `initWithToken(token)`

使用已有的 `token` 初始化 API。

- **参数**:
  - `token` (string, **required**): 你的访问令牌。
- **成功返回**:
  ```json
  {
    "errCode": 0,
    "errMsg": "API 初始化成功"
  }
  ```
- **调用示例**:
  ```javascript
  // 假设从 storage 中获取了 token
  async function initApi() {
    const todoApi = uniCloud.importObject('todolist-api');
    const token = uni.getStorageSync('my_token');
    if (token) {
      try {
        const res = await todoApi.initWithToken(token);
        if (res.errCode === 0) {
          console.log('API 初始化成功');
        } else {
          console.error('初始化失败:', res.errMsg);
        }
      } catch (e) {
        console.error('调用 initWithToken 异常:', e);
      }
    }
  }
  ```

---

## 任务管理 (`taskManager`)

提供所有与任务相关的功能。

### `getTasks(options)`

获取任务列表，支持多种筛选条件。

- **参数**: `options` (object, *optional*)
  - `mode` (string, *optional*, default: `'all'`): 筛选模式。可选值：`'all'`, `'today'`, `'yesterday'`, `'recent_7_days'`。
  - `keyword` (string, *optional*): 任务标题或内容的关键词。
  - `priority` (number, *optional*): 优先级。`0`-最低, `1`-低, `3`-中, `5`-高。
  - `projectName` (string, *optional*): 所属项目的名称。
  - `completed` (boolean, *optional*): 完成状态。`true` 或 `false`。
- **成功返回**:
  ```json
  {
    "errCode": 0,
    "errMsg": "获取任务列表成功",
    "data": [ ... ]
  }
  ```
- **调用示例**:
  ```javascript
  // 获取今天的、优先级为“高”且未完成的任务
  try {
    const res = await todoApi.getTasks({
      mode: 'today',
      priority: 5,
      completed: false
    });
    if (res.errCode === 0) {
      console.log('获取任务成功:', res.data);
    } else {
      console.error('获取任务失败:', res.errMsg);
    }
  } catch (e) {
    console.error('调用 getTasks 异常:', e);
  }
  ```

### `createTask(options)`

创建一个新任务。

- **参数**: `options` (object)
  - `title` (string, **required**): 任务标题。
  - `content` (string, *optional*): 任务内容。
  - `priority` (number, *optional*, default: `0`): 优先级。
  - `projectName` (string, *optional*): 项目名称。
  - `tagNames` (array, *optional*): 标签名称数组。
  - `startDate` (string, *optional*): 开始日期，格式 `YYYY-MM-DD HH:mm`。
  - `dueDate` (string, *optional*): 截止日期，格式 `YYYY-MM-DD HH:mm`。
  - `isAllDay` (boolean, *optional*, default: `false`): 是否为全天任务。
  - `reminder` (string, *optional*): 提醒时间设置。
  - `kind` (string, *optional*, default: `'TEXT'`): 任务类型。
- **成功返回**:
  ```json
  {
    "errCode": 0,
    "errMsg": "任务创建成功",
    "data": { ... } // 返回创建的任务详情
  }
  ```
- **调用示例**:
  ```javascript
  try {
    const res = await todoApi.createTask({
      title: '学习 uniCloud',
      content: '阅读云对象文档',
      priority: 3,
      projectName: '学习',
      tagNames: ['uniCloud', '前端'],
      dueDate: '2024-05-26 18:00'
    });
    if (res.errCode === 0) {
      console.log('任务创建成功:', res.data);
    } else {
      console.error('任务创建失败:', res.errMsg);
    }
  } catch (e) {
    console.error('调用 createTask 异常:', e);
  }
  ```

### `updateTask(taskId, updateData)`

更新一个已有的任务。

- **参数**:
  - `taskId` (string, **required**): 要更新的任务 ID。
  - `updateData` (object, **required**): 包含要更新字段的对象，字段同 `createTask`。
- **成功返回**:
  ```json
  {
    "errCode": 0,
    "errMsg": "任务更新成功",
    "data": {
      "id": "updated_task_id",
      "title": "更新后的标题",
      ...
    }
  }
  ```
- **调用示例**:
  ```javascript
  async function modifyTask(taskId) {
    const todoApi = uniCloud.importObject('todolist-api');
    try {
      const res = await todoApi.updateTask(taskId, {
        title: '学习 uniCloud 云对象（已更新）', // 修改标题
        priority: 5, // 提高优先级
      });
      if (res.errCode === 0) {
        console.log('任务更新成功:', res.data);
      } else {
        console.error('任务更新失败:', res.errMsg);
      }
    } catch (e) {
      console.error('调用 updateTask 异常:', e);
    }
  }
  ```

### `deleteTask(taskId)`

删除一个任务。

- **参数**:
  - `taskId` (string, **required**): 要删除的任务 ID。
- **成功返回**:
  ```json
  {
    "errCode": 0,
    "errMsg": "任务删除成功",
    "data": {
      "taskId": "deleted_task_id"
    }
  }
  ```
- **调用示例**:
  ```javascript
  async function removeTask(taskId) {
    const todoApi = uniCloud.importObject('todolist-api');
    try {
      const res = await todoApi.deleteTask(taskId);
      if (res.errCode === 0) {
        console.log('任务删除成功');
      } else {
        console.error('任务删除失败:', res.errMsg);
      }
    } catch (e) {
      console.error('调用 deleteTask 异常:', e);
    }
  }
  ```

### `completeTask(taskId)` / `uncompleteTask(taskId)`

完成或取消完成一个任务。

- **参数**:
  - `taskId` (string, **required**): 任务 ID。
- **成功返回**:
  ```json
  {
    "errCode": 0,
    "errMsg": "任务更新成功",
    "data": { ... } // 返回更新后的任务详情
  }
  ```
- **调用示例**:
  ```javascript
  async function markTaskAsDone(taskId) {
    const todoApi = uniCloud.importObject('todolist-api');
    try {
      const res = await todoApi.completeTask(taskId);
      if (res.errCode === 0) {
        console.log('任务已完成:', res.data);
      } else {
        console.error('操作失败:', res.errMsg);
      }
    } catch (e) {
      console.error('调用 completeTask 异常:', e);
    }
  }

  async function reopenTask(taskId) {
    const todoApi = uniCloud.importObject('todolist-api');
    try {
      const res = await todoApi.uncompleteTask(taskId);
      if (res.errCode === 0) {
        console.log('任务已重新打开:', res.data);
      } else {
        console.error('操作失败:', res.errMsg);
      }
    } catch (e) {
      console.error('调用 uncompleteTask 异常:', e);
    }
  }
  ```

### `getTask(taskId)`

获取单个任务的详细信息。

- **参数**:
  - `taskId` (string, **required**): 任务 ID。
- **成功返回**:
  ```json
  {
    "errCode": 0,
    "errMsg": "获取任务详情成功",
    "data": { ... } // 任务详情
  }
  ```
- **调用示例**:
  ```javascript
  async function fetchTaskDetail(taskId) {
    const todoApi = uniCloud.importObject('todolist-api');
    try {
      const res = await todoApi.getTask(taskId);
      if (res.errCode === 0) {
        console.log('获取任务详情成功:', res.data);
      } else {
        console.error('获取失败:', res.errMsg);
      }
    } catch (e) {
      console.error('调用 getTask 异常:', e);
    }
  }
  ```

### `batchOperateTasks(options)`

批量操作任务。

- **参数**: `options` (object)
  - `taskIds` (array, **required**): 任务 ID 数组。
  - `action` (string, **required**): 操作类型。可选值：`'complete'`, `'uncomplete'`, `'delete'`。
- **成功返回**:
  ```json
  {
    "errCode": 0,
    "errMsg": "批量操作完成",
    "data": { ... }
  }
  ```
- **调用示例**:
  ```javascript
  try {
    const res = await todoApi.batchOperateTasks({
      taskIds: ['id_1', 'id_2', 'id_3'],
      action: 'delete'
    });
    if (res.errCode === 0) {
      console.log('批量删除成功:', res.data);
    } else {
      console.error('批量删除失败:', res.errMsg);
    }
  } catch (e) {
    console.error('调用 batchOperateTasks 异常:', e);
  }
  ```

---

## 项目管理 (`projectManager`)

提供所有与项目相关的功能。

### `getProjects(options)`

获取项目列表。

- **参数**: `options` (object, *optional*)
  - `keyword` (string, *optional*): 项目名称关键词。
  - `includeClosed` (boolean, *optional*, default: `false`): 是否包含已关闭的项目。
- **成功返回**:
  ```json
  {
    "errCode": 0,
    "errMsg": "获取项目列表成功",
    "data": [ ... ]
  }
  ```
- **调用示例**:
  ```javascript
  try {
    const res = await todoApi.getProjects({ includeClosed: true });
    if (res.errCode === 0) {
      console.log('获取所有项目（包括已关闭的）成功:', res.data);
    } else {
      console.error('获取失败:', res.errMsg);
    }
  } catch (e) {
    console.error('调用 getProjects 异常:', e);
  }
  ```

### `createProject(options)`

创建一个新项目。

- **参数**: `options` (object)
  - `name` (string, **required**): 项目名称。
  - `color` (string, *optional*, default: `'#3498db'`): 项目颜色代码。
  - `kind` (string, *optional*, default: `'TASK'`): 项目类型 (`'TASK'` 或 `'NOTE'`)。
- **成功返回**:
  ```json
  {
    "errCode": 0,
    "errMsg": "项目创建成功",
    "data": { ... } // 返回创建的项目详情
  }
  ```
- **调用示例**:
  ```javascript
  try {
    const res = await todoApi.createProject({
      name: '年度目标',
      color: '#ff0000'
    });
    if (res.errCode === 0) {
      console.log('项目创建成功:', res.data);
    } else {
      console.error('创建失败:', res.errMsg);
    }
  } catch (e) {
    console.error('调用 createProject 异常:', e);
  }
  ```

### `updateProject(projectId, updateData)`

更新一个项目。

- **参数**:
  - `projectId` (string, **required**): 项目 ID。
  - `updateData` (object, **required**): 包含要更新字段的对象。
- **成功返回**:
  ```json
  {
    "errCode": 0,
    "errMsg": "项目更新成功",
    "data": { ... } // 返回更新后的项目详情
  }
  ```
- **调用示例**:
  ```javascript
  async function modifyProject(projectId) {
    const todoApi = uniCloud.importObject('todolist-api');
    try {
      const res = await todoApi.updateProject(projectId, {
        name: '2025 年度目标', // 修改名称
      });
      if (res.errCode === 0) {
        console.log('项目更新成功:', res.data);
      } else {
        console.error('更新失败:', res.errMsg);
      }
    } catch (e) {
      console.error('调用 updateProject 异常:', e);
    }
  }
  ```

### `deleteProject(projectId)`

删除一个项目。

- **参数**:
  - `projectId` (string, **required**): 项目 ID。
- **成功返回**:
  ```json
  {
    "errCode": 0,
    "errMsg": "项目删除成功",
    "data": { "projectId": "deleted_project_id" }
  }
  ```
- **调用示例**:
  ```javascript
  async function removeProject(projectId) {
    const todoApi = uniCloud.importObject('todolist-api');
    try {
      const res = await todoApi.deleteProject(projectId);
      if (res.errCode === 0) {
        console.log('项目删除成功:', res.data);
      } else {
        console.error('删除失败:', res.errMsg);
      }
    } catch (e) {
      console.error('调用 deleteProject 异常:', e);
    }
  }
  ```

### `closeProject(projectId)` / `reopenProject(projectId)`

关闭或重新打开一个项目。

- **参数**:
  - `projectId` (string, **required**): 项目 ID。
- **成功返回**:
  ```json
  {
    "errCode": 0,
    "errMsg": "项目更新成功",
    "data": { ... } // 返回更新后的项目详情
  }
  ```
- **调用示例**:
  ```javascript
  async function archiveProject(projectId) {
    const todoApi = uniCloud.importObject('todolist-api');
    try {
      const res = await todoApi.closeProject(projectId);
      if (res.errCode === 0) {
        console.log('项目已归档:', res.data);
      } else {
        console.error('操作失败:', res.errMsg);
      }
    } catch (e) {
      console.error('调用 closeProject 异常:', e);
    }
  }

  async function restoreProject(projectId) {
    const todoApi = uniCloud.importObject('todolist-api');
    try {
      const res = await todoApi.reopenProject(projectId);
      if (res.errCode === 0) {
        console.log('项目已重新打开:', res.data);
      } else {
        console.error('操作失败:', res.errMsg);
      }
    } catch (e) {
      console.error('调用 reopenProject 异常:', e);
    }
  }
  ```

### `getProject(projectId)`

获取单个项目的详细信息。

- **参数**:
  - `projectId` (string, **required**): 项目 ID。
- **成功返回**:
  ```json
  {
    "errCode": 0,
    "errMsg": "获取项目详情成功",
    "data": { ... } // 项目详情
  }
  ```
- **调用示例**:
  ```javascript
  async function fetchProjectDetail(projectId) {
    const todoApi = uniCloud.importObject('todolist-api');
    try {
      const res = await todoApi.getProject(projectId);
      if (res.errCode === 0) {
        console.log('获取项目详情成功:', res.data);
      } else {
        console.error('获取失败:', res.errMsg);
      }
    } catch (e) {
      console.error('调用 getProject 异常:', e);
    }
  }
  ```

## 错误码

| errCode | 描述 |
|---|---|
| 1001 | 登录失败 |
| 1002 | 未找到 Token |
| 1003 | 未授权，请先登录 |
| 1004 | 参数无效 |
| 1005 | 解析响应数据失败 |
| 1006 | 项目未找到 |
| 2001 | 网络错误或请求失败 |
| 2002 | 请求超时 |
| 9999 | 未知错误 |

---
*文档生成时间：{{CURRENT_DATE}}*
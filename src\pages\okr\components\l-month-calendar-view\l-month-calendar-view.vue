<template>
  <div class="month-calendar-container">
    <!-- 图例说明 -->
    <div class="calendar-legend">
      <div class="legend-item">
        <div class="legend-color completed"></div>
        <span>有执行记录</span>
      </div>
      <div class="legend-item">
        <div class="legend-color cycle-progress"></div>
        <span>周期执行进度</span>
      </div>
      <div class="legend-item">
        <div class="legend-color incomplete"></div>
        <span>未完成</span>
      </div>
    </div>

    <!-- 月份导航 -->
    <div class="month-navigation">
      <div class="month-control prev" @click="prevMonth">
        <i class="fas fa-chevron-left"></i>
      </div>
      <div class="month-title">{{ currentYearMonth }}</div>
      <div class="month-control next" @click="nextMonth">
        <i class="fas fa-chevron-right"></i>
      </div>
    </div>

    <!-- 日历头部 - 星期几 -->
    <div class="calendar-header">
      <div class="calendar-cell header" v-for="day in weekDays" :key="day">{{ day }}</div>
    </div>

    <!-- 日历主体 -->
    <div class="calendar-body" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd">
      <div class="calendar-row" v-for="(week, weekIndex) in calendarDays" :key="weekIndex">
        <div
          class="calendar-cell"
          v-for="(day, dayIndex) in week"
          :key="`${weekIndex}-${dayIndex}`"
          :class="getCellClass(day)"
        >
          <div class="calendar-day-number">{{ day ? day.date : '' }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import dayjs from 'dayjs'
import { getOccurrences } from '@/utils/rrrrule'

// 组件属性定义
const props = defineProps({
  taskDetail: {
    type: Object,
    required: true,
  },
})

// 获取实际使用的任务详情（如果没有传入则使用模拟数据）
const actualTaskDetail = computed(() => {
  return Object.keys(props.taskDetail).length > 0 ? props.taskDetail : mockTaskDetail
})

// 日历状态
const currentDate = ref(dayjs())
const weekDays = ['一', '二', '三', '四', '五', '六', '日']
const calendarDays = ref([])

// 日历数据处理相关状态
const dateStatuses = ref(new Map()) // 存储每个日期的状态
const isDataReady = ref(false)

// 计算当前年月显示
const currentYearMonth = computed(() => {
  return currentDate.value.format('YYYY 年 MM 月')
})

// 模拟任务详情数据（仅在没有真实数据时使用）
const mockTaskDetail = {
  _id: 'mock-task-123',
  title: '每周完成 5 次户外跑步',
  content: '每周至少完成 5 次户外跑步，每次不少于 3 公里',
  startDate: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
  endDate: dayjs().add(1, 'month').format('YYYY-MM-DD'),
  repeatFlag: 'type=WEEKLY_N_TIMES;count=5',
  valType: 'sum',
  initVal: 0,
  curVal: 15,
  tgtVal: 30,
  unit: 'km',
  weight: 2,
  // 模拟进度记录
  recList: [
    {
      _id: 'rec-001',
      val: 3.5,
      recTime: dayjs().subtract(5, 'day').format('YYYY-MM-DD HH:mm:ss'),
      remark: '早晨跑步，感觉不错',
    },
    {
      _id: 'rec-002',
      val: 5,
      recTime: dayjs().subtract(3, 'day').format('YYYY-MM-DD HH:mm:ss'),
      remark: '下午跑步，稍微有点累',
    },
    {
      _id: 'rec-003',
      val: 3,
      recTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
      remark: '早晨跑步，天气很好',
    },
    {
      _id: 'rec-004',
      val: 3.5,
      recTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      remark: '今天跑步完成',
    },
  ],
}

// 处理任务数据，计算日期状态
const processTaskData = () => {
  // 如果任务详情不存在，直接返回
  if (!actualTaskDetail.value) {
    isDataReady.value = false
    return
  }

  isDataReady.value = false
  dateStatuses.value.clear()

  // 获取任务的基本信息
  const {
    startDate, // 开始日期
    endDate, // 结束日期
    repeatFlag, // 循环规则
    recList = [], // 进度记录
  } = actualTaskDetail.value

  // 如果没有开始日期，使用当前日期前一个月
  const effectiveStartDate = startDate || dayjs().subtract(1, 'month').format('YYYY-MM-DD')
  // 如果没有结束日期，默认展示到当前日期后三个月
  const effectiveEndDate = endDate || dayjs().add(3, 'month').format('YYYY-MM-DD')

  try {
    // 使用 rrrrule.ts 中的 getOccurrences 函数获取日期数组
    const { dates, groupedDates } = getOccurrences(
      repeatFlag || 'type=DAILY', // 循环规则，如果没有则默认为每天
      effectiveStartDate,
      effectiveEndDate
    )

    console.log('获取到的日期数组：', dates)
    console.log('按周期分组的日期数组：', groupedDates)

    // 处理进度记录，转换为按日期索引的 Map
    const recordsByDate = new Map()
    recList.forEach((record) => {
      const recordDate = dayjs(record.recTime).format('YYYY-MM-DD')
      if (!recordsByDate.has(recordDate)) {
        recordsByDate.set(recordDate, [])
      }
      recordsByDate.get(recordDate).push(record)
    })

    // 当前日期，用于判断未来日期
    const today = dayjs().format('YYYY-MM-DD')

    // 1. 首先标记所有在日期范围内的日期为"未完成"
    dates.forEach((date) => {
      // 跳过未来的日期
      if (dayjs(date).isAfter(dayjs(today))) {
        dateStatuses.value.set(date, 'future') // 未来日期
        return
      }
      dateStatuses.value.set(date, 'incomplete') // 未完成状态
    })

    // 2. 标记所有有进度记录的日期为"有执行记录"
    recordsByDate.forEach((records, date) => {
      // 只处理日期范围内的记录
      if (dateStatuses.value.has(date)) {
        const totalValue = records.reduce((sum, record) => sum + (record.val || 0), 0)
        if (totalValue > 0) {
          dateStatuses.value.set(date, 'has-record') // 有执行记录状态
        }
      }
    })

    // 3. 处理循环任务的周期分组
    if (repeatFlag && groupedDates.length > 0) {
      // 遍历每个周期
      groupedDates.forEach((periodDates) => {
        // 计算当前周期内有多少天有记录
        let daysWithRecords = 0
        periodDates.forEach((date) => {
          if (recordsByDate.has(date) && recordsByDate.get(date).some((record) => record.val > 0)) {
            daysWithRecords++
          }
        })

        // 如果有记录但未全部完成，将未记录的日期标记为"周期执行进度"
        if (daysWithRecords > 0 && daysWithRecords < periodDates.length) {
          periodDates.forEach((date) => {
            // 只处理未完成且不是未来日期的日期
            if (dateStatuses.value.get(date) === 'incomplete' && dayjs(date).isSameOrBefore(dayjs(today))) {
              dateStatuses.value.set(date, 'in-progress') // 周期执行进度状态
            }
          })
        }
      })
    }

    isDataReady.value = true
  } catch (error) {
    console.error('处理任务数据时出错：', error)
    isDataReady.value = false
  }

  // 刷新日历显示
  generateCalendarDays()
}

// 触摸事件处理 - 用于滑动切换月份
let touchStartX = 0
const handleTouchStart = (event) => {
  touchStartX = event.touches[0].clientX
}

const handleTouchMove = (event) => {
  // 可以添加一些视觉反馈
}

const handleTouchEnd = (event) => {
  const touchEndX = event.changedTouches[0].clientX
  const diff = touchEndX - touchStartX

  if (diff > 50) {
    // 向右滑动 -> 上个月
    prevMonth()
  } else if (diff < -50) {
    // 向左滑动 -> 下个月
    nextMonth()
  }
}

// 根据日期状态返回 CSS 类
const getCellClass = (day) => {
  if (!day) return 'empty-cell'

  const classes = []

  // 标记今天
  const today = dayjs().format('YYYY-MM-DD')
  if (day.fullDate === today) {
    classes.push('today')
  }

  // 添加日期状态类
  if (isDataReady.value && dateStatuses.value.has(day.fullDate)) {
    const status = dateStatuses.value.get(day.fullDate)
    classes.push(status)
  }

  return classes
}

// 月份切换时保持数据状态
const prevMonth = () => {
  currentDate.value = currentDate.value.subtract(1, 'month')
  generateCalendarDays()
}

const nextMonth = () => {
  currentDate.value = currentDate.value.add(1, 'month')
  generateCalendarDays()
}

// 生成日历数据
const generateCalendarDays = () => {
  const year = currentDate.value.year()
  const month = currentDate.value.month()

  // 获取当月第一天
  const firstDayOfMonth = dayjs(new Date(year, month, 1))
  // 获取当月有多少天
  const daysInMonth = firstDayOfMonth.daysInMonth()
  // 获取当月第一天是星期几 (0-6，0 为星期天)
  // 转换为星期一为一周第一天的习惯 (1-7，7 为星期天)
  const firstDayWeekday = firstDayOfMonth.day() || 7

  const calendar = []
  let week = []

  // 添加上个月的尾部日期
  for (let i = 1; i < firstDayWeekday; i++) {
    week.push(null) // 用null表示上个月的日期
  }

  // 添加当月的日期
  for (let day = 1; day <= daysInMonth; day++) {
    const date = dayjs(new Date(year, month, day))
    const fullDate = date.format('YYYY-MM-DD')

    week.push({
      date: day,
      fullDate: fullDate,
      isCurrentMonth: true,
      // 从dateStatuses中获取此日期的状态
      status: isDataReady.value && dateStatuses.value.has(fullDate) ? dateStatuses.value.get(fullDate) : 'normal',
    })

    // 如果到周日或者是月的最后一天，创建新的一周
    if (week.length === 7 || day === daysInMonth) {
      // 如果最后一周不足7天，补充下个月的日期
      while (week.length < 7) {
        week.push(null) // 用null表示下个月的日期
      }

      calendar.push(week)
      week = []
    }
  }

  calendarDays.value = calendar
}

// 初始化
onMounted(() => {
  processTaskData()
})

// 监听任务详情变化，重新处理数据
watch(
  () => props.taskDetail,
  (newValue) => {
    if (newValue) {
      processTaskData()
    }
  },
  { deep: true, immediate: true }
)
</script>

<style scoped>
.month-calendar-container {
  padding: 16px 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

/* 图例样式 */
.calendar-legend {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.legend-color.completed {
  background-color: var(--color-success); /* 绿色 - 有执行记录 */
}

.legend-color.cycle-progress {
  background-color: var(--color-warning); /* 黄色 - 周期执行进度 */
}

.legend-color.incomplete {
  background-color: var(--color-danger); /* 红色 - 未完成 */
}

/* 月份导航样式 */
.month-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.month-title {
  font-size: 18px;
  font-weight: 500;
}

.month-control {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: var(--color-gray-100);
  transition: all 0.2s;
}

.month-control:hover {
  background-color: var(--color-gray-200);
}

/* 日历样式 */
.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  font-weight: 500;
  margin-bottom: 8px;
}

.calendar-cell.header {
  padding: 8px;
  color: var(--color-gray-600);
}

.calendar-body {
  user-select: none;
  touch-action: pan-y;
}

.calendar-row {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  margin-bottom: 2px;
}

.calendar-cell {
  aspect-ratio: 1/1;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-100);
  transition: all 0.2s;
}

.calendar-cell.empty-cell {
  border: none;
  background-color: transparent;
}

.calendar-cell.today {
  border-color: var(--color-primary);
  background-color: rgba(var(--color-primary-rgb), 0.05);
}

.calendar-day-number {
  font-size: 14px;
  font-weight: 500;
}

/* 状态样式 - 将在数据逻辑任务中完善 */
.calendar-cell.has-record {
  background-color: var(--color-success-transparent); /* 绿色 - 有执行记录 */
}

.calendar-cell.in-progress {
  background-color: var(--color-warning-transparent); /* 黄色 - 周期执行进度 */
}

.calendar-cell.incomplete {
  background-color: var(--color-danger-transparent); /* 红色 - 未完成 */
}

.calendar-cell.future {
  background-color: rgba(158, 158, 158, 0.1); /* 灰色 - 未到达 */
  color: var(--color-gray-400);
}

@media (max-width: 768px) {
  .calendar-legend {
    flex-wrap: wrap;
    gap: 8px;
  }
}
</style>

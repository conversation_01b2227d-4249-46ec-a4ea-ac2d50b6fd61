<template>
  <navigator :render-link="false" url="/pages/home/<USER>" class="p-3 my-3 bg-white rr flex items-center">
    <view>
      <view class="mb-2">任务统计</view>
      <view class="flex">
        <view class="text-center mr-8">
          <view class="text-25 text-gray mb-1">待完成</view>
          <view>{{ unCompTaskList.length }}</view>
        </view>
        <view class="text-center">
          <view class="text-25 text-gray mb-1 text-green">已完成</view>
          <view>{{ compTaskList.length }}</view>
        </view>
      </view>
    </view>
    <view class="flex items-center ml-auto">
      <view class="mr-2 text-40">50%</view>
      <uni-icons type="right" color="" size="20" style="text-gray" />
    </view>
  </navigator>
  <!-- <view class="charts-box">
    <qiun-data-charts type="column" :chartData="chartData" />
  </view> -->
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps({
  compTaskList: {
    type: Array,
    default: [],
  },
  unCompTaskList: {
    type: Array,
    default: [],
  },
})

// 跳转到统计页面
const goStatisticsPage = () => {
  uni.navigateTo({
    url: '/pages/home/<USER>',
  })
}

const chartData = ref()
setTimeout(() => {
  let res = {
    categories: ['2016', '2017', '2018', '2019', '2020', '2021'],
    series: [
      {
        name: '目标值',
        data: [35, 36, 31, 33, 13, 34],
      },
      {
        name: '完成量',
        data: [18, 27, 21, 24, 6, 28],
      },
    ],
  }
  chartData.value = JSON.parse(JSON.stringify(res))
}, 500)
</script>
<style scoped lang="scss">
.charts-box {
  width: 100%;
  height: 30px;
}
</style>

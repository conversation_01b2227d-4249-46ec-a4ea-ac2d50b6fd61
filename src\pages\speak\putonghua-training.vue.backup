import request from '@/utils/request'

// 生成复述素材
export const generateRetellConetnt = async (story: string) => {
const params = {
message: '请根据以下故事生成复述素材：' + story,
messages: [],
model: 'deepseek-chat',
system: `# 角色
你是一位专业的内容创作者，擅长创作出有趣且富有讨论价值的内容，同时能精准提炼其中的关键信息。

## 技能
### 技能 1: 生成素材和关键词
1. 生成一段 200 - 300 字的素材内容，内容要求：
- 主题要有趣且具有讨论价值；
- 内容要逻辑清晰；
- 难度适中，适合口头表达。
2. 从这段内容中提取 5 - 8 个关键词，这些关键词应该：
- 能够概括内容的主要脉络；
- 按照内容出现的顺序排列；
- 包含核心人物、事件、地点或概念。
3. 为生成的素材内容拟定一个 title。
4. 以如下格式输出：
{
"title": "生成的标题",
"content": "素材内容",
"keyWord": "关键词 1，关键词 2"
}

## 限制：
- 输出内容必须严格按照上述格式进行组织，不能偏离框架要求。`,
}
const res = (await request.post('/ai/speak', params)) as { content: string }
let content = res.content

// 移除可能包裹 JSON 的```json 和```标记
if (content.includes('```json')) {
content = content.replace(/```json|```/g, '').trim()
}

return JSON.parse(content)
}

// 点评复述
export const evaluateRetell = async (retell: string) => {
const params = {
message: '请根据以下复述点评：' + retell,
messages: [],
model: 'deepseek-chat',
system: `# 角色
你是一位专业的表达能力分析师，能够精准分析用户表达的优劣并给出针对性建议。

## 技能
### 技能 1: 表达分析
1. 从以下几个维度分析用户的表达：
- 内容完整性（原始内容的重要信息是否都被覆盖）
- 表达流畅度（语句是否流畅，过渡是否自然）
- 语法和词汇（语法是否正确，词汇选择是否恰当）
- 理解深度（对内容的理解是否深入，有无独到见解）

2. 以如下格式输出：
{
"内容完整性": "对内容完整性的评价说明",
"表达流畅度": "对表达流畅度的评价说明",
"语法和词汇": "对语法和词汇的评价说明",
"理解深度": "对理解深度的评价说明",
"总体评价": "总体评价和简短建议"
}

## 限制：
- 输出内容必须严格按照上述 JSON 格式进行组织，不能偏离框架要求。
- 每个维度的评价说明应简明扼要，不超过 50 字。
- 仅围绕用户提供的复述内容进行分析，不回答与表达能力分析无关的话题。`,
}
const res = (await request.post('/ai/speak', params)) as { content: string }
let content = res.content

// 移除可能包裹 JSON 的```json 和```标记
if (content.includes('```json')) {
content = content.replace(/```json|```/g, '').trim()
}

return JSON.parse(content)
}

// 点评关键词故事
export const evaluateKeywordStory = async (story: string, keywords: string[]) => {
const params = {
message: `请根据以下关键词和故事进行点评：\n\n关键词：${keywords.join('，')}\n\n故事：${story}`,
messages: [],
model: 'deepseek-chat',
system: `# 角色
你是一位专业的创意写作评论家，擅长分析故事的创意、结构和表达，并能够提供有建设性的建议和创意示例。

## 技能
### 技能 1: 故事分析与创作
1. 从以下几个维度分析用户创作的故事：
- 关键词使用（是否自然、巧妙地融入了所有给定的关键词）
- 故事创意（故事是否新颖、有趣，情节是否吸引人）
- 表达流畅度（语句是否通顺，叙事节奏是否得当）
- 语法和词汇（语法是否正确，词汇运用是否丰富、生动，并给出 3-5 个可以替换的更优词汇或语法结构建议）

2. 创作两个额外的故事：
- AI 示例故事：基于给定关键词创作一个简短的示例故事（150 字以内）
- 故事改良版：基于用户的原始故事进行改良，保持原意但提升表达和创意（150 字以内）

3. 以如下格式输出：
{
"关键词使用": "对关键词使用情况的评价说明",
"故事创意": "对故事创意的评价说明",
"表达流畅度": "对表达流畅度的评价说明",
"语法和词汇": "对语法和词汇的评价说明，包含 3-5 个具体的词汇或语法改进建议",
"AI 示例故事": "基于关键词创作的示例故事",
"故事改良版": "对用户故事的改良版本",
"总体评价": "对整个故事的总体评价和鼓励"
}

## 限制：
- 输出内容必须严格按照上述 JSON 格式进行组织，不能偏离框架要求。
- 每个评价维度的说明应简明扼要，不超过 60 字。
- 示例故事和改良版故事各不超过 150 字。
- 保持积极、鼓励的口吻。
- 故事改良版应保留用户故事的核心内容和风格，但提升其表达和创意。`,
}
const res = (await request.post('/ai/speak', params)) as { content: string }
let content = res.content

// 移除可能包裹 JSON 的```json 和```标记
if (content.includes('```json')) {
content = content.replace(/```json|```/g, '').trim()
}

return JSON.parse(content)
}

// 根据单字生成词语和短句
export const generateWordsAndPhrasesFromChar = async (char: string) => {
const params = {
message: `请根据以下单字生成词语和短句：${char}`,
messages: [],
model: 'deepseek-chat',
system: `# 角色
你是一位专业的语言学家和内容创作者，擅长基于单个汉字进行丰富的语言扩展，并提供标准拼音。

## 技能
### 技能 1: 词语和短句生成
1. 根据用户提供的一个单字，围绕这个字生成相关内容。
2. 生成 5 个包含该字的常见词语或成语。
3. 根据生成的词语跟成语，各生成一个句子（即每个词语/成语对应生成一个句子）。
4. 生成一段包含所有生成词语和成语的连贯话语。
5. 为每一个词语、成语、句子、段落都附上带声调的拼音。
6. 以如下格式输出：
{
"wordsAndPhrases": [{ "text": "词语或成语 1", "pinyin": "cí yǔ huò chéng yǔ yī" }, { "text": "词语或成语 2", "pinyin": "cí yǔ huò
chéng yǔ èr" }],
"sentences": [{ "text": "句子 1", "pinyin": "jù zi yī" }, { "text": "句子 2", "pinyin": "jù zi èr" }],
"paragraph": { "text": "包含所有词语和成语的连贯段落", "pinyin": "bāo hán suǒ yǒu cí yǔ hé chéng yǔ de lián guàn duàn luò" }
}

## 限制：
- 输出内容必须严格按照上述 JSON 格式进行组织。
- 所有生成的内容都必须包含用户提供的那个单字。
- 内容应通俗易懂，适合语言学习者。
- 拼音必须是标准的，带声调的。
- 句子数量必须与词语/成语数量一致（5 个）。
- 段落必须自然地包含前面生成的所有词汇。`,
}
const res = (await request.post('/ai/speak', params)) as { content: string }
let content = res.content

// 移除可能包裹 JSON 的```json 和```标记
if (content.includes('```json')) {
content = content.replace(/```json|```/g, '').trim()
}

return JSON.parse(content)
}

      :async-close="true"
    >
      <view class="modal-content">
        <u-input v-model="newTagInput" placeholder="请输入单个汉字" maxlength="1" fontSize="16px" clearable></u-input>
        <view class="category-selector">
          <view class="category-title">分类</view>
          <view class="category-tags">
            <view
              v-for="category in categories"
              :key="category"
              class="category-tag"
              :class="{ active: selectedCategory === category }"
              @click="selectedCategory = category"
            >
              {{ category }}
            </view>
          </view>
        </view>
      </view>
    </u-modal>
  </view>

  <!-- 训练记录弹窗 -->
  <uni-popup ref="recordListPopupRef" type="bottom" @change="handleRecordListPopupChange" style="z-index: 10000">
    <view class="record-popup-content">
      <view class="record-popup-header">
        <text class="record-popup-title">训练记录</text>
        <view class="header-actions">
          <text class="clear-all-btn" @click="handleClearAllRecords">全部清空</text>
          <view class="record-popup-close-icon" @click="closeRecordListPopup">
            <uni-icons type="closeempty" size="20"></uni-icons>
          </view>
        </view>
      </view>
      <scroll-view scroll-y class="record-popup-scroll-view">
        <z-loading v-if="isLoadingRecords" text="正在加载..." :font-size="32" padding="40rpx 0" />
        <view v-else-if="!parsedRecords.length" class="record-popup-empty-state">
          <text>暂无记录</text>
        </view>
        <view v-else class="record-popup-list">
          <view
            v-for="record in parsedRecords"
            :key="record._id"
            :class="['record-popup-item', { active: record.character === currentWord }]"
            @click="handleSelectRecord(record)"
          >
            <view class="record-popup-item-main">
              <view class="record-popup-item-header">
                <text class="record-popup-character">{{ record.character }}</text>
                <text class="record-popup-date">{{ formatDateForRecord(record.createTime) }}</text>
              </view>
            </view>
            <view class="record-popup-item-delete" @click.stop="handleDeleteRecord(record._id)">
              <i class="fas fa-trash-alt"></i>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick, onUnmounted } from 'vue'
import ZMessageInput from '@/components/z-message-input/z-message-input.vue'
import ZAudioPlayer from '@/components/z-audio-player/z-audio-player.vue'
import ZPageNavbar from '@/components/z-page-navbar.vue'
import ZLoading from '@/components/z-loading/index.vue'
import { generateWordsAndPhrasesFromChar } from '@/api/speak'
import { addChatRecordApi, getChatRecordListApi, updateChatRecordApi, delChatRecordApi } from '@/api/chatRecord'

console.log('[DEBUG] 普通话训练页面脚本开始加载：', new Date().toISOString())

const getHistory = async () => {
  try {
    const allRecords = await getChatRecordListApi()
    const characterRecords = allRecords
      .filter((r) => {
        try {
          if (r.content && typeof r.content === 'string' && r.content.trim().startsWith('{')) {
            const content = JSON.parse(r.content)
            return content.type === 'putonghuaCharacter'
          }
          return false
        } catch {
          return false
        }
      })
      .map((r) => {
        try {
          const content = JSON.parse(r.content)
          return {
            character: r.title,
            category: content.category || '未分类',
          }
        } catch {
          return { character: r.title, category: '未分类' }
        }
      })

    // 去重
    const uniqueRecords = characterRecords.reduce((acc, current) => {
      if (!acc.some((item) => item.character === current.character)) {
        acc.push(current)
      }
      return acc
    }, [])

    // 按分类分组
    const grouped = uniqueRecords.reduce((acc, record) => {
      const { category, character } = record
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push(character)
      return acc
    }, {})

    // 对每个分类下的汉字排序
    for (const category in grouped) {
      grouped[category].sort((a, b) => a.localeCompare(b, 'zh-Hans-CN'))
    }

    return grouped
  } catch (error) {
    console.error('Failed to fetch history tags', error)
    uni.showToast({ title: '加载字库失败', icon: 'none' })
    return {}
  }
}

const addHistory = async (word, category) => {
  const allWords = Object.values(wordBank.value).flat()
  if (allWords.includes(word)) {
    return { success: false, message: '该字已在您的字库中' }
  }
  try {
    await addChatRecordApi({
      title: word,
      content: JSON.stringify({ type: 'putonghuaCharacter', category }),
    })
    return { success: true }
  } catch (error) {
    console.error('Failed to add history tag:', error)
    return { success: false, message: '添加失败，请重试' }
  }
}

const isWordBankCollapsed = ref(false)

console.log('[DEBUG] 开始初始化响应式数据')

const showPinyin = ref(true)
console.log('[DEBUG] showPinyin 初始化完成：', showPinyin.value)

const wordBank = ref({})
console.log('[DEBUG] wordBank 初始化完成：', wordBank.value)

const currentWord = ref('')
console.log('[DEBUG] currentWord 初始化完成：', currentWord.value)

const contentLoading = ref(false)
console.log('[DEBUG] contentLoading 初始化完成：', contentLoading.value)

const mockAudioUrl = ref('https://downsc.chinaz.net/Files/DownLoad/sound1/201906/11582.mp3') // 模拟音频

const generatedContent = ref({
  wordsAndPhrases: [],
  sentences: [],
  paragraph: null,
})
console.log('[DEBUG] generatedContent 初始化完成：', JSON.stringify(generatedContent.value))

// 添加日志监控 generatedContent 的变化
watch(
  generatedContent,
  (newVal, oldVal) => {
    console.log('[DEBUG] generatedContent 变化：', {
      newVal: JSON.stringify(newVal),
      oldVal: JSON.stringify(oldVal),
      timestamp: new Date().toISOString(),
    })
  },
  { deep: true }
)

// 添加单字弹窗相关
const showAddModal = ref(false)
const newTagInput = ref('')
const categories = ref(['平舌音', '翘舌音', '前鼻音', '后鼻音'])
const selectedCategory = ref('')

// 训练记录相关
const recordListPopupRef = ref(null)
const showRecordListPopup = ref(false)
const records = ref([])
const isLoadingRecords = ref(false)

// 录音相关状态
const inputMessage = ref('')
const currentAudioURL = ref('')
const isPlaying = ref(false)
const audioPlayerRef = ref(null)
const currentRecordId = ref(null) // 当前录音记录的 ID
const isTranscribing = ref(false)
const transcriptionText = ref('')
const currentAudioData = ref(null)
const currentSentences = ref(null)

const openAddModal = () => {
  console.log('ccccc')
  newTagInput.value = ''
  selectedCategory.value = ''
  showAddModal.value = true
}

const closeAddModal = () => {
  showAddModal.value = false
}

const addHistoryTag = async () => {
  if (!newTagInput.value || !/^[\u4e00-\u9fa5]$/.test(newTagInput.value)) {
    uni.showToast({ title: '请输入单个汉字', icon: 'none' })
    return
  }
  if (!selectedCategory.value) {
    uni.showToast({ title: '请选择一个分类', icon: 'none' })
    return
  }

  try {
    const result = await addHistory(newTagInput.value, selectedCategory.value)
    if (result.success) {
      const newWord = newTagInput.value
      const category = selectedCategory.value

      if (!wordBank.value[category]) {
        wordBank.value[category] = []
      }
      wordBank.value[category].push(newWord)
      wordBank.value[category].sort((a, b) => a.localeCompare(b, 'zh-Hans-CN'))

      handleTagClick(newWord) // 添加成功后自动选中
    } else {
      uni.showToast({ title: result.message || '添加失败，字已存在', icon: 'none' })
    }
  } finally {
    closeAddModal()
  }
}

const handleTagClick = async (tag) => {
  console.log('[DEBUG] handleTagClick 开始：', { tag, timestamp: new Date().toISOString() })

  currentWord.value = tag
  isWordBankCollapsed.value = true // 选择后自动收起
  uni.setStorageSync('lastSelectedPinyinChar', tag)
  contentLoading.value = true

  console.log('[DEBUG] 清空 generatedContent 前：', JSON.stringify(generatedContent.value))
  generatedContent.value = { wordsAndPhrases: [], sentences: [], paragraph: null } // Clear old content
  console.log('[DEBUG] 清空 generatedContent 后：', JSON.stringify(generatedContent.value))

  currentAudioURL.value = '' // 清空之前的录音
  transcriptionText.value = '' // 清空之前的转写
  currentSentences.value = null

  try {
    console.log('[DEBUG] 开始调用 generateWordsAndPhrasesFromChar:', tag)
    const result = await generateWordsAndPhrasesFromChar(tag)
    console.log('[DEBUG] generateWordsAndPhrasesFromChar 返回结果：', JSON.stringify(result))

    console.log('[DEBUG] 设置 wordsAndPhrases 前：', JSON.stringify(generatedContent.value))
    generatedContent.value.wordsAndPhrases = result.wordsAndPhrases || []
    console.log('[DEBUG] 设置 wordsAndPhrases 后：', JSON.stringify(generatedContent.value))

    console.log('[DEBUG] 设置 sentences 前：', JSON.stringify(generatedContent.value))
    generatedContent.value.sentences = result.sentences || []
    console.log('[DEBUG] 设置 sentences 后：', JSON.stringify(generatedContent.value))

    console.log('[DEBUG] 设置 paragraph 前：', JSON.stringify(generatedContent.value))
    generatedContent.value.paragraph = result.paragraph || null
    console.log('[DEBUG] 设置 paragraph 后：', JSON.stringify(generatedContent.value))

    // 查找是否有该字符的录音记录
    console.log('[DEBUG] 开始加载录音记录：', tag)
    await loadExistingRecording(tag)

    console.log('[DEBUG] 开始保存记录')
    await saveRecord()
    console.log('[DEBUG] 保存记录完成')
  } catch (error) {
    console.error('[ERROR] handleTagClick 失败：', error)
    uni.showToast({ title: '内容生成失败', icon: 'none' })
  } finally {
    contentLoading.value = false
    console.log('[DEBUG] handleTagClick 完成：', { tag, timestamp: new Date().toISOString() })
  }
}

// 加载已存在的录音记录
const loadExistingRecording = async (character) => {
  try {
    // 查找该字符的录音记录
    const trainingRecord = parsedRecords.value.find(
      (r) => r.character === character && r.recordType === 'putonghuaTraining'
    )

    if (trainingRecord && trainingRecord.audioURL) {
      currentAudioURL.value = trainingRecord.audioURL
      transcriptionText.value = trainingRecord.transcript || ''
      currentSentences.value = trainingRecord.sentences || null
      console.log('已加载录音记录：', trainingRecord.audioURL)
    } else {
      currentAudioURL.value = ''
      transcriptionText.value = ''
      currentSentences.value = null
    }
  } catch (error) {
    console.error('加载录音记录失败：', error)
  }
}

onMounted(async () => {
  console.log('[DEBUG] onMounted 开始：', new Date().toISOString())

  try {
    // 先获取训练记录和字库
    console.log('[DEBUG] 开始获取训练记录')
    await fetchRecords()
    console.log('[DEBUG] 训练记录获取完成，记录数量：', records.value.length)

    console.log('[DEBUG] 开始获取字库历史')
    wordBank.value = await getHistory()
    console.log('[DEBUG] 字库历史获取完成，标签数量：', Object.keys(wordBank.value).length)

    // 优先选择最新的训练记录
    if (parsedRecords.value.length > 0) {
      console.log('[DEBUG] 找到训练记录，选择最新记录')
      const latestRecord = parsedRecords.value[0] // 已按时间倒序排列
      console.log('[DEBUG] 最新记录：', JSON.stringify(latestRecord))
      handleSelectRecord(latestRecord)
      return
    }

    // 如果没有训练记录，按原逻辑处理字库
    if (Object.keys(wordBank.value).length > 0) {
      console.log('[DEBUG] 没有训练记录，使用字库逻辑')
      const lastSelectedWord = uni.getStorageSync('lastSelectedPinyinChar')
      console.log('[DEBUG] 上次选择的字符：', lastSelectedWord)

      const allWords = Object.values(wordBank.value).flat()
      if (lastSelectedWord && allWords.includes(lastSelectedWord)) {
        console.log('[DEBUG] 使用上次选择的字符：', lastSelectedWord)
        handleTagClick(lastSelectedWord)
      } else {
        const firstCategory = Object.keys(wordBank.value)[0]
        if (wordBank.value[firstCategory]?.length > 0) {
          console.log('[DEBUG] 使用第一个字符：', wordBank.value[firstCategory][0])
          handleTagClick(wordBank.value[firstCategory][0])
        }
      }
    } else {
      console.log('[DEBUG] 没有字库数据')
    }
  } catch (error) {
    console.error('[ERROR] onMounted 失败：', error)
  }

  console.log('[DEBUG] onMounted 完成：', new Date().toISOString())
})

// --- 训练记录逻辑 ---

const fetchRecords = async () => {
  isLoadingRecords.value = true
  try {
    const allRecords = await getChatRecordListApi()
    records.value = allRecords
      .filter((r) => {
        try {
          if (typeof r.content === 'string' && r.content.trim().startsWith('{')) {
            const content = JSON.parse(r.content)
            // 只保留 putonghuaTraining 记录
            return content.type === 'putonghuaTraining'
          }
          return false
        } catch {
          return false
        }
      })
      .sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
  } catch (error) {
    console.error('Failed to fetch records', error)
    records.value = []
  } finally {
    isLoadingRecords.value = false
  }
}

watch(showRecordListPopup, (newValue) => {
  if (newValue) {
    fetchRecords()
    recordListPopupRef.value?.open()
  } else {
    recordListPopupRef.value?.close()
  }
})

const handleRecordListPopupChange = (e) => {
  if (!e.show) {
    showRecordListPopup.value = false
  }
}

const closeRecordListPopup = () => {
  showRecordListPopup.value = false
}

const parsedRecords = computed(() => {
  console.log('[DEBUG] parsedRecords 计算开始，原始记录数量：', records.value?.length || 0)

  if (!records.value || records.value.length === 0) {
    console.log('[DEBUG] parsedRecords: 没有原始记录')
    return []
  }

  const parsed = records.value
    .map((record, index) => {
      try {
        console.log(`[DEBUG] 解析记录 ${index}:`, JSON.stringify(record))
        const content = JSON.parse(record.content)
        console.log(`[DEBUG] 解析内容 ${index}:`, JSON.stringify(content))

        const result = {
          _id: record._id,
          createTime: record.createTime,
          character: content.character,
          generatedContent: content.content,
          audioURL: content.audioURL || '', // 添加录音 URL 支持
          recordType: content.type, // 记录类型
          transcript: content.transcript || '', // 解析转写文本
          sentences: content.sentences || null,
        }

        console.log(`[DEBUG] 解析结果 ${index}:`, JSON.stringify(result))
        return result
      } catch (e) {
        console.error(`[ERROR] 解析记录 ${index} 失败:`, e, record)
        return null
      }
    })
    .filter(Boolean)

  console.log('[DEBUG] parsedRecords 计算完成，解析记录数量：', parsed.length)
  return parsed
})

const formatDateForRecord = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(
    2,
    '0'
  )} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

const handleSelectRecord = async (selectedRecord) => {
  console.log('[DEBUG] handleSelectRecord 开始：', JSON.stringify(selectedRecord))

  currentWord.value = selectedRecord.character
  isWordBankCollapsed.value = true // 从记录选择后也收起字库
  console.log('[DEBUG] 设置 currentWord:', selectedRecord.character)

  console.log('[DEBUG] 设置 generatedContent 前：', JSON.stringify(generatedContent.value))
  generatedContent.value = selectedRecord.generatedContent
  console.log('[DEBUG] 设置 generatedContent 后：', JSON.stringify(generatedContent.value))

  // 恢复录音数据（如果存在）
  currentAudioURL.value = selectedRecord.audioURL || ''
  transcriptionText.value = selectedRecord.transcript || ''
  currentSentences.value = selectedRecord.sentences || null

  // 更新本地存储，保持与字库选择的一致性
  uni.setStorageSync('lastSelectedPinyinChar', selectedRecord.character)

  // 刷新字库以确保新添加的字可见
  wordBank.value = await getHistory()

  closeRecordListPopup()
  console.log('[DEBUG] handleSelectRecord 完成')
}

const handleDeleteRecord = async (recordId) => {
  try {
    await delChatRecordApi(recordId)
    uni.showToast({ title: '删除成功', icon: 'none' })
    records.value = records.value.filter((r) => r._id !== recordId)
    if (parsedRecords.value.length === 0) {
      handleReset()
    }
  } catch (error) {
    console.error('Failed to delete record:', error)
    uni.showToast({ title: '删除失败', icon: 'none' })
  }
}

const handleClearAllRecords = async () => {
  if (parsedRecords.value.length === 0) return
  isLoadingRecords.value = true
  try {
    const recordIdsToDelete = parsedRecords.value.map((r) => r._id)
    await Promise.all(recordIdsToDelete.map((id) => delChatRecordApi(id)))
    uni.showToast({ title: '已清空所有记录', icon: 'none' })
    records.value = []
    handleReset()
  } catch (error) {
    console.error('Failed to clear records:', error)
    uni.showToast({ title: '清空失败', icon: 'none' })
  } finally {
    isLoadingRecords.value = false
  }
}

const saveRecord = async () => {
  if (!currentWord.value) return

  const recordPayload = {
    title: currentWord.value,
    content: JSON.stringify({
      type: 'putonghuaTraining',
      character: currentWord.value,
      content: generatedContent.value,
      // 当有新录音时，使用新的数据，否则保留旧的
      audioURL: currentAudioData.value?.tempFileURL || currentAudioURL.value,
      transcript: transcriptionText.value,
      sentences: currentSentences.value,
      fileID: currentAudioData.value?.fileID,
      duration: currentAudioData.value?.duration,
    }),
  }

  try {
    const existingRecord = parsedRecords.value.find(
      (r) => r.character === currentWord.value && r.recordType === 'putonghuaTraining'
    )
    if (existingRecord) {
      await updateChatRecordApi(existingRecord._id, recordPayload)
      currentRecordId.value = existingRecord._id
    } else {
      const newId = await addChatRecordApi(recordPayload)
      currentRecordId.value = newId
    }
    await fetchRecords() // Refresh the list after saving
  } catch (error) {
    console.error('Failed to save record:', error)
  }
}

// 录音相关方法
const handleAudioSubmit = async (audioData) => {
  console.log('收到录音数据', audioData)

  if (audioData && audioData.tempFileURL) {
    currentAudioURL.value = audioData.tempFileURL
    currentAudioData.value = audioData // 暂存音频数据，用于转写后保存

    // 使用 nextTick 确保 DOM 更新后，player 组件的 ref 可用
    nextTick(() => {
      if (audioPlayerRef.value) {
        // 显式调用子组件的转写方法
        audioPlayerRef.value.transcribe()
      }
    })
  }
}

/*
const saveRecordingData = async (audioData, transcript = '') => {
  if (!currentWord.value) return

  const recordPayload = {
    title: `${currentWord.value}_录音练习`,
    content: JSON.stringify({
      type: 'putonghuaRecording',
      character: currentWord.value,
      audioURL: audioData.tempFileURL,
      fileID: audioData.fileID,
      duration: audioData.duration,
      transcript: transcript, // 保存转写结果
      timestamp: new Date().toISOString(),
    }),
  }

  try {
    // 查找是否已存在该字符的录音记录
    const existingRecordingRecord = parsedRecords.value.find((r) => {
      try {
        const content = JSON.parse(r.content || '{}')
        return content.type === 'putonghuaRecording' && content.character === currentWord.value
      } catch {
        return false
      }
    })

    if (existingRecordingRecord) {
      // 更新现有录音记录（单条记录策略）
      await updateChatRecordApi(existingRecordingRecord._id, recordPayload)
      console.log('录音记录已更新：', existingRecordingRecord._id)
    } else {
      // 创建新的录音记录
      const newId = await addChatRecordApi(recordPayload)
      console.log('新录音记录已保存：', newId)
    }

    await fetchRecords() // 刷新记录列表
  } catch (error) {
    console.error('保存录音记录失败：', error)
    uni.showToast({
      title: '保存录音失败',
      icon: 'none',
    })
  }
}
*/

const handleUploadProgress = (progress) => {
  console.log('上传进度', progress)
}

const handleRecordError = (error) => {
  console.error('录音错误', error)
  uni.showToast({
    title: '录音出错，请重试',
    icon: 'none',
    duration: 3000,
  })
}

// 转写事件处理
const handleTranscriptionStart = () => {
  console.log('开始转写')
  isTranscribing.value = true
  transcriptionText.value = ''
}

const handleTranscriptionEnd = async (result) => {
  console.log('转写结束', result)
  isTranscribing.value = false

  if (result.success && result.transcript) {
    transcriptionText.value = result.transcript
    currentSentences.value = result.sentences
    uni.showToast({
      title: '识别成功',
      icon: 'success',
    })
    // 保存包含转写结果的录音记录
    if (currentAudioData.value) {
      await saveRecord()
      currentAudioData.value = null // 清理临时数据
    }
  } else {
    uni.showToast({
      title: '识别失败，请重试',
      icon: 'none',
    })
    // 即使转写失败，也保存录音文件
    if (currentAudioData.value) {
      transcriptionText.value = '' // 确保转写文本为空
      currentSentences.value = null
      await saveRecord()
      currentAudioData.value = null // 清理临时数据
    }
  }
}

// 音频播放器事件处理
const onAudioPlay = () => {
  isPlaying.value = true
}

const onAudioPause = () => {
  isPlaying.value = false
}

const onAudioEnded = () => {
  isPlaying.value = false
}

const handleReset = () => {
  currentWord.value = ''
  generatedContent.value = { wordsAndPhrases: [], sentences: [], paragraph: null }
  wordBank.value = {}
  records.value = []

  // 清理录音相关状态
  currentAudioURL.value = ''
  inputMessage.value = ''
  currentRecordId.value = null
  transcriptionText.value = ''
  currentSentences.value = null
  isTranscribing.value = false
  currentAudioData.value = null

  // 停止任何正在播放的音频
  if (audioPlayerRef.value) {
    audioPlayerRef.value.pause()
  }
  isPlaying.value = false
}

// 组件卸载时清理资源
onUnmounted(() => {
  if (currentAudioURL.value && currentAudioURL.value.startsWith('blob:')) {
    URL.revokeObjectURL(currentAudioURL.value)
  }

  // 停止音频播放
  if (audioPlayerRef.value) {
    audioPlayerRef.value.pause()
  }
})

// 文字和拼音对齐处理函数
const splitTextIntoCharacters = (text) => {
  console.log('[DEBUG] splitTextIntoCharacters 输入：', text)
  if (!text) {
    console.log('[DEBUG] splitTextIntoCharacters: 文本为空')
    return []
  }
  // 将文本分割成单个字符，包括汉字、标点符号等
  const result = text.split('')
  console.log('[DEBUG] splitTextIntoCharacters 结果：', result)
  return result
}

const getPinyinForCharacter = (pinyinString, charIndex) => {
  console.log('[DEBUG] getPinyinForCharacter 输入：', { pinyinString, charIndex })
  if (!pinyinString) {
    console.log('[DEBUG] getPinyinForCharacter: 拼音字符串为空')
    return ''
  }

  // 将拼音字符串按空格分割
  const pinyinArray = pinyinString.trim().split(/\s+/)
  console.log('[DEBUG] getPinyinForCharacter 拼音数组：', pinyinArray)

  // 返回对应位置的拼音，如果超出范围则返回空字符串
  const result = pinyinArray[charIndex] || ''
  console.log('[DEBUG] getPinyinForCharacter 结果：', result)
  return result
}
</script>

<style lang="scss" scoped>
.training-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  padding-right: 5px;

  .action-btn {
    font-size: 18px;
    color: var(--color-text-secondary);
    cursor: pointer;

    &:hover {
      color: var(--color-primary);
    }
  }
}

.navbar-pinyin-toggle {
  font-size: 14px;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
  padding: 4px 8px;
  border-radius: var(--rounded-md);
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: var(--color-primary-light-9);
  }
}

.word-bank-section {
  background-color: #fff;
  margin: 15px;
  padding: 15px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  .word-bank-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
  }

  .word-bank-title-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .current-word-display {
    background-color: var(--color-primary-light-9);
    color: var(--color-primary);
    padding: 3px 8px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
  }

  .collapse-icon {
    color: #909399;
    font-size: 14px;
  }

  &.collapsed .word-bank-content {
    display: none;
  }

  &:not(.collapsed) .word-bank-header {
    margin-bottom: 15px;
  }

  .word-bank-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 0;
  }
}

.word-bank-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}

.category-group {
  display: inline-flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
  background-color: #f8f9fa;
  padding: 5px 10px;
  border-radius: 8px;
}

.category-name-tag {
  background-color: #e9ecef;
  color: #495057;
  border-radius: 6px;
  padding: 8px 10px;
  font-size: 14px;
  font-weight: 500;
}

.history-tag {
  background-color: var(--color-primary-light-9);
  color: var(--color-primary);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  &.active {
    background-color: var(--color-primary);
    color: #fff;
    font-weight: bold;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(41, 121, 255, 0.4);
  }
}

.add-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 8px;
  background-color: #f5f7fa;
  border: 1px dashed #dcdfe6;
  cursor: pointer;
  transition: all 0.2s;

  i {
    font-size: 16px;
    color: #909399;
  }

  &:hover {
    border-color: var(--color-primary);
    background-color: var(--color-primary-light-9);

    i {
      color: var(--color-primary);
    }
  }
}

.content-section {
  flex: 1;
  overflow-y: auto;
  padding: 0 15px 100px 15px; // 增加底部内边距为录音输入区域留空间
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 200px;
}

.content-block {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  width: 100%;

  .content-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 12px;

    i {
      color: var(--color-primary);
      margin-right: 8px;
    }
  }

  // 文字和拼音对齐样式
  .text-pinyin-container {
    display: inline-block;
    width: 100%;
  }

  .character-row {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    gap: 2px;
  }

  .character-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    min-width: 24px;
    margin: 0 1px;
    position: relative;
  }

  .pinyin-text {
    font-size: 10px;
    color: #888;
    line-height: 1.2;
    text-align: center;
    white-space: nowrap;
    font-weight: normal;
    margin-bottom: 2px;
    min-height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .chinese-text {
    font-size: 18px;
    line-height: 1.4;
    color: #333;
    text-align: center;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 25px;
  }
}

.phrase-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
}

.phrase-item,
.sentence-item {
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-block;
  margin: 4px;

  &:hover {
    background-color: #e9ecf0;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.sentence-item {
  display: block;
  width: 100%;
  margin: 8px 0;

  .character-row {
    justify-content: flex-start;
  }
}

// 录音练习区域样式
.recording-practice-block {
  margin-bottom: 20px;
  border: 2px solid var(--color-primary-light-8);
  background: linear-gradient(135deg, #f8fbff 0%, #f0f7ff 100%);

  .content-title {
    color: var(--color-primary);
    font-weight: 600;

    i {
      color: var(--color-primary);
    }
  }
}

.audio-player-section {
  margin-top: 15px;
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid var(--color-primary-light-7);
}

.practice-audio-player {
  width: 100%;
}

.recording-hint {
  text-align: center;
  padding: 20px;
  color: var(--color-text-secondary);
  font-size: 15px;
  font-style: italic;

  text {
    color: var(--color-primary);
    font-weight: 500;
  }
}

// 录音输入区域样式
.recording-input-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--color-white, #fff);
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.06);
  z-index: 100;
  border-top: 1px solid var(--color-border, #e9e9e9);
}
.audio-player-placeholder {
  padding: 0 15px 10px;
}

.recording-section {
  border-top: 1px solid #e0e0e0;
}

.modal-content {
  padding: 15px;
}

.category-selector {
  margin-top: 20px;
}
.category-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 12px;
}
.category-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
.category-tag {
  background-color: #f3f4f6;
  color: #606266;
  padding: 6px 15px;
  border-radius: 20px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}
.category-tag:hover {
  opacity: 0.8;
}
.category-tag.active {
  background-color: var(--color-primary);
  color: white;
  font-weight: 500;
}

/* 训练记录弹窗样式 */
.record-popup-content {
  background-color: white;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
.record-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 1;
}
.record-popup-title {
  font-size: 16px;
  font-weight: bold;
}
.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}
.clear-all-btn {
  font-size: 14px;
  color: var(--color-danger, #f56c6c);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}
.clear-all-btn:hover {
  background-color: var(--color-danger-light-2, #fde2e2);
}
.record-popup-close-icon {
  cursor: pointer;
}
.record-popup-scroll-view {
  height: 50vh;
}
.record-popup-empty-state {
  text-align: center;
  padding: 40px;
  color: #999;
}
.record-popup-list {
  padding: 0 15px;
}
.record-popup-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 5px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}
.record-popup-item.active {
  background-color: var(--color-primary-light-9);
}
.record-popup-item:hover {
  background-color: #f5f5f5;
}
.record-popup-item:last-child {
  border-bottom: none;
}
.record-popup-item-main {
  flex: 1;
  min-width: 0;
}
.record-popup-item-delete {
  padding: 8px;
  color: #999;
  border-radius: 50%;
  transition: all 0.2s ease;
}
.record-popup-item-delete:hover {
  background-color: #fde2e2;
  color: #f56c6c;
}
.record-popup-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.record-popup-date {
  font-size: 12px;
  color: #999;
}
.record-popup-character {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}
</style>

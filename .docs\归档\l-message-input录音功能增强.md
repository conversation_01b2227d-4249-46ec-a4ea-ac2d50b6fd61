# l-message-input 录音功能增强需求

## 背景
当前 l-message-input 组件仅具备基础录音功能，录音完成后的上传、转文字等功能都需要在父组件中实现，这导致组件复用性较差，每次使用都需要在父组件中重复实现相同的逻辑。为提高组件复用性和功能完整性，需要将录音上传和转文字功能集成到组件内部。

## 需求

### 功能需求
1. 将录音上传功能集成到组件内部
2. 将录音转文字功能集成到组件内部
3. 支持通过 props 控制是否启用转文字功能
4. 录音完成后自动上传并返回录音文件信息给父组件
5. 启用转文字功能时，自动调用接口进行转写，完成后通过回调函数返回结果
6. 保留现有的录音、暂停、继续等基础功能

### 非功能需求
1. 组件接口设计合理，便于复用
2. 良好的错误处理机制
3. 支持自定义上传进度展示
4. 合理的状态管理和反馈机制

## 技术方案

### 组件接口设计

#### Props
```javascript
props: {
  // 继承现有 props
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '输入消息...',
  },
  // 新增 props
  enableTranscription: {
    type: Boolean,
    default: false,
  },
  maxDuration: {
    type: Number,
    default: 60000, // 默认最长录音时间 1 分钟
  },
  audioFormat: {
    type: String,
    default: 'mp3',
  },
  cloudPath: {
    type: String,
    default: '', // 为空时使用默认路径规则
  },
}
```

#### Emits
```javascript
emits: [
  'update:modelValue',   // 双向绑定
  'send',                // 发送文本消息
  'send-audio',          // 发送录音（不转写）
  'transcription-start', // 开始转写
  'transcription-end',   // 转写结束
  'upload-progress',     // 上传进度
  'error'                // 错误事件
]
```

### 实现思路

#### 1. 录音功能增强
保留现有的录音、暂停、继续功能，增加自动上传功能。

#### 2. 录音上传功能
```mermaid
graph TD
    A[录音完成] --> B{是否有录音文件?}
    B -->|是| C[准备上传]
    B -->|否| D[返回错误]
    C --> E[生成云端路径]
    E --> F[上传文件]
    F --> G[触发上传进度事件]
    F --> H[上传完成]
    H --> I{是否启用转写?}
    I -->|是| J[调用转写API]
    I -->|否| K[触发send-audio事件]
    J --> L[轮询转写结果]
    L --> M[转写完成]
    M --> N[触发transcription-end事件]
```

#### 3. 转写功能实现
参考 useAiChat.ts 中的转写实现，将其集成到组件内部：
- 调用 /speak/asr 接口发起转写请求
- 通过轮询 /speak/getAsr 接口获取转写结果
- 转写完成后通过事件通知父组件

### 代码实现关键点

1. 引入必要的依赖
```javascript
import { ref, watch, computed, onMounted, nextTick, onUnmounted } from 'vue'
import useRecord from '@/hooks/useRecord'
import dayjs from 'dayjs'
import request from '@/utils/request'
```

2. 录音上传功能
```javascript
const uploadAudio = async (blob, filePath) => {
  try {
    uploadProgress.value = { show: true, percent: 0 }
    
    const cloudPath = props.cloudPath || `speak/${dayjs().format('YYYY-MM-DD')}_${Date.now()}.${props.audioFormat}`
    
    const uploadResult = await uniCloud.uploadFile({
      filePath,
      cloudPath,
      cloudPathAsRealPath: true,
      onUploadProgress: (e) => {
        const percent = Math.round((e.loaded / e.total) * 100)
        uploadProgress.value.percent = percent
        emit('upload-progress', { percent })
      },
    })
    
    uploadProgress.value.percent = 100
    setTimeout(() => { uploadProgress.value.show = false }, 500)
    
    const { fileList } = await uniCloud.getTempFileURL({ fileList: [uploadResult.fileID] })
    const audioUrl = fileList[0].tempFileURL
    
    return { fileID: uploadResult.fileID, tempFileURL: audioUrl }
  } catch (error) {
    uploadProgress.value.show = false
    emit('error', { type: 'upload', error })
    throw error
  }
}
```

3. 转写功能
```javascript
const transcribeAudio = async (audioUrl) => {
  try {
    emit('transcription-start')
    const { data } = await request.post('/speak/asr', { url: audioUrl })
    await pollAsrResult(data.asrId)
  } catch (error) {
    emit('error', { type: 'transcription', error })
    throw error
  }
}

const pollAsrResult = async (asrId) => {
  let retryCount = 0
  const maxRetries = 30
  const pollInterval = 1000

  const poll = async () => {
    try {
      const { data: result } = await request.post('/speak/getAsr', { taskId: asrId })
      const status = result.Data.StatusStr
      
      if (status === 'success') {
        const transcript = result.Data.ResultDetail.map((item) => item.FinalSentence).join('\n')
        emit('transcription-end', { success: true, transcript })
        return
      } else if (status === 'failed') {
        throw new Error('语音转写失败')
      }

      retryCount++
      if (retryCount < maxRetries) {
        setTimeout(poll, pollInterval)
      } else {
        throw new Error('语音转写超时')
      }
    } catch (error) {
      emit('transcription-end', { success: false, error })
    }
  }
  poll()
}
```

4. 发送录音处理
```javascript
const handleSend = async () => {
  if (isRecording.value) {
    // 发送录音
    try {
      const blob = await stopRecording()
      console.log('发送录音', blob.size)
      
      // 获取文件路径
      let filePath
      // #ifdef H5
      filePath = URL.createObjectURL(blob)
      // #endif
      // #ifndef H5
      filePath = blob
      // #endif
      
      // 上传文件
      const uploadResult = await uploadAudio(blob, filePath)
      
      // 发送录音事件
      emit('send-audio', { 
        blob, 
        duration: duration.value,
        fileID: uploadResult.fileID,
        tempFileURL: uploadResult.tempFileURL
      })
      
      // 如果启用了转写，调用转写API
      if (props.enableTranscription) {
        await transcribeAudio(uploadResult.tempFileURL)
      }
      
      // 重置录音状态
      cancelRecording()
      isRecording.value = false
    } catch (error) {
      console.error('发送录音失败：', error)
      cancelRecording()
      isRecording.value = false
      emit('error', { type: 'send', error })
    }
  } else if (inputValue.value.trim()) {
    // 发送文本
    sendMessage()
  }
}
```

### 风险评估

#### 假设与未知因素
1. 假设当前项目已有可用的录音转写API
2. 假设uniCloud环境已配置正确
3. 未知不同平台（H5、App、小程序）的兼容性问题

#### 潜在风险
1. 录音权限问题：用户可能拒绝授权
   - 解决方案：增加权限检查和引导
   
2. 网络问题：上传和转写过程中网络中断
   - 解决方案：增加重试机制和错误处理
   
3. 平台兼容性问题：不同平台的文件处理方式不同
   - 解决方案：使用条件编译处理不同平台差异

4. 转写API限制：可能存在调用频率或文件大小限制
   - 解决方案：增加文件大小检查和错误处理 
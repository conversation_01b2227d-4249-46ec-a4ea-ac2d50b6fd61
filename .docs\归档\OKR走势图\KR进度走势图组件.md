# KR 进度走势图组件

status: draft

## 背景

目前 OKR 系统中的关键结果 (KR) 详情页面展示了进度历史和统计情况，但缺乏直观的趋势图表来显示 KR 进度随时间的变化情况。用户难以从现有视图中快速理解 KR 进展的时间趋势，特别是对于长期跟踪的 KR，无法按照不同时间维度（日、周、月）来查看进度变化。

增加进度走势图功能将帮助用户更好地理解 KR 的进展模式，及时调整工作重点，有效提高 OKR 的完成质量。

## 需求

### 功能需求

1. 在 KR 详情页 (`krDetail.vue`) 中添加"进度走势图"标签页，与现有的"进度历史"和"统计情况"并列
2. 走势图应显示 KR 进度随时间的变化情况，采用折线图形式展示
3. 走势图上方提供时间维度切换功能，包括：
   - 日线：显示每日进度变化
   - 周线：显示每周进度变化
   - 月线：显示每月进度变化
4. 图表应直观显示进度变化趋势，支持滑动查看历史数据
5. 当没有足够数据时，显示适当的空状态提示

### 非功能需求

1. 用户体验：图表交互应流畅，视觉呈现清晰
2. 性能：图表数据加载和渲染应在 1 秒内完成
3. 适配性：组件应适配移动端和 PC 端不同屏幕尺寸

## 技术方案

### 实现思路

1. 创建独立的走势图组件，放置在 `src/pages/okr/components/l-trend-chart/` 目录下，遵循局部组件以 `l-` 开头的命名规范
2. 在 `krDetail.vue` 中引入该组件，添加为第三个标签页选项
3. 组件内部实现时间维度切换功能，通过 tab 切换不同的视图
4. 利用现有的 KR 进度记录数据，按照不同时间维度进行数据处理和聚合
5. 使用纯 CSS+HTML 实现折线图，避免引入额外的图表库以保持项目轻量

### 架构设计

```mermaid
graph TD
    A[krDetail.vue] --> B[l-trend-chart组件]
    B --> C1[日线视图]
    B --> C2[周线视图]
    B --> C3[月线视图]
    D[KR进度数据] --> B
    B --> E[数据处理与聚合]
    E --> F[折线图渲染]
```

### 组件结构设计

```
/src/pages/okr/components/
  l-trend-chart/
    l-trend-chart.vue  // 主组件
    index.ts          // 导出文件（可选）
```

### 界面设计

1. 标签页设计：在现有"进度历史"和"统计情况"标签页旁添加"进度走势图"标签
2. 走势图组件顶部：显示日线、周线、月线切换选项
3. 主体区域：折线图显示进度变化趋势
4. 横轴：时间点（日期/周/月）
5. 纵轴：进度值
6. 图表区域应支持左右滑动查看更多历史数据

### 数据处理方案

1. 日线数据：直接使用每日记录的进度的最新值
2. 周线数据：按周聚合数据，使用每周的最新进度值
3. 月线数据：按月聚合数据，使用每月的最新进度值
4. 数据聚合策略：
   - 对于所有类型的 KR，统一使用最新记录的进度值
   - 不区分不同计算方式（sum、latest、max、avg），简化数据处理逻辑

### 技术栈与约束

- 前端框架：Vue.js 3
- UI 框架：使用项目现有的样式系统
- 数据处理：使用 Day.js 处理日期计算
- 性能约束：图表渲染不影响页面其他部分的交互体验

## 风险评估

### 假设与未知因素

- 假设 KR 的进度记录数据格式保持一致，包含完整的日期和进度值
- 可能存在的极端情况：长时间跨度的 KR 可能产生大量数据点

### 潜在风险

1. 数据量过大导致图表渲染性能问题
   - 解决方案：实现数据抽样和简化算法，对大量数据点进行抽样处理
2. 移动端屏幕宽度有限，图表可能显示不完整

   - 解决方案：确保图表支持横向滚动，并优化移动端视图的数据点密度

3. 不同类型 KR 的界面展示可能存在差异
   - 解决方案：统一使用最新值的展示方式，确保界面风格一致

# 类型定义与接口设计

## 任务描述

创建录音功能所需的类型定义和接口规范，确保 H5 和 App 实现遵循相同的接口标准，为后续开发提供基础。

## 所属功能模块

录音功能兼容 App 端

## 技术实现详情

### 1. 创建类型定义文件

创建 `src/hooks/types/record.ts` 文件，包含以下内容：

```typescript
import { Ref } from 'vue'

/**
 * 录音配置选项
 */
export interface UseRecordOptions {
  /** 采样率 */
  sampleRate?: number
  /** 声道数 */
  numberOfAudioChannels?: number
  /** 比特率 */
  desiredSampleRate?: number
  /** 音频类型 */
  mimeType?: string
  /** 最大录音时长 (ms)，默认 60 秒 */
  maxDuration?: number
  /** 静音检测 */
  checkForInactiveTracks?: boolean
  /** 禁用日志 */
  disableLogs?: boolean
  /** App 端特有配置 */
  appOptions?: {
    /** 音频格式，有效值 aac/mp3/wav/PCM */
    format?: string
    /** 指定帧大小，单位 KB */
    frameSize?: number
  }
}

/**
 * 录音结果类型
 * 兼容 H5 的 Blob 和 App 的临时文件路径
 */
export type RecordResult = Blob | string

/**
 * 录音 Hook 返回值接口
 */
export interface UseRecordReturn {
  /** 录音状态 */
  isRecording: Readonly<Ref<boolean>>
  /** 是否已暂停 */
  isPaused: Readonly<Ref<boolean>>
  /** 录音时长 (ms) */
  duration: Readonly<Ref<number>>
  /** 音量大小 0-100 */
  volume: Readonly<Ref<number>>
  /** 录音文件 Blob 或 文件路径 */
  recordBlob: Readonly<Ref<RecordResult | null>>
  /** 录音文件 URL */
  recordURL: Readonly<Ref<string>>
  /** 开始录音 */
  startRecording: () => Promise<void>
  /** 暂停录音 */
  pauseRecording: () => void
  /** 继续录音 */
  resumeRecording: () => void
  /** 停止录音 */
  stopRecording: () => Promise<RecordResult>
  /** 播放录音 */
  playRecording: () => void
  /** 获取音频波形数据 */
  getAudioWaveform: () => Uint8Array | null
  /** 取消录音 */
  cancelRecording: () => void
  /** 清理资源 */
  destroy: () => void
}

/**
 * 平台特定实现的接口定义
 */
export type PlatformRecordImplementation = (options?: UseRecordOptions) => UseRecordReturn
```

### 2. 定义统一错误处理

创建 `src/hooks/utils/recordErrors.ts` 文件，包含录音相关的错误处理逻辑：

```typescript
/**
 * 录音相关错误类型
 */
export enum RecordErrorType {
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  NOT_SUPPORTED = 'NOT_SUPPORTED',
  INITIALIZATION_FAILED = 'INITIALIZATION_FAILED',
  RECORDING_FAILED = 'RECORDING_FAILED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * 录音错误类
 */
export class RecordError extends Error {
  public type: RecordErrorType

  constructor(type: RecordErrorType, message: string) {
    super(message)
    this.type = type
    this.name = 'RecordError'
  }

  /**
   * 权限被拒绝错误
   */
  static permissionDenied(details?: string): RecordError {
    return new RecordError(RecordErrorType.PERMISSION_DENIED, `录音权限被拒绝${details ? ': ' + details : ''}`)
  }

  /**
   * 平台不支持错误
   */
  static notSupported(feature?: string): RecordError {
    return new RecordError(RecordErrorType.NOT_SUPPORTED, `当前平台不支持${feature ? feature + '功能' : '录音'}`)
  }

  /**
   * 初始化失败错误
   */
  static initializationFailed(details?: string): RecordError {
    return new RecordError(RecordErrorType.INITIALIZATION_FAILED, `录音初始化失败${details ? ': ' + details : ''}`)
  }

  /**
   * 录音失败错误
   */
  static recordingFailed(details?: string): RecordError {
    return new RecordError(RecordErrorType.RECORDING_FAILED, `录音失败${details ? ': ' + details : ''}`)
  }
}
```

### 3. 添加平台检测工具

创建 `src/hooks/utils/platform.ts` 文件，用于检测当前运行平台：

```typescript
/**
 * 获取当前运行平台信息
 * 注：此文件不使用条件编译，因为需要在运行时判断平台
 */
export const getPlatformInfo = () => {
  // #ifdef H5
  const isH5 = true
  // #endif
  // #ifndef H5
  const isH5 = false
  // #endif

  // #ifdef APP-PLUS
  const isApp = true
  // #endif
  // #ifndef APP-PLUS
  const isApp = false
  // #endif

  return {
    isH5,
    isApp,
    platform: isH5 ? 'h5' : isApp ? 'app' : 'unknown',
  }
}

/**
 * 检查当前环境是否支持录音功能
 */
export const checkRecordSupport = () => {
  const { isH5, isApp } = getPlatformInfo()

  if (isH5) {
    // 检查 H5 环境的 mediaDevices API 支持
    return Boolean(navigator && navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
  }

  if (isApp) {
    // App 环境下假设总是支持
    return true
  }

  // 其他平台暂不支持
  return false
}
```

## 验收标准

1. 所有必要的类型定义和接口文件已创建并放置在正确的位置
2. 类型定义覆盖了 H5 和 App 两种实现需要的所有参数和返回值
3. 错误处理逻辑完整，包含所有可能的错误场景
4. 平台检测工具能够正确区分不同运行环境
5. 代码通过 TypeScript 编译，无类型错误
6. 类型定义文档注释完整，便于开发人员理解和使用

## 依赖关系

- 无上游依赖
- 下游依赖：task-record-02, task-record-03

## 优先级

高 - 该任务是后续任务的基础

## 状态追踪

- [ ] 已创建类型定义文件
- [ ] 已实现错误处理逻辑
- [ ] 已添加平台检测工具
- [ ] 已完成代码审查
- [ ] 已通过验收测试

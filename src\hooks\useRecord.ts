import { ref } from 'vue'
import type { Ref } from 'vue'
import { UseRecordOptions, UseRecordReturn } from './types/record'

// #ifdef H5
import useRecordH5 from './useRecordH5'
// #endif

// #ifdef APP-PLUS
import useRecordApp from './useRecordApp'
// #endif

/**
 * 录音Hook，自动根据运行环境选择合适的实现
 *
 * @param options 录音配置选项
 * @returns 录音控制对象
 */
export default function useRecord(options?: UseRecordOptions): UseRecordReturn {
  // #ifdef H5
  return useRecordH5(options)
  // #endif

  // #ifdef APP-PLUS
  return useRecordApp(options)
  // #endif

  // 在非H5和非App平台，返回一个空实现，避免编译错误
  // @ts-ignore
  return {
    isRecording: ref(false),
    isPaused: ref(false),
    duration: ref(0),
    volume: ref(0),
    recordBlob: ref(null),
    recordURL: ref(''),
    startRecording: () => Promise.reject(new Error('Not supported')),
    pauseRecording: () => {},
    resumeRecording: () => {},
    stopRecording: () => Promise.reject(new Error('Not supported')),
    playRecording: () => {},
    getAudioWaveform: () => null,
    cancelRecording: () => {},
    destroy: () => {},
  } as UseRecordReturn
}

<template>
  <view class="loading-container" :style="{ padding: padding }">
    <view class="loading-text-wrapper">
      <!-- Wave Effect -->
      <view
        v-if="effect === 'wave'"
        class="loading-text wave-effect"
        :style="{
          fontSize: fontSize + 'rpx',
          color: color,
        }"
      >
        <text
          v-for="(char, index) in text.split('')"
          :key="index"
          class="loading-char"
          :style="{ animationDelay: `${index * 80}ms` }"
          >{{ char }}</text
        >
      </view>
      <!-- Breathing Effect (default) -->
      <text
        v-else
        class="loading-text breathing-effect"
        :style="{
          fontSize: fontSize + 'rpx',
          color: color,
        }"
        >{{ text }}</text
      >
    </view>
  </view>
</template>

<script setup>
import { defineProps } from 'vue'

defineProps({
  text: {
    type: String,
    default: '加载中',
  },
  fontSize: {
    type: [Number, String],
    default: 42,
  },
  color: {
    type: String,
    default: '#3a7edc',
  },
  padding: {
    type: String,
    default: '60rpx 0',
  },
  effect: {
    type: String,
    default: 'breathing', // 'breathing' or 'wave'
    validator: (value) => ['breathing', 'wave'].includes(value),
  },
})
</script>

<style lang="scss" scoped>
.loading-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .loading-text-wrapper {
    position: relative;
    display: flex;
    justify-content: center;

    .loading-text {
      font-weight: 500;
    }

    /* Wave effect styles */
    .wave-effect {
      white-space: pre;
      letter-spacing: 4rpx;
    }
    .loading-char {
      display: inline-block;
      animation: wave 1.6s ease-in-out infinite;
    }

    /* Breathing effect styles */
    .breathing-effect {
      letter-spacing: 2rpx;
      animation: breathing-scale 2s ease-in-out infinite;
      transform-origin: center center;
    }
  }
}

@keyframes wave {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15rpx);
  }
}

@keyframes breathing-scale {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}
</style>

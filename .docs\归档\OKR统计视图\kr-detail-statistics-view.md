# 关键结果详情页添加统计视图功能

status: draft

## 背景
目前关键结果详情页（krDetail.vue）只展示了进度历史记录，缺乏直观的统计数据展示功能。为了让用户更清晰地了解任务的计划量和完成量，需要增加统计视图功能，以图表形式展示数据。

## 需求

### 功能需求
1. 在关键结果详情页的进度历史上方添加 tab 切换功能，可以在"进度历史"和"统计情况"之间切换
2. "进度历史"tab 展示原有的执行历史内容
3. "统计情况"tab 展示新的统计组件
4. 统计组件以柱状图形式展示数据，支持左右滑动查看
5. 图表上方显示总计划量和总完成量
6. 柱状图根据任务的循环类型（`repeatFlag`）自动划分周期，x 轴展示周期，y 轴展示完成量

### 非功能需求
1. 界面设计统一，与现有 UI 风格保持一致
2. 保证在不同尺寸设备上的良好适配性
3. 确保柱状图滑动操作流畅

## 技术方案

### 实现思路

1. 在`krDetail.vue`中添加 tab 切换组件，包含"进度历史"和"统计情况"两个选项
2. 创建新的`l-statistics.vue`组件，负责展示统计图表，使用模拟数据进行开发，确保 UI 显示和交互效果良好
3. 进行 UI 验收测试，确认统计图表的显示效果和交互体验符合预期
4. 在 UI 验收通过后，根据任务的 `repeatFlag`（循环规则），开发数据处理逻辑，自动对进度记录进行分组聚合，生成实际的柱状图数据
5. 根据选中的 tab 动态切换显示内容

### 组件设计

#### Tab 切换组件
```html
<div class="card-header">
  <div>
    <div class="card-title">进度记录</div>
  </div>
  <div class="card-tabs">
    <div 
      class="card-tab" 
      :class="{ active: activeTab === 'history' }" 
      @click="activeTab = 'history'"
    >
      进度历史
    </div>
    <div 
      class="card-tab" 
      :class="{ active: activeTab === 'statistics' }" 
      @click="activeTab = 'statistics'"
    >
      统计情况
    </div>
  </div>
</div>
```

#### 统计组件设计
创建`/src/pages/okr/components/l-statistics.vue`组件，主要包含：
1. 柱状图展示
2. 顶部计划量和完成量指标展示
3. 左右滑动功能实现

```mermaid
graph TD
    A[krDetail.vue] --> B[Tab切换组件]
    B -->|选择"进度历史"| C[l-progress-history组件]
    B -->|选择"统计情况"| D[l-statistics组件]
    D --> E[模拟数据开发与UI验收]
    E --> F[对接真实数据]
    F --> G[柱状图展示]
    G --> H[滑动功能]
```

### 数据处理与周期划分
统计图表的周期划分将完全由 `src/utils/rrrrule.ts` 中的 `getOccurrences` 函数驱动。

1.  **调用 `getOccurrences`**:
    -   传入任务的循环规则 (`repeatFlag`)、开始日期和结束日期。
    -   获取返回结果中的 `groupedDates` 数组。`groupedDates` 是一个二维数组，如 `[['2023-01-01', '2023-01-02'], ['2023-01-03']]`，每个内部数组代表一个周期。

2.  **生成柱状图数据**:
    -   遍历 `groupedDates` 数组。
    -   每个内部数组（周期）对应一个柱子。
    -   将该周期内的所有 `recList` 记录的 `val` 值进行累加，作为该柱子的高度（完成量）。
    -   每个周期的计划量也需要相应计算。

3.  **生成 X 轴标签**:
    -   根据每个周期数组的第一个和最后一个日期，生成 X 轴的标签。
    -   如果周期只有一天，标签为 "MM-DD"。
    -   如果周期为多天，标签为 "MM/DD-MM/DD"。

### 技术栈与约束
- UI 组件：使用现有组件库
- 图表库：考虑使用 echarts 或 u-charts 等轻量级图表库
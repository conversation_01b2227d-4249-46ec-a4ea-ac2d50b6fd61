<template>
  <div class="p-4">

    <div @click="showKeyboard = true" class="color-blue text-80">
      {{ params.amount }}
    </div>
    <u-form :model="params" ref="form1">
      <u-form-item label="分类" prop="name" @click="showCategory = true">{{ categoryText }}</u-form-item>
      <u-form-item label="时间" prop="date" @click="showDate = true">{{ params.date || '选择时间' }}</u-form-item>
      <u-form-item label="备注" prop="remark"><u-input type="textarea" v-model="params.remark" /></u-form-item>
    </u-form>
    <div>

      <u-button type="primary" @click="submit">提交</u-button>
    </div>
    <u-keyboard mode="number" ref="uKeyboard1" v-model="showKeyboard" @change="valChange"
      @backspace="backspace"></u-keyboard>
    <u-calendar v-model="showDate" mode="date" @change="changeDate"></u-calendar>
    <u-popup v-model="showCategory" mode="bottom">
      <div class="p-4">
        <div v-for="(item, index) in classList" :key="index">
          <div class="text-30 mb-3 mt-2">{{ item.label }}</div>
          <div class="grid grid-cols-4  gap-x-4 gap-y-2">
            <div @click="setCategory(child)"
              class="flex justify-center flex-col items-center text-center h-120 rounded-1"
              v-for="(child, childIndex) in item.children" :key="childIndex"
              :class="child._id === params.category ? 'bg-blue color-white' : ''">
              <div>{{ child.label }}</div>
            </div>
          </div>
        </div>
        <!-- <u-icon name="plus-circle" size="80" @click="goClass"></u-icon> -->
        <u-button type="primary" @click="goClass">添加分类</u-button>
      </div>

    </u-popup>
  </div>
</template>

<script setup lang="ts">
const sys = useIsPC()
const showKeyboard = ref(false)
const showCategory = ref(false)
const showDate = ref(false)

const classList = ref([])

const [params, resetParams] = useParams({
  amount: '0.00',
  category: '',
  date: '',
  remark: ''
})

const changeDate = (date: any) => {
  params.date = date.result
  showDate.value = false
}
const submit = () => {
  console.log('params', params)
  addBillApi({
    ...params,
    amount: Number(params.amount),
  }).then((res) => {
    console.log('res', res)
    uni.showToast({
      title: '添加成功',
      icon: 'success',
    })
    resetParams()
    uni.navigateTo({
      url: '/pages/bill/flow',
    })
  })
}

const setCategory = (item) => {
  params.category = item._id
  categoryText.value = item.label
}

const goClass = () => {
  uni.navigateTo({
    url: '/pages/bill/category',
  })
}

const valChange = (val) => {
  console.log('val', val)
  if (params.amount === '0.00') {
    params.amount = ''
  }
  params.amount += val
}
const backspace = () => {
  if (params.amount.length) params.amount = params.amount.substr(0, params.amount.length - 1);
}

const categoryText = ref('选择分类')

const init = () => {
  getCategoryApi().then((res) => {
    const newList = res
      .filter((e) => !e.parentId)
      .map((e) => {
        return {
          _id: e._id,
          label: e.name,
          children: [],
        }
      })
    newList.forEach((e) => {
      e.children = res.filter((i) => i.parentId === e._id).map((i) => {
        return {
          _id: i._id,
          label: i.name,
        }
      })
    })
    console.log(newList, 'aaaa')

    classList.value = newList
  })
}
onMounted(init)
</script>

<style scoped lang="scss">
.pages {
  padding: 15px;
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;

  &__title {
    font-size: 22px;
  }
}

.fixed-placeholder {
  padding-top: calc(var(--status-bar-height) + 80rpx);
}
</style>

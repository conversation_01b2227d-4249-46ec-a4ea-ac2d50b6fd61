import request from '@/utils/request'
import db from './database'
import { tableSchema } from './dataSchema'

// TODO: 完全依赖于 lastSyncTime，如果 lastSyncTime 丢失，或者不准确，会出问题
// TODO: 什么时候重置 lastSyncTime
// TODO！是否需要添加事务，当运行没完成时就中断，需要回滚操作码？
const tableList: string[] = Object.keys(tableSchema)

let isFetching = false // 正在同步中
const isConnected = true // 是否有网络连接
// 监听网络状态
// uni.onNetworkStatusChange(function (res) {
//   isConnected = res.isConnected
// })

// 保存数据到本地
// TODO: 保存使用 put 方式，除去添加方法，其他的都需要重新查出全量数据，比较耗费性能，是否能优化？
const saveToDB = async (syncStorage: any) => {
  const saveTable: any = {} // 待保存的数据，按表名分类，减少数据库操作次数
  for (const key in syncStorage) {
    const item = syncStorage[key]
    // 如果数据待同步，不保存
    if (!syncStorage[key].isSyncing) continue
    if (!saveTable[item.tableName]) {
      saveTable[item.tableName] = []
    }
    // TODO 要处理插入错误的情况，有哪几种情况？1 数据错误
    const { isDirty, ...rest } = item.data
    saveTable[item.tableName].push(rest)
  }
  // 保存数据到数据库
  const promises = Object.keys(saveTable).map((tName) =>
    db.table(tName).bulkPut(saveTable[tName], { isCloudData: true })
  )
  await Promise.all(promises)
  // 清空 syncStorage 中已同步的数据
  for (const key in syncStorage) {
    if (syncStorage[key].isSyncing) {
      delete syncStorage[key]
    }
  }
}

const uploadServer = async (syncList) => {
  const lastSyncTime = uni.getStorageSync('lastSyncTime')
  const t = uni.getStorageSync('uni_id_token')
  const params = {
    tableList,
    lastSyncTime,
    syncData: syncList,
    t,
  }
  console.log('ssssssssssss')
  const serverinfo = await request.post('/okr/sync', params)
  console.log('cccccccccccc')
  if (serverinfo.errCode !== 0) {
    isFetching = false
    throw new Error(`上传服务器出错：${JSON.stringify(serverinfo)}`)
  } else {
    return serverinfo
  }
}

// 收集未同步数据
const collectSyncData = async (syncStorage: any) => {
  // 判断 syncStorage 是否有数据
  const noData = Object.keys(syncStorage).length > 0
  if (noData) {
    // 没有数据，从本地数据库获取未同步数据，添加到 syncStorage
    for (const tName in tableSchema) {
      const list = await db.table(tName).where(`isDirty == 1`, { filterDelete: false }).toArray()
      list?.forEach((item: any) => {
        syncStorage[item._id] = {
          tableName: tName,
          data: item,
          isSyncing: false,
        }
      })
    }
  }
}

// 获取笔记列表
export const getNoteList = async () => {
  const data = getMenuMd()
  console.log('getNoteList', data)
}

/**
 * 同步到服务端
 * @param {Object} sData 需要同步的数据
 */
const syncToCloud = async (sData: any) => {
  // 检查 lastSyncTime 是否为空，为空则跳转到数据下载页面
  // const lastSyncTime = uni.getStorageSync('lastSyncTime')
  // if (!lastSyncTime) {
  //   // 跳转到下载数据页面
  //   router.push('/pages/setting/download-data')
  // }

  const { syncStorage } = toolGlobalData.get()
  // 添加未同步数据到 syncStorage
  if (sData) {
    for (const key in sData) {
      syncStorage[key] = {
        ...sData[key],
        isSyncing: false,
      }
    }
  }

  const isSync = toolCloudSync.isOpen() // 是否开启同步

  // 3 种情况直接返回：1 未开启同步，2 正在同步中，3 无网络连接
  const netWorkType = await toolGetNetworkType() // TODO: 这里应该使用监听的方式，性能更好
  if (!isSync || isFetching || netWorkType === 'none') return uni.$emit('updateCloud', { isRefresh: false })

  try {
    isFetching = true // 设置为同步状态
    // 发布同步状态为进行中
    uni.$emit('syncStatus', { isSyncing: true })

    // 收集未同步数据
    await collectSyncData(syncStorage)
    // 将上传的数据设置正在同步中
    Object.keys(syncStorage).forEach((key) => {
      syncStorage[key].isSyncing = true
    })
    // 上传数据到服务端
    const { serverData, lastSyncTime } = await uploadServer(syncStorage)

    // 本次上传期间，本地可能产生的新未同步数据
    // 合并本地未同步数据和服务端同步数据 TODO: 优化 serverData 没有值的情况
    if (serverData) {
      Object.keys(serverData).forEach((tName) => {
        serverData[tName].forEach((sItem) => {
          // 本地有该条数据，且 isSyncing 为 false，说明本地有更新，需要合并
          if (syncStorage[sItem._id] && !syncStorage[sItem._id].isSyncing) {
            // 哪边的数据更新时间更晚，就以哪边的数据为准
            const localTime = new Date(syncStorage[sItem._id].updateTime).getTime()
            const serverTime = new Date(sItem.updateTime).getTime()
            if (serverTime > localTime) {
              // 服务端数据比较新，以服务端数据为准
              syncStorage[sItem._id].data = sItem
              syncStorage[sItem._id].isSyncing = true
            }
          } else {
            // 本地没有该条数据，直接添加 or 本地有该条数据，且 isSyncing 为 true，说明本地无更新，直接覆盖写入
            syncStorage[sItem._id] = {
              data: sItem,
              tableName: tName,
              isSyncing: true,
            }
          }
        })
      })
    }
    await saveToDB(syncStorage)
    uni.setStorageSync('lastSyncTime', lastSyncTime)
    // 发布订阅模式，通知其他页面数据更新
    uni.$emit('updateCloud', { isRefresh: !!serverData })
    isFetching = false
    // 发布同步状态为已完成
    uni.$emit('syncStatus', { isSyncing: false })

    // 如果执行栈中还有任务，继续执行
    if (syncStorage.length > 0) await syncToServer()
  } catch (error) {
    isFetching = false
    // 发布同步状态为已完成 (出错)
    uni.showToast({
      title: '同步失败',
      icon: 'none',
    })
    uni.$emit('syncStatus', { isSyncing: false, isError: true })
    console.error(error)
    uni.$emit('updateCloud', { isRefresh: false })
  }

  uni.hideLoading()
}

export const syncToServer = toolDebounce(syncToCloud, 1000)

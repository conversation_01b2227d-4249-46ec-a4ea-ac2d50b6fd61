# 任务：OKR进度历史组件筛选条件区域实现

## 任务描述
实现进度历史组件的筛选条件区域，包括月份显示及切换、展示模式切换功能。

## 所属功能模块
OKR进度历史组件

## 技术实现详情

### 筛选条件区域UI实现
1. 在`l-progress-history.vue`组件中完善筛选条件区域：
   ```html
   <view class="filter-area">
     <!-- 月份选择区域 -->
     <view class="month-selector">
       <view class="month-arrow left" @click="changeMonth(-1)">
         <text class="iconfont icon-left"></text>
       </view>
       <view class="month-text">{{ filterMonth }}</view>
       <view class="month-arrow right" @click="changeMonth(1)">
         <text class="iconfont icon-right"></text>
       </view>
     </view>
     
     <!-- 展示模式切换 -->
     <view class="mode-switch">
       <view 
         class="mode-item" 
         :class="{ active: filterMode === 'day' }" 
         @click="switchMode('day')"
       >
         按天
       </view>
       <view 
         class="mode-item" 
         :class="{ active: filterMode === 'month' }" 
         @click="switchMode('month')"
       >
         按月
       </view>
     </view>
   </view>
   ```

### 筛选相关方法实现
1. 月份切换方法：
   ```javascript
   methods: {
     /**
      * 切换月份
      * @param {Number} direction 方向，1表示下个月，-1表示上个月
      */
     changeMonth(direction) {
       const currentMonth = dayjs(this.filterMonth);
       const newMonth = currentMonth.add(direction, 'month');
       this.filterMonth = newMonth.format('YYYY-MM');
       
       // 更新选中日期，保持在新月份内
       if (this.filterMode === 'day') {
         const currentDay = dayjs(this.selectedDate).date();
         const daysInNewMonth = newMonth.daysInMonth();
         const newDay = Math.min(currentDay, daysInNewMonth);
         this.selectedDate = newMonth.date(newDay).format('YYYY-MM-DD');
       }
       
       // 重新筛选数据
       this.filterRecords();
     },
     
     /**
      * 切换展示模式
      * @param {String} mode 模式，'day'或'month'
      */
     switchMode(mode) {
       if (this.filterMode === mode) return;
       this.filterMode = mode;
       
       // 重新筛选数据
       this.filterRecords();
     },
     
     /**
      * 根据当前筛选条件过滤记录
      */
     filterRecords() {
       if (!this.records.length) return;
       
       const month = this.filterMonth;
       
       if (this.filterMode === 'day') {
         // 按天筛选
         const day = this.selectedDate;
         this.filteredRecords = this.records.filter(record => {
           return dayjs(record.createTime).format('YYYY-MM-DD') === day;
         });
       } else {
         // 按月筛选
         this.filteredRecords = this.records.filter(record => {
           return dayjs(record.createTime).format('YYYY-MM') === month;
         });
       }
       
       // 按时间倒序排列
       this.filteredRecords.sort((a, b) => {
         return new Date(b.createTime).getTime() - new Date(a.createTime).getTime();
       });
     }
   }
   ```

### 更新标记日期方法
1. 实现计算当前月份中哪些日期有记录的方法：
   ```javascript
   /**
    * 更新有记录的日期数组
    */
   updateDateHasRecord() {
     const month = this.filterMonth;
     this.dateHasRecord = [];
     
     // 收集当前月份中有记录的日期
     this.records.forEach(record => {
       const recordDate = dayjs(record.createTime);
       if (recordDate.format('YYYY-MM') === month) {
         const day = recordDate.format('YYYY-MM-DD');
         if (!this.dateHasRecord.includes(day)) {
           this.dateHasRecord.push(day);
         }
       }
     });
   }
   ```

### 样式实现
1. 筛选条件区域的样式：
   ```css
   .filter-area {
     display: flex;
     justify-content: space-between;
     padding: 12px 16px;
     border-bottom: 1px solid #eee;
   }
   
   .month-selector {
     display: flex;
     align-items: center;
   }
   
   .month-text {
     margin: 0 10px;
     font-weight: bold;
   }
   
   .month-arrow {
     width: 24px;
     height: 24px;
     display: flex;
     align-items: center;
     justify-content: center;
     border-radius: 50%;
     background-color: #f5f5f5;
   }
   
   .mode-switch {
     display: flex;
     background-color: #f5f5f5;
     border-radius: 16px;
     overflow: hidden;
   }
   
   .mode-item {
     padding: 6px 12px;
     font-size: 14px;
   }
   
   .mode-item.active {
     background-color: var(--primary-color, #007aff);
     color: #fff;
   }
   ```

## 验收标准
1. 筛选条件区域UI正确显示，包括月份显示/切换和展示模式切换
2. 月份切换功能正常，点击左右按钮能正确切换到上一个/下一个月份
3. 展示模式切换功能正常，可以在"按天"和"按月"两种模式之间切换
4. 筛选条件变化时，记录列表能够正确更新，显示对应的筛选结果
5. 组件样式符合设计要求，UI美观且易用

## 依赖关系
- 上游依赖：task-progress-history-01.md
- 下游依赖：task-progress-history-03.md, task-progress-history-04.md

## 优先级
高

## 估计工作量
1.5人日 
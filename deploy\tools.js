// SECRETID 和 SECRETKEY 请登录 https://console.cloud.tencent.com/cam/capi 进行查看和管理
const { SecretId, SecretKey, Bucket, Region } = require('./conf')
const fs = require('fs')

const log = require('./log')
var COS = require('cos-nodejs-sdk-v5')
const { join } = require('path')
var cos = new COS({
  SecretId,
  SecretKey,
})

exports.uploadFile = (filePath, fileName) => {
  const promise = new Promise((resolve, reject) => {
    let spinner = log.loading(`上传 COS ${fileName}\n`)
    cos.uploadFile(
      {
        Bucket /* 填入您自己的存储桶，必须字段 */,
        Region /* 存储桶所在地域，例如 ap-beijing，必须字段 */,
        Key: fileName /* 存储在桶里的对象键（例如 1.jpg，a/b/test.txt），必须字段 */,
        FilePath: filePath /* 必须 */,
        // SliceSize: 1024 * 1024 * 5,     /* 触发分块上传的阈值，超过 5MB 使用分块上传，非必须 */
        onTaskReady: function (taskId) {
          /* 非必须 */
          console.log(taskId)
        },
        onProgress: function (progressData) {
          /* 非必须 */
          // console.log(JSON.stringify(progressData));      //TODO: 可以搞一个进度条
        },
        onFileFinish: function (err, data, options) {
          /* 非必须 */
          spinner.stop()
          if (err) {
            log.error(options.Key + ' 上传失败')
          } else {
            log.succeed('上传成功，地址：https://' + data.Location)
          }
        },
      },
      function (err, data) {
        if (err) {
          reject(err)
          return
        }
        if (data.statusCode === 200) {
          resolve(data.Location)
        }
      }
    )
  })
  return promise
}

exports.removeJsComments = (code) => {
  return code
    .replace(/(?:^|\n|\r)\s*\/\*[\s\S]*?\*\/\s*(?:\r|\n|$) | (\/\*(.|[\r\n])*?\*\/)/g, '\n')
    .replace(/(?:^|\n|\r)\s*\/\/.*(?:\r|\n|$)/g, '\n')
}

/**
 * 数字前面补 0
 */
exports.addZero = (num) => {
  let t = (num + '').length,
    s = ''
  for (let i = 0; i < 3 - t; i++) {
    s += '0'
  }
  return s + num
}

import { Ref } from 'vue'

/**
 * 录音配置选项
 */
export interface UseRecordOptions {
  /** 采样率 */
  sampleRate?: number
  /** 声道数 */
  numberOfAudioChannels?: number
  /** 比特率 */
  desiredSampleRate?: number
  /** 音频类型 */
  mimeType?: string
  /** 最大录音时长 (ms)，默认 60 秒 */
  maxDuration?: number
  /** 静音检测 */
  checkForInactiveTracks?: boolean
  /** 禁用日志 */
  disableLogs?: boolean
  /** App端特有配置 */
  appOptions?: {
    /** 音频格式，有效值 aac/mp3/wav/PCM */
    format?: string
    /** 指定帧大小，单位 KB */
    frameSize?: number
  }
}

/**
 * 录音结果类型
 * 兼容H5的Blob和App的临时文件路径
 */
export type RecordResult = Blob | string

/**
 * 录音Hook返回值接口
 */
export interface UseRecordReturn {
  /** 录音状态 */
  isRecording: Readonly<Ref<boolean>>
  /** 是否已暂停 */
  isPaused: Readonly<Ref<boolean>>
  /** 录音时长 (ms) */
  duration: Readonly<Ref<number>>
  /** 音量大小 0-100 */
  volume: Readonly<Ref<number>>
  /** 录音文件 Blob 或 文件路径 */
  recordBlob: Readonly<Ref<RecordResult | null>>
  /** 录音文件 URL */
  recordURL: Readonly<Ref<string>>
  /** 开始录音 */
  startRecording: () => Promise<void>
  /** 暂停录音 */
  pauseRecording: () => void
  /** 继续录音 */
  resumeRecording: () => void
  /** 停止录音，返回可用于上传的文件路径 */
  stopRecording: () => Promise<string>
  /** 播放录音 */
  playRecording: () => void
  /** 获取音频波形数据 */
  getAudioWaveform: () => Uint8Array | null
  /** 取消录音 */
  cancelRecording: () => void
  /** 清理资源 */
  destroy: () => void
}

/**
 * 平台特定实现的接口定义
 */
export type PlatformRecordImplementation = (options?: UseRecordOptions) => UseRecordReturn

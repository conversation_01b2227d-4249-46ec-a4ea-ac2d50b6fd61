<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  title: string
  // 右侧按钮类型：edit-编辑，save-保存，delete-删除，add-添加，more-更多
  // 不传则不显示按钮
  rightButtonType?: 'edit' | 'save' | 'delete' | 'add' | 'more' | ''
}

const props = withDefaults(defineProps<Props>(), {
  rightButtonType: '', // 默认不显示按钮
})

const emit = defineEmits<{
  (e: 'back'): void
  (e: 'rightButtonClick', type: string): void
  (e: 'menuItemClick', action: string): void
}>()

const showMenu = ref(false)
const { isPC } = getPlatform()

const handleBack = () => {
  router.back()
  // emit('back')
}

const handleRightButtonClick = () => {
  if (props.rightButtonType === 'more') {
    showMenu.value = !showMenu.value
  } else {
    emit('rightButtonClick', props.rightButtonType)
  }
}

const handleMenuItemClick = (action: string) => {
  showMenu.value = false
  emit('menuItemClick', action)
}

const closeMenu = () => {
  showMenu.value = false
}

// 添加菜单项点击委托处理
const handleActionMenuContentClick = (event: any) => {
  // 在 uni-app 环境中，不能使用 target.closest 方法
  // 检查事件目标或其父元素是否包含特定类名
  let isMenuItem = false

  // 检查点击的元素本身是否有这个类
  if (event && event.target && event.target.className && typeof event.target.className === 'string') {
    isMenuItem = event.target.className.includes('action-menu-item')
  }

  // 如果点击的是菜单项内部元素，也应该关闭菜单
  if (!isMenuItem && event.currentTarget) {
    const menuItems = uni
      .createSelectorQuery()
      .in(event.instance || event.currentTarget)
      .selectAll('.action-menu-item')

    // 简单处理：如果点击了菜单内容区域，我们就关闭菜单
    // 这是一个简化的处理方式，确保菜单可以正常关闭
    isMenuItem = true
  }

  if (isMenuItem) {
    // 如果点击的是菜单项，关闭菜单
    setTimeout(() => {
      showMenu.value = false
    }, 100) // 短暂延迟确保先触发点击事件再关闭
  }
}

// 按钮图标映射
const buttonIcons = {
  edit: 'fa-edit',
  save: 'fa-save',
  delete: 'fa-trash',
  add: 'fa-plus',
  more: 'fa-ellipsis-h',
}

// 按钮文本映射（用于 aria-label 和提示）
const buttonLabels = {
  edit: '编辑',
  save: '保存',
  delete: '删除',
  add: '添加',
  more: '更多',
}
</script>

<template>
  <div class="navbar">
    <div class="status-bar-placeholder"></div>
    <a v-if="!isPC" href="#" @click.prevent="handleBack" class="navbar-back" aria-label="返回">
      <i class="fas fa-chevron-left"></i>
    </a>
    <div class="navbar-title">{{ title }}</div>
    <div style="flex: 1"></div>

    <!-- 内置按钮类型 -->
    <div
      v-if="rightButtonType"
      class="action-btn"
      :class="rightButtonType + '-btn'"
      @click="handleRightButtonClick"
      :aria-label="buttonLabels[rightButtonType]"
    >
      <i class="fas" :class="buttonIcons[rightButtonType]"></i>
    </div>

    <!-- 自定义插槽，优先级高于内置按钮 -->
    <slot name="right"></slot>

    <!-- 操作菜单 -->
    <div class="action-menu" v-if="showMenu" @click="closeMenu">
      <div class="action-menu-content" @click.stop @click="handleActionMenuContentClick">
        <!-- 使用具名插槽允许父组件定义菜单项 -->
        <slot name="menu-items">
          <!-- 默认菜单项 -->
          <div class="action-menu-item" @click="handleMenuItemClick('edit')">
            <i class="fas fa-edit"></i>
            <span>编辑</span>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.navbar {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);
  position: sticky;
  top: 0;
  z-index: 40;
  flex-wrap: wrap;
}

.navbar-title {
  font-weight: 600;
  font-size: 18px;

  color: var(--color-primary);
}

.navbar-back {
  margin-right: 15px;
  color: var(--color-primary);
  font-size: 16px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--rounded-sm);
  background: var(--color-gray-100);
  text-decoration: none;
}

.navbar-back:hover {
  background: var(--color-gray-200);
}

// 通用按钮样式
.action-btn {
  border-radius: var(--rounded-sm);
  padding: 8px 10px;
  font-size: 16px;
  background: var(--color-white);
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  cursor: pointer;
  box-sizing: border-box;

  &:hover {
    background: var(--color-gray-100);
  }

  i {
    margin-right: 0;
  }
}

// 特定按钮样式 - 只有删除按钮使用红色，其他都使用主题色
.delete-btn {
  color: var(--color-danger, #dc3545);
  border-color: var(--color-danger, #dc3545);
}

// 操作菜单样式
.action-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 999;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding-top: calc(60px + var(--status-bar-height));
  padding-right: 20px;
  animation: fadeIn 0.2s ease;
}

.action-menu-content {
  background: var(--color-white);
  border-radius: var(--rounded-md);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  width: 180px;

  // 使用:deep() 选择器确保样式应用到插槽内容
  :deep(.action-menu-item) {
    display: flex;
    align-items: center;
    padding: 14px 16px;
    font-size: 14px;
    color: var(--color-gray-700);
    transition: var(--transition-fast);
    cursor: pointer;

    &:hover {
      background: var(--color-gray-100);
    }

    i {
      margin-right: 10px;
      width: 20px;
      text-align: center;
      font-size: 16px;
      color: var(--color-primary);
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// Font Awesome 图标
.fa-edit:before {
  content: '\f044';
}

.fa-save:before {
  content: '\f0c7';
}

.fa-trash:before {
  content: '\f1f8';
}

.fa-plus:before {
  content: '\f067';
}

.fa-ellipsis-h:before {
  content: '\f141';
}
</style>

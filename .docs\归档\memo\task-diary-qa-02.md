# 任务：AI问答服务开发

- **所属功能模块**: `diary-qa`
- **任务ID**: `task-diary-qa-02`
- **优先级**: 中

## 任务描述
实现AI问答服务，核心是根据用户当日的日记内容动态生成一系列有引导性的问题。这将通过一个API端点（或客户端等效逻辑）`/api/diary/generateQuestions` 来实现。

## 技术实现详情
1.  **本地问题库**:
    - 创建一个本地的、分类别的问题库（例如，可以是一个JSON文件或代码中的常量）。
    - 问题分类可以包括：常规反思、情绪探索、成就回顾、人际关系、未来规划等。
    - 预设至少50个高质量的引导性问题。

2.  **问题生成逻辑**:
    - 实现 `/api/diary/generateQuestions` 的逻辑。
    - **输入**: 当日的所有日记条目（`entries`）。
    - **处理**:
        - 对输入的日记内容进行简单的关键词分析（如：工作、学习、家庭、情绪词等）。
        - 根据分析结果，从本地问题库中智能筛选出3-5个最相关的问题。
        - （可选高级功能）如果关键词匹配度不高，可以调用通用AI模型，根据日记内容动态生成1-2个开放性问题。
        - 确保问题具有多样性，避免重复。
    - **输出**: 一个包含问题字符串的数组。

## 验收标准
- 本地问题库已建立并包含足量的高质量问题。
- 调用问题生成服务/函数并传入模拟的日记内容后，能返回一个包含3-5个相关问题的数组。
- 问题生成逻辑能考虑到输入内容的相关性。
- 服务/函数有良好的错误处理机制。

## 依赖关系
- `task-diary-data-01`: 依赖其定义的 `entries` 数据结构作为输入。

## 状态追踪
- 未开始 
import Dexie from 'dexie'
export default class webDB extends Dexie {
  constructor(dataConfig: any, tableSchema: any) {
    super(dataConfig.databaseName)

    // 建立索引
    // TODO 抽离索引，与 qrery 结合，智能优化查询性能
    // TODO dexie 再进行一层封装，使得添加索引、表更加方便
    const stores: any = {}
    Object.keys(tableSchema).forEach((sName) => {
      stores[sName] = []
      const sObj = tableSchema[sName]
      Object.keys(sObj).forEach((field) => {
        const { aType, wType, primaryKey, defaultVal } = sObj[field]
        const s = wType ? wType + field : field
        stores[sName].push(s)
      })
      stores[sName] = stores[sName].join(',')
    })
    this.version(dataConfig.webVersion).stores(stores)
  }
}

# 关键词故事功能增强需求

## 1. 背景

`关键词讲故事`是`语言训练`模块的核心功能之一，旨在通过提供关键词来激发用户的创意和口语表达能力。当前版本功能较为单一，仅支持随机生成关键词，缺乏针对性和系统性的训练模式，难以满足用户多样化的练习需求（如备考、专业术语巩固等）。

**核心目标深化：** 本次增强旨在超越简单的“词汇使用”，聚焦于两大核心能力的锻炼：
1.  **概念熟练度：** 帮助用户熟练、准确地运用概念（关键词）。
2.  **思维跳转力：** 训练用户在不同概念之间进行流畅、优雅、有逻辑的思维转换和语言衔接。

为了实现这一目标，吸引用户进行更持久、更高效的刻意练习，现提出功能增强方案。

## 2. 需求

### 2.1 关键词系统增强

#### 2.1.1 新增“内置词库”模式

- **需求描述：** 在现有“随机生成”模式基础上，新增“内置词库”模式。用户可以选择不同领域或场景的专业词库进行训练。
- **功能细节：**
    - 提供一个词库选择器，以下拉菜单或标签页形式展示不同分类。
    - **初步分类规划：**
        - **考试相关：** 雅思、托福
        - **专业领域：** 商务、科技、医疗
        - **生活场景：** 旅行、社交、面试
    - 选择一个分类后，系统从中随机抽取 3-5 个词作为本轮训练的关键词。
- **验收标准：**
    - 用户可以在页面上看到词库分类并能成功选择。
    - 选择分类后，能展示对应领域的关键词。
    - 系统能正确记录当前选择的词库模式。

#### 2.1.2 新增“用户自定义”模式

- **需求描述：** 允许用户自行输入一组关键词进行训练。
- **功能细节：**
    - 提供一个文本输入区域，让用户可以输入 3-8 个关键词，以逗号或换行符分隔。
    - 系统需要对输入内容进行校验，确保关键词数量和格式符合要求。
- **验收标准：**
    - 用户可以找到输入框并成功提交自定义关键词。
    - 系统能正确解析用户输入的关键词列表。
    - 输入不符合规范时，应有明确的错误提示。

### 2.2 引入新训练模式

#### 2.2.1 新增“计时挑战”模式

- **需求描述：** 增加一种带有时间限制的训练模式。
- **功能细节：**
    - 用户开始录音后，屏幕上出现倒计时（可设置为 60 秒/90 秒/120 秒）。
    - 时间结束后，录音自动停止并提交。
- **验收标准：**
    - 模式选择界面有“计时挑战”选项。
    - 录音开始后，倒计时正确显示并运行。
    - 时间耗尽后，能自动完成录音和提交过程。

#### 2.2.2 新增“概念接龙”模式 (New)

- **需求描述：** 新增一种逐一串联关键词的训练模式，以精准锻炼概念之间的“跳转”能力。
- **功能细节：**
    - 系统首先只展示第一个关键词。
    - 用户围绕该词完成第一段录音后，系统再展示第二个关键词。
    - 用户需要在之前故事的基础上，将新关键词无缝衔接进来，继续讲述。
    - 过程循环，直至所有关键词被串联成一个完整的故事。
- **验收标准：**
    - 模式选择界面有“概念接龙”选项。
    - 关键词能按顺序、逐一展示给用户。
    - 用户可以分段录音，系统能将各段录音拼接成一个完整的音频文件。

### 2.3 优化 AI 反馈与数据追踪

#### 2.3.1 新增“关键词高亮”功能

- **需求描述：** 在 AI 生成的文字稿中，高亮显示用户成功使用的关键词。
- **功能细节：**
    - 对比用户故事的文字稿和本轮的关键词列表。
    - 将文字稿中匹配到的关键词用特殊样式（如不同颜色或背景色）标出。
- **验收标准：**
    - 语音转写完成后，文字稿中用到的关键词被成功高亮。

#### 2.3.2 升级 AI 评价维度：新增“逻辑连贯性” (New)

- **需求描述：** 在现有的 AI 反馈中，增加对故事“逻辑连贯性”的评价，以评估概念跳转的质量。
- **功能细节：**
    - AI 不仅检查关键词是否被使用，更要分析其上下文关联是否自然、有逻辑。
    - 反馈内容应包含对逻辑组织的总体评价，并指出具体值得称赞或可以改进的地方。
    - **示例反馈：** “您巧妙地将‘全球变暖’与‘极端天气’构造成因果关系，过渡自然。但从‘极端天气’到‘文艺复兴’的衔接略显生硬，可以尝试寻找一个连接点，如‘极端天气这样的全球性挑战，需要我们像文艺复兴时期那样，进行一场思想上的革新’。”
- **验收标准：**
    - AI 反馈卡片中新增“逻辑连贯性”一项。
    - 该项的评价内容能反映出对故事逻辑的分析。

#### 2.3.3 新增“AI 过渡示范”功能 (New)

- **需求描述：** 当用户的逻辑连贯性得分较低时，AI 能提供具体的“过渡示范”。
- **功能细节：**
    - 在 AI 反馈中，针对两个衔接生硬的关键词，提供 1-2 个更优的过渡句式作为参考。
- **验收标准：**
    - 在符合条件的评价结果中，会出现“AI 过渡示范”模块。
    - 示范内容需包含对应的两个关键词，并构成一个逻辑通顺的句子或语段。

#### 2.3.4 新增“个人训练档案”页面

- **需求描述：** 创建一个新页面，用于展示用户的长期训练数据和进步趋势。
- **技术方案：**
    - **前端实现：**
        - 使用图表库（如 ECharts）进行数据可视化。
        - 新建`src/pages/speak/training-archive.vue`页面。
    - **数据存储：**
        - 每次训练结束后，将评价结果（流畅度、关键词使用率等核心指标）及训练日期存入数据库。
        - 需扩展`chatRecord`表结构或新建一个`trainingLog`表来存储这些结构化数据。
- **可视化图表规划：**
    - **训练日历：** 以热力图或打点方式展示用户的训练频率。
    - **能力雷达图：** 综合展示用户在流畅度、创意、语法等多个维度的平均得分。
    - **进步曲线图：** 按周或月为单位，展示核心指标（如平均分、**逻辑连贯性得分**）的变化趋势。
- **验收标准：**
    - 用户可以从某个入口进入“个人训练档案”页面。
    - 页面上的图表能正确加载并展示历史训练数据。

### 2.4 趣味性与激励机制 (New)

- **核心目标：** 引入游戏化（Gamification）元素，增强用户粘性，将枯燥的训练过程变得有趣、有回报，让用户“上瘾”。

#### 2.4.1 新增成就与徽章系统

- **需求描述：** 为了激励用户持续训练，设计一套成就体系。用户在达成特定里程碑时，会获得可视化的徽章作为奖励。
- **功能细节：**
    - 创建“成就墙”或“荣誉室”页面，展示用户已获得的徽章和待解锁的成就。
    - **徽章设计示例：**
        - **新手上路：** 完成首次关键词故事训练。
        - **持之以恒：** 连续训练 7 天 / 30 天。
        - **词汇大师：** 累计使用 100 / 500 个不重复的关键词。
        - **逻辑鬼才：** 连续 5 次获得“逻辑连贯性”高分评价。
        - **领域专家：** 完成特定内置词库（如“雅思”）下所有关键词的训练。
- **验收标准：**
    - 用户可以在个人档案中看到成就模块。
    - 当满足条件时，系统能自动授予相应徽章。
    - 徽章有明确的获得条件说明。

#### 2.4.2 引入积分与等级体系

- **需求描述：** 建立一套积分和等级系统，将用户的训练行为量化，提供即时反馈和长期成长路径。
- **功能细节：**
    - **积分获取：**
        - 完成一次训练：+10分
        - 获得高分评价：额外 +5分
        - 每日首次训练：额外 +5分
    - **等级提升：** 累计积分达到阈值后，用户等级自动提升 (Lv1, Lv2, ...)。
    - **等级权益：** 更高的等级可以解锁新的内置词库、特殊的挑战模式或自定义功能。
- **验收标准：**
    - 用户界面能展示当前的积分和等级。
    - 训练完成后积分能正确增加。
    - 达到升级条件后，等级和权益能正确更新。

#### 2.4.3 建立排行榜功能

- **需求描述：** 引入社交竞争元素，通过排行榜展示优秀用户，激发用户的表现欲和好胜心。
- **功能细节：**
    - 提供周榜和月榜。
    - **榜单维度：**
        - **勤奋榜：** 按训练总时长或次数排名。
        - **学霸榜：** 按训练平均分排名。
    - 排行榜应包含用户昵称、头像和对应的数值。为保护隐私，可提供匿名上榜选项。
- **验收标准：**
    - 应用内有排行榜入口。
    - 榜单数据能正确、准时地更新（如每周一零点刷新周榜）。
    - 用户可以查看自己的排名。

#### 2.4.4 增加社交分享功能

- **需求描述：** 允许用户将自己的成就和优秀作品分享出去，满足其炫耀心理，并利用社交网络进行自传播。
- **功能细节：**
    - 用户可以分享：
        - 获得的某个特定徽章。
        - 某次训练的高分评价卡片。
        - 自己满意的故事录音（生成一个可公开访问的分享链接）。
    - 分享时自动生成精美的海报图片，包含应用 LOGO、用户头像、成就信息和二维码。
- **验收标准：**
    - 在成就、评价和历史记录页面有分享按钮。
    - 可以成功生成分享海报或链接。
    - 分享内容在社交媒体（如微信朋友圈）上能被正常访问。

## 3. 技术方案

### 3.1 前端实现思路

- **组件化改造：**
    - **关键词模块 (`l-keywords-panel.vue`):** 将关键词的生成、选择、展示逻辑封装成一个独立的局部组件。该组件负责处理不同模式（随机、内置、自定义）下的关键词状态管理，并通过事件向父组件（`keyword-story-page.vue`）派发关键词列表。
    - **模式选择模块 (`l-mode-selector.vue`):** 将训练模式（经典、计时挑战、**概念接龙**）的选择功能封装成一个组件。
- **状态管理：**
    - 使用 Pinia 或 Vuex 来管理训练的全局状态，如当前的关键词来源模式、训练模式、用户信息等，方便跨组件共享状态。
- **UI 设计:**
    - 在`keyword-story-page.vue`页面顶部或关键词区域增加模式切换的 UI（如 Segmented Control）。
    - 自定义关键词功能可以放在一个弹窗（Popup）中，或者在关键词面板内提供一个输入框。
    - 个人训练档案的入口可以放在`我的`页面，或者在关键词故事页面的导航栏右上角增加一个档案图标按钮。
    - **新增激励模块UI：** 需要设计成就墙、排行榜页面，并在用户个人中心添加入口。分享功能需要设计专门的分享海报模板。

### 3.2 架构设计

```mermaid
graph TD
    subgraph "keyword-story-page.vue"
        A[模式选择器 l-mode-selector] --> C{当前训练模式};
        B[关键词面板 l-keywords-panel] --> D{当前关键词};
        C & D --> E[故事录入/转写];
        E --> F[提交AI评价];
    end

    subgraph "后端服务"
        G[AI评价引擎]
        H[数据库]
    end

    subgraph "新功能"
        B -- 内置/自定义 --> I[词库管理];
        F --> J[训练数据记录];
        J --> H;
        K[个人训练档案页面] -- 从 --> H;
        F -- 增加评价维度 --> G;
    end

    subgraph "趣味性与激励系统 (New)"
        J --> L[成就/积分/等级计算];
        L --> H;
        M[成就墙/排行榜页面] -- 从 --> H;
        M --> N[社交分享模块];
    end

    F --> G;
    G --> F;

    style K fill:#d4fcd7,stroke:#333,stroke-width:2px
    style J fill:#d4fcd7,stroke:#333,stroke-width:2px
    style I fill:#d4fcd7,stroke:#333,stroke-width:2px
    style L fill:#ffe4b5,stroke:#333,stroke-width:2px
    style M fill:#ffe4b5,stroke:#333,stroke-width:2px
    style N fill:#ffe4b5,stroke:#333,stroke-width:2px
```

### 3.3 技术栈与约束

- **前端框架：** Vue 3
- **UI 库：** uni-app
- **图表库：** `qiun-data-charts` (项目已有) 或引入 ECharts for uniapp
- **状态管理：** Pinia
- **AI 能力要求：** “逻辑连贯性”评价和“过渡示范”对大语言模型（LLM）的理解和生成能力提出了更高要求。需要设计更精准、更复杂的 Prompt，并可能需要对模型进行微调（Fine-tuning）以达到理想效果。
- **用户体验：** 新增多种模式和选项后，页面布局会变得更复杂。需要精心设计 UI，避免信息过载，保持简洁易用的操作流程。
- **性能：** 个人训练档案页面加载大量历史数据和图表时，可能会有性能问题。需要考虑数据分页加载或后端聚合等优化手段。
- **激励机制设计风险：** 积分、等级和成就体系的设计需要仔细权衡，避免规则过于复杂或过于简单，失去激励效果。需要警惕用户为了刷分而进行低质量训练，导致“劣币驱逐良币”。
- **社交功能隐私风险：** 排行榜和分享功能涉及用户数据，必须提供充分的隐私保护选项（如匿名、关闭分享），避免用户信息泄露风险。 
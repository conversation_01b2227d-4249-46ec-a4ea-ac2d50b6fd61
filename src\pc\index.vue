<template>
  <view class="pc-container">
    <!-- 左侧导航区 -->
    <view class="sidebar">
      <Sidebar :active-tab="activeTab" @tab-change="handleTabChange" />
    </view>

    <!-- 中间主内容区 -->
    <view class="main-content">
      <MainContent :active-tab="activeTab" />
    </view>

    <!-- 右侧详情区 -->
    <view class="detail-pane">
      <DetailPane :item-id="selectedItemId" />
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { router, getRoute } from '@/utils/tools'
import Sidebar from './components/l-sidebar.vue'
import MainContent from './components/l-main-content.vue'
import DetailPane from './components/l-detail-pane.vue'

const activeTab = ref('')
// 移除 selectedItemId，因为它现在由具体的页面管理
// const selectedItemId = ref(null)

const handleTabChange = (tabId) => {
  activeTab.value = tabId
  // 使用 router.redirect 更新 URL，避免在历史记录中创建新条目
  router.redirect('/pc/index', { pcTab: tabId })
}

onMounted(() => {
  const params = getRoute.params()
  activeTab.value = params.pcTab || 'goals' // 从 URL 或默认值恢复
})
</script>

<style scoped>
.pc-container {
  display: flex;
  height: 100vh;
  width: 100%; /* 使用 100% 以配合 max-width */
  max-width: 1600px; /* 新增：设置最大宽度 */
  margin: 0 auto; /* 新增：使容器在宽屏下居中 */
  background-color: #f0f2f5;
  box-sizing: border-box;
}

.sidebar {
  width: 60px; /* 修改：缩小侧边栏宽度 */
  flex-shrink: 0;
  box-sizing: border-box;
}

.main-content {
  width: 320px; /* 再次缩小主内容区域宽度 */
  flex-shrink: 0;
  overflow-y: auto;
  box-sizing: border-box;
}

.detail-pane {
  flex-grow: 1;
  min-width: 300px;
  overflow-y: auto;
  box-sizing: border-box;
  border-left: 1px solid #e8e8e8;
}
</style>

/**
 * 录音相关错误类型
 */
export enum RecordErrorType {
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  NOT_SUPPORTED = 'NOT_SUPPORTED',
  INITIALIZATION_FAILED = 'INITIALIZATION_FAILED',
  RECORDING_FAILED = 'RECORDING_FAILED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * 录音错误类
 */
export class RecordError extends Error {
  public type: RecordErrorType

  constructor(type: RecordErrorType, message: string) {
    super(message)
    this.type = type
    this.name = 'RecordError'
  }

  /**
   * 权限被拒绝错误
   */
  static permissionDenied(details?: string): RecordError {
    return new RecordError(RecordErrorType.PERMISSION_DENIED, `录音权限被拒绝${details ? ': ' + details : ''}`)
  }

  /**
   * 平台不支持错误
   */
  static notSupported(feature?: string): RecordError {
    return new RecordError(RecordErrorType.NOT_SUPPORTED, `当前平台不支持${feature ? feature + '功能' : '录音'}`)
  }

  /**
   * 初始化失败错误
   */
  static initializationFailed(details?: string): RecordError {
    return new RecordError(RecordErrorType.INITIALIZATION_FAILED, `录音初始化失败${details ? ': ' + details : ''}`)
  }

  /**
   * 录音失败错误
   */
  static recordingFailed(details?: string): RecordError {
    return new RecordError(RecordErrorType.RECORDING_FAILED, `录音失败${details ? ': ' + details : ''}`)
  }
}

/**
 * 数值处理类
 */
import {
  round
} from "../utils/digit.js"
import Decimal from "../utils/decimal.js"
export class NumberManager {
  constructor() {}

  /**
   * 排列组合
   */
  combine = (m, n) => {
    if (m < n || n < 0) {
      return 0;
    }
    return this.factorial(m, m - n + 1) / this.factorial(n, 1);
  }

  /**
   * 阶乘函数
   */
  factorial = (max, min) => {
    if (max >= min && max > 1) {
      return max * this.factorial(max - 1, min);
    } else {
      return 1;
    }
  }

  /**
   * 随机一个区间数
   */
  random = (min, max) => {
    if (min >= 0 && max > 0 && max >= min) {
      const gab = max - min + 1
      return Math.floor(Math.random() * gab + min)
    }
    return 0
  }

  /**
   * @description 数字格式化
   * @param {number|string} number 要格式化的数字
   * @param {number} decimals 保留几位小数
   * @param {string} decimalPoint 小数点符号
   * @param {string} thousandsSeparator 千分位符号
   * @returns {string} 格式化后的数字
   * 
   * 来源：uview
   */
  format = (number, decimals = 0, decimalPoint = '.', thousandsSeparator = ',') => {
    number = (`${number}`).replace(/[^0-9+-Ee.]/g, '')
    const n = !isFinite(+number) ? 0 : +number
    const prec = !isFinite(+decimals) ? 0 : Math.abs(decimals)
    const sep = (typeof thousandsSeparator === 'undefined') ? ',' : thousandsSeparator
    const dec = (typeof decimalPoint === 'undefined') ? '.' : decimalPoint
    let s = ''

    s = (prec ? round(n, prec) + '' : `${Math.round(n)}`).split('.')
    const re = /(-?\d+)(\d{3})/
    while (re.test(s[0])) {
      s[0] = s[0].replace(re, `$1${sep}$2`)
    }

    if ((s[1] || '').length < prec) {
      s[1] = s[1] || ''
      s[1] += new Array(prec - s[1].length + 1).join('0')
    }
    return s.join(dec)
  }

  /**
   * 是否是数字（包含小数点）
   */
  isNumber = (value) => {
    return /^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(value)
  }

  /**
   * 是否是整数
   */
  isDigits = (value) => {
    return /^\d+$/.test(value)
  }

  /**
   * 金额转大写
   */
  priceToUpper = (value) => {
    var fraction = ['角', '分'];
    var digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    var unit = [
      ['元', '万', '亿'],
      ['', '拾', '佰', '仟']
    ];
    var head = value < 0 ? '欠' : '';
    value = Math.abs(value);

    var s = '';

    for (var i = 0; i < fraction.length; i++) {
      s += (digit[Math.floor(value * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
    }
    s = s || '整';
    value = Math.floor(value);

    for (var i = 0; i < unit[0].length && value > 0; i++) {
      var p = '';
      for (var j = 0; j < unit[1].length && value > 0; j++) {
        p = digit[value % 10] + unit[1][j] + p;
        value = Math.floor(value / 10);
      }
      s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
    }
    return head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
  }

  /**
   * 高精度加法
   * v1.1.4
   */
  add(a, b) {
    let result = new Decimal(a).add(new Decimal(b));
    return result.toNumber();
  }

  /**
   * 高精度减法
   * v1.1.4
   */
  sub(a, b) {
    let result = new Decimal(a).sub(new Decimal(b));
    return result.toNumber();
  }

  /**
   * 高精度乘法
   * v1.1.4
   */
  mul(a, b) {
    let result = new Decimal(a).mul(new Decimal(b));
    return result.toNumber();
  }

  /**
   * 高精度除法
   * v1.1.4
   */
  div(a, b) {
    let result = new Decimal(a).div(new Decimal(b));
    return result.toNumber();
  }
}
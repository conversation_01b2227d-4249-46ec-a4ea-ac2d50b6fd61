<template>
  <uni-popup ref="popup" @maskClick="closePop">
    <view class="bg-white p-2">
      <uni-forms :model-value="krForm" label-position="left">
        <uni-forms-item label="值" name="val">
          <uni-easyinput v-model="krForm.val" type="number" placeholder="请输入" />
        </uni-forms-item>
        <uni-forms-item label="使用负值" name="isNeg">
          <switch @change="switch2Change" />
        </uni-forms-item>
        <uni-forms-item label="时间">
          {{ krForm.recTime }}
        </uni-forms-item>
      </uni-forms>
      <u-button type="primary" @click="onSubmit">提交</u-button>
      <u-button mt-4 type="error" @click="onDelete">删除</u-button>
    </view>
  </uni-popup>
</template>
<script setup>
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  recId: {
    type: String,
    default: '',
  },
  krInfo: {
    type: Object,
    required: true,
  },
})
const emits = defineEmits('update:show', 'onSubmit')

// 获取缓存的 kr 添加记录
// watch(
//   () => props.krInfo._id,
//   (newValue, oldValue) => {
//     if (newValue) {
//       const draft = uni.getStorageSync(`draft:kr_${newValue}`)
//       if (draft) {
//         resetKrForm({
//           ...JSON.parse(draft),
//           recTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
//         })
//       }
//     }
//   },
//   {
//     immediate: true,
//   }
// )

const [krForm, resetKrForm] = useParams({
  val: 1,
  recTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  isNeg: false,
})

// 使用负值
const switch2Change = (e) => {
  krForm.isNeg = e.detail.value
}

const popup = ref()
watch(
  () => props.show,
  (newValue, oldValue) => {
    if (newValue) {
      // 打开
      console.log('props.recId', props.recId)
      if (props.recId) {
        // 编辑
        console.log('props.krInfo===')
        console.log(props.krInfo)
        const rec = props.krInfo.recList.find((item) => item._id === props.recId)
        resetKrForm({
          ...rec,
          isNeg: rec.val < 0,
        })
      }
      popup.value.open('bottom')
    } else {
      // 关闭
      popup.value.close()
    }
  }
)

// 提交
const onSubmit = async () => {
  const { recList } = props.krInfo
  // 添加记录
  if (props.recId) {
    // 编辑
    const index = recList.findIndex((item) => item._id === props.recId)
    recList[index] = {
      ...krForm,
      val: krForm.isNeg ? krForm.val * -1 : krForm.val,
    }
  } else {
    // 添加
    recList.push({
      _id: generateUUID(),
      val: krForm.isNeg ? krForm.val * -1 : krForm.val,
      recTime: krForm.recTime,
      updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    })
  }
  await updateTaskApi(props.krInfo._id, {
    recList,
  })

  // 缓存 kr 添加记录，下次添加时填充
  // uni.setStorageSync(`draft:kr_${props.krInfo._id}`, JSON.stringify(krForm))

  emits('update:show', false)
  emits('onSubmit')
}
// 删除
const onDelete = async () => {
  const { recList } = props.krInfo
  const index = recList.findIndex((item) => item._id === props.recId)
  recList.splice(index, 1)
  await updateTaskApi(props.krInfo._id, {
    recList,
  })
  emits('update:show', false)
  emits('onSubmit')
}

// 关闭弹窗
const closePop = () => {
  emits('update:show', false)
}
</script>

<style lang="scss"></style>

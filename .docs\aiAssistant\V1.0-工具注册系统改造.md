# V1.0 - 工具注册系统改造

## 1. 改动概述

### 1.1 核心目标
- 建立标准化的工具注册机制，为后续版本的工具调用功能奠定基础
- 增强参数验证能力，借鉴MCP的验证思想提升系统健壮性
- 动态生成工具描述，让AI能够了解可用的工具函数

### 1.2 解决的问题
- **当前问题**：意图识别后无法执行实际的任务操作
- **解决方案**：建立工具注册表，为工具调用做准备
- **技术债务**：硬编码的系统提示词，缺少工具信息

### 1.3 不改动的部分
- 保持现有的SSE流式推送机制
- 保持现有的意图识别逻辑
- 保持chatStreamSSE函数的接口签名

## 2. 技术方案

### 2.1 工具注册系统架构

```javascript
// 新增：工具注册表结构（借鉴MCP设计理念）
const TOOL_REGISTRY = {
  getProjects: {
    name: 'getProjects',
    description: '获取滴答清单中的所有项目',
    usage: '当用户想要查看、搜索项目时使用',
    parameters: {
      filter: {
        type: 'string',
        required: false,
        default: '',
        description: '项目名称筛选关键词',
        validation: {
          maxLength: 50,
          pattern: '^[\\u4e00-\\u9fa5a-zA-Z0-9\\s]*$'
        }
      }
    },
    outputFormat: {
      type: 'object',
      description: '项目列表返回格式',
      properties: {
        projects: { type: 'array', description: '项目数组' },
        metadata: { type: 'object', description: '元数据信息' }
      }
    },
    metadata: {
      priority: 0.8,
      category: 'data_retrieval',
      estimatedTime: 1500,
      retryable: true
    },
    cloudFunction: 'dida-todo',
    method: 'getProjects'
  },
  
  getTasks: {
    name: 'getTasks',
    description: '获取指定项目下的任务列表',
    usage: '当用户想要查看、搜索、查询任务时使用',
    parameters: {
      projectId: {
        type: 'string',
        required: true,
        description: '项目ID，必须是有效的项目标识符',
        validation: {
          pattern: '^[a-zA-Z0-9-_]+$',
          minLength: 1
        }
      },
      completed: {
        type: 'boolean',
        required: false,
        default: false,
        description: '是否获取已完成的任务'
      },
      limit: {
        type: 'number',
        required: false,
        default: 50,
        description: '返回任务数量限制',
        validation: {
          minimum: 1,
          maximum: 100
        }
      }
    },
    outputFormat: {
      type: 'object',
      description: '任务列表返回格式',
      properties: {
        tasks: { type: 'array', description: '任务数组' },
        metadata: { type: 'object', description: '查询元数据' }
      }
    },
    metadata: {
      priority: 0.9,
      category: 'data_retrieval',
      estimatedTime: 2000,
      retryable: true
    },
    cloudFunction: 'dida-todo',
    method: 'getTasks'
  }
}
```

### 2.2 参数验证器（借鉴MCP验证机制）

```javascript
// 新增：参数验证器类
class ParameterValidator {
  static validate(toolName, parameters) {
    const tool = TOOL_REGISTRY[toolName]
    if (!tool) {
      throw new Error(`未找到工具：${toolName}`)
    }

    const validatedParams = {}
    const errors = []

    for (const [paramName, paramConfig] of Object.entries(tool.parameters)) {
      const value = parameters[paramName]
      
      // 检查必需参数
      if (paramConfig.required && (value === undefined || value === null)) {
        errors.push(`缺少必需参数：${paramName}`)
        continue
      }

      // 应用默认值
      const finalValue = value !== undefined ? value : paramConfig.default

      if (finalValue !== undefined) {
        // 类型验证
        const typeError = this.validateType(paramName, finalValue, paramConfig.type)
        if (typeError) {
          errors.push(typeError)
          continue
        }

        // 验证规则检查
        if (paramConfig.validation) {
          const validationError = this.validateRules(paramName, finalValue, paramConfig.validation)
          if (validationError) {
            errors.push(validationError)
            continue
          }
        }

        validatedParams[paramName] = finalValue
      }
    }

    if (errors.length > 0) {
      throw new Error(`参数验证失败：${errors.join(', ')}`)
    }

    return validatedParams
  }

  static validateType(paramName, value, expectedType) {
    const actualType = typeof value
    
    switch (expectedType) {
      case 'string':
        if (actualType !== 'string') {
          return `参数 ${paramName} 应为字符串类型，实际为 ${actualType}`
        }
        break
      case 'number':
        if (actualType !== 'number' || isNaN(value)) {
          return `参数 ${paramName} 应为数字类型，实际为 ${actualType}`
        }
        break
      case 'boolean':
        if (actualType !== 'boolean') {
          return `参数 ${paramName} 应为布尔类型，实际为 ${actualType}`
        }
        break
    }
    
    return null
  }

  static validateRules(paramName, value, validation) {
    // 数值范围验证
    if (typeof value === 'number') {
      if (validation.minimum !== undefined && value < validation.minimum) {
        return `参数 ${paramName} 不能小于 ${validation.minimum}`
      }
      if (validation.maximum !== undefined && value > validation.maximum) {
        return `参数 ${paramName} 不能大于 ${validation.maximum}`
      }
    }

    // 字符串验证
    if (typeof value === 'string') {
      if (validation.minLength !== undefined && value.length < validation.minLength) {
        return `参数 ${paramName} 长度不能小于 ${validation.minLength}`
      }
      if (validation.maxLength !== undefined && value.length > validation.maxLength) {
        return `参数 ${paramName} 长度不能大于 ${validation.maxLength}`
      }
      if (validation.pattern && !new RegExp(validation.pattern).test(value)) {
        return `参数 ${paramName} 格式不符合要求：${validation.pattern}`
      }
    }

    return null
  }
}
```

### 2.3 动态提示词生成器

```javascript
// 新增：动态提示词生成器
function generateToolPrompt(toolRegistry) {
  let toolPrompt = '你可以使用以下工具来帮助用户完成任务：\n\n'

  for (const [toolKey, tool] of Object.entries(toolRegistry)) {
    toolPrompt += `**${tool.name}** (${toolKey})\n`
    toolPrompt += `- 功能：${tool.description}\n`
    toolPrompt += `- 使用场景：${tool.usage}\n`
    toolPrompt += `- 优先级：${tool.metadata?.priority || 'N/A'}\n`
    toolPrompt += `- 参数：\n`

    if (tool.parameters) {
      for (const [paramName, param] of Object.entries(tool.parameters)) {
        const required = param.required ? '必需' : '可选'
        const defaultValue = param.default ? ` (默认: ${param.default})` : ''
        toolPrompt += `  - ${paramName} (${param.type}, ${required}${defaultValue}): ${param.description}\n`
      }
    }
    
    toolPrompt += '\n'
  }

  return toolPrompt
}
```

## 3. 代码修改对比

### 3.1 主要修改点

**修改前（当前代码）：**
```javascript
system = `你是一个专业的任务分析助手。你的主要职责是理解用户输入的内容，并判断用户的意图类型。

请分析用户输入内容，并将其归类为以下三种意图之一：
1. create_task: 当用户想要创建、添加、设置、安排一个新任务时
2. find_task: 当用户想要查询、搜索、查看已有的任务时
3. chat: 其他所有不属于创建任务或查找任务的内容，视为一般闲聊`
```

**修改后（V1.0版本）：**
```javascript
// 动态生成包含工具信息的系统提示词
const toolPrompt = generateToolPrompt(TOOL_REGISTRY)
system = `你是一个专业的任务分析助手。你的主要职责是理解用户输入的内容，并判断用户的意图类型。

${toolPrompt}

请分析用户输入内容，并将其归类为以下三种意图之一：
1. create_task: 当用户想要创建、添加、设置、安排一个新任务时
2. find_task: 当用户想要查询、搜索、查看已有的任务时
3. chat: 其他所有不属于创建任务或查找任务的内容，视为一般闲聊

注意：虽然现在有可用的工具，但在V1.0版本中暂不执行工具调用，仅进行意图识别。`
```

### 3.2 新增代码结构

```javascript
// 在文件顶部新增
const TOOL_REGISTRY = { /* 工具注册表 */ }
class ParameterValidator { /* 参数验证器 */ }
function generateToolPrompt(toolRegistry) { /* 动态提示词生成器 */ }

// 在chatStreamSSE函数中修改system参数的生成逻辑
```

## 4. 测试用例

### 4.1 工具注册表测试
```javascript
// 测试用例1：验证工具注册表结构
test('工具注册表结构验证', () => {
  expect(TOOL_REGISTRY.getProjects).toBeDefined()
  expect(TOOL_REGISTRY.getTasks).toBeDefined()
  expect(TOOL_REGISTRY.getProjects.name).toBe('getProjects')
})

// 测试用例2：参数验证功能
test('参数验证功能', () => {
  // 正确参数
  const validParams = { projectId: 'test-123', completed: false }
  expect(() => ParameterValidator.validate('getTasks', validParams)).not.toThrow()
  
  // 缺少必需参数
  const invalidParams = { completed: false }
  expect(() => ParameterValidator.validate('getTasks', invalidParams)).toThrow('缺少必需参数：projectId')
})
```

### 4.2 动态提示词生成测试
```javascript
test('动态提示词生成', () => {
  const prompt = generateToolPrompt(TOOL_REGISTRY)
  expect(prompt).toContain('getProjects')
  expect(prompt).toContain('getTasks')
  expect(prompt).toContain('功能：')
  expect(prompt).toContain('参数：')
})
```

### 4.3 集成测试
```javascript
test('chatStreamSSE集成测试', async () => {
  const params = {
    message: '查看我的项目列表',
    channel: mockSSEChannel
  }
  
  const result = await chatStreamSSE(params)
  expect(result.errCode).toBe(0)
  expect(result.data.intentType).toBe('find_task')
})
```

## 5. 部署指南

### 5.1 部署步骤
1. **代码更新**：将新增的工具注册表和验证器代码添加到index.obj.js
2. **测试验证**：在测试环境运行所有测试用例
3. **灰度发布**：先在10%用户中测试新版本
4. **全量发布**：确认无问题后全量发布

### 5.2 回滚方案
- 保留原有代码的备份
- 如有问题可立即回滚到原版本
- 回滚时间预计不超过5分钟

## 6. 风险评估

### 6.1 技术风险
- **风险**：新增代码可能影响现有功能
- **概率**：低
- **应对**：充分的测试覆盖和灰度发布

### 6.2 性能风险
- **风险**：动态提示词生成可能增加响应时间
- **概率**：低
- **应对**：提示词生成逻辑简单，性能影响可忽略

## 7. 下一版本预告

V1.1版本将在V1.0的基础上：
- 引入执行上下文管理器
- 实现基础的工具函数调用能力
- 支持简单的执行计划生成
- 为动态参数解析做技术铺垫

V1.0版本的工具注册表将为V1.1版本的工具调用提供完整的元数据支持。

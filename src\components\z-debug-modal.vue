<template>
  <div class="debug-modal" v-if="show">
    <div class="debug-modal-mask" @click="handleClose"></div>
    <div class="debug-modal-container">
      <div class="debug-modal-header">
        <div class="debug-modal-title">{{ title }}</div>
        <div class="debug-modal-close" @click="handleClose">
          <i class="fas fa-times"></i>
        </div>
      </div>
      <div class="debug-modal-content">
        <div class="debug-modal-type">{{ errorType }}</div>
        <div class="debug-modal-message">{{ errorMessage }}</div>
        <div class="debug-modal-details">
          <pre>{{ formatErrorDetails(errorDetails) }}</pre>
        </div>
      </div>
      <div class="debug-modal-footer">
        <div class="debug-modal-btn debug-modal-copy" @click="copyErrorInfo">
          <i class="fas fa-copy"></i> 复制错误信息
        </div>
        <div class="debug-modal-btn debug-modal-close-btn" @click="handleClose">
          关闭
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'Debug 信息'
  },
  errorType: {
    type: String,
    default: ''
  },
  errorMessage: {
    type: String,
    default: ''
  },
  errorDetails: {
    type: [Object, Array, String],
    default: null
  }
});

const emit = defineEmits(['update:show', 'close']);

// 处理关闭弹窗
const handleClose = () => {
  emit('update:show', false);
  emit('close');
};

// 格式化错误详情
const formatErrorDetails = (details) => {
  if (!details) return 'No details available';
  
  try {
    if (typeof details === 'string') {
      return details;
    }
    
    return JSON.stringify(details, null, 2);
  } catch (e) {
    return String(details);
  }
};

// 复制错误信息
const copyErrorInfo = () => {
  try {
    const errorInfo = `
Error Type: ${props.errorType}
Error Message: ${props.errorMessage}
Error Details: ${formatErrorDetails(props.errorDetails)}
    `.trim();
    
    // 使用 uni 环境下的复制API
    uni.setClipboardData({
      data: errorInfo,
      success: () => {
        uni.showToast({
          title: '复制成功',
          icon: 'success',
          duration: 2000
        });
      },
      fail: () => {
        uni.showToast({
          title: '复制失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  } catch (error) {
    console.error('复制错误信息失败', error);
    uni.showToast({
      title: '复制失败',
      icon: 'none',
      duration: 2000
    });
  }
};
</script>

<style lang="scss" scoped>
.debug-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.debug-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.debug-modal-container {
  position: relative;
  width: 80%;
  max-width: 600px;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.debug-modal-header {
  padding: 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.debug-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.debug-modal-close {
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  border-radius: 50%;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #f5f5f5;
    color: #666;
  }
}

.debug-modal-content {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

.debug-modal-type {
  font-size: 16px;
  font-weight: 600;
  color: #e74c3c;
  margin-bottom: 12px;
}

.debug-modal-message {
  font-size: 15px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 16px;
  word-break: break-word;
}

.debug-modal-details {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #eee;
  
  pre {
    font-family: monospace;
    font-size: 13px;
    color: #333;
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 300px;
    overflow-y: auto;
  }
}

.debug-modal-footer {
  padding: 12px 16px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.debug-modal-btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &.debug-modal-copy {
    background-color: #3498db;
    color: white;
    
    &:hover {
      background-color: #2980b9;
    }
  }
  
  &.debug-modal-close-btn {
    background-color: #f5f5f5;
    color: #333;
    
    &:hover {
      background-color: #e5e5e5;
    }
  }
}
</style> 
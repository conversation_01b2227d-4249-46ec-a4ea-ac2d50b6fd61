# 任务：KR 详情页统计视图 - 柱状图渲染与模拟数据

## 任务描述
此任务负责创建统计视图的 UI 组件，使用模拟数据开发和验证图表的展示效果和交互体验。需要实现一个美观、可交互的自定义柱状图，完成后进行 UI 验收。

## 所属功能模块
OKR 统计视图

## 技术实现详情

1.  **自定义柱状图组件**
    -   不使用外部图表库，使用纯 CSS 和 HTML/Vue 实现自定义柱状图组件
    -   采用 div/span 元素构建柱状图的视觉结构
    -   实现柱子高度动态变化的动画效果

2.  **创建模拟数据**
    -   在 `l-statistics.vue` 组件中创建一个 `mockChartData` 函数，生成不同场景下的模拟数据。
    -   模拟数据应覆盖以下场景：
        -   日循环任务的周期数据
        -   周循环任务的周期数据
        -   月循环任务的周期数据
        -   特定天数任务的周期数据
    -   模拟数据格式为：`[{ label: '10/10-10/16', plan: 7, completed: 5 }, ...]`。
    -   创建变量 `totalPlan` 和 `totalCompleted` 用于显示总计划量和总完成量。

3.  **图表容器和模板**
    -   在 `l-statistics.vue` 的 `<template>` 中，设置用于渲染柱状图的 div 容器结构
    -   实现顶部的统计总览区域，用于展示 `totalPlan` 和 `totalCompleted` 的值。
    -   将图表容器包裹在一个可水平滚动的 `scroll-view` 中，并设置 `scroll-x="true"`。
    -   为 `scroll-view` 内部的图表容器动态绑定宽度，确保其宽度足以容纳所有柱子而不会被压缩。

4.  **柱状图渲染**
    -   使用模拟数据初始化并渲染自定义柱状图
    -   **柱状图结构**:
        -   **X 轴**: 使用 div 元素创建刻度和标签，显示模拟数据中每个对象的 `label` 属性
        -   **Y 轴**: 使用 div 元素创建刻度和数值标签
        -   **柱子**: 使用 div 元素创建柱子，高度根据数据动态计算
            -   计划量柱子：样式设置独特颜色，高度根据 `plan` 值计算
            -   完成量柱子：样式设置独特颜色，高度根据 `completed` 值计算
        -   **柱子布局**: 计划量和完成量的柱子并排显示，每组占据固定宽度
        -   **数值显示**: 在柱子顶部或内部显示具体数值
        -   **图例**: 在图表下方使用 div 元素创建"计划量"和"完成量"的图例

5.  **样式与交互**
    -   使用 CSS 实现柱状图的所有视觉效果，包括柱子、轴线、标签等
    -   为柱子添加悬停效果，显示详细数据提示
    -   确保 `scroll-view` 的滚动条样式（如果可见）不影响整体美观。
    -   测试在不同设备宽度下的显示效果，确保图表和文字不会溢出或显示不全。
    -   确保在数据为空时，显示一个友好的空状态提示（例如："暂无统计数据"）。

6.  **UI 验收准备**
    -   准备不同场景下的模拟数据演示
    -   确保组件在不同设备上的显示效果
    -   测试交互流畅度和用户体验

## 验收标准
-   统计视图中能正确渲染出自定义柱状图。
-   图表的 X 轴和 Y 轴数据与模拟数据完全对应。
-   "计划量"和"完成量"的柱子使用不同的颜色区分。
-   当数据点较多，超出屏幕宽度时，图表可以水平顺畅滑动。
-   页面顶部的总计划量和总完成量数据正确显示。
-   图表下方的图例正确显示。
-   组件的整体视觉效果与需求设计图一致。
-   在没有进度数据的情况下，能优雅地展示空状态。

## 依赖关系
-   **上游依赖**: `task-kr-statistics-01.md`
-   **下游依赖**: `task-kr-statistics-03.md`

## 优先级
高 
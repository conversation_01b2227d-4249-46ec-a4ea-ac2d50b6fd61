# 滴答清单API云对象使用示例

## 概述

本文档提供了滴答清单API云对象的详细使用示例，包括认证流程、各功能模块的调用方法和响应格式。

## 基础调用方式

### 1. 云对象调用
```javascript
// 在uniCloud环境中调用
const didaApi = uniCloud.importObject('dida-api')

// 调用方法
const result = await didaApi.getWeChatQRCode()
console.log(result)
```

### 2. 云函数调用
```javascript
// 通过云函数调用
const result = await uniCloud.callFunction({
  name: 'dida-api',
  data: {
    action: 'getWeChatQRCode',
    params: {}
  }
})
console.log(result)
```

## 认证流程示例

### 1. 获取微信登录二维码
```javascript
// 获取二维码
const qrResult = await didaApi.getWeChatQRCode()
console.log('二维码URL:', qrResult.data.qr_url)
console.log('二维码密钥:', qrResult.data.qr_key)

// 响应格式
{
  "errCode": null,
  "errMsg": "获取微信登录二维码成功",
  "data": {
    "qr_url": "https://open.weixin.qq.com/connect/qrconnect?...",
    "qr_key": "uuid-string",
    "expire_time": 1234567890
  },
  "timestamp": *************
}
```

### 2. 轮询二维码状态
```javascript
// 轮询扫码状态
const pollResult = await didaApi.pollQRStatus(qrResult.data.qr_key, 60)
console.log('登录结果:', pollResult)

// 成功响应格式
{
  "errCode": null,
  "errMsg": "微信登录成功",
  "data": {
    "status": "success",
    "auth_token": "auth-token-string",
    "csrf_token": "csrf-token-string",
    "user_info": {
      "id": "user-id",
      "username": "用户名",
      "email": "<EMAIL>"
    }
  },
  "timestamp": *************
}
```

### 3. 设置认证会话
```javascript
// 设置认证会话
const sessionResult = await didaApi.setAuthSession(
  pollResult.data.auth_token,
  pollResult.data.csrf_token
)
console.log('会话设置结果:', sessionResult)
```

## 任务管理示例

### 1. 获取所有任务
```javascript
// 获取任务列表
const tasksResult = await didaApi.getAllTasks()
console.log('任务列表:', tasksResult.data)

// 响应格式
{
  "errCode": null,
  "errMsg": "获取任务列表成功",
  "data": {
    "tasks": [
      {
        "id": "task-id",
        "title": "任务标题",
        "content": "任务内容",
        "status": 0,
        "priority": 1,
        "project_id": "project-id",
        "created_time": "2024-01-01T00:00:00Z",
        "due_date": "2024-01-02T00:00:00Z",
        "tags": ["标签1", "标签2"]
      }
    ],
    "grouped_tasks": {
      "today": [],
      "tomorrow": [],
      "upcoming": [],
      "overdue": []
    },
    "statistics": {
      "total_tasks": 10,
      "completed_tasks": 5,
      "pending_tasks": 5
    }
  }
}
```

### 2. 搜索任务
```javascript
// 搜索任务
const searchResult = await didaApi.searchTasks("工作", 20)
console.log('搜索结果:', searchResult.data)

// 响应格式
{
  "errCode": null,
  "errMsg": "搜索任务成功",
  "data": {
    "tasks": [...],
    "total": 5,
    "keyword": "工作",
    "limit": 20
  }
}
```

### 3. 获取已完成任务
```javascript
// 获取已完成任务（分页）
const completedResult = await didaApi.getCompletedTasks(0, 50)
console.log('已完成任务:', completedResult.data)
```

## 专注管理示例

### 1. 获取专注概览
```javascript
// 获取专注统计
const pomodoroResult = await didaApi.getPomodoroGeneral()
console.log('专注概览:', pomodoroResult.data)

// 响应格式
{
  "errCode": null,
  "errMsg": "获取专注概览成功",
  "data": {
    "today": {
      "focus_time": 3600,
      "focus_time_formatted": "1小时",
      "pomodoro_count": 2,
      "task_count": 5
    },
    "week": {
      "focus_time": 18000,
      "focus_time_formatted": "5小时",
      "pomodoro_count": 10,
      "task_count": 25
    },
    "total": {
      "focus_time": 360000,
      "focus_time_formatted": "100小时",
      "pomodoro_count": 200,
      "task_count": 1000
    },
    "trend": [...],
    "ranking": {
      "current_rank": 100,
      "total_users": 10000,
      "beat_percentage": 99
    }
  }
}
```

### 2. 获取专注分布
```javascript
// 获取专注分布数据
const distributionResult = await didaApi.getFocusDistribution(
  "2024-01-01", 
  "2024-01-31"
)
console.log('专注分布:', distributionResult.data)
```

### 3. 获取专注热力图
```javascript
// 获取专注热力图
const heatmapResult = await didaApi.getFocusHeatmap(
  "2024-01-01", 
  "2024-01-31"
)
console.log('专注热力图:', heatmapResult.data)
```

## 项目管理示例

### 1. 获取项目列表
```javascript
// 获取项目列表
const projectsResult = await didaApi.getProjects()
console.log('项目列表:', projectsResult.data)

// 响应格式
{
  "errCode": null,
  "errMsg": "获取项目列表成功",
  "data": {
    "projects": [
      {
        "id": "project-id",
        "name": "项目名称",
        "color": "#FF0000",
        "icon": "icon-name",
        "task_count": 10,
        "completed_count": 5,
        "statistics": {
          "total_tasks": 10,
          "completed_tasks": 5,
          "pending_tasks": 5,
          "completion_rate": 50
        }
      }
    ],
    "grouped_projects": {
      "active": [...],
      "closed": [...],
      "archived": [...]
    },
    "statistics": {
      "total_projects": 5,
      "active_projects": 3,
      "total_tasks": 50
    }
  }
}
```

## 习惯管理示例

### 1. 获取习惯列表
```javascript
// 获取习惯列表
const habitsResult = await didaApi.getHabits()
console.log('习惯列表:', habitsResult.data)

// 响应格式
{
  "errCode": null,
  "errMsg": "获取习惯列表成功",
  "data": {
    "habits": [
      {
        "id": "habit-id",
        "name": "习惯名称",
        "description": "习惯描述",
        "frequency": "daily",
        "current_streak": 7,
        "best_streak": 30,
        "statistics": {
          "completion_rate": 85,
          "total_days": 100,
          "completed_days": 85
        }
      }
    ],
    "grouped_habits": {
      "active": [...],
      "archived": [...]
    },
    "statistics": {
      "total_habits": 5,
      "active_habits": 3,
      "average_streak": 15
    }
  }
}
```

### 2. 获取本周习惯统计
```javascript
// 获取本周习惯统计
const weekStatsResult = await didaApi.getWeekCurrentStatistics()
console.log('本周习惯统计:', weekStatsResult.data)
```

## 统计服务示例

### 1. 获取用户排名
```javascript
// 获取用户排名统计
const rankingResult = await didaApi.getUserRanking()
console.log('用户排名:', rankingResult.data)
```

### 2. 获取通用统计
```javascript
// 获取通用统计信息
const statsResult = await didaApi.getGeneralStatistics()
console.log('通用统计:', statsResult.data)
```

## 用户服务示例

### 1. 获取用户信息
```javascript
// 获取用户资料
const profileResult = await didaApi.getUserProfile()
console.log('用户信息:', profileResult.data)

// 响应格式
{
  "errCode": null,
  "errMsg": "获取用户信息成功",
  "data": {
    "basic_info": {
      "id": "user-id",
      "username": "用户名",
      "email": "<EMAIL>",
      "nickname": "昵称",
      "avatar": "头像URL"
    },
    "account_info": {
      "is_premium": false,
      "registration_date": "2024-01-01T00:00:00Z",
      "login_count": 100
    },
    "settings": {
      "timezone": "Asia/Shanghai",
      "language": "zh_CN",
      "theme": "light"
    },
    "statistics": {
      "total_tasks": 1000,
      "completed_tasks": 800,
      "total_projects": 10,
      "focus_time": 360000
    }
  }
}
```

## 错误处理示例

### 1. 参数错误
```javascript
try {
  const result = await didaApi.searchTasks(null) // 缺少必需参数
} catch (error) {
  console.error('错误:', error)
  // 错误响应格式
  {
    "errCode": "PARAM_IS_NULL",
    "errMsg": "参数 keyword 不能为空",
    "data": null,
    "timestamp": *************
  }
}
```

### 2. 认证错误
```javascript
try {
  // 未设置认证会话时调用需要认证的方法
  const result = await didaApi.getAllTasks()
} catch (error) {
  console.error('认证错误:', error)
  // 错误响应
  {
    "errCode": "AUTH_REQUIRED",
    "errMsg": "需要先设置认证会话",
    "data": null,
    "timestamp": *************
  }
}
```

## 最佳实践

### 1. 错误处理
```javascript
async function safeApiCall(apiFunction, ...args) {
  try {
    const result = await apiFunction(...args)
    if (result.errCode === null) {
      return result.data
    } else {
      throw new Error(result.errMsg)
    }
  } catch (error) {
    console.error('API调用失败:', error.message)
    return null
  }
}

// 使用示例
const tasks = await safeApiCall(didaApi.getAllTasks)
if (tasks) {
  console.log('获取到任务:', tasks.tasks.length)
}
```

### 2. 批量操作
```javascript
// 批量获取数据
async function getAllData() {
  const [tasks, projects, habits] = await Promise.all([
    didaApi.getAllTasks(),
    didaApi.getProjects(),
    didaApi.getHabits()
  ])
  
  return {
    tasks: tasks.data,
    projects: projects.data,
    habits: habits.data
  }
}
```

### 3. 分页处理
```javascript
// 分页获取已完成任务
async function getAllCompletedTasks() {
  const allTasks = []
  let page = 0
  const limit = 50
  
  while (true) {
    const result = await didaApi.getCompletedTasks(page, limit)
    const tasks = result.data.tasks
    
    allTasks.push(...tasks)
    
    if (tasks.length < limit) {
      break // 已获取所有数据
    }
    
    page++
  }
  
  return allTasks
}
```

---

*更多使用示例和最佳实践将持续更新*

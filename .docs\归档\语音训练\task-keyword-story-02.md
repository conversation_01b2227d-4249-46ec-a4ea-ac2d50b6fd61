# 任务：在关键词讲故事页面集成记录弹窗组件

- **任务ID**: `task-keyword-story-02`
- **所属功能模块**: 语音训练 - 关键词讲故事
- **优先级**: 高
- **状态**: 未开始
- **依赖关系**: `task-keyword-story-01`

---

## 1. 任务描述
将上一步创建的 `z-record-list-popup` 组件集成到 `src/pages/speak/keyword-story-page.vue` 页面中。在页面上添加一个入口按钮，用于打开此弹窗，并处理弹窗的选择和关闭事件。

此阶段，我们依然使用**模拟数据**来驱动弹窗，目的是确保组件间的通信和基础交互正常工作。

## 2. 技术实现详情
### 引入组件
-   在 `keyword-story-page.vue` 的 `<script setup>` 中，引入 `z-record-list-popup` 组件。
    ```javascript
    import ZRecordListPopup from '@/components/z-record-list-popup/z-record-list-popup.vue';
    ```

### 添加UI入口
-   在页面的 `z-page-navbar` 右侧区域，增加一个“复述记录”的图标按钮。
    ```html
    <template #right>
      <div class="action-btn" @click="showRecordPopup = true" aria-label="复述记录">
        <i class="fas fa-history"></i>
      </div>
      <!-- 现有重置按钮 -->
    </template>
    ```

### 状态管理
-   在 `keyword-story-page.vue` 的 `<script setup>` 中，定义以下 `ref`：
    -   `showRecordPopup`: `ref(false)` - 控制弹窗的显示状态。
    -   `mockRecords`: `ref([])` - 用于存放传递给弹窗的模拟数据。在 `onLoad` 或 `setup` 中为其填充与上个任务相同的模拟数据。

### 组件集成
-   在 `keyword-story-page.vue` 的 `<template>` 中，添加组件实例：
    ```html
    <z-record-list-popup
      v-model:show="showRecordPopup"
      title="复述记录"
      :records="mockRecords"
      @select-item="handleSelectRecord"
      @close="showRecordPopup = false"
    />
    ```

### 事件处理
-   创建一个新的函数 `handleSelectRecord(record)`。
-   在此函数内部，暂时仅使用 `console.log(record)` 打印被选中的记录，以验证事件通信是否成功。

## 3. 验收标准
-   [ ] `z-record-list-popup` 组件被成功引入到 `keyword-story-page.vue` 中。
-   [ ] 导航栏右侧出现“复述记录”图标按钮。
-   [ ] 点击该按钮可以正常打开记录弹窗，并显示模拟数据。
-   [ ] 关闭弹窗后，页面状态正常。
-   [ ] 在弹窗中点击任意一条记录，控制台会打印出对应的记录对象。 
<template>
  <view>
    <view class="flex justify-center items-center flex-col bg-white py-10 mb-2">
      <view class="text-30 color-gray"
        >{{ dayjs(currentMonth).format('M') }} 月 {{ categoryMap[currentCategory] }} 共支出
      </view>
      <view class="text-50 color-black font-bold mt-2">
        <text>￥</text>
        {{ totalAmount.toFixed(2) }}
      </view>
    </view>
    <view class="bg-white py-2 px-4 mb-2">
      <view class="flex items-center">
        <view
          v-for="item in typeList"
          :key="item.key"
          :class="['rounded-1 px-2 py-1 mr-2', { 'active-type': selectedType === item.key }]"
          @click="selectType(item.key)"
        >
          {{ item.name }}
        </view>
      </view>
      <uni-swipe-action>
        <uni-swipe-action-item
          v-for="(item, i) in bills"
          :key="item._id"
          :right-options="options"
          @click="onClick($event, item)"
        >
          <view class="flex items-center px-4" @click="showBillPop(item._id)">
            <view class="bg-[#3eb575] mr-4 w-70 h-70 rounded-[50%] color-white flex justify-center items-center">
              {{ categoryMap[item.category] ? categoryMap[item.category][0] : '无' }}
            </view>
            <view class="flex-1 py-4" style="border-bottom: 1px solid #eee">
              <view class="flex justify-between font-medium">
                <text>{{ categoryMap[item.category] }}</text>
                <text class="font-semibold">-{{ item.amount.toFixed(2) }}</text>
              </view>
              <view class="mt-1 flex justify-between items-center text-gray text-25">
                <view>{{ item.remark || item.merchants }}</view>
                <view>{{ dayjs(item.date).format('M 月 D 日 HH:mm') }}</view>
              </view>
            </view>
          </view>
        </uni-swipe-action-item>
      </uni-swipe-action>
    </view>
    <l-new-bill :editId="editId" v-model:open="isNewBillOpen" @submit="newBill"></l-new-bill>
  </view>
</template>

<script setup lang="ts">
const typeList = ref([
  {
    name: '按金额',
    key: 'amount',
  },
  {
    name: '按日期',
    key: 'date',
  },
])

const selectedType = ref('amount')

// 滑动操作选项
const options = [
  {
    text: '删除',
    key: 'delete',
    style: {
      backgroundColor: '#dd524d',
    },
  },
]

// 新增：声明 bills 与 categoryMap
const bills = ref([])
const month = ref([])

interface CategoryMap {
  [key: string]: string
}
const categoryMap = ref<CategoryMap>({})

// 新增：声明 currentMonth
const currentMonth = ref('')
const currentCategory = ref('')

// 新增：声明 totalAmount 计算属性
const totalAmount = computed(() => {
  return bills.value.reduce((sum, bill) => sum + bill.amount, 0)
})

// 新增：选择排序类型
const selectType = (key) => {
  selectedType.value = key
  sortBills()
}

// 新增：排序账单
const sortBills = () => {
  if (selectedType.value === 'amount') {
    bills.value.sort((a, b) => b.amount - a.amount)
  } else if (selectedType.value === 'date') {
    bills.value.sort((a, b) => new Date(b.date) - new Date(a.date))
  }
}

// 通过 onLoad 获取路由参数
onLoad((options) => {
  currentMonth.value = options.month || dayjs().format('YYYY-MM')
  currentCategory.value = options.category || ''
  init()
})

// 修改 init 函数以支持分类筛选
const init = async () => {
  const res = await getBillListApi()
  const categories = await getCategoryApi()

  categories.forEach((c) => {
    categoryMap.value[c._id] = c.name
  })

  const targetMonth = currentMonth.value
  bills.value = res.filter((bill) => {
    const isSameMonth = dayjs(bill.date).isSame(targetMonth, 'month')
    const isSameCategory = currentCategory.value ? bill.category === currentCategory.value : true
    return isSameMonth && isSameCategory
  })
  sortBills()
}

// 删除当前这条账单
const onClick = async (e, item) => {
  if (e.position === 'right' && e.content.key === 'delete') {
    // 删除账单
    try {
      await delBillApi(item._id)
      // 重新初始化账单数据
      // 这里同样要继续按原路由参数过滤
      // 可直接再次调用 init()，如需保证同一月份，可存下当前 month
      init()
      uni.showToast({
        title: '删除成功',
        icon: 'success',
      })
    } catch (error) {
      uni.showToast({
        title: '删除失败',
        icon: 'none',
      })
    }
  }
}

// 添加编辑相关变量
const editId = ref('')
const isNewBillOpen = ref(false)

// 添加编辑方法
const showBillPop = (id) => {
  editId.value = id
  isNewBillOpen.value = true
}

// 添加提交回调方法
const newBill = () => {
  isNewBillOpen.value = false
  init()
}
</script>

<style scoped lang="scss">
.pages {
  padding: 15px;
}

.active-type {
  background-color: #3eb5753b;
  color: #3eb575;
}
</style>

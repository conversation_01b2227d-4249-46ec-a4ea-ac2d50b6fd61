<template>
  <view class="px-3 py-4 bg-white r-5">
    <!-- 普通任务 100 -->
    <view
      v-if="todo.type === 'todo'"
      :class="{ 'todo-done': todo.status === 1 || isDisabled }"
      class="flex justify-center items-center"
      @click="onTaskDetail('todo')"
    >
      <view class="mr-2">
        <view :class="{ 'text-red line-through': todo.status === 1 }" class="text-30 break-all text-black">{{
          todo.title
        }}</view>
        <view class="mt-2 text-22 text-gray">
          <text class="mr-2" v-if="todo.okr">目标：{{ todo.okr.title }}</text>
          <text class="mr-2" v-if="todo.repeatFlag">循环</text>
          <text class="mr-2"
            >{{ dayjs(todo.startDate).format('MM-DD') }}
            <text v-if="todo.startDate !== todo.endDate">~ {{ dayjs(todo.endDate).format('MM-DD') }}</text>
          </text>
          <u-icon v-if="todo.content" name="order"></u-icon>
        </view>
      </view>
      <z-checkbox :status="todo.status" :disabled="isDisabled" @on-change="onChangeStatus" />
    </view>
    <!-- 关键结果 -->
    <view v-else class="flex justify-center items-center" @click="onTaskDetail('kr')">
      <keep-alive>
        <u-circle-progress duration="1000" :width="60" :border-width="6" :active-color="color" :percent="progress">
        </u-circle-progress>
      </keep-alive>
      <view class="ml-3">
        <view class="text-black">{{ todo.title }}</view>
        <view v-if="todo.okr" class="mt-1 text-20 text-gray">{{ todo.okr.title }}</view>
      </view>
      <view
        class="relative ml-auto wh-40 border-4 b-solid r50"
        :style="{
          borderColor: isDisabled ? '#ccc' : color,
        }"
        @click.stop="onAddRec()"
      >
        <view
          class="add-line"
          :style="{
            backgroundColor: color,
          }"
        />
        <view
          class="add-line"
          :style="{
            backgroundColor: color,
          }"
        />
      </view>
    </view>
  </view>
</template>
<script setup>
import { defineProps, defineEmits, watch, ref } from 'vue'
const props = defineProps({
  todo: {
    type: Object,
    default: () => ({}),
  },
  day: {
    type: String,
    default: '',
  },
  color: {
    type: String,
    default: '#ee734a',
  },
})
const emits = defineEmits('update:visible', 'onChange', 'onAddRec')

const todoStatus = ref(props.todo.status)

// 进度条
const progress = computed(() => {
  const { todo } = props
  if (todo.type === 'kr') {
    return ((todo.curVal - todo.initVal) / (todo.tgtVal - todo.initVal)) * 100
  }
  return 0
})

// 添加量化任务进度
const onAddRec = () => {
  emits('onAddRec', {
    editId: props.todo._id,
    type: 'kr',
  })
}

// 禁用打卡
const isDisabled = computed(() => {
  const { todo } = props
  // 重复任务
  if (todo.repeatFlag) {
    // 没有传入日期，不能打卡
    if (!props.day) return true
  }
  return false
})

// 跳转到关键结果详情
const onTaskDetail = (type) => {
  const { okrId, _id } = props.todo
  if (type === 'todo') {
    // 普通任务，跳转到任务编辑页面
    uni.navigateTo({
      url: `/pages/okr/krEdit?okrId=${okrId}&id=${_id}`,
    })
  } else {
    // 关键结果，跳转到关键结果详情页面
    uni.navigateTo({
      url: `/pages/okr/krDetail?okrId=${okrId}&id=${_id}`,
    })
  }
}

// 修改任务状态
const onChangeStatus = async (status) => {
  const { todo } = props
  const params = {}
  if (!todo.repeatFlag) {
    // 普通 todo
    params.status = status
    if (params.status === 1) params.completeTime = new Date().toISOString()
  } else {
    // 重复 todo
    if (isDisabled.value) return
    const curDate = dayjs(props.day).format('YYYY-MM-DD')
    params.compInfos = todo.compInfos || []

    // 判断是否已经打卡
    const index = params.compInfos.findIndex((e) => e.date === curDate)
    if (index !== -1) {
      // 当天已打卡、取消打卡
      params.compInfos.splice(index, 1)
    } else {
      // 当天未打卡，添加打卡记录
      params.compInfos.push({
        _id: generateUUID(),
        date: curDate,
        recTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        val: todo.progVal,
      })
    }
  }
  await updateTaskApi(todo._id, params)
  // TODO 需要兼容更新失败的情况
  emits('onChange')
}
</script>
<style lang="scss" scope>
.add-line {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20rpx;
  height: 3rpx;
  border-radius: 3rpx;

  &:first-child {
    transform: translate(-50%, -50%);
  }
  &:last-child {
    transform: translate(-50%, -50%) rotate(90deg);
  }
}
.todo-done {
  filter: grayscale(100%);
}

.your-element {
  animation: checkmark 0.2s ease-out;
}
@keyframes checkmark {
  0% {
    transform: translateY(5px);
  }
  50% {
    transform: translateY(-2px);
  }
  100% {
    transform: translateY(0px);
  }
}
</style>

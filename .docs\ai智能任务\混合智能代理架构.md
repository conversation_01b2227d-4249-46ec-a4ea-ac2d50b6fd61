## 1. 背景
当前 AI 助手的 "一次性规划" 架构在处理结构化任务时具有高效率和低成本的优势。然而，其核心能力受限于预定义的结构化工具集，存在两大短板：
1.  **能力边界**：在 `task` 模式下，无法处理需要高级语言理解能力的开放式任务（如文本摘要、内容创作等）。
2.  **鲁棒性不足**：执行阶段遇到预期之外的错误时，缺乏动态的、智能的恢复机制，容易导致整个任务链中断。

为了突破这些限制，提升系统的智能化水平和可靠性，现提出混合智能代理架构升级。

## 2. 需求
### 功能需求
1.  **规划阶段引入大模型工具**
    - 在 `task` 模式的工具集 (`TOOL_REGISTRY`) 中，注册一个通用的“大模型处理工具”。
    - 当 AI 规划器判断用户任务无法由现有结构化工具完成时，应能自动选择并规划调用此大模型工具。
    - 此工具应能处理包括但不限于：文本摘要、内容翻译、创意写作、知识问答等非结构化任务。

2.  **执行阶段引入大模型作为错误顾问**
    - 当 `executeRobustPlan` 执行引擎在执行某个步骤时捕获到关键错误（如参数解析失败、工具调用失败且无法通过简单重试解决），应触发“咨询大模型”机制。
    - 系统需能动态构建包含完整上下文的“调试提示词”，并调用大模型请求解决方案。
    - 大模型应能返回结构化的解决方案，包括：
        - **修正 (FIX)**: 提供一个修正后的、可替代当前失败步骤的新步骤对象。
        - **反馈 (RESPOND)**: 当问题无法修正时，提供一段对用户友好的、可直接显示的解释文本。
    - 执行引擎需能根据大模型返回的方案，执行“替换步骤并重试”或“中止任务并反馈用户”的操作。

## 3. 技术方案
### 实现思路
#### 规划阶段集成
1.  **工具注册 (`config.js`)**: 在 `TOOL_REGISTRY` 中新增一个伪工具，例如 `processTextWithAI`。此工具的 `description` 需清晰描述其作为通用语言处理器的能力。
2.  **工具调用 (`tools.js`)**: 修改 `callRealTool` 函数。增加一个逻辑分支判断，当 `toolName` 为 `processTextWithAI` 时，不再调用 `uniCloud.callFunction`，而是构造特定任务的提示词（如摘要、翻译），直接调用豆包大模型 API，并将结果返回。

#### 执行阶段集成
1.  **错误处理器 (`EnhancedErrorHandler.js`)**: 新增一个错误处理策略 `consultAIAdvisor`。
2.  **调试提示词构建**: 在 `consultAIAdvisor` 中，动态创建一个包含以下信息的调试提示词：失败步骤详情、错误信息、用户原始指令、当前上下文`context`中的关键数据。
3.  **执行引擎 (`executor.js`)**: 在 `catch` 块中调用 `EnhancedErrorHandler`。当返回的策略是“咨询 AI”时：
    - 调用大模型获取结构化的解决方案 JSON。
    - 若方案为 `FIX`，则使用 `payload` 中的新步骤对象替换计划中的失败步骤，并 `continue` 重试循环。
    - 若方案为 `RESPOND`，则中止整个执行计划，并将 `payload` 中的文本作为错误信息向上抛出。

### 架构设计
```mermaid
graph TD
    subgraph "规划阶段"
        A[用户输入] --> B{意图识别};
        B -- task --> C[Planner生成执行计划];
        C -- 结构化任务 --> D[生成工具调用步骤];
        C -- 非结构化任务 --> E[生成'processTextWithAI'步骤];
    end

    subgraph "执行阶段"
        F[Executor执行计划] --> G[执行步骤];
        G -- 成功 --> H[存储结果, 继续下一步];
        G -- 失败 --> I{错误处理器};
        I -- 可重试 --> G;
        I -- 关键失败 --> J[咨询AI顾问];
        J --> K{返回解决方案};
        K -- FIX --> G;
        K -- RESPOND --> L[中止并向用户反馈];
    end

    C --> F;
```

## 4. 风险评估
- **潜在风险**:
    - 大模型作为错误顾问时，其解决方案可能并非最优或依然错误，导致循环失败。
    - 两次大模型调用（规划、执行/纠错）会增加特定任务的延迟和费用。
- **应对策略**:
    - 为“咨询 AI 顾问”机制设置调用次数上限，避免无限循环。
    - 详细记录咨询过程的日志，便于分析和优化调试提示词。
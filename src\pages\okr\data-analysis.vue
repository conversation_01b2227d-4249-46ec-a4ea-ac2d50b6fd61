<template>
  <view class="content">
    <!-- 顶部区域 -->
    <view class="status-bar-placeholder"></view>
    <view class="app-header">
      <view class="page-title">数据分析</view>
      <view class="segment-control">
        <view class="segment-item" :class="{ active: currentView === 'day' }" @click="changeView('day')">天</view>
        <view class="segment-item" :class="{ active: currentView === 'week' }" @click="changeView('week')">周</view>
        <view class="segment-item" :class="{ active: currentView === 'month' }" @click="changeView('month')">月</view>
      </view>
    </view>

    <!-- 加载状态显示 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 日统计部分 -->
    <view v-else-if="currentView === 'day'">
      <!-- 日期选择器 -->
      <view class="section-card date-selector">
        <view class="date-nav">
          <view class="date-nav-btn" @click="changeDate('day', -1)">
            <i class="fas fa-chevron-left"></i>
          </view>
          <view class="current-date">{{ formattedDate.day }}</view>
          <view class="date-nav-btn" @click="changeDate('day', 1)">
            <i class="fas fa-chevron-right"></i>
          </view>
        </view>
      </view>

      <!-- 今日数据卡片 -->
      <view class="section-card">
        <view class="section-title">
          <i class="fas fa-chart-line"></i>
          今日生产力
        </view>
        <view class="progress-row">
          <view
            class="progress-circle"
            :style="{
              background: `conic-gradient(var(--color-primary) 0% ${dayStats.completionRate}%, var(--color-gray-200) ${dayStats.completionRate}% 100%)`,
            }"
          >
            <view class="progress-content">
              <view class="progress-percentage">{{ dayStats.completionRate }}</view>
              <view class="progress-label">今日完成率</view>
            </view>
          </view>
          <view class="progress-details">
            <view class="progress-item">
              <view class="progress-item-header">
                <view class="progress-item-title">推动的目标数</view>
                <view class="progress-item-value">{{ dayStats.goalProgress.current }}/{{ dayStats.goalProgress.total }}</view>
              </view>
              <view class="chart-bar">
                <view class="chart-fill" :class="getProgressClass(dayStats.goalProgress.percentage)" :style="{ width: dayStats.goalProgress.percentage + '%' }"></view>
              </view>
            </view>
            <view class="progress-item">
              <view class="progress-item-header">
                <view class="progress-item-title">推动的任务数</view>
                <view class="progress-item-value">{{ dayStats.taskProgress.current }}/{{ dayStats.taskProgress.total }}</view>
              </view>
              <view class="chart-bar">
                <view class="chart-fill" :class="getProgressClass(dayStats.taskProgress.percentage)" :style="{ width: dayStats.taskProgress.percentage + '%' }"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 今日任务统计 -->
      <view class="section-card">
        <view class="section-title">
          <i class="fas fa-tasks"></i>
          今日任务情况
        </view>
        <view v-if="dailyTasks.length === 0" class="empty-state">
          <i class="fas fa-clipboard-list"></i>
          <view>今日暂无任务</view>
        </view>
        <view class="time-block" v-else v-for="(task, index) in dailyTasks" :key="'task-' + index">
          <view class="time-block-header">
            <view class="time-block-title">{{ task.title }}</view>
            <view class="time-block-objective" v-if="task.objective">
              <i class="fas fa-bullseye"></i> {{ task.objective }}
            </view>
          </view>
          <view>
            <view class="time-block-progress">
              <view class="chart-bar">
                <view
                  class="chart-fill"
                  :class="getProgressClass(task.progressRate)"
                  :style="{ width: task.progressRate + '%' }"
                ></view>
              </view>
              <view class="progress-text">{{ task.progressRate }}%</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 周统计部分 -->
    <view v-else-if="currentView === 'week'">
      <!-- 周选择器 -->
      <view class="section-card date-selector">
        <view class="date-nav">
          <view class="date-nav-btn" @click="changeDate('week', -1)">
            <i class="fas fa-chevron-left"></i>
          </view>
          <view class="current-date-container">
            <view class="current-date">{{ formattedDate.week }}</view>
            <view class="date-range">{{ formattedDate.weekDateRange }}</view>
          </view>
          <view class="date-nav-btn" @click="changeDate('week', 1)">
            <i class="fas fa-chevron-right"></i>
          </view>
        </view>
      </view>

      <!-- 本周生产力卡片 -->
      <view class="section-card">
        <view class="section-title">
          <i class="fas fa-chart-line"></i>
          本周生产力
        </view>
        <view class="progress-row">
          <view
            class="progress-circle"
            :style="{
              background: `conic-gradient(var(--color-primary) 0% ${weekStats.completionRate}%, var(--color-gray-200) ${weekStats.completionRate}% 100%)`,
            }"
          >
            <view class="progress-content">
              <view class="progress-percentage">{{ weekStats.completionRate }}</view>
              <view class="progress-label">周平均完成率</view>
            </view>
          </view>
          <view class="progress-details">
            <view class="progress-item">
              <view class="progress-item-header">
                <view class="progress-item-title">推动的目标数</view>
                <view class="progress-item-value">{{ weekStats.goalProgress.current }}/{{ weekStats.goalProgress.total }}</view>
              </view>
              <view class="chart-bar">
                <view class="chart-fill" :class="getProgressClass(weekStats.goalProgress.percentage)" :style="{ width: weekStats.goalProgress.percentage + '%' }"></view>
              </view>
            </view>
            <view class="progress-item">
              <view class="progress-item-header">
                <view class="progress-item-title">推动的任务数</view>
                <view class="progress-item-value">{{ weekStats.taskProgress.current }}/{{ weekStats.taskProgress.total }}</view>
              </view>
              <view class="chart-bar">
                <view
                  class="chart-fill"
                  :class="getProgressClass(weekStats.taskProgress.percentage)"
                  :style="{ width: weekStats.taskProgress.percentage + '%' }"
                ></view>
              </view>
            </view>
          </view>
        </view>

        <view class="chart-grid">
          <view class="chart-day" v-for="(day, index) in weekStats.dailyData" :key="'day-' + index">
            <view class="chart-day-bar">
              <view class="chart-day-fill" :class="getProgressClass(day.completionRate)" :style="{ height: day.completionRate + '%' }"></view>
            </view>
            <view class="chart-day-label">{{ day.label }}</view>
          </view>
        </view>
      </view>


      <!-- 本周任务情况 -->
      <view class="section-card">
        <view class="section-title">
          <i class="fas fa-tasks"></i>
          本周任务情况
        </view>
        <view v-if="weekStats.tasks.length === 0" class="empty-state">
          <i class="fas fa-clipboard-list"></i>
          <view>本周暂无任务</view>
        </view>
        <view class="time-block" v-else v-for="(task, index) in weekStats.tasks" :key="'week-task-' + index">
          <view class="time-block-header">
            <view class="time-block-title">{{ task.title }}</view>
            <view class="time-block-objective" v-if="task.objective">
              <i class="fas fa-bullseye"></i> {{ task.objective }}
            </view>
          </view>
          <view>
            <view class="time-block-progress">
              <view class="chart-bar">
                <view
                  class="chart-fill"
                  :class="getProgressClass(task.progressRate)"
                  :style="{ width: task.progressRate + '%' }"
                ></view>
              </view>
              <view class="progress-text">{{ task.progressRate }}%</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 月统计部分 -->
    <view v-else-if="currentView === 'month'">
      <!-- 月选择器 -->
      <view class="section-card date-selector">
        <view class="date-nav">
          <view class="date-nav-btn" @click="changeDate('month', -1)">
            <i class="fas fa-chevron-left"></i>
          </view>
          <view class="current-date">{{ formattedDate.month }}</view>
          <view class="date-nav-btn" @click="changeDate('month', 1)">
            <i class="fas fa-chevron-right"></i>
          </view>
        </view>
      </view>

      <!-- 本月数据卡片 -->
      <view class="section-card">
        <view class="section-title">
          <i class="fas fa-chart-line"></i>
          本月效率分析
        </view>
        <view class="progress-row">
          <view
            class="progress-circle"
            :style="{
              background: `conic-gradient(var(--color-primary) 0% ${monthStats.completionRate}%, var(--color-gray-200) ${monthStats.completionRate}% 100%)`,
            }"
          >
            <view class="progress-content">
              <view class="progress-percentage">{{ monthStats.completionRate }}</view>
              <view class="progress-label">月平均完成率</view>
            </view>
          </view>
          <view class="progress-details">
            <view v-if="monthStats.weeklyData.length === 0" class="empty-state">
              <i class="fas fa-calendar-week"></i>
              <view>本月暂无数据</view>
            </view>
            <view
              class="progress-item"
              v-else
              v-for="(week, index) in monthStats.weeklyData"
              :key="'month-week-' + index"
            >
              <view class="progress-item-header">
                <view class="progress-item-title">{{ week.name }}</view>
                <view class="progress-item-value">{{ week.completionRate }}</view>
              </view>
              <view class="chart-bar">
                <view
                  class="chart-fill"
                  :class="getProgressClass(week.completionRate)"
                  :style="{ width: week.completionRate + '%' }"
                ></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 本月目标完成情况 -->
      <view class="section-card">
        <view class="section-title">
          <i class="fas fa-bullseye"></i>
          本月目标完成情况
        </view>
        <view class="progress-row">
          <view
            class="progress-circle"
            :style="{
              background: `conic-gradient(var(--color-primary) 0% ${monthStats.totalCompletionRate}%, var(--color-gray-200) ${monthStats.totalCompletionRate}% 100%)`,
            }"
          >
            <view class="progress-content">
              <view class="progress-percentage">{{ monthStats.totalCompletionRate }}</view>
              <view class="progress-label">总体进度</view>
            </view>
          </view>
          <view class="progress-details">
            <view v-if="monthStats.goals.length === 0" class="empty-state">
              <i class="fas fa-bullseye"></i>
              <view>本月暂无目标</view>
            </view>
            <view class="progress-item" v-else v-for="(goal, index) in monthStats.goals" :key="'month-goal-' + index">
              <view class="progress-item-header">
                <view class="progress-item-title">{{ goal.name }}</view>
                <view class="progress-item-value">{{ goal.progress }}</view>
              </view>
              <view class="chart-bar">
                <view
                  class="chart-fill"
                  :class="getProgressClass(goal.progress)"
                  :style="{ width: goal.progress + '%' }"
                ></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 本月任务情况 -->
      <view class="section-card">
        <view class="section-title">
          <i class="fas fa-tasks"></i>
          本月任务情况
        </view>
        <view v-if="monthStats.tasks && monthStats.tasks.length === 0" class="empty-state">
          <i class="fas fa-clipboard-list"></i>
          <view>本月暂无任务</view>
        </view>
        <view class="time-block" v-else v-for="(task, index) in monthStats.tasks" :key="'month-task-' + index">
          <view class="time-block-header">
            <view class="time-block-title">{{ task.title }}</view>
            <view class="time-block-objective" v-if="task.objective">
              <i class="fas fa-bullseye"></i> {{ task.objective }}
            </view>
          </view>
          <view>
            <view class="time-block-info">
              <view class="time-block-status" :class="task.progressRate >= 100 ? 'status-completed' : 'status-progress'">
                {{ task.progressRate >= 100 ? '已完成' : '进行中' }}
              </view>
              <view class="time-block-dates" v-if="task.startDate && task.endDate">
                {{ task.startDate }} - {{ task.endDate }}
              </view>
            </view>
            <view class="time-block-progress">
              <view class="chart-bar">
                <view
                  class="chart-fill"
                  :class="getProgressClass(task.progressRate)"
                  :style="{ width: task.progressRate + '%' }"
                ></view>
              </view>
              <view class="progress-text">{{ task.progressRate }}%</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, inject } from 'vue'
import dayjs from 'dayjs'
import { getTaskListApi } from '@/api/task'
import { getOkrListApi } from '@/api/okr'
import * as rrule from '@/utils/rrrrule'
import { getUrlParams } from '@/utils/tools'
import { StatusUtils } from '@/constants/status'

// 注入刷新触发器
const reloadTrigger = inject('reloadTrigger', ref({count: 0, tabKey: ''}))

// 当前选择的视图 (day, week, month)
const currentView = ref('day')

// 当前日期
const currentDate = ref(new Date())

// 加载状态
const loading = ref(false)

// 原始任务数据
const tasks = ref([])

// 判断当前页面是否激活
const isActiveTab = computed(() => {
  const urlParams = getUrlParams()
  const urlTabKey = urlParams?.tab
  return urlTabKey && reloadTrigger.value.tabKey === urlTabKey
})

// 格式化日期显示
const formattedDate = computed(() => {
  const date = currentDate.value
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()

  // 获取当前是一年中的第几周
  const firstDayOfYear = new Date(year, 0, 1)
  const pastDaysOfYear = (date - firstDayOfYear) / 86400000
  const weekNumber = Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7)

  // 计算当前周的周一和周日
  const currentDay = date.getDay() // 0 是周日，1-6 是周一到周六
  const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay
  const sundayOffset = currentDay === 0 ? 0 : 7 - currentDay

  const monday = new Date(date)
  monday.setDate(date.getDate() + mondayOffset)

  const sunday = new Date(date)
  sunday.setDate(date.getDate() + sundayOffset)

  return {
    day: `${year}年${month}月${day}日`,
    week: `${year}年第${weekNumber}周`,
    weekDateRange: `${monday.getMonth() + 1}月${monday.getDate()}日-${sunday.getMonth() + 1}月${sunday.getDate()}日`,
    month: `${year}年${month}月`,
    // 添加格式化的日期，用于后续使用
    formattedDay: dayjs(date).format('YYYY-MM-DD'),
    formattedMonday: dayjs(monday).format('YYYY-MM-DD'),
    formattedSunday: dayjs(sunday).format('YYYY-MM-DD'),
    formattedMonthStart: dayjs(date).startOf('month').format('YYYY-MM-DD'),
    formattedMonthEnd: dayjs(date).endOf('month').format('YYYY-MM-DD'),
  }
})

// 日视图数据
const dayStats = reactive({
  completionRate: 0,
  goalProgress: {
    current: 0,
    total: 0,
    percentage: 0
  },
  taskProgress: {
    current: 0,
    total: 0,
    percentage: 0
  },

})

// 日任务列表
const dailyTasks = reactive([])

// 周视图数据
const weekStats = reactive({
  completionRate: 0,
  goalProgress: {
    current: 0,
    total: 0,
    percentage: 0
  },
  taskProgress: {
    current: 0,
    total: 0,
    percentage: 0
  },
  totalProgress: 0,
  dailyData: [
    { label: '一', completionRate: 0 },
    { label: '二', completionRate: 0 },
    { label: '三', completionRate: 0 },
    { label: '四', completionRate: 0 },
    { label: '五', completionRate: 0 },
    { label: '六', completionRate: 0 },
    { label: '日', completionRate: 0 },
  ],
  goals: [],
  tasks: []
})

// 月视图数据
const monthStats = reactive({
  completionRate: 0,
  totalCompletionRate: 0,
  goalProgress: {
    current: 0,
    total: 0,
    percentage: 0
  },
  taskProgress: {
    current: 0,
    total: 0,
    percentage: 0
  },
  weeklyData: [],
  goals: [],
  tasks: []
})

// 切换视图 (天/周/月)
const changeView = (view) => {
  currentView.value = view
  onInit()
}

// 加载数据
const onInit = async () => {
  loading.value = true
  try {
    if (currentView.value === 'day') {
      await fetchDayViewData()
    } else if (currentView.value === 'week') {
      await fetchWeekViewData()
    } else if (currentView.value === 'month') {
      await fetchMonthViewData()
    }
  } catch (error) {
    console.error('加载数据失败：', error)
    uni.showToast({
      title: '数据加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 获取日视图数据
const fetchDayViewData = async () => {
  try {
    // 获取当前选择日期的任务
    await fetchTasksByDate(formattedDate.value.formattedDay)
    
    // 计算日视图统计数据
    calculateDayStats()
    
    // 格式化任务列表用于显示
    formatDailyTasks()
  } catch (error) {
    console.error('获取日视图数据失败：', error)
    throw error
  }
}

/**
 * 获取指定日期的任务数据
 *
 * 该函数执行以下操作：
 * 1. 获取指定日期的所有 KR 类型任务
 * 2. 查询并关联目标信息
 * 3. 过滤任务（重复任务和暂停目标）
 * 4. 计算每个任务在选定日期的完成情况
 * 5. 格式化任务数据用于显示
 *
 * @param {string} date - 要获取任务的日期，格式为 YYYY-MM-DD
 */
const fetchTasksByDate = async (date) => {
  try {
    // 1. 获取所有 KR 类型任务
    let allTasks = []
    try {
      allTasks = await getTaskListApi(`type == 'kr' && startDate <= '${date}' && endDate >= '${date}'`)
    } catch (apiError) {
      console.error('[错误] 获取任务列表失败', apiError)
      allTasks = [] // 保证流程继续
    }

    // 2. 查询目标信息
    const okrIds = allTasks.map((item) => item.okrId).filter((id) => id && id !== '')
    const uniqueOkrIds = [...new Set(okrIds)]

    let targetList = []
    if (uniqueOkrIds.length > 0) {
      try {
        const idsQuery = uniqueOkrIds.map((id) => `"${id}"`).join(',')
        targetList = await getOkrListApi(`_id, anyOf(${idsQuery})`)
      } catch (targetError) {
        console.error('[错误] 获取目标失败', targetError)
        targetList = []
      }
    }

    // 关联目标信息到任务对象
    const tasksWithRelations = allTasks.map((item) => {
      try {
        const target = targetList.find((t) => t._id === item.okrId) || null
        return { ...item, target }
      } catch (relationError) {
        console.error(`[错误] 关联任务时出错:`, relationError)
        return { ...item, target: null }
      }
    })

    // 3. 过滤任务（重复任务和暂停目标）
    const filteredTasks = tasksWithRelations.filter((item) => {
      try {
        // 检查是否为重复任务，如果是则判断当前选中日期是否应该显示
        if (item.repeatFlag) {
          try {
            const progressData = rrule.getTaskProgressData(date, item.repeatFlag, item.recList || [], {
              totalRequired: item.dailyTarget || 1,
              startDate: item.startDate,
              endDate: item.endDate,
            })
            // 保存 progressData 到任务对象中
            item.progressData = progressData
            // 通过 isOccurrenceToday 判断是否是发生日
            const shouldShow = progressData.isOccurrenceToday
            if (!shouldShow) {
              return false // 不符合重复规则，过滤掉
            }
          } catch (error) {
            console.error(`[错误] 判断重复任务规则出错:`, error)
            // 出错时不过滤
          }
        } else {
          // 对于非重复任务，创建一个基本的 progressData
          item.progressData = {
            completedToday: 0,
            isOccurrenceToday: true,
          }
        }

        // 检查目标是否为活跃状态（只显示进行中的目标）
        const isHide = !item.target || !StatusUtils.isOkrActive(item.target.status)
        return !isHide
      } catch (filterError) {
        console.error(`[错误] 过滤任务时出错:`, filterError)
        return false
      }
    })

    // 保存过滤后的任务数据
    tasks.value = filteredTasks
  } catch (error) {
    console.error('[错误] 获取任务失败：', error)
    uni.showToast({
      title: '获取任务失败',
      icon: 'none',
    })
    tasks.value = []
  }
}

/**
 * 获取指定日期范围内的任务数据
 *
 * 该函数执行以下操作：
 * 1. 获取日期范围内的所有 KR 类型任务
 * 2. 查询并关联目标信息
 * 3. 过滤任务（暂停目标）
 * 4. 计算每个任务在选定日期范围内的完成情况
 * 
 * @param {string} startDate - 开始日期，格式为 YYYY-MM-DD
 * @param {string} endDate - 结束日期，格式为 YYYY-MM-DD
 * @returns {Array} 处理后的任务数组
 */
const fetchTasksForDateRange = async (startDate, endDate) => {
  try {
    // 1. 获取日期范围内的所有 KR 类型任务
    let allTasks = []
    try {
      // 查询条件：任务类型为 kr，且任务的时间范围与所选日期范围有交集
      allTasks = await getTaskListApi(`type == 'kr' && startDate <= '${endDate}' && endDate >= '${startDate}'`)
    } catch (apiError) {
      console.error('[错误] 获取任务列表失败', apiError)
      allTasks = [] // 保证流程继续
    }

    // 2. 查询目标信息
    const okrIds = allTasks.map((item) => item.okrId).filter((id) => id && id !== '')
    const uniqueOkrIds = [...new Set(okrIds)]

    let targetList = []
    if (uniqueOkrIds.length > 0) {
      try {
        const idsQuery = uniqueOkrIds.map((id) => `"${id}"`).join(',')
        targetList = await getOkrListApi(`_id, anyOf(${idsQuery})`)
      } catch (targetError) {
        console.error('[错误] 获取目标失败', targetError)
        targetList = []
      }
    }

    // 关联目标信息到任务对象
    const tasksWithRelations = allTasks.map((item) => {
      try {
        const target = targetList.find((t) => t._id === item.okrId) || null
        return { ...item, target }
      } catch (relationError) {
        console.error(`[错误] 关联任务时出错:`, relationError)
        return { ...item, target: null }
      }
    })

    // 3. 过滤暂停的目标
    const filteredTasks = tasksWithRelations.filter((item) => {
      try {
        // 检查目标是否为活跃状态（只显示进行中的目标）
        const isHide = !item.target || !StatusUtils.isOkrActive(item.target.status)
        return !isHide
      } catch (filterError) {
        console.error(`[错误] 过滤任务时出错:`, filterError)
        return false
      }
    })

    // 4. 计算每个任务在日期范围内的完成情况
    // 创建一个包含日期范围内所有日期的数组
    const dateRange = []
    const currentDate = new Date(startDate)
    const lastDate = new Date(endDate)
    
    while (currentDate <= lastDate) {
      dateRange.push(dayjs(currentDate).format('YYYY-MM-DD'))
      currentDate.setDate(currentDate.getDate() + 1)
    }

    // 为每个任务添加日期范围内的进度数据
    const tasksWithProgressData = filteredTasks.map(task => {
      // 为每个任务创建一个 weekProgress 对象，包含每天的进度数据
      const weekProgress = {}
      
      // 遍历日期范围内的每一天
      dateRange.forEach(date => {
        let dailyProgress = { completedToday: 0, isOccurrenceToday: false }
        
        // 检查是否为重复任务
        if (task.repeatFlag) {
          try {
            // 获取任务在特定日期的进度数据
            dailyProgress = rrule.getTaskProgressData(date, task.repeatFlag, task.recList || [], {
              totalRequired: task.dailyTarget || 1,
              startDate: task.startDate,
              endDate: task.endDate,
            })
          } catch (error) {
            console.error(`[错误] 计算重复任务进度出错:`, error, task)
          }
        } else {
          // 对于非重复任务，检查日期是否在任务范围内
          const taskStartDate = new Date(task.startDate)
          const taskEndDate = new Date(task.endDate)
          const checkDate = new Date(date)
          
          if (checkDate >= taskStartDate && checkDate <= taskEndDate) {
            // 非重复任务在其日期范围内每天都应该显示
            dailyProgress.isOccurrenceToday = true
            
            // 从 recList 中查找当天的完成记录
            if (task.recList && task.recList.length > 0) {
              const todayRecord = task.recList.find(rec => rec.date === date)
              if (todayRecord) {
                dailyProgress.completedToday = todayRecord.value || 0
              }
            }
          }
        }
        
        // 保存当天的进度数据
        weekProgress[date] = dailyProgress
      })
      
      // 将周进度数据添加到任务对象
      return { ...task, weekProgress }
    })
    
    return tasksWithProgressData
  } catch (error) {
    console.error('[错误] 获取日期范围内的任务失败：', error)
    return []
  }
}

// 计算日视图统计数据
const calculateDayStats = () => {
  try {
    // 1. 统计当天所有任务总数
    const totalTasksCount = tasks.value.length
    
    // 2. 统计当天有进度变化的任务数量
    const tasksWithProgressChange = tasks.value.filter(task => {
      // 从 progressData 中获取当天完成的进度
      const completedToday = task.progressData?.completedToday || 0
      return completedToday > 0
    }).length
    
    // 3. 统计当天所有任务涉及到的目标总数（去重）
    const uniqueGoalIds = [...new Set(tasks.value.map(task => task.okrId).filter(id => id))]
    const totalGoalsCount = uniqueGoalIds.length
    
    // 4. 统计当天有进度变化的目标数量（去重）
    const goalsWithProgressChange = [...new Set(
      tasks.value
        .filter(task => {
          // 从 progressData 中获取当天完成的进度
          const completedToday = task.progressData?.completedToday || 0
          return completedToday > 0 && task.okrId
        })
        .map(task => task.okrId)
    )].length
    
    // 5. 计算进度百分比
    const taskProgressPercentage = totalTasksCount > 0 
      ? Math.round((tasksWithProgressChange / totalTasksCount) * 100)
      : 0
      
    const goalProgressPercentage = totalGoalsCount > 0 
      ? Math.round((goalsWithProgressChange / totalGoalsCount) * 100)
      : 0
    
    // 6. 计算今日完成率（根据任务进度值总和计算）
    // 计算今天推动的任务进度值总和
    const totalCompletedProgress = tasks.value.reduce((sum, task) => {
      const completedToday = task.progressData?.completedToday || 0
      return sum + completedToday
    }, 0)
    
    // 计算当天总任务进度值总和
    const totalRequiredProgress = tasks.value.reduce((sum, task) => {
      return sum + (task.dailyTarget || 1)
    }, 0)
    
    // 计算完成率
    const completionRate = totalRequiredProgress > 0
      ? Math.round((totalCompletedProgress / totalRequiredProgress) * 100)
      : 0
    
    // 7. 更新统计数据
    dayStats.completionRate = completionRate
    dayStats.goalProgress.current = goalsWithProgressChange
    dayStats.goalProgress.total = totalGoalsCount
    dayStats.goalProgress.percentage = goalProgressPercentage
    dayStats.taskProgress.current = tasksWithProgressChange
    dayStats.taskProgress.total = totalTasksCount
    dayStats.taskProgress.percentage = taskProgressPercentage
  } catch (error) {
    console.error('[错误] 计算日视图统计数据失败：', error)
    // 保持默认值
  }
}

// 格式化任务列表用于显示
const formatDailyTasks = () => {
  try {
    // 清空当前任务列表
    dailyTasks.splice(0, dailyTasks.length)
    
    // 格式化任务数据
    const formattedTasks = tasks.value.map(task => {
      // 计算任务进度百分比（今日该任务推动的进度值/今日该任务的目标值）* 100%
      const completedToday = task.progressData?.completedToday || 0
      const dailyTarget = task.dailyTarget || 1
      const progressRate = dailyTarget > 0 
        ? Math.round((completedToday / dailyTarget) * 100) 
        : 0
      
      // 计算计划进度百分比
      const planRate = 100
      
      // 任务状态不再需要，已移除
      
      return {
        title: task.title || '无标题',
        objective: task.target ? task.target.title : '',
        progressRate: progressRate,
        planRate: planRate
      }
    })
    
    // 按完成进度比例排序
    formattedTasks.sort((a, b) => {
      const aCompletionRate = a.progressRate / a.planRate * 100
      const bCompletionRate = b.progressRate / b.planRate * 100
      return bCompletionRate - aCompletionRate
    })
  
    // 添加到 dailyTasks
    dailyTasks.push(...formattedTasks)
  } catch (error) {
    console.error('[错误] 格式化任务列表失败：', error)
  }
}

/**
 * 获取周视图数据
 *
 * 该函数执行以下操作：
 * 1. 获取当前选择周的日期范围内的任务
 * 2. 计算周视图统计数据
 * 3. 格式化任务列表用于显示
 */
const fetchWeekViewData = async () => {
  try {
    // 1. 获取当前选择周的开始和结束日期
    const startDate = formattedDate.value.formattedMonday
    const endDate = formattedDate.value.formattedSunday
    
    // 2. 获取日期范围内的任务
    const weekTasks = await fetchTasksForDateRange(startDate, endDate)
    
    // 3. 计算周视图统计数据
    calculateWeekStats(weekTasks, startDate, endDate)
    
    // 4. 格式化任务列表用于显示
    formatWeekTasks(weekTasks)
  } catch (error) {
    console.error('[错误] 获取周视图数据失败：', error)
    throw error
  }
}

/**
 * 计算周视图统计数据
 *
 * @param {Array} weekTasks - 周内任务数据
 * @param {string} startDate - 周开始日期
 * @param {string} endDate - 周结束日期
 */
const calculateWeekStats = (weekTasks, startDate, endDate) => {
  try {
    // 1. 创建一个包含周内所有日期的数组
    const dateRange = []
    const currentDate = new Date(startDate)
    const lastDate = new Date(endDate)
    
    while (currentDate <= lastDate) {
      dateRange.push(dayjs(currentDate).format('YYYY-MM-DD'))
      currentDate.setDate(currentDate.getDate() + 1)
    }
    
    // 2. 计算每日完成率，并更新 dailyData
    const dailyCompletionRates = []
    
    dateRange.forEach((date, index) => {
      // 统计当天所有任务总数
      const dayTasks = weekTasks.filter(task => task.weekProgress[date]?.isOccurrenceToday)
      const totalTasksCount = dayTasks.length
      
      // 计算当天的完成率
      let completionRate = 0
      
      if (totalTasksCount > 0) {
        // 计算当天推动的任务进度值总和
        const totalCompletedProgress = dayTasks.reduce((sum, task) => {
          const completedToday = task.weekProgress[date]?.completedToday || 0
          return sum + completedToday
        }, 0)
        
        // 计算当天总任务进度值总和
        const totalRequiredProgress = dayTasks.reduce((sum, task) => {
          return sum + (task.dailyTarget || 1)
        }, 0)
        
        // 计算完成率
        completionRate = totalRequiredProgress > 0
          ? Math.round((totalCompletedProgress / totalRequiredProgress) * 100)
          : 0
      }
      
      // 保存当天完成率
      dailyCompletionRates.push(completionRate)
      
      // 更新 dailyData
      weekStats.dailyData[index].completionRate = completionRate
    })
    
    // 3. 计算周平均完成率
    const weekAvgCompletionRate = dailyCompletionRates.length > 0
      ? Math.round(dailyCompletionRates.reduce((sum, rate) => sum + rate, 0) / dailyCompletionRates.length)
      : 0
    
    weekStats.completionRate = weekAvgCompletionRate
    
    // 4. 统计推动的目标数
    // 4.1 统计周内所有任务涉及到的目标总数（去重）
    const uniqueGoalIds = [...new Set(weekTasks.map(task => task.okrId).filter(id => id))]
    const totalGoalsCount = uniqueGoalIds.length
    
    // 4.2 统计周内有进度变化的目标数量（去重）
    const goalsWithProgressChange = new Set()
    
    weekTasks.forEach(task => {
      if (!task.okrId) return
      
      // 检查任务在周内是否有进度变化
      const hasProgress = Object.values(task.weekProgress).some(
        progress => progress.completedToday > 0
      )
      
      if (hasProgress) {
        goalsWithProgressChange.add(task.okrId)
      }
    })
    
    const goalsWithProgressCount = goalsWithProgressChange.size
    
    // 4.3 计算目标进度百分比
    const goalProgressPercentage = totalGoalsCount > 0 
      ? Math.round((goalsWithProgressCount / totalGoalsCount) * 100)
      : 0
    
    // 5. 统计推动的任务数
    // 5.1 周内所有任务总数
    const totalTasksCount = weekTasks.length
    
    // 5.2 统计周内有进度变化的任务数量
    const tasksWithProgressChange = weekTasks.filter(task => {
      // 检查任务在周内是否有进度变化
      return Object.values(task.weekProgress).some(
        progress => progress.completedToday > 0
      )
    }).length
    
    // 5.3 计算任务进度百分比
    const taskProgressPercentage = totalTasksCount > 0 
      ? Math.round((tasksWithProgressChange / totalTasksCount) * 100)
      : 0
    
    // 6. 更新统计数据
    weekStats.goalProgress.current = goalsWithProgressCount
    weekStats.goalProgress.total = totalGoalsCount
    weekStats.goalProgress.percentage = goalProgressPercentage
    
    weekStats.taskProgress.current = tasksWithProgressChange
    weekStats.taskProgress.total = totalTasksCount
    weekStats.taskProgress.percentage = taskProgressPercentage
    
  } catch (error) {
    console.error('[错误] 计算周视图统计数据失败：', error)
    // 保持默认值
  }
}

/**
 * 格式化周任务数据用于显示
 *
 * @param {Array} weekTasks - 周内任务数据
 */
const formatWeekTasks = (weekTasks) => {
  try {
    // 清空当前任务列表
    weekStats.tasks = []
    
    // 格式化任务数据
    const formattedTasks = weekTasks.map(task => {
      // 计算周内总进度值（本周该任务推动的进度值总和）
      const totalCompleted = Object.values(task.weekProgress).reduce((sum, progress) => {
        return sum + (progress.completedToday || 0)
      }, 0)
      
      // 计算周内应完成的总进度值（本周该任务的目标值总和）
      const totalRequired = Object.values(task.weekProgress).reduce((sum, progress) => {
        return progress.isOccurrenceToday ? sum + (task.dailyTarget || 1) : sum
      }, 0)
      
      // 计算进度百分比（本周该任务推动的进度值总和/本周该任务的目标值总和）* 100%
      const progressRate = totalRequired > 0 
        ? Math.round((totalCompleted / totalRequired) * 100) 
        : 0
      
      // 组装任务数据，结构与日视图保持一致
      return {
        title: task.title || '无标题',
        objective: task.target ? task.target.title : '',
        progressRate: progressRate
      }
    })
    
    // 按完成进度比例排序（与日视图保持一致）
    formattedTasks.sort((a, b) => {
      return b.progressRate - a.progressRate
    })
    
    // 添加到 weekStats.tasks
    weekStats.tasks.push(...formattedTasks)
    
    // 更新目标进度列表
    updateWeekGoals(weekTasks)
  } catch (error) {
    console.error('[错误] 格式化周任务列表失败：', error)
  }
}

/**
 * 更新周视图的目标进度列表
 *
 * @param {Array} weekTasks - 周内任务数据
 */
const updateWeekGoals = (weekTasks) => {
  try {
    // 清空当前目标列表
    weekStats.goals = []
    
    // 获取所有唯一的目标
    const uniqueGoals = new Map()
    
    // 遍历所有任务，收集目标信息
    weekTasks.forEach(task => {
      if (!task.target || !task.okrId) return
      
      if (!uniqueGoals.has(task.okrId)) {
        uniqueGoals.set(task.okrId, {
          id: task.okrId,
          name: task.target.title,
          tasks: [],
          totalCompleted: 0,
          totalRequired: 0
        })
      }
      
      const goalData = uniqueGoals.get(task.okrId)
      
      // 添加任务到目标
      goalData.tasks.push(task)
      
      // 计算周内总进度值
      const taskCompleted = Object.values(task.weekProgress).reduce((sum, progress) => {
        return sum + (progress.completedToday || 0)
      }, 0)
      
      // 计算周内应完成的总进度值
      const taskRequired = Object.values(task.weekProgress).reduce((sum, progress) => {
        return progress.isOccurrenceToday ? sum + (task.dailyTarget || 1) : sum
      }, 0)
      
      // 更新目标总计
      goalData.totalCompleted += taskCompleted
      goalData.totalRequired += taskRequired
    })
    
    // 格式化目标数据
    const formattedGoals = Array.from(uniqueGoals.values()).map(goal => {
      // 计算目标进度百分比
      const progress = goal.totalRequired > 0 
        ? Math.round((goal.totalCompleted / goal.totalRequired) * 100) 
        : 0
      
      return {
        name: goal.name,
        progress: progress
      }
    })
    
    // 按进度排序
    formattedGoals.sort((a, b) => b.progress - a.progress)
    
    // 添加到 weekStats.goals
    weekStats.goals.push(...formattedGoals)
  } catch (error) {
    console.error('[错误] 更新周目标列表失败：', error)
  }
}

// 修改日期
const changeDate = (type, offset) => {
  if (type === 'day') {
    // 天的切换
    currentDate.value.setDate(currentDate.value.getDate() + offset)
  } else if (type === 'week') {
    // 周的切换
    currentDate.value.setDate(currentDate.value.getDate() + offset * 7)
  } else if (type === 'month') {
    // 月的切换
    currentDate.value.setMonth(currentDate.value.getMonth() + offset)
  }
  // 触发日期计算
  currentDate.value = new Date(currentDate.value)

  // 加载新数据
  onInit()
}

// 根据进度获取样式类
const getProgressClass = (progress) => {
  if (progress >= 80) return 'success'
  if (progress >= 50 && progress < 80) return '' // 默认蓝色
  return 'warning' // 低于50%黄色
}

// 监听日期变化
watch(() => formattedDate.value.formattedDay, (newDate) => {
  if (currentView.value === 'day') {
    onInit()
  }
})

// 监听周日期变化
watch(() => formattedDate.value.formattedMonday, (newDate) => {
  if (currentView.value === 'week') {
    onInit()
  }
})

/**
 * 获取月视图数据
 *
 * 该函数执行以下操作：
 * 1. 获取当前选择月的开始和结束日期
 * 2. 获取日期范围内的任务
 * 3. 计算月视图统计数据
 * 4. 格式化任务列表用于显示
 */
const fetchMonthViewData = async () => {
  try {
    // 1. 获取当前选择月的开始和结束日期
    const startDate = formattedDate.value.formattedMonthStart
    const endDate = formattedDate.value.formattedMonthEnd
    
    // 2. 获取日期范围内的任务
    const monthTasks = await fetchTasksForDateRange(startDate, endDate)
    
    // 3. 计算月视图统计数据
    calculateMonthStats(monthTasks, startDate, endDate)
    
    // 4. 格式化任务列表用于显示
    formatMonthTasks(monthTasks)
  } catch (error) {
    console.error('[错误] 获取月视图数据失败：', error)
    throw error
  }
}

/**
 * 计算月视图统计数据
 *
 * @param {Array} monthTasks - 月内任务数据
 * @param {string} startDate - 月开始日期
 * @param {string} endDate - 月结束日期
 */
const calculateMonthStats = (monthTasks, startDate, endDate) => {
  try {
    // 1. 获取月内的所有周数据
    const weekRanges = getMonthWeeks(startDate, endDate)
    
    // 2. 计算每周的完成率
    const weeklyCompletionRates = []
    const weeklyData = []
    
    weekRanges.forEach((weekRange, index) => {
      const { weekStart, weekEnd, weekName } = weekRange
      
      // 获取该周内任务的完成情况
      const weekCompletionRate = calculateWeekCompletionRate(monthTasks, weekStart, weekEnd)
      
      // 保存周完成率
      weeklyCompletionRates.push(weekCompletionRate)
      
      // 添加到周数据数组
      weeklyData.push({
        name: weekName,
        completionRate: weekCompletionRate
      })
    })
    
    // 3. 计算月平均完成率（所有周完成率的平均值）
    const monthAvgCompletionRate = weeklyCompletionRates.length > 0
      ? Math.round(weeklyCompletionRates.reduce((sum, rate) => sum + rate, 0) / weeklyCompletionRates.length)
      : 0
    
    // 4. 统计推动的目标数
    // 4.1 统计月内所有任务涉及到的目标总数（去重）
    const uniqueGoalIds = [...new Set(monthTasks.map(task => task.okrId).filter(id => id))]
    const totalGoalsCount = uniqueGoalIds.length
    
    // 4.2 统计月内有进度变化的目标数量（去重）
    const goalsWithProgressChange = new Set()
    
    monthTasks.forEach(task => {
      if (!task.okrId) return
      
      // 检查任务在月内是否有进度变化
      const hasProgress = Object.values(task.weekProgress).some(
        progress => progress.completedToday > 0
      )
      
      if (hasProgress) {
        goalsWithProgressChange.add(task.okrId)
      }
    })
    
    const goalsWithProgressCount = goalsWithProgressChange.size
    
    // 4.3 计算目标进度百分比
    const goalProgressPercentage = totalGoalsCount > 0 
      ? Math.round((goalsWithProgressCount / totalGoalsCount) * 100)
      : 0
    
    // 5. 统计推动的任务数
    // 5.1 月内所有任务总数
    const totalTasksCount = monthTasks.length
    
    // 5.2 统计月内有进度变化的任务数量
    const tasksWithProgressChange = monthTasks.filter(task => {
      // 检查任务在月内是否有进度变化
      return Object.values(task.weekProgress).some(
        progress => progress.completedToday > 0
      )
    }).length
    
    // 5.3 计算任务进度百分比
    const taskProgressPercentage = totalTasksCount > 0 
      ? Math.round((tasksWithProgressChange / totalTasksCount) * 100)
      : 0
    
    // 6. 更新统计数据
    monthStats.completionRate = monthAvgCompletionRate
    monthStats.totalCompletionRate = monthAvgCompletionRate // 保持一致
    monthStats.weeklyData = weeklyData
    
    monthStats.goalProgress.current = goalsWithProgressCount
    monthStats.goalProgress.total = totalGoalsCount
    monthStats.goalProgress.percentage = goalProgressPercentage
    
    monthStats.taskProgress.current = tasksWithProgressChange
    monthStats.taskProgress.total = totalTasksCount
    monthStats.taskProgress.percentage = taskProgressPercentage
    
  } catch (error) {
    console.error('[错误] 计算月视图统计数据失败：', error)
    // 保持默认值
  }
}

/**
 * 获取月内所有周的开始和结束日期
 * 
 * @param {string} monthStart - 月开始日期
 * @param {string} monthEnd - 月结束日期
 * @returns {Array} 包含每周日期范围的数组
 */
const getMonthWeeks = (monthStart, monthEnd) => {
  const weeks = []
  
  // 第一天是周几（0 是周日，1-6 是周一到周六）
  const firstDay = new Date(monthStart)
  // 计算第一周的开始日期（周一）
  const firstWeekStartDay = firstDay.getDay()
  const firstWeekStartOffset = firstWeekStartDay === 0 ? -6 : 1 - firstWeekStartDay
  
  // 第一周的开始日期（可能在上个月）
  const firstWeekStart = new Date(firstDay)
  firstWeekStart.setDate(firstDay.getDate() + firstWeekStartOffset)
  
  // 如果第一周开始日期早于月开始日期，则使用月开始日期
  const startDate = new Date(Math.max(firstWeekStart.getTime(), firstDay.getTime()))
  const endDateObj = new Date(monthEnd)
  
  // 当前处理的日期
  let currentDate = new Date(startDate)
  let weekIndex = 1
  
  while (currentDate <= endDateObj) {
    // 计算本周的结束日期（周日）
    const currentWeekEnd = new Date(currentDate)
    const daysToSunday = currentDate.getDay() === 0 ? 0 : 7 - currentDate.getDay()
    currentWeekEnd.setDate(currentDate.getDate() + daysToSunday)
    
    // 如果周结束日期超出了月结束日期，使用月结束日期
    const weekEnd = new Date(Math.min(currentWeekEnd.getTime(), endDateObj.getTime()))
    
    // 添加周日期范围
    weeks.push({
      weekStart: dayjs(currentDate).format('YYYY-MM-DD'),
      weekEnd: dayjs(weekEnd).format('YYYY-MM-DD'),
      weekName: `第 ${weekIndex} 周`
    })
    
    // 下一周的开始日期（当前周结束日期的下一天）
    const nextWeekStart = new Date(weekEnd)
    nextWeekStart.setDate(weekEnd.getDate() + 1)
    
    // 更新当前日期为下一周开始日期
    currentDate = nextWeekStart
    weekIndex++
    
    // 如果已经超出月范围，退出循环
    if (currentDate > endDateObj) {
      break
    }
  }
  
  return weeks
}

/**
 * 计算指定周内的任务完成率
 * 
 * @param {Array} tasks - 所有任务数据
 * @param {string} weekStart - 周开始日期
 * @param {string} weekEnd - 周结束日期
 * @returns {number} 完成率百分比
 */
const calculateWeekCompletionRate = (tasks, weekStart, weekEnd) => {
  try {
    // 创建一个包含周内所有日期的数组
    const dateRange = []
    const startDateObj = new Date(weekStart)
    const endDateObj = new Date(weekEnd)
    const currentDate = new Date(startDateObj)
    
    while (currentDate <= endDateObj) {
      dateRange.push(dayjs(currentDate).format('YYYY-MM-DD'))
      currentDate.setDate(currentDate.getDate() + 1)
    }
    
    // 计算每日完成率，然后取平均值
    const dailyCompletionRates = []
    
    dateRange.forEach(date => {
      // 统计当天所有任务总数
      const dayTasks = tasks.filter(task => task.weekProgress[date]?.isOccurrenceToday)
      const totalTasksCount = dayTasks.length
      
      // 计算当天的完成率
      let completionRate = 0
      
      if (totalTasksCount > 0) {
        // 计算当天推动的任务进度值总和
        const totalCompletedProgress = dayTasks.reduce((sum, task) => {
          const completedToday = task.weekProgress[date]?.completedToday || 0
          return sum + completedToday
        }, 0)
        
        // 计算当天总任务进度值总和
        const totalRequiredProgress = dayTasks.reduce((sum, task) => {
          return sum + (task.dailyTarget || 1)
        }, 0)
        
        // 计算完成率
        completionRate = totalRequiredProgress > 0
          ? Math.round((totalCompletedProgress / totalRequiredProgress) * 100)
          : 0
      }
      
      // 保存当天完成率
      dailyCompletionRates.push(completionRate)
    })
    
    // 计算周平均完成率
    const weekAvgCompletionRate = dailyCompletionRates.length > 0
      ? Math.round(dailyCompletionRates.reduce((sum, rate) => sum + rate, 0) / dailyCompletionRates.length)
      : 0
    
    return weekAvgCompletionRate
  } catch (error) {
    console.error('[错误] 计算周完成率失败：', error)
    return 0
  }
}

/**
 * 格式化月任务数据用于显示
 *
 * @param {Array} monthTasks - 月内任务数据
 */
const formatMonthTasks = (monthTasks) => {
  try {
    // 清空当前任务列表和目标列表
    monthStats.tasks = []
    monthStats.goals = []
    
    // 格式化任务数据
    const formattedTasks = monthTasks.map(task => {
      // 计算月内总进度值（本月该任务推动的进度值总和）
      const totalCompleted = Object.values(task.weekProgress).reduce((sum, progress) => {
        return sum + (progress.completedToday || 0)
      }, 0)
      
      // 计算月内应完成的总进度值（本月该任务的目标值总和）
      const totalRequired = Object.values(task.weekProgress).reduce((sum, progress) => {
        return progress.isOccurrenceToday ? sum + (task.dailyTarget || 1) : sum
      }, 0)
      
      // 计算进度百分比（本月该任务推动的进度值总和/本月该任务的目标值总和）* 100%
      const progressRate = totalRequired > 0 
        ? Math.round((totalCompleted / totalRequired) * 100) 
        : 0
      
      // 格式化日期
      const startDate = task.startDate ? dayjs(task.startDate).format('MM-DD') : ''
      const endDate = task.endDate ? dayjs(task.endDate).format('MM-DD') : ''
      
      // 组装任务数据
      return {
        title: task.title || '无标题',
        objective: task.target ? task.target.title : '',
        startDate,
        endDate,
        progressRate: progressRate
      }
    })
    
    // 按完成进度比例排序
    formattedTasks.sort((a, b) => {
      return b.progressRate - a.progressRate
    })
    
    // 添加到 monthStats.tasks
    monthStats.tasks.push(...formattedTasks)
    
    // 更新目标进度列表
    updateMonthGoals(monthTasks)
  } catch (error) {
    console.error('[错误] 格式化月任务列表失败：', error)
  }
}

/**
 * 更新月视图的目标进度列表
 *
 * @param {Array} monthTasks - 月内任务数据
 */
const updateMonthGoals = (monthTasks) => {
  try {
    // 清空当前目标列表
    monthStats.goals = []
    
    // 获取所有唯一的目标
    const uniqueGoals = new Map()
    
    // 遍历所有任务，收集目标信息
    monthTasks.forEach(task => {
      if (!task.target || !task.okrId) return
      
      if (!uniqueGoals.has(task.okrId)) {
        uniqueGoals.set(task.okrId, {
          id: task.okrId,
          name: task.target.title,
          tasks: [],
          totalCompleted: 0,
          totalRequired: 0
        })
      }
      
      const goalData = uniqueGoals.get(task.okrId)
      
      // 添加任务到目标
      goalData.tasks.push(task)
      
      // 计算月内总进度值
      const taskCompleted = Object.values(task.weekProgress).reduce((sum, progress) => {
        return sum + (progress.completedToday || 0)
      }, 0)
      
      // 计算月内应完成的总进度值
      const taskRequired = Object.values(task.weekProgress).reduce((sum, progress) => {
        return progress.isOccurrenceToday ? sum + (task.dailyTarget || 1) : sum
      }, 0)
      
      // 更新目标总计
      goalData.totalCompleted += taskCompleted
      goalData.totalRequired += taskRequired
    })
    
    // 格式化目标数据
    const formattedGoals = Array.from(uniqueGoals.values()).map(goal => {
      // 计算目标进度百分比
      const progress = goal.totalRequired > 0 
        ? Math.round((goal.totalCompleted / goal.totalRequired) * 100) 
        : 0
      
      return {
        name: goal.name,
        progress: progress
      }
    })
    
    // 按进度排序
    formattedGoals.sort((a, b) => b.progress - a.progress)
    
    // 添加到 monthStats.goals
    monthStats.goals.push(...formattedGoals)
  } catch (error) {
    console.error('[错误] 更新月目标列表失败：', error)
  }
}

// 监听刷新触发和激活状态
watch(() => [reloadTrigger.value.count, isActiveTab.value], ([count, isActive]) => {
  console.log('数据分析页监听到刷新触发：', count, '当前页是否激活：', isActive, '当前 tab:', reloadTrigger.value.tabKey)
  if (isActive) {
    console.log('数据分析页接到了刷新通知，且当前为激活页面')
    onInit()
  } else {
    console.log('数据分析页接到了刷新通知，但当前不是激活页面')
  }
}, { deep: true })

// 页面加载完成
onShow(() => {
  // 初始化数据
  onInit()
})
</script>

<style lang="scss" scoped>
.content {
  padding: 20rpx;
  background: var(--color-bg);
  height: 100vh;
  display: flex;
  flex-direction: column;
  scrollbar-width: thin;
  scrollbar-color: #a0a0a0 #f1f1f1;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-gray-200);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 14px;
  color: var(--color-gray-600);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
  color: var(--color-gray-500);
  
  i {
    font-size: 32px;
    margin-bottom: 10px;
    opacity: 0.7;
  }
  
  view {
    font-size: 14px;
  }
}

/* 顶部区域 */
.app-header {
  padding: 20px;
  background: var(--color-white);
  border-radius: 0 0 var(--rounded-lg) var(--rounded-lg);
  margin-bottom: 16px;
  box-shadow: var(--shadow-sm);
  padding-top: 24rpx;
}

.page-title {
  font-weight: 700;
  font-size: 28px;
  margin-bottom: 16px;
  background: linear-gradient(120deg, var(--color-primary), var(--color-primary-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.5px;
}

.segment-control {
  display: flex;
  background-color: var(--color-gray-100);
  border-radius: var(--rounded-lg);
  padding: 4px;
  margin-bottom: 20px;
  box-shadow: var(--shadow-sm);
  border: none;
  
  .segment-item {
    flex: 1;
    text-align: center;
    padding: 12px 0;
    font-size: 14px;
    border-radius: calc(var(--rounded-lg) - 4px);
    transition: var(--transition-normal);
    font-weight: 500;
    color: var(--color-gray-600);
    
    &.active {
      background: linear-gradient(120deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      box-shadow: 0 4px 10px rgba(94, 106, 210, 0.25);
      transform: translateY(-1px);
    }
  }
}

/* 卡片通用样式 */
.section-card {
  background: var(--color-white);
  border-radius: var(--rounded-lg);
  margin: 0 20px 20px;
  padding: 22px;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
  }
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-gray-800);
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  
  i {
    margin-right: 10px;
    color: var(--color-primary);
    font-size: 20px;
  }
}

/* 日期选择器样式 */
.date-selector {
  margin-bottom: 10px;
  padding: 15px;
}

.date-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  &-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--color-gray-100);
    border: none;
    color: var(--color-gray-600);
    transition: var(--transition-fast);
    
    &:hover {
      background: var(--color-gray-200);
      color: var(--color-primary);
    }
  }
}

.current-date-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 0 10px;
}

.current-date {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-gray-800);
  text-align: center;
  background: linear-gradient(120deg, var(--color-primary), var(--color-primary-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.date-range {
  font-size: 12px;
  color: var(--color-gray-500);
  margin-top: 4px;
}

/* 进度显示组件 */
.progress-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.progress-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: var(--shadow-sm);
  
  &::before {
    content: '';
    position: absolute;
    width: 90px;
    height: 90px;
    border-radius: 50%;
    background-color: var(--color-white);
  }
}

.progress-content {
  position: relative;
  z-index: 1;
  text-align: center;
}

.progress-percentage {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-primary);
}

.progress-label {
  font-size: 12px;
  color: var(--color-gray-600);
  margin-top: 4px;
}

.progress-details {
  flex: 1;
  margin-left: 20px;
}

.progress-item {
  margin-bottom: 12px;
  
  &-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
  }
  
  &-title {
    font-weight: 500;
    color: var(--color-gray-700);
    font-size: 14px;
  }
  
  &-value {
    font-weight: 600;
    color: var(--color-gray-800);
    font-size: 14px;
  }
}

.chart-bar {
  height: 8px;
  background-color: var(--color-gray-200);
  border-radius: 4px;
  overflow: hidden;
}

.chart-fill {
  height: 100%;
  border-radius: 4px;
  background: linear-gradient(to right, var(--color-primary-light), var(--color-primary));
  
  &.success {
    background: linear-gradient(to right, #56c993, #3aab7a);
  }
  
  &.warning {
    background: linear-gradient(to right, #ffae57, #ff9636);
  }
  
  &.danger {
    background: linear-gradient(to right, #ff7c7c, #ff5252);
  }
}

/* 任务块样式 */
.time-block {
  background: var(--color-gray-100);
  border-radius: var(--rounded-md);
  padding: 16px;
  margin-bottom: 12px;
  transition: var(--transition-fast);
  
  &:hover {
    background: var(--color-gray-200);
  }
  
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }
  
  &-title {
    font-weight: 600;
    color: var(--color-gray-800);
    font-size: 16px;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 10px;
  }
  
  &-time {
    font-size: 14px;
    color: var(--color-gray-600);
    font-weight: 500;
  }

  &-objective {
    font-size: 12px;
    color: var(--color-gray-700);
    background-color: rgba(94, 106, 210, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 40%;

    i {
      color: var(--color-primary);
      margin-right: 4px;
    }
  }

  &-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    margin-bottom: 8px;
  }

  &-status {
    font-size: 12px;
    font-weight: 500;
    padding: 3px 8px;
    border-radius: 12px;
    
    &.status-completed {
      background-color: rgba(58, 171, 122, 0.1);
      color: #3aab7a;
    }
    
    &.status-progress {
      background-color: rgba(94, 106, 210, 0.1);
      color: var(--color-primary);
    }
  }

  &-dates {
    font-size: 12px;
    color: var(--color-gray-600);
  }

  &-progress {
    display: flex;
    align-items: center;
    margin-top: 8px;

    .chart-bar {
      flex: 1;
      margin-right: 10px;
    }

    .progress-text {
      font-size: 12px;
      font-weight: 500;
      color: var(--color-gray-700);
      min-width: 40px;
      text-align: right;
    }
  }
  
  &-category {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 8px;
  }
}

.category {
  &-work {
    background-color: rgba(94, 106, 210, 0.1);
    color: var(--color-primary);
  }
  
  &-meeting {
    background-color: rgba(78, 168, 222, 0.1);
    color: var(--color-secondary);
  }
  
  &-break {
    background-color: rgba(255, 174, 87, 0.1);
    color: var(--color-warning);
  }
}

/* 统计卡片样式 */
.summary-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 20px;
}

.stat-card {
  background: var(--color-gray-100);
  border-radius: var(--rounded-md);
  padding: 16px;
  text-align: center;
  transition: var(--transition-fast);
  
  &:hover {
    background: var(--color-gray-200);
  }
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--color-gray-600);
  font-weight: 500;
}

/* 周视图图表 */
.chart-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  margin-top: 20px;
}

.chart-day {
  text-align: center;
  
  &-bar {
    height: 100px;
    background-color: var(--color-gray-200);
    border-radius: var(--rounded-md);
    position: relative;
    margin-bottom: 8px;
    overflow: hidden;
  }
  
  &-fill {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    border-radius: 0 0 var(--rounded-md) var(--rounded-md);
    background: linear-gradient(to top, var(--color-primary), var(--color-primary-light));
    
    &.success {
      background: linear-gradient(to top, #3aab7a, #56c993);
    }
    
    &.warning {
      background: linear-gradient(to top, #ff9636, #ffae57);
    }
  }
  
  &-label {
    font-size: 12px;
    color: var(--color-gray-600);
    font-weight: 500;
  }
}
</style>

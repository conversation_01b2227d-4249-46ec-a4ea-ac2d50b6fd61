<template>
  <uni-popup ref="popupRef" type="center" background-color="#fff" border-radius="10px" @change="onPopupChange">
    <view class="history-popup-container">
      <view class="popup-header">
        <view class="popup-title">{{ title }}</view>
        <i class="fas fa-times popup-close-icon" @click="close"></i>
      </view>
      <scroll-view scroll-y class="history-list-scroll">
        <view class="history-list">
          <view
            v-for="evaluation in historyList"
            :key="evaluation.id"
            class="history-item"
            :class="{ active: currentItemId === evaluation.id }"
            @click="handleSelectItem(evaluation.id)"
          >
            <view class="history-item-info">
              <text class="attempt-number">第 {{ evaluation.attempt }} 次尝试</text>
              <text class="timestamp">{{ formatTimestamp(evaluation.timestamp) }}</text>
            </view>
            <i class="fas fa-chevron-right history-item-icon"></i>
          </view>
        </view>
      </scroll-view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  show: {
    type: Bo<PERSON>an,
    default: false,
  },
  title: {
    type: String,
    default: '历史记录',
  },
  historyList: {
    type: Array,
    default: () => [],
  },
  currentItemId: {
    type: String,
    default: null,
  },
})

const emit = defineEmits(['update:show', 'select-item'])

const popupRef = ref(null)

watch(
  () => props.show,
  (newVal) => {
    if (newVal) {
      popupRef.value?.open()
    } else {
      popupRef.value?.close()
    }
  }
)

const onPopupChange = (e) => {
  // 当 uni-popup 的状态改变时触发
  // 如果弹窗是关闭的，确保父组件的 v-model:show 也更新为 false
  if (!e.show) {
    emit('update:show', false)
  }
}

const close = () => {
  // 这里我们直接调用 uni-popup 的 close 方法
  // change 事件会处理后续的 emit
  popupRef.value?.close()
}

const handleSelectItem = (id) => {
  emit('select-item', id)
  close()
}

// 格式化时间戳显示
const formatTimestamp = (date) => {
  if (!date) return ''
  const d = new Date(date)
  const month = (d.getMonth() + 1).toString().padStart(2, '0')
  const day = d.getDate().toString().padStart(2, '0')
  const hours = d.getHours().toString().padStart(2, '0')
  const minutes = d.getMinutes().toString().padStart(2, '0')
  return `${month}-${day} ${hours}:${minutes}`
}
</script>

<style scoped>
.history-popup-container {
  width: 80vw;
  max-width: 400px;
  padding: 16px;
  padding-bottom: 24px;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary, #333);
}

.popup-close-icon {
  font-size: 20px;
  color: var(--color-text-secondary, #666);
  cursor: pointer;
}

.history-list-scroll {
  max-height: 60vh;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.history-item {
  background-color: var(--color-bg-light, #f9fafb);
  padding: 12px 16px;
  border-radius: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid var(--color-border, #eee);
  transition: all 0.2s ease;
  cursor: pointer;
}

.history-item.active {
  background-color: var(--color-primary-light-hover, #e8f4ff);
  border-color: var(--color-primary, #b3d8ff);
}

.history-item:hover:not(.active) {
  background-color: var(--color-bg-hover, #f0f1f2);
  border-color: var(--color-border-hover, #ddd);
}

.history-item-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.history-item-info .attempt-number {
  font-weight: 600;
  color: var(--color-text-primary, #333);
  font-size: 15px;
}

.history-item-info .timestamp {
  color: var(--color-text-secondary, #888);
  font-size: 13px;
}

.history-item-icon {
  color: var(--color-icon-secondary, #bbb);
  font-size: 14px;
  transition: color 0.2s ease;
}

.history-item.active .history-item-icon,
.history-item:hover .history-item-icon {
  color: var(--color-primary, #007aff);
}
</style>

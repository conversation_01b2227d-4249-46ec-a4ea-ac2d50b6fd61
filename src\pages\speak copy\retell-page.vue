<template>
  <div class="speak-page-container">
    <template v-if="isTrainingStarted">
      <!-- 头部 -->
      <l-chat-header title="复述" @back="handleBack" @reset="handleReset" @record="openDrawer" />

      <!-- 消息列表区域 -->
      <l-message-list ref="messageListRef" :messages="messages" />

      <!-- 上传进度条 -->
      <div v-if="uploadProgress.show" class="upload-progress">
        <z-progress :percent="uploadProgress.percent" :stroke-width="4" :color="'#07c160'" :show-info="true" />
        <div class="upload-text">正在上传录音...</div>
      </div>

      <!-- 输入区域 -->
      <l-message-input v-model="userInput" @send="sendMessage" @send-audio="sendAudioMessage" />

      <!-- 聊天记录抽屉 -->
      <uni-drawer ref="drawerRef" mode="right" width="650rpx" :mask-click="true">
        <l-chat-record-list
          ref="recordListRef"
          :active-id="currentChatId"
          @select="selectChatRecord"
          @clear="clearChatRecords"
        />
      </uni-drawer>
    </template>
    <div v-else class="start-training-container">
      <div class="start-training-card">
        <div class="card-title">开始你的复述训练</div>
        <div class="card-description">通过复述，将知识内化为自己的理解。点击下方按钮，开启第一次训练吧！</div>
        <z-button @click="startTraining" type="primary" size="large" block>
          <i class="fas fa-play-circle" style="margin-right: 8px"></i>
          开始训练
        </z-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted, onBeforeUnmount } from 'vue'
import dayjs from 'dayjs'
import LChatHeader from './components/l-chat-header.vue'
import LMessageList from './components/l-message-list.vue'
import LMessageInput from './components/l-message-input.vue'
import LChatRecordList from './components/l-chat-record-list.vue'
import ZProgress from '@/components/z-progress/z-progress.vue'
import { addChatRecordApi, updateChatRecordApi, getChatRecordApi } from '@/api/chatRecord'
import { generateSpeakConetnt } from '@/api/workflow'
import request from '@/utils/request'
import { useRecord, useRecordCompat } from '@/hooks'

// 注册组件
const components = {
  LChatHeader,
  LMessageList,
  LMessageInput,
  LChatRecordList,
  ZProgress,
}

// 消息列表 - 模拟微信聊天的数据
const messages = ref([])

const isTrainingStarted = ref(false)
const userInput = ref('')
const messageListRef = ref(null)
const drawerRef = ref(null)
const recordListRef = ref(null)
let recordTitle = ''
// 当前聊天记录 ID
const currentChatId = ref('')
// 笔记内容和标题
const noteContent = ref('')
const noteTitle = ref('')
// AI 内容
const aiContent = ref('')

const uploadProgress = ref({
  show: false,
  percent: 0,
})

// 新的录音 Hook 实现
const { isRecording, duration, startRecording, stopRecording, cancelRecording } = useRecord({
  maxDuration: 60000, // 最大录音时长 60 秒
  appOptions: {
    format: 'mp3', // App 端录音格式
  },
})

// 兼容性检查
const { isSupported, requestPermission, openSettings } = useRecordCompat()

// 检查录音兼容性并请求权限
const checkRecordPermission = async () => {
  if (!isSupported.value) {
    uni.showModal({
      title: '不支持录音',
      content: '当前环境不支持录音功能',
      showCancel: false,
    })
    return false
  }

  const hasPermission = await requestPermission()
  if (!hasPermission) {
    uni.showModal({
      title: '需要权限',
      content: '需要录音权限才能使用该功能',
      confirmText: '去设置',
      success: (res) => {
        if (res.confirm) {
          // #ifdef APP-PLUS
          openSettings()
          // #endif
        }
      },
    })
    return false
  }

  return true
}

// 打开抽屉
const openDrawer = () => {
  drawerRef.value.open()
}

// 选择历史聊天记录
const selectChatRecord = async (record) => {
  try {
    // 设置当前聊天记录 ID
    currentChatId.value = record._id

    // 如果有内容，则解析并显示
    if (record.content) {
      messages.value = JSON.parse(record.content)

      // 滚动到底部
      await nextTick()
      messageListRef.value.scrollToBottom()
    }

    // 关闭抽屉
    drawerRef.value.close()

    uni.showToast({
      title: '已切换到历史对话',
      icon: 'none',
    })
  } catch (error) {
    console.error('加载聊天记录失败', error)
    uni.showToast({
      title: '加载聊天记录失败',
      icon: 'error',
    })
  }
}

// 清空所有聊天记录后的回调
const clearChatRecords = () => {
  if (currentChatId.value) {
    // 如果当前正在查看某个聊天记录，需要重置
    clearMessages()
    currentChatId.value = ''
  }
}

// 保存聊天记录
const saveChatRecord = async () => {
  try {
    if (!messages.value.length) return

    // 如果用户没有进行回复，则本次聊天不添加到聊天记录里面
    const hasUserMessage = messages.value.some((msg) => msg.isUser)
    if (!hasUserMessage) {
      return
    }

    if (currentChatId.value) {
      // 更新已有记录
      await updateChatRecordApi(currentChatId.value, {
        content: JSON.stringify(messages.value),
        updateTime: dayjs().toISOString(),
      })
    } else {
      // 创建新记录
      const result = await addChatRecordApi({
        content: JSON.stringify(messages.value),
        title: recordTitle || dayjs().format('YYYY-MM-DD HH:mm:ss'),
      })
      currentChatId.value = result._id

      // 刷新聊天记录列表
      if (recordListRef.value) {
        recordListRef.value.refresh()
      }
    }
  } catch (error) {
    console.error('保存聊天记录失败：', error)
  }
}

// 发送文本消息
const sendMessage = async () => {
  if (!userInput.value.trim()) return

  // 添加用户消息
  messages.value.push({
    type: 'text',
    content: userInput.value,
    isUser: true,
    time: dayjs().valueOf(),
  })

  // 清空输入框
  const userQuestion = userInput.value
  userInput.value = ''

  // 滚动到底部
  await messageListRef.value.scrollToBottom()

  // 添加 AI 正在输入的提示
  messages.value.push({
    loading: true,
    isUser: false,
    time: dayjs().valueOf(),
  })

  let { data } = await workflowApi('7493204454884720677', {
    speakContent: userQuestion,
    aiContent: aiContent.value,
    noteContent: noteContent.value || '',
  })
  // 移除 loading 状态
  messages.value.pop()
  // 添加 AI 回复
  messages.value.push({
    type: 'text',
    content: data.output,
    isUser: false,
    time: dayjs().valueOf(),
  })

  // 保存聊天记录
  await saveChatRecord()
}

// 发送音频消息
const sendAudioMessage = async (audioData) => {
  if (!(await checkRecordPermission())) return

  try {
    uploadProgress.value.show = true
    uploadProgress.value.percent = 0

    const cloudPath = `speak/${dayjs().format('YYYY-MM-DD')}_${Date.now()}.mp3`
    let filePath
    let estimatedDuration

    // #ifdef H5
    // 在 H5 平台，useRecord hook 返回的是一个包含 blob 的对象
    if (audioData && audioData.blob) {
      filePath = URL.createObjectURL(audioData.blob)
      estimatedDuration = audioData.duration || 0
    } else {
      console.error('H5 端录音数据格式不正确', audioData)
      uploadProgress.value.show = false
      return
    }
    // #endif

    // #ifndef H5
    // 在 App 端，useRecord hook 返回的是一个包含 tempFilePath 的对象
    if (audioData && audioData.blob) {
      // App 端可能返回 blob 字段作为文件路径
      filePath = audioData.blob
      estimatedDuration = audioData.duration || 0
    } else if (audioData && audioData.tempFilePath) {
      filePath = audioData.tempFilePath
      estimatedDuration = duration.value
    } else {
      console.error('App 端录音数据格式不正确', audioData)
      uploadProgress.value.show = false
      return
    }
    // #endif

    const uploadResult = await uniCloud.uploadFile({
      filePath: filePath,
      cloudPath: cloudPath,
      cloudPathAsRealPath: true,
      onUploadProgress: (progressEvent) => {
        const percent = Math.round((progressEvent.loaded / progressEvent.total) * 100)
        uploadProgress.value.percent = percent
      },
    })

    uploadProgress.value.percent = 100
    setTimeout(() => {
      uploadProgress.value.show = false
    }, 500)

    const fileUrl = await uniCloud.getTempFileURL({
      fileList: [uploadResult.fileID],
    })

    const audioUrl = fileUrl.fileList[0].tempFileURL
    const fileID = uploadResult.fileID

    // 添加用户音频消息
    messages.value.push({
      type: 'audio',
      audioUrl: fileID,
      audioDuration: estimatedDuration,
      isUser: true,
      time: dayjs().valueOf(),
      isTranscribing: true, // 添加转写状态
    })

    // 滚动到底部
    await messageListRef.value.scrollToBottom()

    // 语音转文字请求
    try {
      const { data } = await request.post(
        '/speak/asr',
        {
          url: audioUrl,
        },
        {
          header: {
            'Content-Type': 'application/json',
          },
        }
      )

      console.log('语音转文字结果：', data)
      const { asrId } = data

      // 开始轮询结果
      let retryCount = 0
      const maxRetries = 30 // 最多轮询 30 次，约 30 秒
      const pollInterval = 1000 // 每秒轮询一次

      const pollAsrResult = async () => {
        try {
          const { data: result } = await request.post('/speak/getAsr', {
            taskId: asrId,
          })

          console.log('result===', result.Data.StatusStr)
          console.log(result)
          const status = result.Data.StatusStr

          // 找到最后一条音频消息
          const lastAudioMessage = [...messages.value].reverse().find((msg) => msg.type === 'audio' && msg.isUser)

          if (status === 'success') {
            // 转写成功，更新状态和转写结果
            if (lastAudioMessage) {
              lastAudioMessage.isTranscribing = false
              lastAudioMessage.transcribeResult = result.Data.ResultDetail.map((item) => item.FinalSentence).join('\n')
            }
            await messageListRef.value.scrollToBottom()

            // 语音转文字完成后保存聊天记录
            await saveChatRecord()
            return
          } else if (result.status === 'failed') {
            // 转写失败
            if (lastAudioMessage) {
              lastAudioMessage.isTranscribing = false
            }
            messages.value.push({
              type: 'text',
              content: '语音转写失败，请重试',
              isUser: false,
              time: dayjs().valueOf(),
            })
            await messageListRef.value.scrollToBottom()
            return
          }

          // 继续轮询
          retryCount++
          if (retryCount < maxRetries) {
            setTimeout(pollAsrResult, pollInterval)
          } else {
            // 超时
            if (lastAudioMessage) {
              lastAudioMessage.isTranscribing = false
            }
            messages.value.push({
              type: 'text',
              content: '语音转写超时，请重试',
              isUser: false,
              time: dayjs().valueOf(),
            })
            await messageListRef.value.scrollToBottom()
          }
        } catch (error) {
          console.error('获取转写结果失败：', error)
          const lastAudioMessage = [...messages.value].reverse().find((msg) => msg.type === 'audio' && msg.isUser)
          if (lastAudioMessage) {
            lastAudioMessage.isTranscribing = false
          }
          messages.value.push({
            type: 'text',
            content: '获取转写结果失败，请重试',
            isUser: false,
            time: dayjs().valueOf(),
          })
          await messageListRef.value.scrollToBottom()
        }
      }

      // 开始轮询
      pollAsrResult()
    } catch (error) {
      console.error('处理音频消息时出错：', error)
      const lastAudioMessage = [...messages.value].reverse().find((msg) => msg.type === 'audio' && msg.isUser)
      if (lastAudioMessage) {
        lastAudioMessage.isTranscribing = false
      }
      messages.value.push({
        type: 'text',
        content: '语音转写失败，请重试',
        isUser: false,
        time: dayjs().valueOf(),
      })
      await messageListRef.value.scrollToBottom()
    }
  } catch (error) {
    uploadProgress.value.show = false
    console.error('录音或上传失败：', error)
    uni.showToast({
      title: '录音或上传失败',
      icon: 'none',
    })
  }
}

// 获取状态对应的提示消息
const getStatusMessage = (status) => {
  const statusMessages = {
    doing: '正在转写语音...',
    waiting: '正在处理中...',
    success: '转写完成',
    failed: '转写失败',
  }
  return statusMessages[status] || '正在转写语音...'
}

const startTraining = async () => {
  isTrainingStarted.value = true
  const hasPermission = await checkRecordPermission()
  if (!hasPermission) {
    // 权限请求失败，可以给出提示，但继续执行流程
    console.warn('录音权限未授予，部分功能可能受限')
  }

  // 如果有笔记内容，则使用笔记内容初始化
  if (noteContent.value) {
    initWithNoteContent()
  } else {
    // 添加 AI 正在输入的提示
    messages.value.push({
      loading: true,
      isUser: false,
      time: dayjs().valueOf(),
    })

    try {
      const res = await generateSpeakConetnt()
      const data = res.data
      recordTitle = data.title
      noteContent.value = data.content
      noteTitle.value = data.title
      aiContent.value = data.content

      messages.value.pop() // 移除 loading
      messages.value.push({
        type: 'text',
        content: `我们来聊聊"${data.title}"这个主题，请用自己的话复述下面的内容：\n\n${data.content}`,
        isUser: false,
        time: dayjs().valueOf(),
      })
    } catch (error) {
      messages.value.pop() // 移除 loading
      messages.value.push({
        type: 'text',
        content: '抱歉，内容生成失败，请稍后再试。',
        isUser: false,
        time: dayjs().valueOf(),
      })
    }
  }
}

const handleBack = () => {
  uni.navigateBack()
}

const clearMessages = () => {
  messages.value = [
    {
      type: 'text',
      content: `我们来聊聊"${noteTitle.value}"这个主题，请用自己的话复述下面的内容：\n\n${noteContent.value}`,
      isUser: false,
      time: dayjs().valueOf(),
    },
  ]
}

const handleReset = () => {
  uni.showModal({
    title: '确认重置',
    content: '确定要开始新一轮的复述训练吗？',
    success: (res) => {
      if (res.confirm) {
        currentChatId.value = ''
        noteContent.value = ''
        noteTitle.value = ''
        recordTitle = ''
        messages.value = []
        startTraining()
      }
    },
  })
}

onMounted(() => {
  const eventChannel = getCurrentPages()[getCurrentPages().length - 1].getOpenerEventChannel()
  if (eventChannel && eventChannel.on) {
    eventChannel.on('acceptDataFromOpenerPage', (data) => {
      if (data.note) {
        noteContent.value = data.note.content
        noteTitle.value = data.note.title
        recordTitle = data.note.title
        aiContent.value = data.note.content
        isTrainingStarted.value = true
        messages.value.push({
          type: 'text',
          content: `我们来聊聊"${data.note.title}"这个主题，请用自己的话复述下面的内容：\n\n${data.note.content}`,
          isUser: false,
          time: dayjs().valueOf(),
        })
      }
    })
  }
})

// 使用笔记内容初始化复述训练
const initWithNoteContent = async () => {
  // 重置聊天内容
  clearMessages()
  console.log('使用笔记内容初始化复述训练')
  // 重置当前聊天 ID
  currentChatId.value = ''

  // 直接使用笔记内容作为复述材料
  aiContent.value = noteContent.value

  // 添加笔记内容到消息列表
  messages.value.push({
    type: 'text',
    content: noteContent.value,
    isUser: false,
    time: dayjs().valueOf(),
    isCollapsed: false,
    onToggle: () => {
      const message = messages.value.find((m) => m.content === noteContent.value)
      if (message) {
        message.isCollapsed = !message.isCollapsed
      }
    },
  })

  recordTitle = noteTitle.value || '笔记复述训练'

  // 滚动到底部
  await messageListRef.value.scrollToBottom()

  // 保存聊天记录
  await saveChatRecord()
}

// 清理资源
onBeforeUnmount(() => {
  // 如果有正在进行的录音，取消它
  if (isRecording.value) {
    cancelRecording()
  }
})

/** 执行工作流 API
 * @param {string} workflow_id - 工作流 ID
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} 包含处理结果的响应对象
 */
const workflowApi = async (workflow_id, params) => {
  const res = await request.post(
    'https://api.coze.cn/v1/workflow/run',
    {
      workflow_id,
      app_id: '7492972882780995620',
      parameters: params,
    },
    {
      timeout: 300000,
      header: {
        Authorization: `Bearer ${getApp().globalData.kzToken}`,
        'Content-Type': 'application/json',
      },
    }
  )
  return {
    ...res,
    data: JSON.parse(res.data),
  }
}
</script>

<style lang="scss" scoped>
.speak-page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f7f7;
}

.start-training-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.start-training-card {
  width: 80%;
  max-width: 400px;
  padding: 30px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-align: center;

  .card-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
  }

  .card-description {
    font-size: 16px;
    color: #666;
    margin-bottom: 25px;
  }
}

.upload-progress {
  padding: 10px;
  background-color: #fff;
  .upload-text {
    font-size: 12px;
    color: #999;
    text-align: center;
    margin-top: 4px;
  }
}
</style>

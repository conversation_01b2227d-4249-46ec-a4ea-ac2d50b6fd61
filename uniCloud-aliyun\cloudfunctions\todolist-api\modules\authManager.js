/**
 * 认证管理模块
 * 提供用户登录、token 管理、HTTP 请求封装等功能
 */

const { API_CONFIG, ERROR_CODES } = require('../config.js')
const { 
	createSuccessResponse, 
	createErrorResponse, 
	validateParams,
	parseHttpResponse,
	extractTokenFromCookies
} = require('../utils.js')

/**
 * 创建认证管理器
 * @param {object} context - 云对象上下文
 * @returns {object} 认证管理器实例
 */
function createAuthManager(context) {
	return {
		/**
		 * 用户登录认证
		 * @param {string} username - 用户名（手机号或邮箱）
		 * @param {string} password - 用户密码
		 * @param {boolean} isPhone - 是否使用手机号登录，默认 true
		 * @returns {object} 登录结果
		 */
		async login(username, password, isPhone = true) {
			console.log('[AuthManager] [login] - 开始处理登录请求...');
			// 参数校验
			const validation = validateParams({ username, password }, ['username', 'password'])
			if (validation) {
				console.warn('[AuthManager] [login] - 参数校验失败：', validation);
				return validation
			}

			try {
				const loginUrl = API_CONFIG.LOGIN_URL

				// 根据登录类型选择不同的字段名
				const loginData = isPhone ? {
					password: password,
					phone: username
				} : {
					password: password,
					email: username
				}

				const headers = API_CONFIG.LOGIN_HEADERS

				const loginType = isPhone ? "手机号" : "邮箱"
				if (context.debug) {
					console.log(`[AuthManager] [login] - 尝试使用${loginType}登录账号：${username}`)
				}

				// 发送登录请求
				console.log(`[AuthManager] [login] - 向 ${loginUrl} 发送登录请求...`);
				const response = await uniCloud.httpclient.request(loginUrl, {
					method: 'POST',
					headers: headers,
					data: loginData,
					timeout: API_CONFIG.TIMEOUT
				})
				console.log(`[AuthManager] [login] - 收到登录响应，状态码：${response.status}`);

				if (response.status !== 200) {
					console.error(`[AuthManager] [login] - 登录请求失败，状态码：${response.status}`, response.data);
					return createErrorResponse(
						ERROR_CODES.LOGIN_ERROR,
						`登录失败，状态码：${response.status}`,
						response.data
					)
				}

				// 解析响应数据
				let responseData
				try {
					responseData = typeof response.data === 'string'
						? JSON.parse(response.data)
						: response.data
				} catch (parseError) {
					console.error('[AuthManager] [login] - 解析响应数据失败：', parseError);
					return createErrorResponse(
						ERROR_CODES.PARSE_ERROR,
						'响应数据解析失败',
						response.data
					)
				}
				if(context.debug) {
					console.log('[AuthManager] [login] - 响应头中的 set-cookie:', response.headers['set-cookie']);
				}

				// 从 cookies 中获取 token
				let token = null
				if (response.headers && response.headers['set-cookie']) {
					token = extractTokenFromCookies(response.headers['set-cookie'])
				}
				// 如果登录失败，dida 会返回一个过去的 cookie，导致 token 为空字符串""
				// 此处判断如果 token 是""，则也认为是空
				if (token === '""') {
					token = null
				}
				
				console.log('[AuthManager] [login] - 从 Cookie 中提取的 token:', token ? `${token.substring(0, 10)}...` : '未找到');

				// 如果 cookies 中没有 token，尝试从响应体中获取
				if (!token && responseData && typeof responseData === 'object' && responseData.token) {
					token = responseData.token
					console.log('[AuthManager] [login] - 从响应体中获取的 token:', token ? `${token.substring(0, 10)}...` : '未找到');
				}

				if (!token) {
					console.error('[AuthManager] [login] - 登录成功但未在响应中找到 token。响应体：', responseData);
					return createErrorResponse(
						ERROR_CODES.TOKEN_NOT_FOUND,
						responseData.errorMsg || '登录成功但未获取到 token',
						responseData
					)
				}

				// 设置 token 和请求头到上下文
				context.token = token
				context.headers = {
					...API_CONFIG.DEFAULT_HEADERS,
					"Cookie": `t=${token}`
				}
				console.log('[AuthManager] [login] - Token 设置成功，API 已准备就绪。');

				if (context.debug) {
					console.log(`[AuthManager] [login] - 最终获取到的 token: ${token.substring(0, 10)}...`)
				}

				return createSuccessResponse("登录成功", {
					token: token,
					loginType: loginType
				})

			} catch (error) {
				console.error('[AuthManager] [login] - 登录过程中发生异常：', error)

				if (error.code === 'TIMEOUT') {
					console.error('[AuthManager] [login] - 登录请求超时。');
					return createErrorResponse(ERROR_CODES.TIMEOUT_ERROR, '登录请求超时，请稍后重试')
				}

				return createErrorResponse(ERROR_CODES.NETWORK_ERROR, error.message || '登录失败', error)
			}
		},

		/**
		 * 初始化 API（使用已有 token）
		 * @param {string} token - 访问令牌
		 * @returns {object} 初始化结果
		 */
		async initWithToken(token) {
			console.log('[AuthManager] [initWithToken] - 开始使用 token 初始化...');
			const validation = validateParams({ token }, ['token'])
			if (validation) {
				console.warn('[AuthManager] [initWithToken] - Token 参数校验失败：', validation);
				return validation
			}

			context.token = token
			context.headers = {
				...API_CONFIG.DEFAULT_HEADERS,
				"Cookie": `t=${token}`
			}

			console.log(`[AuthManager] [initWithToken] - 使用提供的 token 初始化成功：${token.substring(0, 10)}...`);
			return createSuccessResponse("API 初始化成功")
		},

		/**
		 * 发送 HTTP 请求的通用方法
		 * @param {string} method - HTTP 方法
		 * @param {string} endpoint - API 端点
		 * @param {object} data - 请求数据
		 * @param {object} params - URL 参数
		 * @returns {object} 请求结果
		 */
		async _request(method, endpoint, data = null, params = null) {
			if (!context.token) {
				console.error('[AuthManager] [_request] - 未授权的请求，缺少 token。');
				return createErrorResponse(ERROR_CODES.UNAUTHORIZED, '请先登录或初始化 token')
			}
			console.log('context======', context)

			const url = `${context.BASE_URL}${endpoint}`
			console.log(`[AuthManager] [_request] - 发送 ${method} 请求至 ${url}`);
			
			try {
				const requestOptions = {
					method: method,
					headers: context.headers,
					timeout: API_CONFIG.TIMEOUT
				}

				if (data) {
					requestOptions.data = data
					if(context.debug) console.log('[AuthManager] [_request] - 请求体：', data);
				}

				if (params) {
					requestOptions.params = params
					if(context.debug) console.log('[AuthManager] [_request] - URL 参数：', params);
				}

				const response = await uniCloud.httpclient.request(url, requestOptions)
				if(context.debug) console.log(`[AuthManager] [_request] - 收到响应，状态码：${response.status}`);
				return parseHttpResponse(response)

			} catch (error) {
				console.error(`[AuthManager] [_request] - HTTP 请求异常 (${method} ${url}):`, error)

				if (error.code === 'TIMEOUT') {
					console.error(`[AuthManager] [_request] - 请求超时：${url}`);
					return createErrorResponse(ERROR_CODES.TIMEOUT_ERROR, '请求超时，请稍后重试')
				}

				return createErrorResponse(ERROR_CODES.NETWORK_ERROR, error.message || 'HTTP 请求失败', error)
			}
		},

		/**
		 * 获取所有基础数据（任务、项目、标签）
		 * @returns {object} 基础数据
		 */
		async getBatchData() {
			console.log('[AuthManager] [getBatchData] - 开始请求批量基础数据...');
			const result = await this._request('GET', API_CONFIG.BATCH_DATA_URL)
			if (result.errCode) {
				console.error('[AuthManager] [getBatchData] - 获取批量数据失败：', result);
				return result
			}

			if(context.debug) {
				console.log('[AuthManager] [getBatchData] - 成功获取批量数据，任务数：', result.data.syncTaskBean?.update?.length || 0, ', 项目数：', result.data.projectProfiles?.length || 0, ', 标签数：', result.data.tags?.length || 0);
				console.log(result.data)
			}

			return createSuccessResponse('获取基础数据成功', {
				tasks: result.data.syncTaskBean?.update || [],
				projects: result.data.projectProfiles || [],
				tags: result.data.tags || []
			})
		}
	}
}

module.exports = createAuthManager

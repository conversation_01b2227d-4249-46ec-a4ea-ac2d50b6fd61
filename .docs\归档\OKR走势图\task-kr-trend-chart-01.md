# 任务：KR 进度走势图组件基础结构与标签页集成

## 所属功能模块

OKR - KR 进度走势图组件

## 任务描述

创建 KR 进度走势图组件的基础结构，并在 KR 详情页面中集成为新标签页。

## 技术实现详情

1. 在`src/pages/okr/components/`目录下创建新组件目录`l-trend-chart/`
2. 创建基础组件文件`l-trend-chart.vue`，实现基础组件结构
3. 在`krDetail.vue`中引入走势图组件，添加为第三个标签页选项
4. 实现标签页切换逻辑，确保与现有的"进度历史"和"统计情况"标签页协同工作
5. 创建基础 UI 布局，包括图表容器和时间维度切换标签组

## 代码实现要点

1. 组件目录结构：

   ```
   src/pages/okr/components/
     l-trend-chart/
       l-trend-chart.vue  // 主组件
   ```

2. 在`l-trend-chart.vue`中实现：

   - 基础组件结构
   - props 定义（接收 KR 数据）
   - 时间维度切换 UI（日、周、月）
   - 图表容器占位区域

3. 在`krDetail.vue`中：
   - 导入新组件
   - 添加标签页选项
   - 处理标签切换逻辑

## 验收标准

1. 组件目录和文件结构正确创建
2. KR 详情页面成功显示"进度走势图"标签页
3. 标签页能够正常切换，不影响已有功能
4. 组件能正确接收 KR 数据作为 props
5. 时间维度切换 UI（日、周、月）正确显示，但功能暂不需实现

## 依赖关系

- 无上游依赖

## 优先级

高

## 状态

待开发

<template>
  <div class="progress-history">
    <!-- 筛选条件区域 -->
    <div class="filter-area">
      <div class="month-selector">
        <div class="month-nav-btn" @click="prevMonth">
          <i class="fas fa-chevron-left"></i>
        </div>
        <div class="current-month">{{ formatYearMonth(filterMonth) }}</div>
        <div class="month-nav-btn" @click="nextMonth">
          <i class="fas fa-chevron-right"></i>
        </div>
      </div>
      <div class="view-mode-toggle">
        <div class="view-mode-btn" :class="{ active: filterMode === 'day' }" @click="changeFilterMode('day')">按天</div>
        <div class="view-mode-btn" :class="{ active: filterMode === 'month' }" @click="changeFilterMode('month')">
          按月
        </div>
      </div>
    </div>

    <!-- 日历区域 -->
    <div class="calendar-area" v-if="filterMode === 'day'">
      <!-- 添加周几头部 -->
      <div class="weekday-header">
        <div class="weekday-item" v-for="(day, index) in ['日', '一', '二', '三', '四', '五', '六']" :key="index">
          {{ day }}
        </div>
      </div>
      <div class="calendar-wrapper">
        <swiper class="week-swiper" :current="currentWeekIndex" @change="onWeekChange" :indicator-dots="false"
          :circular="false" :disable-touch="false">
          <swiper-item v-for="(week, weekIndex) in weeksInMonth" :key="weekIndex" class="swiper-item">
            <div class="week-row">
              <div class="day-cell" v-for="(day, dayIndex) in week" :key="dayIndex" :class="{
                selected: isSelectedDay(day.date),
                'has-record': hasRecord(day.date),
                'current-month': isCurrentMonth(day.date),
                today: isToday(day.date),
              }" @click="selectDate(day.date)">
                <div class="day-number">{{ day.day }}</div>
                <div class="record-dot" v-if="hasRecord(day.date)"></div>
              </div>
            </div>
          </swiper-item>
        </swiper>
      </div>
    </div>

    <!-- 记录列表区域 -->
    <div class="records-list">
      <div v-if="filteredRecords.length > 0" class="timeline">
        <div v-for="(record, index) in filteredRecords" :key="index" class="timeline-item">
          <div class="timeline-marker" :class="{ 'is-increase': isProgressIncrease(index) }">
            <i :class="isProgressIncrease(index) ? 'fas fa-arrow-up' : 'fas fa-arrow-right'"></i>
          </div>
          <div class="timeline-content" @click="openEditProgressModal(record)">
            <div class="timeline-header">
              <span class="timeline-date">{{ formatDate(record.recTime) }}</span>
              <div class="timeline-progress">
                <span class="progress-value">{{ record.val }}{{ unit }}</span>
              </div>
              <i class="fas fa-trash-alt timeline-delete-icon" @click.stop="confirmDeleteProgressRecord(record)"></i>
            </div>
            <div v-if="record.remark" class="timeline-body">
              {{ record.remark }}
              <div class="timeline-edit-hint">
                <i class="fas fa-edit"></i>
                <span>点击编辑</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态展示 -->
      <div v-if="filteredRecords.length === 0 && !loading" class="empty-data">
        <i class="fas fa-history empty-icon"></i>
        <div>暂无进度记录</div>
        <div class="empty-hint">
          {{ filterMode === 'day' ? '该日期' : '该月份' }}没有进度记录，请选择其他时间范围或添加新的进度记录
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <i class="fas fa-spinner fa-pulse"></i>
        <div>加载中...</div>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <z-confirm-modal v-model:visible="showDeleteConfirm" title="确认删除" :message="'确定要删除这条' + (recordToDelete ? formatDate(recordToDelete.recTime) : '') + '的进度记录吗？此操作不可恢复。'
      " confirm-text="删除" cancel-text="取消" type="danger" @confirm="handleDeleteRecord" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { getTaskApi } from '@/api/task'
import dayjs from 'dayjs'
import ZConfirmModal from '@/components/z-confirm-modal.vue'

// 组件接口定义
interface Props {
  // 关键结果 ID
  krId: string
  // 初始数据（可选，用于优化首次加载）
  initialRecords?: any[]
  // KR 单位（可选）
  unit?: string
}

// 定义日历日期类型
interface DayInfo {
  date: string
  day: number
  month: number
  isCurrentMonth: boolean
}

const props = withDefaults(defineProps<Props>(), {
  initialRecords: () => [],
  unit: '',
})

// 事件定义
const emit = defineEmits<{
  (e: 'update-record', record: any): void
  (e: 'delete-record', recordId: string): void
}>()

// 组件状态数据
const filterMonth = ref(dayjs().format('YYYY-MM')) // 当前筛选月份，默认为当前月
const filterMode = ref('day') // 筛选模式，默认按天
const selectedDate = ref(dayjs().format('YYYY-MM-DD')) // 选中的日期，默认当天
const records = ref<any[]>([]) // 原始进度记录数组
const filteredRecords = ref<any[]>([]) // 筛选后的记录数组
const dateHasRecord = ref<string[]>([]) // 有记录的日期数组，用于日历标记
const loading = ref(false) // 加载状态
const showDeleteConfirm = ref(false) // 删除确认弹窗
const recordToDelete = ref<any>(null) // 要删除的记录

// 周历相关数据
const weeksInMonth = ref<DayInfo[][]>([]) // 当月的所有周数据
const currentWeekIndex = ref(0) // 当前显示的周索引

// 计算当前选中日期所在的周索引
const selectedDateWeekIndex = computed(() => {
  const selectedDateObj = dayjs(selectedDate.value)
  return weeksInMonth.value.findIndex((week) => week.some((day) => day.date === selectedDateObj.format('YYYY-MM-DD')))
})

// 在组件挂载时加载数据
onMounted(async () => {
  if (props.initialRecords && props.initialRecords.length > 0) {
    records.value = [...props.initialRecords]
  } else {
    await loadRecords()
  }

  // 初始化周历数据
  calculateWeeksInMonth()

  // 更新有记录的日期
  updateDateHasRecord()

  // 初始化筛选
  applyFilters()
})

// 加载记录数据
const loadRecords = async () => {
  if (!props.krId) return

  loading.value = true
  try {
    const taskDetail = await getTaskApi(props.krId, { progress: true })
    if (taskDetail && taskDetail.recList && Array.isArray(taskDetail.recList)) {
      records.value = [...taskDetail.recList]
    } else {
      records.value = []
    }

    // 更新日期标记
    updateDateHasRecord()

    // 应用筛选条件，更新显示的记录
    applyFilters()
  } catch (error) {
    console.error('加载进度记录失败：', error)
    uni.showToast({
      title: '加载进度记录失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 更新哪些日期有记录的数组
const updateDateHasRecord = () => {
  const dates = new Set<string>()
  records.value.forEach((record) => {
    if (record.recTime) {
      dates.add(dayjs(record.recTime).format('YYYY-MM-DD'))
    }
  })
  dateHasRecord.value = Array.from(dates)
}

// 应用筛选条件
const applyFilters = () => {
  if (records.value.length === 0) {
    filteredRecords.value = []
    return
  }

  let filtered = [...records.value]

  // 按月筛选
  const year = parseInt(filterMonth.value.split('-')[0])
  const month = parseInt(filterMonth.value.split('-')[1]) - 1 // JavaScript 月份从 0 开始

  filtered = filtered.filter((record) => {
    const recordDate = dayjs(record.recTime)
    return recordDate.year() === year && recordDate.month() === month
  })

  // 如果是按天筛选，再加上日期筛选
  if (filterMode.value === 'day' && selectedDate.value) {
    filtered = filtered.filter((record) => {
      return dayjs(record.recTime).format('YYYY-MM-DD') === selectedDate.value
    })
  }

  // 按时间倒序排列（最新记录在顶部）
  filtered.sort((a, b) => {
    return dayjs(b.recTime).valueOf() - dayjs(a.recTime).valueOf()
  })

  filteredRecords.value = filtered
}

// 切换到上个月
const prevMonth = () => {
  const newMonth = dayjs(filterMonth.value).subtract(1, 'month')
  filterMonth.value = newMonth.format('YYYY-MM')

  // 更新选中日期到新月份的同一天（如果可能）
  const currentDay = dayjs(selectedDate.value).date()
  const daysInNewMonth = newMonth.daysInMonth()
  const newDay = Math.min(currentDay, daysInNewMonth)
  selectedDate.value = newMonth.date(newDay).format('YYYY-MM-DD')
}

// 切换到下个月
const nextMonth = () => {
  const newMonth = dayjs(filterMonth.value).add(1, 'month')
  filterMonth.value = newMonth.format('YYYY-MM')

  // 更新选中日期到新月份的同一天（如果可能）
  const currentDay = dayjs(selectedDate.value).date()
  const daysInNewMonth = newMonth.daysInMonth()
  const newDay = Math.min(currentDay, daysInNewMonth)
  selectedDate.value = newMonth.date(newDay).format('YYYY-MM-DD')
}

// 切换筛选模式
const changeFilterMode = (mode: 'day' | 'month') => {
  filterMode.value = mode
  applyFilters()
}

// 格式化日期显示
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 格式化年月显示
const formatYearMonth = (yearMonth: string) => {
  return dayjs(yearMonth).format('YYYY 年 MM 月')
}

// 判断进度是否增加（相比上一条记录）
const isProgressIncrease = (index: number) => {
  if (index < filteredRecords.value.length - 1) {
    // 当前记录的值 > 下一条记录的值（时间更早的记录）
    return filteredRecords.value[index].val > filteredRecords.value[index + 1].val
  }
  return false
}



// 打开编辑进度记录弹窗
const openEditProgressModal = (record: any) => {
  emit('update-record', record)
}

// 确认删除进度记录
const confirmDeleteProgressRecord = (record: any) => {
  recordToDelete.value = record
  showDeleteConfirm.value = true
}

// 处理删除记录
const handleDeleteRecord = () => {
  if (recordToDelete.value) {
    // 调用父组件的删除记录事件
    emit('delete-record', recordToDelete.value._id)
    // 重置删除确认弹窗
    recordToDelete.value = null
    showDeleteConfirm.value = false

    // 重新加载数据
    setTimeout(() => {
      loadRecords()
    }, 500) // 延迟 500ms，确保后端处理完成
  }
}

// 计算当前月份的周数据
const calculateWeeksInMonth = () => {
  const monthStart = dayjs(filterMonth.value).startOf('month')
  const monthEnd = dayjs(filterMonth.value).endOf('month')

  // 获取包含这个月的所有周
  const firstDay = monthStart.startOf('week')
  const lastDay = monthEnd.endOf('week')

  const totalDays = lastDay.diff(firstDay, 'day') + 1
  const weeksCount = Math.ceil(totalDays / 7)

  const weeksData: DayInfo[][] = []
  let currentDate = firstDay

  for (let week = 0; week < weeksCount; week++) {
    const weekDays: DayInfo[] = []

    for (let i = 0; i < 7; i++) {
      weekDays.push({
        date: currentDate.format('YYYY-MM-DD'),
        day: currentDate.date(),
        month: currentDate.month(),
        isCurrentMonth: currentDate.month() === monthStart.month(),
      })

      currentDate = currentDate.add(1, 'day')
    }

    weeksData.push(weekDays)
  }

  weeksInMonth.value = weeksData

  // 设置当前周为包含选中日期的那一周
  const weekIndex = selectedDateWeekIndex.value
  currentWeekIndex.value = weekIndex >= 0 ? weekIndex : findCurrentWeek()
}

// 找到当前日期所在的周索引
const findCurrentWeek = () => {
  const today = dayjs().format('YYYY-MM-DD')
  for (let i = 0; i < weeksInMonth.value.length; i++) {
    if (weeksInMonth.value[i].some((day) => day.date === today)) {
      return i
    }
  }
  return 0 // 默认显示第一周
}

// 周切换事件处理
const onWeekChange = (e: any) => {
  currentWeekIndex.value = e.detail.current
}

// 选择日期
const selectDate = (date: string | undefined) => {
  if (!date) return
  selectedDate.value = date
  applyFilters()
}

// 检查是否是选中的日期
const isSelectedDay = (date: string | undefined) => {
  return date === selectedDate.value
}

// 检查日期是否有记录
const hasRecord = (date: string | undefined) => {
  return date && dateHasRecord.value.includes(date)
}

// 检查是否是当前月份的日期
const isCurrentMonth = (date: string | undefined) => {
  if (!date) return false
  return dayjs(date).format('YYYY-MM') === filterMonth.value
}

// 检查是否是今天
const isToday = (date: string | undefined) => {
  if (!date) return false
  return date === dayjs().format('YYYY-MM-DD')
}

// 监听 selectedDate 变化，更新筛选
watch(selectedDate, () => {
  if (filterMode.value === 'day') {
    applyFilters()
  }
})

// 监听 filterMonth 变化，重新计算周数据
watch(filterMonth, () => {
  calculateWeeksInMonth()
  applyFilters()
  updateDateHasRecord()
})

// 监听 filterMode 变化，更新筛选结果
watch(filterMode, () => {
  applyFilters()
})

// 暴露方法给父组件
defineExpose({
  loadRecords,
  applyFilters,
})
</script>

<style scoped lang="scss">
.progress-history {
  width: 100%;
  padding: 10px 0;
}

.filter-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 5px;
}

.month-selector {
  display: flex;
  align-items: center;
  height: 36px;
}

.month-nav-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-gray-100);
  border-radius: 50%;
  cursor: pointer;
  color: var(--color-gray-600);

  &:hover {
    background: var(--color-gray-200);
    color: var(--color-primary);
  }
}

.current-month {
  font-weight: 600;
  padding: 0 10px;
  min-width: 110px;
  text-align: center;
  color: var(--color-gray-700);
}

.view-mode-toggle {
  display: flex;
  background: var(--color-gray-100);
  border-radius: 20px;
  overflow: hidden;
  padding: 2px;
}

.view-mode-btn {
  padding: 6px 14px;
  font-size: 14px;
  cursor: pointer;
  border-radius: 16px;
  transition: all 0.2s ease;

  &.active {
    background: var(--color-primary);
    color: white;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

/* 周历组件样式 */
.calendar-area {
  padding: 12px 0 10px 0;
  background-color: #fff;
  border-radius: 12px;
  border: 1px solid var(--color-gray-200);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
}

.weekday-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  width: 100%;
  max-width: 360px;
  margin: 0 auto;
  margin-bottom: 5px;
}

.weekday-item {
  width: 35px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 12px;
  color: var(--color-gray-500);
  font-weight: 500;
}

.calendar-wrapper {
  padding: 0;
  width: 100%;
}

.week-swiper {
  height: 56px;
  width: 100%;
}

.swiper-item {
  width: 100% !important;
  display: flex;
  justify-content: center;
}

.week-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  width: 100%;
  max-width: 360px;
  margin: 0 auto;
}

.day-cell {
  width: 35px;
  height: 35px;
  min-width: 35px;
  max-width: 35px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 50%;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0;

  &:hover:not(.selected) {
    background-color: var(--color-gray-100);
  }

  &.selected {
    background-color: var(--color-primary, #007aff);
    color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &.has-record .record-dot {
    width: 4px;
    height: 4px;
    background-color: var(--color-primary, #007aff);
    border-radius: 50%;
    position: absolute;
    bottom: 3px;
  }

  &.selected.has-record .record-dot {
    background-color: #fff;
  }

  &.today:not(.selected) {
    border: 1px solid var(--color-primary, #007aff);
    font-weight: bold;
  }

  &:not(.current-month) {
    opacity: 0.4;
    color: var(--color-gray-400);
  }
}

.day-number {
  font-size: 14px;
  line-height: 1;
}

.records-list {
  position: relative;
  min-height: 150px;
}

.timeline {
  position: relative;
  margin-top: 12px;
  padding-left: 24px;

  &:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 11px;
    width: 2px;
    background: var(--color-gray-200);
  }
}

.timeline-item {
  position: relative;
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.timeline-marker {
  position: absolute;
  top: 2px;
  left: -24px;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-white);
  border: 2px solid var(--color-gray-300);
  border-radius: 50%;
  z-index: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &.is-increase {
    background: var(--color-primary);
    border-color: var(--color-primary);
    color: white;
    box-shadow: 0 2px 6px rgba(0, 122, 255, 0.3);
  }

  i {
    font-size: 11px;
    line-height: 1;
  }
}

.timeline-content {
  background: var(--color-white);
  border-radius: 10px;
  padding: 14px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--color-gray-100);
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    border-color: var(--color-gray-200);
  }
}

.timeline-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.timeline-date {
  font-size: 13px;
  color: var(--color-gray-500);
  font-weight: 500;
}

.timeline-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-value {
  font-weight: 600;
  font-size: 16px;
  color: var(--color-gray-800);
}

.timeline-delete-icon {
  color: var(--color-gray-400);
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    color: var(--color-red-500);
    background: var(--color-red-50);
  }
}

.timeline-body {
  font-size: 14px;
  line-height: 1.5;
  color: var(--color-gray-700);
  position: relative;
  cursor: pointer;
  padding: 8px 0 0 0;
  border-top: 1px solid var(--color-gray-100);
  margin-top: 8px;

  .timeline-edit-hint {
    display: none;
    position: absolute;
    right: 0;
    bottom: 0;
    font-size: 12px;
    color: var(--color-gray-500);
    background: var(--color-white);
    padding: 2px 6px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    i {
      margin-right: 4px;
    }
  }

  &:hover .timeline-edit-hint {
    display: flex;
    align-items: center;
  }
}

.empty-data {
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--color-gray-400);
  text-align: center;
  background-color: var(--color-gray-50);
  border-radius: 12px;
  border: 1px dashed var(--color-gray-200);
  margin-top: 16px;

  .empty-icon {
    font-size: 36px;
    margin-bottom: 12px;
    color: var(--color-gray-300);
  }

  .empty-hint {
    font-size: 13px;
    margin-top: 8px;
    color: var(--color-gray-400);
    max-width: 80%;
    line-height: 1.5;
  }
}

.loading-state {
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--color-gray-500);

  i {
    font-size: 24px;
    margin-bottom: 10px;
  }
}
</style>

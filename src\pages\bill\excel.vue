<template>
  <div class="p-4">
    <div class="flex items-center justify-center h-50">
      <span class="color-gray">单次处理数量：</span>
      <u-input v-model="length" />
    </div>
    <div class="flex items-center h-50" v-if="rawExcelData.length > 0">
      <span class="color-gray">待处理数量：</span>{{ rawExcelData.length }}
    </div>
    <!-- <div class="flex items-center h-50" v-if="loadTimerId > 0">
      <span class="color-gray">ai 分析，耗时：</span>{{ loadTimerId }}s
    </div> -->
    <div v-if="bills.length > 0">
      <uni-table border stripe emptyText="暂无更多数据">
        <!-- 表头行 -->
        <uni-tr>
          <uni-th width="100" align="left">时间</uni-th>
          <uni-th align="left">金额</uni-th>
          <uni-th width="100" align="left">分类</uni-th>
          <uni-th align="left">交易类型</uni-th>
          <uni-th width="100" align="left">交易对方</uni-th>
          <uni-th width="100" align="left">商品</uni-th>
          <uni-th align="left">收支</uni-th>
          <uni-th align="left">支付方式</uni-th>
          <uni-th width="100" align="left">当前状态</uni-th>
          <uni-th width="100" align="left">备注</uni-th>
          <!-- <uni-th align="left">ai 推断理由</uni-th> -->
        </uni-tr>
        <!-- 表格数据行 -->
        <uni-tr v-for="(item, index) in bills" :key="index">
          <template v-if="item.summary">
            <uni-td colspan="11">
              <div class="color-blue">
                日期：{{ item.date }}，总支出：{{ item.totalExpense }}
                {{ item.totalIncome > 0 ? '，总收入：' + item.totalIncome : '' }}
              </div>
            </uni-td>
          </template>
          <template v-else>
            <uni-td>
              {{ item.transactionTime }}
              <u-icon class="cursor-pointer" @click="onDel(item.transactionId)" name="trash" size="28"></u-icon>
              <span v-if="isDuplicateBill(item.transactionId)" class="ml-1 text-red t-22">重</span>
            </uni-td>
            <uni-td v-if="item.businessType === '支出'">
              <div class="color-blue">-{{ item.amount }}</div>
            </uni-td>
            <uni-td v-else>{{ item.amount }}</uni-td>
            <uni-td>
              <picker @change="bindPickerChange($event, item)" :value="item.index" :range="categories"
                range-key="categoryName">
                <div class="uni-input">{{ categories[item.index].categoryName }}</div>
              </picker>
            </uni-td>
            <uni-td>{{ item.transactionType }}</uni-td>
            <uni-td>{{ item.transactionMan }}</uni-td>
            <uni-td>{{ item.commodity }}</uni-td>
            <uni-td>{{ item.businessType }}</uni-td>
            <uni-td>{{ item.payType }}</uni-td>
            <uni-td>{{ item.pyaStatus }}</uni-td>
            <uni-td>
              <u-input v-model="item.remark" />
            </uni-td>
            <!-- <uni-td>{{ item.aiRemark }}</uni-td> -->
          </template>
        </uni-tr>
      </uni-table>
      <!-- <u-button @click="save" class="mt-2">保存</u-button> -->
      <u-button class="mt-2" @click="save(true)">保存并继续处理</u-button>
    </div>
    <template v-else>
      <uni-file-picker class="mt-50" v-model="imageValue" fileMediatype="all" mode="grid" />
      <div v-if="isGetingBill">ai 分析中<u-loading mode="circle"></u-loading></div>
      <div class="cursor-pointer" v-else @click="getBill()">调试</div>
      <div class="cursor-pointer" v-if="hasOldData" @click="hanldOldData()">有未处理的账单，是否继续处理？</div>
    </template>

    <u-modal v-model="show" :show-cancel-button="true" @confirm="handleKnowledgeSubmit" @cancel="handleKnowledgeCancel"
      title="编辑知识库内容">
      <view class="slot-content">
        <u-input type="textarea" v-model="currentKnowledge" placeholder="请输入知识库内容" :autoHeight="true"
          class="w-full knowledge-input"></u-input>
      </view>
    </u-modal>
  </div>
</template>

<script setup>
import dayjs from 'dayjs'
import currency from 'currency.js'

const bills = ref([])
const categories = ref({})
const imageValue = ref('')
const length = ref(10)
const rawExcelData = ref([])
const loadTimerId = ref(0)
const duplicateTransactionIds = ref(new Set())
const show = ref(false)
const currentKnowledge = ref('')

// 添加判断重复账单的方法
const isDuplicateBill = (transactionId) => {
  return duplicateTransactionIds.value.has(transactionId)
}

const sourceFileUrl =
  'https://p26-bot-workflow-sign.byteimg.com/tos-cn-i-mdko3gqilj/f91b836a2027497a932bf33fde668f0a.csv~tplv-mdko3gqilj-image.image?rk3s=81d4c505&x-expires=1764254935&x-signature=KAg1xrdrMI9w06dDlRBNwnCI8HQ%3D&x-wf-file_name=%E5%BE%AE%E4%BF%A1%E6%94%AF%E4%BB%98%E8%B4%A6%E5%8D%95%2820241101-20241201%29%E2%80%94%E2%80%94%E3%80%90%E8%A7%A3%E5%8E%8B%E5%AF%86%E7%A0%81%E5%8F%AF%E5%9C%A8%E5%BE%AE%E4%BF%A1%E6%94%AF%E4%BB%98%E5%85%AC%E4%BC%97%E5%8F%B7%E6%9F%A5%E7%9C%8B%E3%80%91.csv'
const workflowApi = async (workflow_id, params) => {
  const res = await request.post(
    'https://api.coze.cn/v1/workflow/run',
    {
      workflow_id,
      app_id: '7452745454439792676',
      parameters: params,
    },
    {
      timeout: 300000,
      header: {
        Authorization: `Bearer ${getApp().globalData.kzToken}`,
        'Content-Type': 'application/json',
      },
    }
  )
  return {
    ...res,
    data: JSON.parse(res.data),
  }
}

const onDel = (transactionId) => {
  const idx = bills.value.findIndex((item) => item.transactionId === transactionId)
  if (idx !== -1) {
    bills.value.splice(idx, 1)
  }
}

function resetLoadTimer() {
  loadTimerId.value = 0
  document.title = '加载中'
  const timerId = setInterval(() => {
    loadTimerId.value++
    document.title = '加载中' + loadTimerId.value + 's'
  }, 1000)
  return timerId
}

const handleFinally = (timerId) => {
  uni.hideLoading()
  timerId && clearInterval(timerId)
}

const isGetingBill = ref(false)
// 优化 getBill
const getBill = async (fileUrl = sourceFileUrl) => {
  const timerId = resetLoadTimer()
  isGetingBill.value = true
  const classList = await getCategoryApi()
  categories.value = classList.map((item) => {
    return {
      categoryName: item.name,
      categoryId: item._id,
    }
  })
  try {
    const p = {
      classList: categories.value,
      length: length.value,
    }
    console.log('【入参】获取流水', fileUrl)

    if (typeof fileUrl === 'string') {
      p.fileUrl = fileUrl
    } else {
      p.billList = fileUrl
      p.length = fileUrl.length < length.value ? fileUrl.length : length.value
    }
    p.length = p.length * 1

    let { data } = await workflowApi('7452984650220470298', p)
    console.log('【接口数据】AI 添加分类', data)

    // 获取已有账单的 transactionIds
    const allBill = await getBillListApi()
    const existingTransactionIds = new Set(allBill.map(bill => bill.transactionId))

    // 更新重复账单集合
    duplicateTransactionIds.value = existingTransactionIds
    console.log('【重复账单集合】', duplicateTransactionIds.value)

    // 处理账单数据并添加统计信息
    const tempBills = data.aiBills.map((item) => {
      const index = categories.value.findIndex((category) => category.categoryId == item.categoryId)
      return {
        ...item,
        index,
        transactionId: item.transactionId.trim(),
        createTime: dayjs(item.transaction_time).format('YYYY-MM-DD HH:mm'),
      }
    })

    // 按日期分组并添加统计
    const grouped = {}
    tempBills.forEach((b) => {
      const dateKey = dayjs(b.transactionTime).format('YYYY-MM-DD')
      if (!grouped[dateKey]) grouped[dateKey] = []
      grouped[dateKey].push(b)
    })

    // 构建包含统计信息的最终数组
    const result = []
    Object.keys(grouped).forEach((dateKey) => {
      const totalExpense = grouped[dateKey]
        .filter((item) => item.businessType === '支出')
        .reduce((sum, cur) => sum + Number(cur.amount), 0)
        .toFixed(2)
      const totalIncome = grouped[dateKey]
        .filter((item) => item.businessType === '收入')
        .reduce((sum, cur) => sum + Number(cur.amount), 0)
        .toFixed(2)
      result.push({ summary: true, date: dateKey, totalExpense, totalIncome })
      grouped[dateKey].forEach((item) => result.push(item))
    })

    bills.value = result
    if (data.originalDatas.length > 0) rawExcelData.value = data.originalDatas
    document.title = '成功，耗时：' + loadTimerId.value + 's'
  } catch (error) {
    console.log(error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'error',
      duration: 2000,
    })
  } finally {
    handleFinally(timerId)
    isGetingBill.value = false
  }
}

const updateList = []
const bindPickerChange = async (e, item) => {
  if (item.summary) return

  const index = bills.value.findIndex((bill) => bill.transactionId === item.transactionId)
  if (index !== -1) {
    bills.value[index].index = e.detail.value
    bills.value[index].categoryId = categories.value[e.detail.value].categoryId
  }

  const updatedBill = {
    ...bills.value[index],
    categoryId: categories.value[e.detail.value].categoryId,
  }
  updateList.push(updatedBill)

  // 设置默认的知识内容
  let name = `交易对象：${updatedBill.transactionMan}`
  if (updatedBill.commodity) name += `，商品：${updatedBill.commodity}`
  currentKnowledge.value = name

  // 显示编辑模态框
  show.value = true
}

// 更新知识库
const updateKnowledge = async (billsToUpdate = updateList) => {
  const knowledge = [{
    type: billsToUpdate[0].categoryId,
    name: currentKnowledge.value,
  }]
  const params = {
    knowledge,
  }
  try {
    await workflowApi('7452980098364227638', params)
    uni.showToast({
      title: '知识库已更新',
      icon: 'success',
      duration: 2000
    })
  } catch (error) {
    uni.showToast({
      title: '更新失败',
      icon: 'error',
      duration: 2000
    })
  }
}

// 处理知识库更新提交
const handleKnowledgeSubmit = () => {
  if (!currentKnowledge.value.trim()) {
    uni.showToast({
      title: '内容不能为空',
      icon: 'none',
      duration: 2000
    })
    return
  }
  show.value = false
  updateKnowledge()
}

// 处理知识库更新取消
const handleKnowledgeCancel = () => {
  currentKnowledge.value = ''
  show.value = false
}

// 保存到数据库
const save = async (isContinue = false) => {
  // 过滤掉统计行和重复账单
  const billsToSave = bills.value
    .filter(e => !e.summary && !isDuplicateBill(e.transactionId))
    .map((e) => ({
      amount: currency(e.amount).value,
      category: categories.value.find((c) => c.categoryId == e.categoryId).categoryId,
      merchants: e.transactionMan,
      remark: e.remark !== '/' ? e.remark : '',
      type: e.businessType === '支出' ? 'expense' : 'income',
      date: dayjs(e.transactionTime).format('YYYY-MM-DD HH:mm'),
      transactionId: e.transactionId.trim(),
    }))

  await saveBills(billsToSave, isContinue)
}

// 保存账单的通用函数
const saveBills = async (billsToSave, isContinue) => {
  console.log('【保存账单】', billsToSave)

  // 循环保存到数据库
  for (let i = 0; i < billsToSave.length; i++) {
    await addBillApi(billsToSave[i])
  }

  uni.showToast({
    title: '保存成功',
    icon: 'success',
    duration: 2000,
  })
  bills.value = []
  document.title = '保存成功'
  if (updateList.length > 0) updateKnowledge()

  sessionStorage.setItem('rawExcelData', JSON.stringify(rawExcelData.value)) // 缓存原始数据

  const b = rawExcelData.value.slice(0, length.value)
  rawExcelData.value = rawExcelData.value.slice(length.value)
  await getBill(b)
}

watch(imageValue, (value) => {
  const url = value[0].url
  getBill(url)
})
const hasOldData = ref(false)
onMounted(() => {
  const rawExcel = JSON.parse(sessionStorage.getItem('rawExcelData') || '[]')

  if (rawExcel.length > 0) {
    hasOldData.value = true
  }
})
const hanldOldData = () => {
  const rawExcel = JSON.parse(sessionStorage.getItem('rawExcelData') || '[]')

  hasOldData.value = false
  console.log('【缓存数据】', rawExcel)
  rawExcelData.value = rawExcel

  const b = rawExcelData.value.slice(0, length.value)
  rawExcelData.value = rawExcelData.value.slice(length.value)
  getBill(b)
}
</script>
<style scoped>
.grid-table {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
}

.header {
  font-weight: bold;
}

.row {
  display: contents;
}

.uni-input {
  padding: 5px;
  border: 1px solid #ccc;
}

.knowledge-input :deep(.u-textarea__field) {
  min-height: 80px;
  width: 100%;
  padding: 8px;
}

.slot-content {
  padding: 16px;
  width: 100%;
}
</style>

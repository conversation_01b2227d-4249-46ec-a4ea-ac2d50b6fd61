<template>
  <div class="z-confirm-modal" v-if="visible">
    <div class="z-confirm-overlay" @click="handleCancel"></div>
    <div class="z-confirm-content" :class="{ danger: type === 'danger' }">
      <div class="z-confirm-icon" v-if="showIcon">
        <i class="fas" :class="iconClass"></i>
      </div>
      <div class="z-confirm-title">{{ title }}</div>
      <div class="z-confirm-message">{{ message }}</div>
      <div class="z-confirm-actions">
        <button class="z-confirm-cancel" @click="handleCancel">{{ cancelText }}</button>
        <button class="z-confirm-confirm" @click="handleConfirm">{{ confirmText }}</button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'

// 定义组件的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '确认',
  },
  message: {
    type: String,
    default: '确定要执行此操作吗？',
  },
  confirmText: {
    type: String,
    default: '确定',
  },
  cancelText: {
    type: String,
    default: '取消',
  },
  type: {
    type: String,
    default: 'primary',
    validator: (value: string) => ['primary', 'danger', 'warning', 'success'].includes(value),
  },
  showIcon: {
    type: Boolean,
    default: true,
  },
})

// 定义事件
const emit = defineEmits(['confirm', 'cancel', 'update:visible'])

// 计算图标类名
const iconClass = computed(() => {
  switch (props.type) {
    case 'danger':
      return 'fa-exclamation-triangle'
    case 'warning':
      return 'fa-exclamation-circle'
    case 'success':
      return 'fa-check-circle'
    default:
      return 'fa-question-circle'
  }
})

// 处理确认按钮点击
const handleConfirm = () => {
  emit('confirm')
  emit('update:visible', false)
}

// 处理取消按钮点击
const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
}
</script>

<style lang="scss" scoped>
.z-confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.z-confirm-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
}

.z-confirm-content {
  position: relative;
  z-index: 1002;
  background-color: var(--color-white);
  border-radius: var(--rounded-lg);
  box-shadow: var(--shadow-lg);
  width: 80%;
  max-width: 320px;
  padding: 24px;
  text-align: center;
  animation: modalFadeIn 0.3s ease;

  &.danger {
    .z-confirm-icon {
      color: var(--color-danger);
    }

    .z-confirm-confirm {
      background-color: var(--color-danger);

      &:hover {
        background-color: var(--color-danger-dark, #dc2626);
      }
    }
  }
}

.z-confirm-icon {
  font-size: 48px;
  color: var(--color-primary);
  margin-bottom: 16px;
}

.z-confirm-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-gray-800);
  margin-bottom: 12px;
}

.z-confirm-message {
  font-size: 14px;
  color: var(--color-gray-600);
  margin-bottom: 24px;
  line-height: 1.5;
}

.z-confirm-actions {
  display: flex;
  gap: 12px;
  justify-content: center;

  button {
    flex: 1;
    padding: 10px 16px;
    border-radius: var(--rounded-md);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border: none;
  }
}

.z-confirm-cancel {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);

  &:hover {
    background-color: var(--color-gray-200);
  }
}

.z-confirm-confirm {
  background-color: var(--color-primary);
  color: var(--color-white);

  &:hover {
    background-color: var(--color-primary-dark, #2563eb);
  }
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>

import type { OkrStatus, TaskStatus } from '@/constants/status'

declare namespace DB {
  interface Okr {
    _id: string
    // 目标名称
    title: string
    // 目标内容
    content: string
    // 颜色
    color: string
    // 目标开始日期
    startDate: string
    // 目标结束日期
    endDate: string
    // 目标状态
    status?: OkrStatus
    // 当前进度
    curVal?: number
    // 目标动机
    motivation: {
      title: string
      content: string
    }[]
    // 可行性
    feasibility: {
      title: string
      content: string
    }[]
    // 更新时间
    updateTime: string
    // 创建时间
    createTime: string
    // 删除时间
    deleteTime: string
  }
  interface Task {
    _id: string
    // 任务状态 0 未完成（默认值），1 已完成，2 放弃
    status?: 0 | 1 | 2
    // 名称
    title: string
    // 内容
    content: string
    // 类型 kr: 关键结果/量化任务，todo: 待办任务
    type: 'kr' | 'todo'
    // 关联的 okr id
    okrId: string
    // 父级任务 id
    parentId?: string
    // 任务开始日期
    startDate: string
    // 任务结束日期
    endDate?: string

    /**
     * 循环待办 重复规则 RRULE:FREQ=DAILY;UNTIL=20110701T100000Z;INTERVAL=1
     * FREQ: 事件的频率单位，DAILY（每天），WEEKLY（每周），MONTHLY（每月）
     * UNTIL: 事件重复的结束日期
     * COUNT: 事件重复的次数
     * INTERVAL: 事件重复的间隔
     * BYDAY: 在周频率中定义特定的日子，例如 MO（周一），TU（周二）等。例子：FREQ=WEEKLY;INTERVAL=2;BYDAY=MO,WE,FR 表示事件每两周的周一、周三和周五发生。
     * BYMONTHDAY: 在月频率中定义特定的日子，例如 1（每月的第一天），10（每月的第十天）等。
     * BYMONTH: 在年频率中定义特定的月份，例如 1（一月），10（十月）等。
     */
    repeatFlag?: string
    // 量化任务的初始值
    initVal?: number
    // 量化任务的目标值
    tgtVal?: number
    // 任务权重，用于标识任务优先级或重要性
    weight?: number
    // 量化任务的当前值（通过计算得出，不存数据库）
    curVal?: number
    /**
     * 重复任务完成的信息集合
     */
    compInfos?: string | TaskRec[]
    // 量化历史记录
    recList?: string | RecList[]
    // // 循环任务完成记录
    // compDates?: string | RecDetail[]
    // 进度值（完成此任务增加的进度，非量化任务时使用）
    progVal?: number
    // 取值方式 求和（默认值），最新值，平均值，最大值
    valType?: 'sum' | 'latest' | 'avg' | 'max'
    // 每日目标量，自定义设置的每日要达到的目标值
    dailyTarget?: number
    // 完成时间
    completeTime?: string
    // 截止时间
    dueTime?: string
    // 删除时间
    deleteTime: string
    // 更新时间
    updateTime: string
    // 创建时间
    createTime: string
  }
  interface Memo {
    _id: string
    // 父级 id
    parentId?: string
    // 标题
    title: string
    // 内容
    content: string
    // 类型 diary：日回顾 week：周回顾 month：月回顾 tag: 标签 weekGoal: 周目标 weekReview: 周复盘
    type: string
    // 今日总结的日期
    date: string
    // AI 分析结果，JSON 字符串格式
    aiAnalysis?: string
    // 标签列表，JSON 字符串格式
    tagsList?: string
    // 更新时间
    updateTime?: string
    // 创建时间
    createTime?: string
    // 删除时间
    deleteTime?: string
  }
  interface OkrConfig {
    _id: string
    key: string
    value: string
    // 删除时间
    deleteTime: string
    // 更新时间
    updateTime: string
    // 创建时间
    createTime: string
  }
  interface Bill {
    _id: string
    // 金额
    amount: number
    // 收支类型 'expense' | 'income'
    type: 'expense' | 'income'
    // 分类
    category: string
    // 备注
    remark: string
    // 商家
    merchants: string
    // 交易号
    transactionId: string
    // 日期
    date: string
    // 账户
    account: string
    // 删除时间
    deleteTime: string
    // 更新时间
    updateTime: string
    // 创建时间
    createTime: string
  }

  interface TaskRec {
    // 4 位随机数
    key: string
    // 量化值
    val: number
    // 完成时间
    compTime: string
    // 更新时间
    updateTime: string
    // 备注
    remark?: string
  }
  interface RecList {
    _id: string
    // 量化值
    val: number
    // 记录时间
    recTime: string
    // 更新时间
    updateTime: string
    // 关联的子任务 ID
    taskId?: string
    // 备注
    remark?: string
  }
  // 将指定字段设置为可选
  type OptionalKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
  // 将指定字段设置为必选
  type RequiredKeys<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>

  interface ChatRecord {
    _id: string
    // 标题
    title: string
    // 内容
    content: string
    // 删除时间
    deleteTime: string
    // 创建时间
    createTime: string
    // 更新时间
    updateTime: string
    // 是否同步
    isDirty: number
  }
}

declare namespace API {
  // 新增备忘录接口参数
  interface NewMemo {
    // 父级 id
    parentId?: string
    // 标题
    title?: string
    // 内容
    content: string
    // 类型 diary 日回顾，week 周回顾，month 月回顾，tag 标签，weekGoal 周目标，weekReview 周复盘，weekOverview 周概览
    type: string
    // 日期，对于周目标和周复盘，格式为 YYYY-WW
    date: string
    // 标签列表，JSON 字符串格式
    tagsList?: string
  }

  // 编辑备忘录接口参数
  interface EditMemo {
    // 标题
    title?: string
    // 内容
    content?: string
    // 类型
    type?: string
    // 日期
    date?: string
    // 标签列表，JSON 字符串格式
    tagsList?: string
  }

  // OKR 编辑接口参数
  interface EditOkr {
    title?: string
    content?: string
    color?: string
    startDate?: string
    endDate?: string
    status?: 'pending' | 'inProgress' | 'completed' | 'paused' | 'abandoned'
    motivation?: {
      title: string
      content: string
    }[]
    feasibility?: {
      title: string
      content: string
    }[]
  }
}

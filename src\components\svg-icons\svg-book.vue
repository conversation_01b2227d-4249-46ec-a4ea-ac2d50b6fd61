<template>
  <BaseSvgIcon :path="path" :viewBox="viewBox" v-bind="$attrs" />
</template>

<script setup>
import BaseSvgIcon from './BaseSvgIcon.vue'

defineProps({
  viewBox: {
    type: String,
    default: '0 0 1024 1024',
  },
  path: {
    type: String,
    default:
      'M256 0C150 0 64 86 64 192v640c0 106 86 192 192 192h640c35.4 0 64-28.6 64-64s-28.6-64-64-64v-128c35.4 0 64-28.6 64-64V64c0-35.4-28.6-64-64-64H256z m0 768h512v128H256c-35.4 0-64-28.6-64-64s28.6-64 64-64z m64-480c0-17.6 14.4-32 32-32h384c17.6 0 32 14.4 32 32s-14.4 32-32 32H352c-17.6 0-32-14.4-32-32z m32 96h384c17.6 0 32 14.4 32 32s-14.4 32-32 32H352c-17.6 0-32-14.4-32-32s14.4-32 32-32z',
  },
})
</script>

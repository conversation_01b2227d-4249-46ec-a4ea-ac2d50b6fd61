# 任务：多次输入功能集成

- **所属功能模块**: `diary-entry`
- **任务ID**: `task-diary-entry-04`
- **优先级**: 高

## 任务描述
将本模块开发的各个UI组件（时间轴、快捷按钮、编辑器）与数据处理逻辑整合起来，实现完整的"多次输入日记"功能，并集成AI即时回应。

## 技术实现详情
1.  **页面整合**:
    - 在日记主页面，集成 `l-diary-timeline` 和 `l-quick-input` 组件。
    - `l-quick-input` 点击后，应能正确唤起 `z-diary-editor`。
2.  **数据流**:
    - 从数据库加载当天的 `DiaryContent` 对象，将其中的 `entries` 数组传递给 `l-diary-timeline`。
    - `z-diary-editor` 保存后，获取返回的日记条目对象。
    - 将新条目添加到当前 `entries` 数组中，并更新 `l-diary-timeline` 的显示。
    - 将更新后的整个 `DiaryContent` 对象序列化，并保存回数据库。
3.  **AI即时回应**:
    - 在用户保存一条新日记后，异步调用AI服务接口，传入新条目的内容。
    - 获取AI生成的简短回应（`ai_response`）。
    - 将该回应更新到对应的日记条目中，并刷新UI。
    - 同时，将带有AI回应的数据更新到数据库。
    - 此过程需处理好加载状态和潜在的API错误。

## 验收标准
- 用户可以通过快捷按钮打开日记编辑器。
- 用户成功保存新日记后，内容立即显示在时间轴上。
- 保存的日记数据能被正确持久化到数据库。
- 新增日记后，AI的即时回应能在几秒内显示在对应条目下。
- 整个流程数据流正确，UI响应及时。

## 依赖关系
- `task-diary-data-01`: 需要其提供的数据读写和兼容性处理能力。
- `task-diary-entry-01`: 需要 `l-diary-timeline` 组件。
- `task-diary-entry-02`: 需要 `l-quick-input` 组件。
- `task-diary-entry-03`: 需要 `z-diary-editor` 组件。

## 状态追踪
- 未开始 
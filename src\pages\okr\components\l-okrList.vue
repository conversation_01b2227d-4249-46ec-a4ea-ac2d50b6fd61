<template>
  <view class="pl5">
    333
    <view v-for="item in props.list" :key="item._id" class="mt10">
      abc
      <view v-if="item.children">
        <l-okr-list :list="item.children" />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import lOkrList from './l-okrList'
const props = defineProps({
  list: {
    type: Array,
    default: [],
  },
})
</script>

<style scoped lang="scss"></style>

// 测试模块
// 包含各种功能模块的测试用例

const { ExecutionContextManager } = require('./context')
const { IntelligentExecutionPlanner } = require('./planner')
const { simulateToolCall } = require('./tools')

// 测试用例 1: 执行上下文管理器测试
async function testExecutionContextManager() {
  console.log('=== 测试执行上下文管理器 ===')

  const context = new ExecutionContextManager('session-1', '查看 okr 项目的任务')

  // 测试项目结果提取
  const projectResult = {
    success: true,
    data: [
      { id: 'proj-1', name: 'OKR 项目', description: '目标管理项目' },
      { id: 'proj-2', name: '日常任务', description: '日常工作任务' },
    ],
  }

  context.setStepResult('step-1', projectResult)

  console.log('上下文数据：', Array.from(context.contextData.keys()))
  console.log('目标项目：', context.getContextData('targetProject'))

  // 测试任务结果提取
  const taskResult = {
    success: true,
    tasks: [
      { id: 'task-1', title: '制定 Q1 目标', completed: false },
      { id: 'task-2', title: '更新 KR 进度', completed: true },
    ],
  }

  context.setStepResult('step-2', taskResult)
  console.log('任务统计：', context.getContextData('taskCount'))
  console.log('未完成任务：', context.getContextData('uncompletedTasks'))
}

// 测试用例 2: 执行计划生成测试
async function testExecutionPlanner() {
  console.log('\n=== 测试执行计划生成器 ===')

  // 测试查找任务计划（包含项目关键词）
  const plan1 = await IntelligentExecutionPlanner.generatePlan('查看 okr 项目的任务', 'find_task')
  console.log('项目任务查询计划：')
  console.log('- 计划 ID:', plan1.planId)
  console.log('- 总步骤数：', plan1.totalSteps)
  console.log(
    '- 步骤详情：',
    plan1.steps.map((s) => ({ toolName: s.toolName, description: s.description }))
  )

  // 测试查找任务计划（不包含项目关键词）
  const plan2 = await IntelligentExecutionPlanner.generatePlan('查看我的任务', 'find_task')
  console.log('\n直接任务查询计划：')
  console.log('- 计划 ID:', plan2.planId)
  console.log('- 总步骤数：', plan2.totalSteps)
  console.log(
    '- 步骤详情：',
    plan2.steps.map((s) => ({ toolName: s.toolName, description: s.description }))
  )

  // 测试聊天类型（不生成计划）
  const plan3 = await IntelligentExecutionPlanner.generatePlan('你好', 'chat')
  console.log('\n聊天类型计划：')
  console.log('- 总步骤数：', plan3.totalSteps)
}

// 测试用例 3: 模拟工具调用测试
async function testSimulateToolCall() {
  console.log('\n=== 测试模拟工具调用 ===')

  try {
    // 测试获取项目
    console.log('测试 getProjects:')
    const projectsResult = await simulateToolCall('getProjects', { filter: 'okr' })
    console.log('- 成功：', projectsResult.success)
    console.log('- 项目数量：', projectsResult.data?.length)
    console.log(
      '- 项目列表：',
      projectsResult.data?.map((p) => p.name)
    )

    // 测试获取任务
    console.log('\n测试 getTasks:')
    const tasksResult = await simulateToolCall('getTasks', { projectId: 'proj-1', completed: false })
    console.log('- 成功：', tasksResult.success)
    console.log('- 任务数量：', tasksResult.tasks?.length)
    console.log(
      '- 任务列表：',
      tasksResult.tasks?.map((t) => t.title)
    )

    // 测试未知工具
    console.log('\n测试未知工具：')
    try {
      await simulateToolCall('unknownTool', {})
    } catch (error) {
      console.log('- 预期错误：', error.message)
    }
  } catch (error) {
    console.error('工具调用测试失败：', error.message)
  }
}

// 测试用例 4: 关键词提取测试
function testKeywordExtraction() {
  console.log('\n=== 测试关键词提取 ===')

  const testCases = ['查看 okr 项目的任务', '显示 project 任务', '我想看看日常项目', '查看所有任务']

  testCases.forEach((input) => {
    const keyword = IntelligentExecutionPlanner.extractProjectKeyword(input)
    console.log(`输入："${input}" -> 关键词："${keyword}"`)
  })
}

// 运行所有测试
async function runAllTests() {
  console.log('开始 V1.1 功能测试...\n')

  try {
    await testExecutionContextManager()
    await testExecutionPlanner()
    await testSimulateToolCall()
    testKeywordExtraction()

    console.log('\n✅ 所有测试完成！')
  } catch (error) {
    console.error('\n❌ 测试失败：', error.message)
    console.error(error.stack)
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests()
}

module.exports = {
  testExecutionContextManager,
  testExecutionPlanner,
  testSimulateToolCall,
  testKeywordExtraction,
  runAllTests,
}

<template>
  <view class="login-container">
    <view class="login-header">
      <text class="title">滴答清单登录</text>
      <text class="subtitle">连接您的滴答清单账户</text>
    </view>

    <view class="login-form">
      <view class="form-item">
        <text class="label">用户名</text>
        <input
          v-model="formData.username"
          class="input"
          placeholder="请输入邮箱或手机号"
          :disabled="loading"
        />
      </view>

      <view class="form-item">
        <text class="label">密码</text>
        <input
          v-model="formData.password"
          class="input"
          type="password"
          placeholder="请输入密码"
          :disabled="loading"
        />
      </view>

      <view class="form-actions">
        <view
          class="login-btn"
          :class="{ disabled: loading || !canSubmit }"
          @click="handleLogin"
        >
          <text v-if="loading">登录中...</text>
          <text v-else>登录</text>
        </view>
      </view>
    </view>

    <view v-if="userInfo" class="user-info">
      <text class="info-title">登录成功</text>
      <view class="info-item">
        <text class="info-label">用户名:</text>
        <text class="info-value">{{ userInfo.username }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">用户ID:</text>
        <text class="info-value">{{ userInfo.userId }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">专业版:</text>
        <text class="info-value">{{ userInfo.pro ? '是' : '否' }}</text>
      </view>
      <view class="logout-btn" @click="handleLogout">
        <text>退出登录</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { 
  didaPasswordLogin, 
  isDidaLoggedIn, 
  getDidaUserInfo, 
  clearDidaLoginInfo,
  saveDidaLoginInfo 
} from '@/api/dida.js'

// 响应式数据
const loading = ref(false)
const userInfo = ref(null)
const formData = ref({
  username: '',
  password: ''
})

// 计算属性
const canSubmit = computed(() => {
  return formData.value.username.trim() && formData.value.password.trim()
})

// 处理登录
const handleLogin = async () => {
  if (loading.value || !canSubmit.value) return

  loading.value = true
  
  try {
    const result = await didaPasswordLogin(
      formData.value.username.trim(),
      formData.value.password.trim()
    )
    
    // 保存登录信息
    saveDidaLoginInfo(result)
    userInfo.value = result
    
    // 清空表单
    formData.value = {
      username: '',
      password: ''
    }
    
    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })
    
  } catch (error) {
    console.error('登录失败:', error)
    uni.showToast({
      title: error.message || '登录失败，请重试',
      icon: 'none',
      duration: 3000
    })
  } finally {
    loading.value = false
  }
}

// 处理退出登录
const handleLogout = () => {
  clearDidaLoginInfo()
  userInfo.value = null
  uni.showToast({
    title: '已退出登录',
    icon: 'success'
  })
}

// 页面加载时检查登录状态
onMounted(() => {
  if (isDidaLoggedIn()) {
    userInfo.value = getDidaUserInfo()
  }
})
</script>

<style scoped>
.login-container {
  padding: 40rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.login-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.login-form {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.form-item {
  margin-bottom: 40rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.input:focus {
  border-color: #007aff;
}

.form-actions {
  margin-top: 60rpx;
}

.login-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #007aff, #5ac8fa);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
}

.login-btn.disabled {
  background: #ccc;
  color: #999;
}

.user-info {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.info-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-of-type {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.logout-btn {
  margin-top: 40rpx;
  width: 100%;
  height: 70rpx;
  background: #ff3b30;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28rpx;
}
</style>

<template>
  <BaseSvgIcon :path="path" :viewBox="viewBox" v-bind="$attrs" />
</template>

<script setup>
import BaseSvgIcon from './BaseSvgIcon.vue'

defineProps({
  viewBox: {
    type: String,
    default: '0 0 1024 1024',
  },
  path: {
    type: String,
    default:
      'M0 384c0-70.6 57.4-128 128-128h3.2C146 183 210.6 128 288 128c30 0 58 8.2 81.8 22.4C396.4 99.2 450.2 64 512 64s115.6 35.2 142.2 86.4C678 136.2 706 128 736 128c77.4 0 142 55 156.8 128h3.2c70.6 0 128 57.4 128 128 0 23.4-6.2 45.2-17.2 64H17.2C6.2 429.2 0 407.4 0 384z m0 182.8C0 536.6 24.6 512 54.8 512h914.2c30.2 0 54.8 24.6 54.8 54.8 0 141-88.8 261.4-213.4 308.2l-3.4 29c-4 32-31.2 56-63.6 56H280.4c-32.2 0-59.6-24-63.6-56l-3.6-28.8C88.8 828.2 0 707.8 0 566.8z',
  },
})
</script>

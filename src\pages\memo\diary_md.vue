<template>
  <div class="p-4">
    <view style="margin: 0 -32rpx">
      <z-calendar ref="cRef" @on-change="getDay" @on-show-today="(isShow) => (showTodayBtn = isShow)" />
    </view>
    <view v-show="!isEdit">
      <view v-if="markdownContent">
        <view v-html="markdownContent" class="mb-2"></view>
      </view>
      <view v-else class="mt-20">
        <u-empty text="今天没写" mode="data"></u-empty>
      </view>
      <view class="fixed bottom-100 right-50" style="bottom: calc(var(--window-bottom) + 20px)">
        <u-icon size="60" @click="onEdit" name="edit-pen-fill" style="color: blue"></u-icon>
      </view>
    </view>
    <view v-show="isEdit">
      <view class="flex flex-wrap gap-2">
        <view
          v-for="(tag, index) in tags"
          :key="index"
          class="px-4 py-2 rounded-full text-sm transition-colors"
          :class="{
            'bg-gray-200': !selectedTags.includes(tag),
            'bg-blue-500 text-white': selectedTags.includes(tag),
          }"
          @click="toggleTag(tag)"
          @longpress="handleLongPress(tag)"
        >
          {{ tag }}
        </view>
        <view
          class="mt-3 ml-2 w-20 h-20 flex items-center justify-center bg-gray-200 rounded-full cursor-pointer"
          @click="openAddTagDialog"
        >
          <text class="text-lg">+</text>
        </view>
      </view>
      <u-input
        class="mt-4"
        v-model="contentText"
        type="textarea"
        :border="true"
        :height="100"
        :maxlength="3000"
        :auto-height="true"
      />
      <u-button class="mb-4 mt-4" type="primary" @click="onSubmit">提交</u-button>
      <view class="fixed bottom-100 right-50" style="bottom: calc(var(--window-bottom) + 20px)">
        <u-icon size="60" @click="isEdit = false" name="skip-back-left" style="color: blue"></u-icon>
      </view>
    </view>
  </div>
</template>

<script setup>
import MarkdownIt from 'markdown-it'
const md = new MarkdownIt()
const hasDiaryList = ref([])
const curDay = ref('')
const cRef = ref()
const isEdit = ref(false)
const toggleTag = (tag) => {
  const index = selectedTags.value.indexOf(tag)
  if (index === -1) {
    selectedTags.value.push(tag)
  } else {
    selectedTags.value.splice(index, 1)
  }
}
const contentText = ref('')
const memoForm = ref({})
const tags = ref(uni.getStorageSync('diaryTags'))

const handleLongPress = (tag) => {
  uni.showModal({
    title: '删除标签',
    content: `确定要删除"${tag}"标签吗？`,
    success: ({ confirm }) => {
      if (confirm) {
        // 删除标签
        const index = tags.value.indexOf(tag)
        if (index > -1) {
          tags.value.splice(index, 1)
          uni.setStorageSync('diaryTags', tags.value)

          // 从已选标签中移除
          const selectedIndex = selectedTags.value.indexOf(tag)
          if (selectedIndex > -1) {
            selectedTags.value.splice(selectedIndex, 1)
          }
        }
      }
    },
  })
}
const selectedTags = ref([])
const aiRes = ref()
const sys = useIsPC()
// 页面数据
const editorCtx = ref(null)

// 组件挂载时初始化
onMounted(() => {
  // 初始化标签缓存
  if (!uni.getStorageSync('diaryTags')) {
    uni.setStorageSync('diaryTags', ['今天完成了哪些？', '有什么感悟？', '今日完成的最主要事情是什么？'])
  }

  // 获取当前日期
  curDay.value = dayjs().format('YYYY-MM-DD')
  getDay()

  // 获取本月日记
  monthSwitch({
    year: dayjs().year(),
    month: dayjs().month() + 1,
  })
})
/**
 * 获取指定日期的日记数据
 * @param {string} [date=''] - 日期字符串（格式：YYYY-MM-DD）
 * @returns {Promise<void>}
 */
const getDay = async (date = '') => {
  curDay.value = date
  console.log(curDay.value)
  // if (type === 'prev') {
  //   uni.vibrateShort()
  //   curDay.value = dayjs(curDay.value).subtract(1, 'day').format('YYYY-MM-DD')
  // } else if (type === 'next') {
  //   uni.vibrateShort()
  //   curDay.value = dayjs(curDay.value).add(1, 'day').format('YYYY-MM-DD')
  // }
  uni.showLoading({
    title: '加载中',
    mask: true,
  })
  const { data: content } = await reqLife.post(`/getNote`, {
    key: `日记/${curDay.value}.md`,
  })
  uni.hideLoading()

  if (content) {
    aiRes.value = content
  } else {
    aiRes.value = ''
  }
}

const openAddTagDialog = () => {
  uni.showModal({
    title: '新建标签',
    editable: true,
    placeholderText: '请输入标签内容',
    success: ({ confirm, content }) => {
      if (confirm && content) {
        if (!tags.value.includes(content)) {
          tags.value.push(content)
          uni.setStorageSync('diaryTags', tags.value)
        } else {
          uni.showToast({ title: '标签已存在', icon: 'none' })
        }
      }
    },
  })
}

/** 进入编辑模式
 * 1. 初始化编辑器内容
 * 2. 切换编辑状态
 */
const onEdit = () => {
  uni.vibrateShort()
  editorCtx.value?.setContents({
    html: memoForm.value.content,
  })
  isEdit.value = true
}

/** 执行工作流 API
 * @param {string} workflow_id - 工作流 ID
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} 包含处理结果的响应对象
 */
const workflowApi = async (workflow_id, params) => {
  const res = await request.post(
    'https://api.coze.cn/v1/workflow/run',
    {
      workflow_id,
      app_id: '7484161211752431679',
      parameters: params,
    },
    {
      timeout: 300000,
      header: {
        Authorization: `Bearer ${getApp().globalData.kzToken}`,
        'Content-Type': 'application/json',
      },
    }
  )
  return {
    ...res,
    data: JSON.parse(res.data),
  }
}

const markdownContent = computed(() => {
  return aiRes.value ? renderMarkdown(aiRes.value) : ''
})
// 提交
/**
 * 提交日记内容
 * 处理编辑器内容校验、保存数据到接口
 */
const onSubmit = async () => {
  uni.vibrateShort()
  uni.showLoading({
    title: 'ai 复盘中',
    mask: true,
  })
  let { data } = await workflowApi('7484172363748130835', {
    content: contentText.value,
    question: selectedTags.value,
  })
  uni.hideLoading()
  console.log(data.input)
  aiRes.value = data.input + '\n\n' + '### 原文\n' + contentText.value
  isEdit.value = false

  await reqLife.post(`/createNote`, {
    content: aiRes.value,
    fileName: `日记/${curDay.value}.md`,
  })
}

/**
 * 日历确认回调
 * @param {Object} dateObj - 选中日期对象
 * @param {string} dateObj.year - 年份
 * @param {string} dateObj.month - 月份
 * @param {string} dateObj.date - 日期
 */
const onCalendar = ({ year, month, date }) => {
  curDay.value = dayjs(year + month + date).format('YYYY-MM-DD')
  getDay()
}
/**
 * 切换月份获取日记列表
 * @param {Object} param - 包含年份和月份的对象
 * @param {number} param.year - 年份
 * @param {number} param.month - 月份（1-12）
 */
const monthSwitch = ({ year, month }) => {
  getDiaryListApi({
    startDate: dayjs(`${year}-${month}-01`).format('YYYY-MM-DD'),
    endDate: dayjs(`${year}-${month}-01`).endOf('month').format('YYYY-MM-DD'),
  }).then((res) => {
    hasDiaryList.value = res.map((item) => {
      return {
        date: item.date,
      }
    })
  })
}
</script>

<template>
  <div class="okr-detail-page">
    <z-page-navbar title="目标详情" rightButtonType="more" @back="goBack" @menuItemClick="handleMenuAction">
      <template #menu-items>
        <div class="action-menu-item" @click="handleMenuAction('edit')">
          <i class="fas fa-edit"></i>
          <span>编辑</span>
        </div>
        <div class="action-menu-item start" @click="handleMenuAction('start')">
          <i class="fas fa-play-circle"></i>
          <span>进行</span>
        </div>
        <div class="action-menu-item complete" @click="handleMenuAction('complete')">
          <i class="fas fa-check-circle"></i>
          <span>完成</span>
        </div>
        <div class="action-menu-item pause" @click="handleMenuAction('pause')">
          <i class="fas fa-pause-circle"></i>
          <span>暂停</span>
        </div>
        <div class="action-menu-item delete" @click="handleMenuAction('delete')">
          <i class="fas fa-trash"></i>
          <span>删除</span>
        </div>
        <div class="action-menu-item abandon" @click="handleMenuAction('abandon')">
          <i class="fas fa-times-circle"></i>
          <span>放弃</span>
        </div>
      </template>
    </z-page-navbar>

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 目标概览卡片 -->
      <div class="card header-card" v-if="objective">
        <h1 class="goal-title">{{ objective.title }}</h1>

        <div
          v-if="objective.description"
          class="goal-description"
          :class="{ expanded: isDescriptionExpanded }"
          @click="toggleDescription"
        >
          {{ objective.description }}
        </div>
        <div
          class="toggle-description"
          @click.stop="toggleDescription"
          v-if="objective.description && objective.description.length > 100"
        ></div>

        <!-- 动机部分 -->
        <div class="motivation-section" v-if="motivations.length > 0">
          <div class="motivation-chips">
            <div
              class="motivation-chip"
              v-for="(item, index) in motivations"
              :key="index"
              @click="showFullMotivation(item.content)"
            >
              <i class="fas fa-quote-left"></i>
              <span class="motivation-text">{{ item.content }}</span>
            </div>
          </div>
        </div>

        <div class="time-info-container">
          <div class="time-card">
            <i class="fas fa-calendar-plus"></i>
            <div class="time-content">
              <div class="time-label">开始时间</div>
              <div class="time-value">{{ formatDate(objective.startDate) }}</div>
            </div>
          </div>
          <div class="time-card">
            <i class="fas fa-calendar-check"></i>
            <div class="time-content">
              <div class="time-label">结束时间</div>
              <div class="time-value">{{ formatDate(objective.endDate) }}</div>
            </div>
          </div>
          <div class="time-card urgent">
            <i class="fas fa-hourglass-half"></i>
            <div class="time-content">
              <div class="time-label">剩余时间</div>
              <div class="time-value">{{ getRemainingDays(objective.endDate) }}</div>
            </div>
          </div>
          <div class="time-card progress">
            <i class="fas fa-check-circle"></i>
            <div class="time-content">
              <div class="time-label">已完成 KR</div>
              <div class="time-value">{{ getCompletedKrs() }}</div>
            </div>
          </div>
        </div>

        <div class="progress-container">
          <div class="progress-info">
            <div class="progress-label">总体进度</div>
            <div class="progress-value">
              {{ getOverallProgress() }}%
              <span v-if="getOverallProgress() >= 100" class="complete-badge">
                <i class="fas fa-check-circle"></i> 已完成
              </span>
            </div>
          </div>
          <div class="progress-bar">
            <div
              class="progress-fill"
              :class="{ 'progress-fill-completed': getOverallProgress() >= 100 }"
              :style="{ width: getOverallProgress() + '%' }"
            ></div>
          </div>
        </div>
      </div>

      <!-- 关键结果区域 -->
      <h2 class="section-title"><i class="fas fa-bullseye"></i>关键结果 (KRs)</h2>

      <div
        class="kr-card"
        v-for="kr in keyResults"
        :key="kr._id"
        :style="kr.tasksVisible ? {} : { '--progress-width': getKrProgress(kr) + '%' }"
        @click="goToKrDetail(kr._id)"
      >
        <div class="kr-header">
          <div class="kr-title">
            {{ kr.title }}
          </div>
          <div class="kr-meta-right">
            <div class="kr-progress">{{ getKrProgress(kr) }}%</div>
          </div>
        </div>
        <div class="kr-meta">
          <!-- <div class="kr-tag deadline">
            <span>截止：{{ formatDate(kr.endDate) }}</span>
          </div> -->
          <div class="kr-tag weight" v-if="kr.weight !== undefined">
            <i class="fas fa-balance-scale"></i>
            <span>权重：{{ kr.weight }}</span>
          </div>
          <div class="kr-tag value" v-if="kr.curVal !== undefined && kr.tgtVal !== undefined">
            <i class="fas fa-chart-line"></i>
            <span>{{ kr.curVal }}/{{ kr.tgtVal }}{{ kr.unit || '' }}</span>
          </div>
          <div class="kr-tag tasks" @click.stop="toggleKrTasks(kr._id)" v-if="kr.subtasks && kr.subtasks.length > 0">
            <span>{{ getCompletedTasksCount(kr) }}/{{ kr.subtasks.length }} 任务</span>
          </div>
          <div class="kr-tag update-progress" @click.stop="openUpdateProgressModal(kr)">
            <i class="fas fa-chart-line"></i>
            <span>更新进度</span>
          </div>
        </div>
        <div :id="'kr-tasks-' + kr._id" class="kr-tasks" :class="{ visible: kr.tasksVisible }">
          <div class="kr-task-item" v-for="task in kr.subtasks" :key="task._id" :class="{ completed: task.completed }">
            <z-task-checkbox
              :task-id="task._id"
              :status="task.completed ? 1 : 0"
              :parent-id="kr._id"
              :progress-value="task.progressValue || 5"
              @status-change="handleKrTaskStatusChange"
            />
            <div class="kr-task-content">
              <div class="kr-task-title" @click.stop="editTask(task, kr)">{{ task.title }}</div>
              <div class="kr-task-meta">
                <div class="kr-task-deadline" v-if="task.endDate">
                  <i class="fas fa-calendar-alt"></i>
                  <span>{{ formatDate(task.endDate) }}</span>
                </div>
                <div class="kr-task-progress">
                  <i class="fas fa-chart-line"></i>
                  <span>贡献值：{{ task.progressValue }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="add-btn" @click="addKeyResult"><i class="fas fa-plus"></i>添加关键结果</div>

      <!-- 任务列表区域 -->
      <!-- <h2 class="section-title"><i class="fas fa-tasks"></i>相关任务</h2>
      <div class="task-list">
        <div class="task-item" v-for="task in relatedTasks" :key="task._id">
          <z-task-checkbox
            :task-id="task._id"
            :status="task.completed ? 1 : 0"
            @status-change="handleRelatedTaskStatusChange"
          />
          <div class="task-content">
            <div
              class="task-title"
              :style="task.completed ? { textDecoration: 'line-through', color: 'var(--color-gray-400)' } : {}"
            >
              {{ task.title }}
            </div>
            <div class="task-meta">
              <span>{{ getTaskStatusText(task) }}</span>
              <span v-if="task.tag" :class="['badge', task.tag.type]">{{ task.tag.text }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="add-btn" @click="addTask"><i class="fas fa-plus"></i>添加任务</div> -->

      <!-- 阶段性反思 -->
      <!-- <h2 class="section-title"><i class="fas fa-lightbulb"></i>阶段性反思</h2>
      <div class="card reflection-card" v-for="reflection in reflections" :key="reflection._id">
        <p style="margin-bottom: 1rem; color: var(--color-gray-700)">{{ reflection.text }}</p>
        <div class="list-items">
          <div class="list-item" v-for="(item, index) in reflection.points" :key="index">
            {{ item }}
          </div>
        </div>
        <div style="text-align: right; margin-top: 0.75rem; font-size: 0.75rem; color: var(--color-gray-500)">
          更新于 {{ formatDate(reflection.date) }}
        </div>
      </div>
      <div class="add-btn" @click="addReflection"><i class="fas fa-plus"></i>添加反思记录</div> -->
    </div>

    <!-- 引入任务编辑模态框组件 -->
    <z-task-modal
      v-model="showTaskModal"
      :title="modalTitle"
      :is-edit="true"
      :task-id="currentTaskId"
      :initial-data="currentTaskData"
      :kr-info="currentKrInfo"
      @save-success="onTaskSaved"
    />

    <!-- 引入确认删除弹窗组件 -->
    <z-confirm-modal
      v-model:visible="showDeleteConfirmModal"
      title="确认删除"
      message="确定要删除此目标吗？该操作不可恢复。"
      confirm-text="删除"
      cancel-text="取消"
      type="danger"
      @confirm="deleteOkr"
    />

    <!-- 引入确认放弃弹窗组件 -->
    <z-confirm-modal
      v-model:visible="showAbandonConfirmModal"
      title="确认放弃"
      message="确定要放弃此目标吗？放弃后状态将变为已放弃，但数据不会被删除。"
      confirm-text="放弃"
      cancel-text="取消"
      type="warning"
      @confirm="abandonOkr"
    />

    <!-- 引入确认开始弹窗组件 -->
    <z-confirm-modal
      v-model:visible="showStartConfirmModal"
      title="确认开始"
      message="确定要开始执行此目标吗？状态将变为进行中。"
      confirm-text="开始"
      cancel-text="取消"
      type="primary"
      @confirm="startOkr"
    />

    <!-- 引入确认完成弹窗组件 -->
    <z-confirm-modal
      v-model:visible="showCompleteConfirmModal"
      title="确认完成"
      message="确定要将此目标标记为已完成吗？"
      confirm-text="完成"
      cancel-text="取消"
      type="success"
      @confirm="completeOkr"
    />

    <!-- 引入确认暂停弹窗组件 -->
    <z-confirm-modal
      v-model:visible="showPauseConfirmModal"
      title="确认暂停"
      message="确定要暂停此目标吗？可以随时重新开始。"
      confirm-text="暂停"
      cancel-text="取消"
      type="warning"
      @confirm="pauseOkr"
    />

    <!-- 添加进度更新弹窗 -->
    <UpdateProgressModal
      v-model:visible="isUpdateProgressModalVisible"
      :kr-title="currentKrTitle"
      :calc-method="currentKrCalcMethod"
      :task-id="currentKrId"
      @save-success="fetchOkrDetail"
    />

    <!-- 动机详情卡片 -->
    <div class="motivation-detail-overlay" v-if="showMotivationDetail" @click="closeMotivationDetail">
      <div class="motivation-detail-card" @click.stop>
        <div class="motivation-detail-content">
          <i class="fas fa-quote-left motivation-quote-icon"></i>
          <div class="motivation-detail-text">{{ currentMotivationContent }}</div>
          <i class="fas fa-quote-right motivation-quote-icon right"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { router, getRoute } from '@/utils/tools'
import ZPageNavbar from '@/components/z-page-navbar.vue'
import ZTaskModal from '@/components/z-task-modal.vue'
import ZTaskCheckbox from '@/components/z-task-checkbox.vue'
import ZConfirmModal from '@/components/z-confirm-modal.vue'
import UpdateProgressModal from '@/components/z-update-progress-modal.vue'
import { getOkrApi, delOkrApi, updateOkrApi } from '@/api/okr'
import { getTaskListApi, updateTaskApi, getTaskApi } from '@/api/task'
import { calculateWeightedProgress } from '@/utils/okrCalculationUtils'

// --- Interfaces ---
interface Objective {
  _id: string
  title: string
  description?: string
  content?: string
  startDate: string
  endDate: string
  motivation: string[]
  feasibility: string[]
  curVal: number
  tgtVal: number
}

interface KrTask {
  _id: string
  title: string
  completed: boolean
  progressValue?: number
  endDate?: string
}

interface KeyResult {
  _id: string
  title: string
  endDate: string
  tasksVisible?: boolean
  subtasks: KrTask[]
  curVal: number
  tgtVal: number
  weight?: number
  valType?: string
  unit?: string
}

interface RelatedTaskTag {
  text: string
  type: 'blue' | 'green' | 'red' | 'gray'
}
interface RelatedTask {
  _id: string
  title: string
  completed: boolean
  endDate: string
  tag?: RelatedTaskTag
}

interface ReflectionPoint {
  _id: string
  text: string
  points: string[]
  date: string
}

// 扩展的 API 返回类型
interface OkrDetail {
  _id: string
  title: string
  content: string
  description?: string
  startDate: string
  endDate: string
  motivation: any[]
  feasibility: any[]
  curVal: number
  tgtVal: number
  tasks?: any[]
  relatedTasks?: RelatedTask[]
  reflections?: ReflectionPoint[]
}

// 动机数据结构
interface Motivation {
  title: string
  content: string
}

// --- Reactive Data ---
const objective = ref<Objective | null>(null)
const keyResults = ref<KeyResult[]>([])
const relatedTasks = ref<RelatedTask[]>([])
const reflections = ref<ReflectionPoint[]>([])
const isDescriptionExpanded = ref(false)
const motivations = ref<Motivation[]>([])

// 任务编辑模态框相关状态
const showTaskModal = ref(false)
const modalTitle = ref('编辑任务')
const currentTaskId = ref('')
const currentTaskData = ref({})
const currentKrInfo = ref({
  name: '',
  progress: '',
  _id: '',
  okrId: '',
})

// 删除确认弹窗状态
const showDeleteConfirmModal = ref(false)

// 放弃确认弹窗状态
const showAbandonConfirmModal = ref(false)

// 开始确认弹窗状态
const showStartConfirmModal = ref(false)

// 完成确认弹窗状态
const showCompleteConfirmModal = ref(false)

// 暂停确认弹窗状态
const showPauseConfirmModal = ref(false)

// 进度更新弹窗相关状态
const isUpdateProgressModalVisible = ref(false)
const currentKrId = ref('')
const currentKrTitle = ref('')
const currentKrCalcMethod = ref('')

// 动机详情相关状态
const showMotivationDetail = ref(false)
const currentMotivationContent = ref('')

// --- Methods ---
const goBack = () => {
  router.back()
}

// 处理菜单项操作
const handleMenuAction = (action: string) => {
  switch (action) {
    case 'edit':
      if (objective.value) {
        router.push('/pages/okr/okrEdit', { id: objective.value._id })
      }
      break
    case 'share':
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none',
      })
      break
    case 'star':
      uni.showToast({
        title: '已标记为重要',
        icon: 'success',
      })
      break
    case 'delete':
      if (objective.value) {
        showDeleteConfirmModal.value = true
      }
      break
    case 'abandon':
      if (objective.value) {
        showAbandonConfirmModal.value = true
      }
      break
    case 'start':
      if (objective.value) {
        showStartConfirmModal.value = true
      }
      break
    case 'complete':
      if (objective.value) {
        showCompleteConfirmModal.value = true
      }
      break
    case 'pause':
      if (objective.value) {
        showPauseConfirmModal.value = true
      }
      break
    default:
      break
  }
}

// 删除 OKR
const deleteOkr = async () => {
  try {
    if (!objective.value) return

    // 显示加载状态
    uni.showLoading({
      title: '正在删除...',
    })

    // 调用删除 API
    await delOkrApi(objective.value._id)

    // 隐藏加载状态
    uni.hideLoading()

    // 显示成功提示
    uni.showToast({
      title: '删除成功',
      icon: 'success',
    })

    // 返回上一页
    setTimeout(() => {
      router.back()
    }, 500)
  } catch (error) {
    console.error('删除目标失败：', error)
    uni.hideLoading()
    uni.showToast({
      title: '删除失败',
      icon: 'none',
    })
  }
}

// 放弃 OKR
const abandonOkr = async () => {
  try {
    if (!objective.value) return

    // 显示加载状态
    uni.showLoading({
      title: '正在更新...',
    })

    // 调用更新 API 将状态改为放弃
    await updateOkrApi(objective.value._id, {
      status: 'abandoned',
    })

    // 隐藏加载状态
    uni.hideLoading()

    // 显示成功提示
    uni.showToast({
      title: '目标已放弃',
      icon: 'success',
    })

    // 刷新页面数据
    fetchOkrDetail()
  } catch (error) {
    console.error('放弃目标失败：', error)
    uni.hideLoading()
    uni.showToast({
      title: '操作失败',
      icon: 'none',
    })
  }
}

const toggleDescription = () => {
  isDescriptionExpanded.value = !isDescriptionExpanded.value
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`
}

// 获取剩余天数
const getRemainingDays = (endDateString: string) => {
  if (!endDateString) return '0 天'
  const endDate = new Date(endDateString)
  const today = new Date()
  const diffTime = endDate.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays > 0 ? `${diffDays} 天` : '已过期'
}

// 获取已完成的关键结果数量
const getCompletedKrs = () => {
  if (!keyResults.value.length) return '0'
  const completedCount = keyResults.value.filter((kr) => kr.curVal >= kr.tgtVal).length
  return `${completedCount}/${keyResults.value.length}`
}

// 获取总体进度
const getOverallProgress = () => {
  if (!objective.value || !keyResults.value.length) return 0

  // 使用加权计算函数计算总体进度
  const progress = calculateWeightedProgress(keyResults.value)
  return progress
}

// 获取关键结果进度
const getKrProgress = (kr: KeyResult) => {
  if (kr.tgtVal === 0) return 0
  return Math.min(Math.round((kr.curVal / kr.tgtVal) * 100), 100)
}

// 获取已完成的任务数量
const getCompletedTasksCount = (kr: KeyResult) => {
  if (!kr.subtasks) return 0
  return kr.subtasks.filter((task) => task.completed).length
}

// 获取任务状态文本
const getTaskStatusText = (task: RelatedTask) => {
  if (task.completed) return '已完成'
  if (task.endDate) {
    const endDate = new Date(task.endDate)
    const today = new Date()
    if (endDate < today) return '已逾期'
    return `截止日期：${formatDate(task.endDate)}`
  }
  return '进行中'
}

const toggleKrTasks = (krId: string) => {
  const kr = keyResults.value.find((k) => k._id === krId)
  if (kr) {
    kr.tasksVisible = !kr.tasksVisible
  }
}

const toggleKrTaskStatus = async (task: KrTask) => {
  try {
    // 更新 UI 状态
    task.completed = !task.completed

    // 构造更新数据
    const updateData: API.EditTask = {
      status: task.completed ? 1 : (0 as 1 | 0),
    }

    // 如果标记为完成，添加完成时间
    if (task.completed) {
      updateData.completeTime = new Date().toISOString()
    }

    // 调用 API 更新任务状态
    await updateTaskApi(task._id, updateData)

    // 显示提示
    uni.showToast({
      title: task.completed ? '任务已完成' : '任务已取消',
      icon: 'none',
      duration: 1500,
    })

    // 找到当前任务所属的关键结果
    const kr = keyResults.value.find((kr) => kr.subtasks.some((t) => t._id === task._id))
    if (kr) {
      // 重新排序任务：未完成的在上方
      kr.subtasks.sort((a, b) => {
        if (a.completed === b.completed) return 0
        return a.completed ? 1 : -1 // 未完成的在前面
      })
    }
  } catch (error) {
    console.error('更新任务状态失败：', error)
    // 发生错误，回滚状态
    task.completed = !task.completed
    uni.showToast({
      title: '更新任务状态失败',
      icon: 'none',
    })
  }
}

const toggleRelatedTaskStatus = async (task: RelatedTask) => {
  try {
    // 更新 UI 状态
    task.completed = !task.completed

    // 构造更新数据
    const updateData: API.EditTask = {
      status: task.completed ? 1 : (0 as 1 | 0),
    }

    // 如果标记为完成，添加完成时间
    if (task.completed) {
      updateData.completeTime = new Date().toISOString()
    }

    // 调用 API 更新任务状态
    await updateTaskApi(task._id, updateData)

    // 显示提示
    uni.showToast({
      title: task.completed ? '任务已完成' : '任务已取消',
      icon: 'none',
      duration: 1500,
    })

    // 重新排序相关任务：未完成的在上方
    relatedTasks.value.sort((a, b) => {
      if (a.completed === b.completed) return 0
      return a.completed ? 1 : -1 // 未完成的在前面
    })
  } catch (error) {
    console.error('更新任务状态失败：', error)
    // 发生错误，回滚状态
    task.completed = !task.completed
    uni.showToast({
      title: '更新任务状态失败',
      icon: 'none',
    })
  }
}

const addKeyResult = () => {
  if (objective.value) {
    router.push('/pages/okr/krEdit', { okrId: objective.value._id })
  }
}

const addTask = () => {
  if (objective.value) {
    router.push('/pages/okr/krEdit', { okrId: objective.value._id })
  }
}

const addReflection = () => {
  if (objective.value) {
    router.push('/pages/okr/reflectionEdit', { okrId: objective.value._id })
  }
}

const goToKrDetail = (krId: string) => {
  router.push('/pages/okr/krDetail', { id: krId, okrId: objective.value?._id })
}

// 编辑任务
const editTask = (task: KrTask, kr: KeyResult) => {
  // 设置当前任务信息
  currentTaskId.value = task._id

  // 设置初始数据
  const today = new Date().toISOString().split('T')[0]
  currentTaskData.value = {
    title: task.title,
    date: today, // 如果任务有日期，可以设置为 task.endDate
    cycle: 'none' as 'none' | 'daily' | 'weekly' | 'monthly',
    progressValue: 5,
  }

  // 设置关键结果信息
  currentKrInfo.value = {
    name: kr.title,
    progress: getKrProgress(kr) + '%',
    _id: kr._id,
    okrId: objective.value?._id || '',
  }

  // 显示模态框
  modalTitle.value = '编辑任务'
  showTaskModal.value = true
}

// 任务保存成功回调
const onTaskSaved = (result: { type: string; data: any }) => {
  console.log('任务保存成功', result)

  if (result.type === 'update') {
    // 更新 UI 上的任务数据
    keyResults.value.forEach((kr) => {
      const taskIndex = kr.subtasks.findIndex((t) => t._id === result.data._id)
      if (taskIndex !== -1) {
        // 只更新标题，其他属性在模态框内部已处理
        kr.subtasks[taskIndex].title = result.data.title
      }
    })
  }

  // 可以在这里添加刷新任务列表的逻辑，或者直接使用返回的数据更新本地状态
}

// 获取 OKR 详情数据
const fetchOkrDetail = async () => {
  try {
    const params = getRoute.params() as any
    const id = params.id as string

    if (!id) {
      uni.showToast({
        title: '缺少目标 ID',
        icon: 'none',
      })
      return
    }

    // 1. 获取 OKR 主体数据
    const okrData = (await getOkrApi(id)) as unknown as OkrDetail
    if (okrData && okrData._id) {
      // 将 content 映射为 description
      const objData: Objective = {
        _id: okrData._id,
        title: okrData.title,
        description: okrData.description || okrData.content,
        startDate: okrData.startDate,
        endDate: okrData.endDate,
        motivation: okrData.motivation || [],
        feasibility: okrData.feasibility || [],
        curVal: okrData.curVal || 0,
        tgtVal: okrData.tgtVal || 0,
      }

      objective.value = objData

      // 处理动机数据
      motivations.value = Array.isArray(okrData.motivation)
        ? okrData.motivation.filter((item) => item && item.content && item.content.trim() !== '')
        : []

      // 2. 获取关键结果和子任务
      const tasks = (await getTaskListApi(`okrId == "${id}" && deleteTime == ""`)) as Array<{
        _id: string
        title: string
        endDate?: string
        parentId: string
        type: string
        status: number
        curVal?: number
        tgtVal?: number
        okrId?: string
        progVal?: number
        weight?: number
        valType?: string
        unit?: string
      }>

      if (tasks && tasks.length > 0) {
        // 过滤出关键结果（parentId 为空的任务）
        const krs = tasks.filter((task) => task.parentId === '' && task.type === 'kr')

        // 转换为前端需要的数据格式
        keyResults.value = krs.map((kr) => ({
          _id: kr._id,
          title: kr.title,
          endDate: kr.endDate || '',
          tasksVisible: false,
          subtasks: [],
          curVal: kr.curVal || 0,
          tgtVal: kr.tgtVal || 0,
          weight: kr.weight,
          valType: kr.valType || 'sum',
          unit: kr.unit || '',
        }))

        // 为每个关键结果添加子任务
        keyResults.value.forEach((kr) => {
          // 查找当前关键结果的子任务
          const subTasks = tasks.filter((task) => task.parentId === kr._id)

          // 转换为前端需要的数据格式
          kr.subtasks = subTasks.map((task) => ({
            _id: task._id,
            title: task.title,
            completed: task.status === 1,
            progressValue: task.progVal || 5,
            endDate: task.endDate || '',
          }))

          // 排序：未完成的任务显示在上方
          kr.subtasks.sort((a, b) => {
            if (a.completed === b.completed) return 0
            return a.completed ? 1 : -1 // 未完成的在前面 (返回 -1 时排在前面)
          })

          // 设置任务的 unit 字段
          const originalTask = tasks.find((task) => task._id === kr._id)
          if (originalTask) {
            kr.unit = originalTask.unit || ''
          }
        })

        // 3. 获取相关任务（非关键结果的顶级任务）
        const otherTasks = tasks.filter(
          (task) => task.parentId === '' && task.type === 'todo' && !keyResults.value.some((kr) => kr._id === task._id)
        )

        relatedTasks.value = otherTasks.map((task) => {
          // 为任务添加标签
          const tag: RelatedTaskTag | undefined = task.okrId
            ? {
                text: '目标相关',
                type: 'blue',
              }
            : undefined

          return {
            _id: task._id,
            title: task.title,
            completed: task.status === 1,
            endDate: task.endDate || '',
            tag,
          }
        })

        // 排序：未完成的任务显示在上方
        relatedTasks.value.sort((a, b) => {
          if (a.completed === b.completed) return 0
          return a.completed ? 1 : -1 // 未完成的在前面
        })

        // 4. 获取反思记录（这里需要根据实际数据结构调整）
        // 假设反思记录存储在某个地方
        reflections.value = [
          {
            _id: 'reflection1',
            text: '中期反思：进展顺利，但需要加强团队协作。',
            points: [
              '积极因素：团队协作良好，工具选择合适',
              '改进点：增加测试覆盖率，优化流程',
              '下一步计划：组织团队培训，完善文档',
            ],
            date: new Date().toISOString(),
          },
        ]
      }
    } else {
      uni.showToast({
        title: '获取目标详情失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('获取 OKR 详情失败：', error)
    uni.showToast({
      title: '获取目标详情失败',
      icon: 'none',
    })
  }
}

// 处理关键结果任务状态变更
const handleKrTaskStatusChange = async (result: { taskId: string; status: number; statusText: string }) => {
  try {
    // 找到对应的任务并更新状态
    for (const kr of keyResults.value) {
      const task = kr.subtasks.find((t) => t._id === result.taskId)
      if (task) {
        task.completed = result.status === 1
        break
      }
    }
  } catch (error) {
    console.error('处理任务状态变更失败', error)
  }
}

// 处理相关任务状态变更
const handleRelatedTaskStatusChange = async (result: { taskId: string; status: number; statusText: string }) => {
  try {
    // 找到对应的任务并更新状态
    const task = relatedTasks.value.find((t) => t._id === result.taskId)
    if (task) {
      task.completed = result.status === 1
    }
  } catch (error) {
    console.error('处理任务状态变更失败', error)
  }
}

// 打开更新进度弹窗
const openUpdateProgressModal = (kr: KeyResult) => {
  currentKrId.value = kr._id
  currentKrTitle.value = kr.title
  // 获取计算方式，默认为 'sum'
  currentKrCalcMethod.value = kr.valType || 'sum'
  isUpdateProgressModalVisible.value = true
}

// 显示完整动机内容
const showFullMotivation = (content: string) => {
  currentMotivationContent.value = content
  showMotivationDetail.value = true
}

// 关闭动机详情
const closeMotivationDetail = () => {
  showMotivationDetail.value = false
}

// 开始 OKR
const startOkr = async () => {
  try {
    if (!objective.value) return

    // 显示加载状态
    uni.showLoading({
      title: '正在更新...',
    })

    // 调用更新 API 将状态改为进行中
    await updateOkrApi(objective.value._id, {
      status: 'inProgress',
    })

    // 隐藏加载状态
    uni.hideLoading()

    // 显示成功提示
    uni.showToast({
      title: '目标已开始',
      icon: 'success',
    })

    // 刷新页面数据
    fetchOkrDetail()
  } catch (error) {
    console.error('开始目标失败：', error)
    uni.hideLoading()
    uni.showToast({
      title: '操作失败',
      icon: 'none',
    })
  }
}

// 完成 OKR
const completeOkr = async () => {
  try {
    if (!objective.value) return

    // 显示加载状态
    uni.showLoading({
      title: '正在更新...',
    })

    // 调用更新 API 将状态改为已完成
    await updateOkrApi(objective.value._id, {
      status: 'completed',
    })

    // 隐藏加载状态
    uni.hideLoading()

    // 显示成功提示
    uni.showToast({
      title: '目标已完成',
      icon: 'success',
    })

    // 刷新页面数据
    fetchOkrDetail()
  } catch (error) {
    console.error('完成目标失败：', error)
    uni.hideLoading()
    uni.showToast({
      title: '操作失败',
      icon: 'none',
    })
  }
}

// 暂停 OKR
const pauseOkr = async () => {
  try {
    if (!objective.value) return

    // 显示加载状态
    uni.showLoading({
      title: '正在更新...',
    })

    // 调用更新 API 将状态改为暂停中
    await updateOkrApi(objective.value._id, {
      status: 'paused',
    })

    // 隐藏加载状态
    uni.hideLoading()

    // 显示成功提示
    uni.showToast({
      title: '目标已暂停',
      icon: 'success',
    })

    // 刷新页面数据
    fetchOkrDetail()
  } catch (error) {
    console.error('暂停目标失败：', error)
    uni.hideLoading()
    uni.showToast({
      title: '操作失败',
      icon: 'none',
    })
  }
}

// 页面加载时获取数据
onShow(() => {
  fetchOkrDetail()
})
</script>

<style scoped lang="scss">
// General body-like styles for the page container
.okr-detail-page {
  font-family: var(--font-sans);
  background: var(--color-bg);
  min-height: 100vh; // Use min-height for page container
  display: flex;
  flex-direction: column;
  color: var(--color-gray-800); // Matches global --color-gray-800

  // Apply scrollbar styles globally if not already done
  // For Webkit browsers (Chrome, Safari, Edge)
  & ::-webkit-scrollbar {
    width: 8px;
  }
  & ::-webkit-scrollbar-track {
    background: var(--color-gray-100); // Adjusted to global
  }
  & ::-webkit-scrollbar-thumb {
    background: var(--color-gray-400); // Adjusted to global
    border-radius: 4px; // Keep as is, or define a var if needed
  }
  & ::-webkit-scrollbar-thumb:hover {
    background: var(--color-gray-500); // Adjusted to global
  }
  // For Firefox
  scrollbar-width: thin;
  scrollbar-color: var(--color-gray-400) var(--color-gray-100); // Adjusted to global
}

.content-area {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px 30px;
}

.navbar {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200); // Matches global
  position: sticky;
  top: 0;
  z-index: 40;
}

.navbar-title {
  font-weight: 600;
  font-size: 18px;
  margin-left: 15px;
  color: var(--color-primary); // Matches global
}

.navbar-back {
  color: var(--color-primary); // Matches global
  font-size: 16px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--rounded-sm); // Adjusted to global --rounded-sm (8px)
  background: var(--color-gray-100); // Matches global
  text-decoration: none;
}
.navbar-back:hover {
  background: var(--color-gray-200); // Matches global
}

.edit-btn {
  border-radius: var(--rounded-sm); // Adjusted to global --rounded-sm (8px), original was md
  padding: 8px 10px;
  font-size: 16px;
  background: var(--color-white);
  color: var(--color-primary); // Matches global
  border: 1px solid var(--color-primary); // Matches global
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  cursor: pointer;
  box-sizing: border-box;
}

.edit-btn:hover {
  background: var(--color-gray-100); // Adjusted from gray-50 to gray-100 for closer match
}

.edit-btn i {
  margin-right: 0;
}

.card {
  background: var(--color-white);
  border-radius: var(--rounded-lg); // Matches global (16px)
  border: 1px solid var(--color-gray-200); // Matches global
  transition: var(--transition-fast); // Use global transition
  overflow: hidden;
  margin-bottom: 16px;
  padding: 20px;
}

.card:hover {
  border-color: var(--color-gray-300); // Matches global
}

.header-card {
  position: relative;
  margin-top: 15px;
}

.goal-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 16px;
  line-height: 1.4;
  color: var(--color-gray-800); // Matches global
}

.goal-description {
  font-size: 15px;
  line-height: 1.6;
  color: var(--color-gray-600); // Matches global
  margin: 16px 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  position: relative;
  transition: var(--transition-normal); // Use global transition
}

.goal-description.expanded {
  -webkit-line-clamp: unset;
  max-height: none;
}

.toggle-description {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  position: absolute;
  right: 0;
  bottom: 2px;
  color: var(--color-primary);
}

.toggle-description i {
  font-size: 12px;
  transition: transform 0.2s ease-in-out; // Keep specific or use --transition-fast
}

.toggle-description:hover {
  text-decoration: underline;
}

.progress-container {
  margin: 20px 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.progress-label {
  font-weight: 600;
  font-size: 14px;
  color: var(--color-gray-700); // Matches global
}

.progress-value {
  font-weight: 700;
  font-size: 16px;
  color: var(--color-primary); // Matches global
  display: flex;
  align-items: center;
  gap: 8px;
}

.complete-badge {
  display: inline-flex;
  align-items: center;
  background-color: rgba(var(--color-primary-rgb, 99, 102, 241), 0.1);
  color: var(--color-primary);
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 10px;
  gap: 4px;
}

.progress-bar {
  height: 8px;
  background: var(--color-gray-200); // Matches global
  border-radius: 4px; // Or var(--rounded-sm) cut in half, or a new tiny radius var
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--color-primary); // Matches global
  border-radius: 4px; // Consistent with .progress-bar
  transition: width var(--transition-normal); // Use global transition
}

.progress-fill-completed {
  background: var(--color-primary);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-gray-800); // Matches global
  margin: 24px 0 16px;
  display: flex;
  align-items: center;
}

.section-title i {
  margin-right: 8px;
  color: var(--color-primary); // Matches global
  font-size: 1em;
}

.kr-card {
  /* background: var(--color-white); */ /* Replaced by gradient */
  background: linear-gradient(
    to right,
    var(--color-primary-transparent-20, #ebf5ff) var(--progress-width),
    var(--color-white) var(--progress-width)
  );
  border: 1px solid var(--color-gray-200); // Matches global
  border-radius: var(--rounded-md); // Matches global (12px)
  padding: 16px;
  margin-bottom: 12px;
  transition: var(--transition-fast); // Use global transition
  position: relative;
  box-shadow: var(--shadow-sm); // Use global shadow
  display: flex;
  flex-direction: column;
}

.kr-card:hover {
  box-shadow: var(--shadow-md); // Use global shadow
  border-color: var(--color-gray-300); // Matches global
}

.kr-card.kr-completed {
  background: linear-gradient(
    to right,
    rgba(var(--color-primary-rgb, 99, 102, 241), 0.1) var(--progress-width),
    var(--color-white) var(--progress-width)
  );
  border-color: rgba(var(--color-primary-rgb, 99, 102, 241), 0.2);
}

.kr-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.kr-title {
  font-weight: 600;
  font-size: 16px;
  color: var(--color-gray-800); // Matches global
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.kr-complete-badge {
  font-size: 12px;
  font-weight: 500;
  background-color: rgba(var(--color-primary-rgb, 99, 102, 241), 0.1);
  color: var(--color-primary);
  padding: 2px 8px;
  border-radius: 10px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.kr-meta-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.kr-weight {
  font-size: 12px;
  color: var(--color-gray-600);
  background: var(--color-gray-100);
  padding: 2px 8px;
  border-radius: var(--rounded-full);
  white-space: nowrap;
}

.kr-progress {
  font-weight: 600;
  font-size: 14px;
  color: var(--color-white);
  background: var(--color-primary); // Matches global
  padding: 2px 8px;
  border-radius: var(--rounded-full); // Use global for pill shape
}

.kr-progress-completed {
  background: var(--color-primary);
}

.kr-meta {
  display: flex;
  font-size: 13px;
  color: var(--color-gray-600); // Matches global
  gap: 12px;
  margin-top: 8px;
}

.kr-meta i {
  margin-right: 4px;
}

.kr-tag {
  display: inline-flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
  color: var(--color-gray-600); // Matches global
}

.kr-tag.deadline {
  color: var(--color-primary); // Matches global
}

.kr-tag.deadline::before {
  content: '\f073';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  margin-right: 5px;
}

.kr-tag.weight {
  color: var(--color-primary);
}

.kr-tag.tasks {
  color: var(--color-primary);
  cursor: pointer;
  transition: var(--transition-fast);
}

.kr-tag.tasks:hover {
  color: var(--color-primary-dark, #3b70df);
  text-decoration: underline;
}

.kr-tag.tasks::before {
  content: '\f0ae';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  margin-right: 5px;
}

.kr-tag.update-progress {
  color: var(--color-primary);
  cursor: pointer;
  transition: var(--transition-fast);
  margin-left: auto;

  &:hover {
    color: var(--color-primary-dark, #3b70df);
    text-decoration: underline;
  }

  i {
    margin-right: 5px;
  }
}

.kr-tasks {
  margin-top: 10px;
  border-top: 1px dashed var(--color-gray-200); // Matches global
  padding-top: 10px;
  display: none;
}

.kr-tasks.visible {
  display: block;
  animation: fadeIn 0.3s ease; // Consider var(--transition-normal) if animation is simple fade
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.kr-task-item {
  display: flex;
  align-items: flex-start;
  padding: 6px 4px;
  font-size: 13px;
  color: var(--color-gray-700);
  border-radius: var(--rounded-sm);
}

.kr-task-item:hover {
  background: var(--color-gray-100);
}

.kr-task-item.completed {
  color: var(--color-gray-400);
  & .kr-task-title {
    text-decoration: line-through;
  }
}

.kr-task-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.kr-task-title {
  flex: 1;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: var(--rounded-sm);
  transition: var(--transition-fast);
}

.kr-task-title:hover {
  background-color: var(--color-gray-200);
}

.kr-task-meta {
  display: flex;
  font-size: 11px;
  color: var(--color-gray-500);
  margin-top: 2px;
  gap: 10px;
}

.kr-task-meta i {
  margin-right: 4px;
}

.kr-task-deadline {
  display: flex;
  align-items: center;
}

.kr-task-progress {
  display: flex;
  align-items: center;
}

.task-list {
  margin-top: 12px;
}

.task-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-radius: var(--rounded-md);
  margin-bottom: 8px;
  background: var(--color-gray-100);
  border: 1px solid var(--color-gray-200);
}

.task-content {
  flex: 1;
}

.task-title {
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--color-gray-800); // Matches global
  transition: color var(--transition-fast), text-decoration var(--transition-fast);
}

.task-meta {
  font-size: 12px;
  color: var(--color-gray-600); // Matches global
}

.badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: var(--rounded-sm); // Adjusted, original was 4px. Global sm is 8px.
  // Using 4px directly or new var.
  font-size: 12px;
  font-weight: 500;
  margin-left: 6px;
}

// Badge specific colors - these might need to be added to variables.css if globally used
// For now, using closest global equivalents or defining them locally if no good match.
.badge.blue {
  background: var(--color-primary-transparent-20, #ebf5ff); // Global var or fallback
  color: var(--color-primary, #3b82f6);
}

.badge.green {
  background: var(--color-success-transparent-20, #ecfdf5); // Assuming a success transparent var or fallback
  color: var(--color-success, #10b981);
}

.badge.red {
  background: var(--color-danger-transparent-20, #fee2e2); // Assuming a danger transparent var or fallback
  color: var(--color-danger, #ef4444);
}

.badge.gray {
  background: var(--color-gray-100, #f3f4f6); // Global var or fallback
  color: var(--color-gray-600, #6b7280);
}

.list-items {
  margin-left: 12px;
}

.list-item {
  display: flex;
  align-items: baseline;
  margin-bottom: 10px;
  color: var(--color-gray-700); // Matches global
}

.list-item::before {
  content: '•';
  color: var(--color-primary); // Matches global
  font-weight: bold;
  margin-right: 8px;
}

.reflection-card {
  // This is a .card variant
  border-left: 4px solid var(--color-primary); // Matches global
}

.add-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border-radius: var(--rounded-md); // Matches global (12px)
  background: var(--color-gray-100); // Matches global
  color: var(--color-gray-700); // Matches global
  font-weight: 500;
  font-size: 14px;
  border: 1px dashed var(--color-gray-300); // Matches global
  transition: var(--transition-fast);
  margin-top: 16px;
  margin-bottom: 12px;
  margin-left: auto;
  margin-right: auto;
  width: 80%;
  max-width: 300px;
  cursor: pointer;
}

.add-btn:hover {
  background: var(--color-gray-200); // Matches global
  color: var(--color-gray-800); // Matches global
}

.add-btn i {
  margin-right: 8px;
}

.time-info-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin: 16px 0;
}

.time-card {
  background: var(--color-gray-100); // Adjusted from gray-50 to gray-100
  border: 1px solid var(--color-gray-200); // Matches global
  border-radius: var(--rounded-sm); // Matches global (8px)
  padding: 8px 10px;
  display: flex;
  align-items: center;
  transition: var(--transition-fast);
}

.time-card:hover {
  background: var(--color-gray-200); // Adjusted from gray-100 to gray-200
}

.time-card i {
  font-size: 16px;
  color: var(--color-primary); // Matches global
  margin-right: 8px;
}

.time-content {
  display: flex;
  flex-direction: column;
}

.time-label {
  font-size: 11px;
  color: var(--color-gray-500); // Matches global
  line-height: 1;
}

.time-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-gray-800); // Matches global
  margin-top: 2px;
}

.time-card.urgent .time-value {
  color: var(--color-danger); // Use global --color-danger
}
.time-card.urgent i {
  color: var(--color-danger); // Use global --color-danger for icon
}

.time-card.progress .time-value {
  color: var(--color-primary);
}
.time-card.progress i {
  color: var(--color-primary);
}

// 为删除按钮添加红色样式
:deep(.action-menu-item.delete) {
  color: var(--color-danger);

  i {
    color: var(--color-danger);
  }
}

// 为放弃按钮添加橙色样式
:deep(.action-menu-item.abandon) {
  color: var(--color-warning, #f59e0b);

  i {
    color: var(--color-warning, #f59e0b);
  }
}

// 为新增按钮添加样式
:deep(.action-menu-item.start) {
  color: var(--color-primary);

  i {
    color: var(--color-primary);
  }
}

:deep(.action-menu-item.complete) {
  color: var(--color-success, #10b981);

  i {
    color: var(--color-success, #10b981);
  }
}

:deep(.action-menu-item.pause) {
  color: var(--color-warning, #f59e0b);

  i {
    color: var(--color-warning, #f59e0b);
  }
}

// 动机部分样式
.motivation-section {
  margin-bottom: 16px;
  width: 100%;
}

.motivation-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
}

.motivation-chip {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background: rgba(var(--color-primary-rgb, 99, 102, 241), 0.08);
  border-radius: var(--rounded-full);
  font-size: 13px;
  color: var(--color-primary);
  transition: var(--transition-fast);
  max-width: 100%;

  &:hover {
    background: rgba(var(--color-primary-rgb, 99, 102, 241), 0.12);
  }

  i {
    margin-right: 6px;
    font-size: 10px;
    opacity: 0.7;
    flex-shrink: 0;
  }
}

.motivation-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  max-width: 200px;

  @media (max-width: 400px) {
    max-width: 150px;
  }
}

// 动机详情卡片样式
.motivation-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
}

.motivation-detail-card {
  background-color: var(--color-white);
  border-radius: var(--rounded-lg);
  box-shadow: var(--shadow-lg);
  width: 85%;
  max-width: 400px;
  padding: 24px;
  position: relative;
  animation: scaleIn 0.2s ease;
}

.motivation-detail-content {
  position: relative;
  padding: 10px;
  text-align: center;
}

.motivation-detail-text {
  font-size: 16px;
  line-height: 1.6;
  color: var(--color-gray-800);
  margin: 10px 0;
  font-weight: 500;
}

.motivation-quote-icon {
  color: var(--color-primary);
  opacity: 0.3;
  font-size: 18px;
  position: absolute;

  &.right {
    bottom: 0;
    right: 0;
  }

  &:not(.right) {
    top: 0;
    left: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
</style>

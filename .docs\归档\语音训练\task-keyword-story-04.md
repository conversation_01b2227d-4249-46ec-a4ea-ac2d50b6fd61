# 任务：为记录弹窗对接真实数据

- **任务ID**: `task-keyword-story-04`
- **所属功能模块**: 语音训练 - 关键词讲故事
- **优先级**: 中
- **状态**: 未开始
- **依赖关系**: `task-keyword-story-02`, `task-keyword-story-03`

---

## 1. 任务描述
将 `z-record-list-popup` 组件从使用模拟数据切换为使用从 `chatRecord` 数据库中查询到的真实数据。

## 2. 技术实现详情
### 创建数据加载函数
-   在 `keyword-story-page.vue` 的 `<script setup>` 中，创建一个新的 `async` 函数 `loadRecords`。
    ```javascript
    const records = ref([]); // 替换原有的 mockRecords
    const isLoadingRecords = ref(false);

    const loadRecords = async () => {
      isLoadingRecords.value = true;
      try {
        // 使用 LIKE 查询来筛选出关键词故事的记录
        const res = await db.getList('chatRecord', {
          where: "content LIKE '%\"type\":\"keywordStory\"%'",
          order: 'createTime DESC' // 按创建时间降序
        });
        records.value = res;
        console.log('加载到', res.length, '条训练记录');
      } catch (error) {
        console.error('加载训练记录失败:', error);
        uni.showToast({ title: '记录加载失败', icon: 'none' });
      } finally {
        isLoadingRecords.value = false;
      }
    };
    ```

### 修改弹窗打开逻辑
-   修改导航栏“复述记录”按钮的点击事件处理。
    ```html
    <div class="action-btn" @click="openRecordPopup" aria-label="复述记录">
      <i class="fas fa-history"></i>
    </div>
    ```
-   创建一个新函数 `openRecordPopup`。
    ```javascript
    const openRecordPopup = async () => {
      await loadRecords(); // 先加载最新数据
      showRecordPopup.value = true;
    };
    ```

### 更新模板
-   将 `z-record-list-popup` 组件的 `:records` prop 绑定到新的 `records` ref。
    ```html
    <z-record-list-popup
      v-model:show="showRecordPopup"
      :records="records"
      ...
    />
    ```
-   可以给弹窗组件增加一个 `loading` 状态，用于在数据加载时显示提示。

## 3. 验收标准
-   [ ] `loadRecords` 函数被正确创建，并包含 `loading` 状态处理。
-   [ ] `openRecordPopup` 函数被创建并绑定到按钮上。
-   [ ] 点击“复述记录”按钮后，会先执行查询，然后打开弹窗。
-   [ ] 弹窗中显示的是从数据库中查询到的真实训练记录，而不是模拟数据。
-   [ ] 如果数据库没有符合条件的记录，弹窗显示为空列表。
-   [ ] 记录列表按时间倒序排列。 
<template>
  <div class="message-list" ref="messageListRef">
    <div v-for="(message, index) in messages" :key="index" class="message-container">
      <!-- 时间戳 -->
      <l-time-stamp v-if="shouldShowTime(message, index)" :time="message.time" />

      <div class="message-row" :class="{ 'user-message': message.isUser }">
        <!-- 使用消息气泡组件 -->
        <l-message-bubble
          :content="message.content"
          :type="message.type || 'text'"
          :audio-url="message.audioUrl"
          :audio-duration="message.audioDuration"
          :is-user="message.isUser"
          :loading="message.loading"
          :is-collapsed="message.isCollapsed"
          :on-toggle="message.onToggle"
          :is-transcribing="message.isTranscribing"
          :transcribe-result="message.transcribeResult"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue'
import dayjs from 'dayjs'
import LMessageBubble from './l-message-bubble.vue'
import LTimeStamp from './l-time-stamp.vue'

const props = defineProps({
  messages: {
    type: Array,
    default: () => [],
  },
})

const messageListRef = ref(null)

// 判断是否显示时间戳（每组消息只显示一次）
const shouldShowTime = (message, index) => {
  if (index === 0) return true

  const prevMessage = props.messages[index - 1]
  // 如果时间差超过 5 分钟，显示新的时间戳
  return dayjs(message.time).diff(dayjs(prevMessage.time), 'minute') > 5
}

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick()
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight
  }
}

// 监听消息变化，自动滚动到底部
watch(
  () => props.messages,
  () => {
    scrollToBottom()
  },
  { deep: true }
)

// 暴露方法给父组件
defineExpose({
  scrollToBottom,
})

onMounted(() => {
  scrollToBottom()
})
</script>

<style lang="scss" scoped>
.message-list {
  flex: 1;
  padding: 16px;
  overflow: auto;

  &::-webkit-scrollbar {
    width: 3px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 3px;
  }

  .message-container {
    margin-bottom: 16px;

    .message-row {
      display: flex;
      justify-content: flex-start;

      &.user-message {
        justify-content: flex-end;
      }
    }
  }
}
</style>

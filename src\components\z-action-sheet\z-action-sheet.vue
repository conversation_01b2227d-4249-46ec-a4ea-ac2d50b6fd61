<style lang="scss" scope></style>
<template>
  <view @click="popup.open('top')">
    <slot />
  </view>
  <uni-popup ref="popup" type="top" :animation="false">
    <view class="absolute top-80 right-20 bg-white rounded-2 py-1">
      <view
        v-for="(item, index) in props.list"
        :key="index"
        @click="onClick(item)"
        class="flex items-center justify-center h-80 w-200 text-center active:bg-[#eee]"
      >
        <u-icon class="mr-1" :name="item.icon" color="#333" size="28" />
        {{ item.text }}
      </view>
    </view>
  </uni-popup>
</template>
<script lang="ts" setup>
import { defineProps, defineEmits, watch, ref } from 'vue'
const props = defineProps({
  list: {
    type: Array,
    default: [],
  },
})
// const emits = defineEmits('update:visible', 'onChange')

const popup = ref()

const onClick = (item) => {
  setTimeout(() => {
    popup.value.close()
    item.callback && item.callback()
  }, 200)
}

const actionList = ref([
  {
    name: '编辑',
    color: '#576b95',
  },
  {
    name: '删除',
    color: '#f56c6c',
  },
])
</script>

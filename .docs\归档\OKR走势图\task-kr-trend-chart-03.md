# 任务：KR 进度走势图真实数据处理与集成

## 所属功能模块

OKR - KR 进度走势图组件

## 任务描述

将 KR 进度走势图组件与真实数据源集成，实现 KR 进度数据的处理逻辑，根据不同时间维度（日、周、月）对数据进行聚合和格式化。

## 技术实现详情

1. 将模拟数据替换为从 KR 详情页获取的真实进度历史数据
2. 设计数据聚合算法，支持三种不同时间维度的数据处理：
   - 日线：使用每日记录的进度最新值
   - 周线：按周聚合，使用每周最新进度值
   - 月线：按月聚合，使用每月最新进度值
3. 处理边缘情况，如数据缺失、数据量过大等
4. 确保数据处理不影响组件性能和用户体验

## 代码实现要点

1. 在`l-trend-chart.vue`中实现：

   - 移除模拟数据，改为处理 props 传入的真实数据
   - 实现三个数据处理函数：`processDailyData`、`processWeeklyData`和`processMonthlyData`
   - 使用 Day.js 处理日期计算和聚合
   - 实现数据抽样算法，处理大量数据点的情况
   - 提供数据为空时的默认处理

2. 数据结构设计：

   ```typescript
   interface TrendPoint {
     date: string // 日期字符串
     timestamp: number // 时间戳，用于排序
     value: number // 进度值
     label: string // 显示标签
   }

   interface ChartData {
     points: TrendPoint[]
     min: number // 最小值
     max: number // 最大值
     isEmpty: boolean // 是否为空数据
   }
   ```

3. 数据处理关键逻辑：
   ```javascript
   // 示例：周线数据处理
   const processWeeklyData = (rawData) => {
     // 1. 按周对数据进行分组
     const weeklyGroups = {}
     rawData.forEach((item) => {
       const weekStart = getWeekStart(item.date) // 获取所在周的起始日期
       if (!weeklyGroups[weekStart]) {
         weeklyGroups[weekStart] = []
       }
       weeklyGroups[weekStart].push(item)
     })

     // 2. 取每周的最新一条记录
     const weeklyData = Object.keys(weeklyGroups).map((week) => {
       const weekItems = weeklyGroups[week]
       // 按日期排序，取最新
       weekItems.sort((a, b) => new Date(b.date) - new Date(a.date))
       return {
         date: week,
         value: weekItems[0].value,
         label: formatWeekLabel(week),
       }
     })

     // 3. 按日期排序
     return weeklyData.sort((a, b) => new Date(a.date) - new Date(b.date))
   }
   ```

## 验收标准

1. 能正确接收并处理 KR 进度历史数据
2. 日、周、月三种时间维度的数据聚合算法正确实现
3. 对大数据量进行有效处理，不影响性能
4. 当数据不足时提供合理的默认值或空状态
5. 数据处理后的格式符合图表渲染要求
6. 图表正确显示真实数据的变化趋势

## 依赖关系

- 依赖任务：task-kr-trend-chart-02

## 优先级

中

## 状态

待开发

<!-- src/components/z-record-demo/z-record-demo.vue -->
<template>
  <view class="record-demo">
    <view class="status">
      <text>状态: {{ isRecording ? '录音中' : '就绪' }}</text>
      <text>{{ isPaused ? '(已暂停)' : '' }}</text>
    </view>

    <view class="duration"> 录音时长: {{ formatDuration(duration) }} </view>

    <view class="volume-meter">
      <view class="volume-bar" :style="{ width: volume + '%' }"></view>
    </view>

    <view class="controls">
      <button @tap="handleStartRecording" :disabled="isRecording && !isPaused">
        {{ isRecording && !isPaused ? '录音中...' : '开始录音' }}
      </button>

      <button @tap="handlePauseRecording" :disabled="!isRecording || isPaused" v-if="isRecording && !isPaused">
        暂停
      </button>

      <button @tap="handleResumeRecording" :disabled="!isPaused" v-if="isRecording && isPaused">继续</button>

      <button @tap="handleStopRecording" :disabled="!isRecording">停止录音</button>

      <button @tap="handlePlayRecording" :disabled="!recordURL">播放录音</button>
    </view>

    <view class="record-result" v-if="recordURL">
      <text>录音文件: {{ recordURL }}</text>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { useRecordApp } from '@/hooks/useRecordApp'

const {
  isRecording,
  isPaused,
  duration,
  volume,
  recordURL,
  startRecording,
  pauseRecording,
  resumeRecording,
  stopRecording,
  playRecording,
  cancelRecording,
} = useRecordApp({
  maxDuration: 60000, // 最大录音时长 60 秒
  appOptions: {
    format: 'mp3',
  },
})

// 格式化时长显示
const formatDuration = (ms) => {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 开始录音
const handleStartRecording = async () => {
  try {
    await startRecording()
    uni.showToast({
      title: '录音已开始',
      icon: 'none',
    })
  } catch (error) {
    uni.showModal({
      title: '录音失败',
      content: error.message || '无法启动录音',
      showCancel: false,
    })
  }
}

// 暂停录音
const handlePauseRecording = () => {
  pauseRecording()
  uni.showToast({
    title: '录音已暂停',
    icon: 'none',
  })
}

// 继续录音
const handleResumeRecording = () => {
  resumeRecording()
  uni.showToast({
    title: '录音已继续',
    icon: 'none',
  })
}

// 停止录音
const handleStopRecording = async () => {
  try {
    const result = await stopRecording()
    uni.showToast({
      title: '录音已完成',
      icon: 'success',
    })
    console.log('录音文件:', result)
  } catch (error) {
    uni.showModal({
      title: '录音失败',
      content: error.message || '停止录音时发生错误',
      showCancel: false,
    })
  }
}

// 播放录音
const handlePlayRecording = () => {
  playRecording()
  uni.showToast({
    title: '正在播放',
    icon: 'none',
  })
}
</script>

<style lang="scss" scoped>
.record-demo {
  padding: 20px;

  .status {
    font-size: 18px;
    margin-bottom: 10px;
  }

  .duration {
    font-size: 16px;
    margin-bottom: 15px;
  }

  .volume-meter {
    height: 20px;
    background-color: #eee;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;

    .volume-bar {
      height: 100%;
      background-color: #2196f3;
      transition: width 0.1s ease;
    }
  }

  .controls {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;

    button {
      flex: 1;
      min-width: 120px;
      font-size: 16px;
      padding: 10px;
    }
  }

  .record-result {
    margin-top: 20px;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 5px;

    text {
      font-size: 14px;
      word-break: break-all;
    }
  }
}
</style>

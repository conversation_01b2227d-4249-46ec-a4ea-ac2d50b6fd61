# 背景

目前日记模块的交互存在一些问题，用户体验不够流畅：
1. 记录日记时使用弹窗形式，空间受限，影响用户书写体验
2. 编辑已有日记时，之前选择的标签无法回显，用户需要重新选择，造成操作重复

# 需求

## 功能需求
1. 将记录日记的弹窗改为独立页面形式
   - 提供更大的书写空间
   - 更完整的编辑功能体验
2. 编辑日记时已选中的标签需要正确回显
   - 用户可以清楚看到之前选择的标签
   - 可以直接修改标签选择而不必重新选择

## 非功能需求
1. 交互体验更加流畅自然
2. 页面加载性能保持稳定

# 技术方案

## 前端实现

### 1. 日记页面改造
- 创建新的日记编辑页面，替代原有的弹窗组件
- 页面路由设计：
  - 新建日记：`/pages/memo/diary_edit.vue`
  - 编辑日记：`/pages/memo/diary_edit.vue?id=xxx`

### 2. 标签回显实现
- 在编辑模式下，需要从数据库获取日记对应的标签信息
- 将获取的标签数据预设到标签选择组件中

### 3. 页面交互流程
1. 用户点击"写日记"按钮，跳转到日记编辑页面
2. 如果是编辑模式，通过路由参数获取日记 ID，加载日记数据和已选标签
3. 用户完成编辑后，点击"保存"按钮提交数据
4. 保存成功后返回日记列表页面

# 任务

按照开发顺序排列的具体任务：

1. 创建日记编辑页面 `/pages/memo/diary_edit.vue`
   - 实现页面布局和基础样式
   - 添加标题、内容编辑区域
   - 集成标签选择功能

2. 实现数据加载和保存功能
   - 新建日记初始化逻辑
   - 编辑日记数据加载逻辑
   - 保存日记的接口对接

3. 实现标签回显功能
   - 获取已选标签数据
   - 在标签选择组件中正确回显
   - 处理标签变更逻辑

4. 修改原日记列表页面
   - 更新"写日记"按钮的跳转逻辑
   - 更新编辑日记的跳转逻辑

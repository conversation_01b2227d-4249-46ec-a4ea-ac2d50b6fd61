# 任务：移除原生 TabBar 配置

## 任务信息

- **所属功能模块**: TabBar 栏自定义
- **优先级**: 高
- **预估工时**: 1 人日
- **状态**: 待开发

## 任务描述

从项目中移除原有的 uniapp 自带 tabBar 配置，并收集相关页面路径用于后续自定义实现。同时调整 pages.json 中的页面顺序，将新的主页设置为首页。

## 技术实现详情

### 1. 移除原生 tabBar 配置

从`pages.json`文件中删除以下部分：

```json
"tabBar": {
  "color": "#7A7E83",
  "height": "50px",
  "selectedColor": "#64b6f7",
  "borderStyle": "black",
  "backgroundColor": "#FFFFFF",
  "list": [
    {
      "pagePath": "pages/okr/okrList",
      "iconPath": "static/tabbar/goal.png",
      "selectedIconPath": "static/tabbar/goal_active.png",
      "text": "目标"
    },
    {
      "pagePath": "pages/okr/today",
      "iconPath": "static/tabbar/task.png",
      "selectedIconPath": "static/tabbar/task_active.png",
      "text": "今天"
    },
    // ... 其他tab配置 ...
  ]
}
```

### 2. 收集原有 tabBar 页面路径

整理并记录原 tabBar 中使用的页面路径列表，供后续自定义 TabBar 组件使用：

- `pages/okr/okrList`（目标）
- `pages/okr/today`（今天）
- `pages/okr/data-analysis`（分析）
- `pages/memo/diary`（日记）
- `pages/setting/setting`（设置）

### 3. 调整 pages.json 中的页面顺序

将新建的主页调整为页面列表的第一位，确保应用启动时首先加载此页面：

```json
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "主页",
        "navigationStyle": "custom"
      }
    }
    // ... 其他页面配置 ...
  ]
}
```

## 验收标准

1. pages.json 文件已正确修改：
   - 已移除 tabBar 配置
   - 已添加主页作为首页
2. 原有的 tabBar 页面路径已收集并记录
3. 项目构建无错误

## 依赖关系

- 此任务为当前功能的第一个任务，无其他任务依赖
- 后续任务 task-tabbar-02 依赖此任务完成

## 注意事项

1. 移除 tabBar 配置后，需确保应用仍能正常启动和运行
2. 收集的页面路径应精确无误，以确保后续自定义 TabBar 能正确导航

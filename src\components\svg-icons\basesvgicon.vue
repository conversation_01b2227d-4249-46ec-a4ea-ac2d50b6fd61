<template>
  <svg class="svg-icon" :style="svgStyle" :viewBox="viewBox" v-bind="$attrs">
    <path :fill="computedColor" :d="path" />
  </svg>
</template>

<script setup>
const props = defineProps({
  color: {
    type: String,
    default: 'currentColor',
  },
  size: {
    type: [Number, String],
    default: 24,
  },
  viewBox: {
    type: String,
    default: '0 0 24 24',
  },
  path: {
    type: String,
    required: true,
  },
})

const computedColor = computed(() => props.color)
const svgStyle = computed(() => ({
  width: typeof props.size === 'number' ? `${props.size}px` : props.size,
  height: typeof props.size === 'number' ? `${props.size}px` : props.size,
}))
</script>

<style scoped>
.svg-icon {
  display: inline-block;
  vertical-align: middle;
  shape-rendering: geometricPrecision;
}
</style>

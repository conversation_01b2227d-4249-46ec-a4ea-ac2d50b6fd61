# 任务：创建周目标与周复盘的共用编辑页面

- **所属功能模块**: 周计划与复盘
- **优先级**: 高
- **状态**: 待办

## 任务描述
创建一个新的Vue页面 `weekEdit.vue`，该页面将作为编辑周目标和周复盘的通用页面。页面行为将通过URL参数进行区分。

## 技术实现详情
1.  **创建新页面**:
    - 在 `/pages/okr/` 目录下创建 `weekEdit.vue` 文件。
    - 在 `pages.json` 中注册该页面。
2.  **参数化逻辑**:
    - 页面加载时，通过 `onLoad`生命周期钩子函数获取 `type` URL参数 (`goal` 或 `review`)。
    - 根据 `type` 的值，动态设置页面的标题（如：“编辑周目标”或“编辑周复盘”）。
3.  **UI布局**:
    - 页面包含一个导航栏，显示动态标题和返回按钮。
    - 页面主体部分包含一个 `textarea` 用于文本输入。
    - 根据 `type` 的值，在 `textarea` 中显示不同的提示文字（placeholder）。
    - 页面底部设置一个"保存"按钮。

## 验收标准
- 访问 `/pages/okr/weekEdit.vue?type=goal` 时，页面标题为"编辑周目标"。
- 访问 `/pages/okr/weekEdit.vue?type=review` 时，页面标题为"编辑周复盘"。
- 页面正确渲染出文本输入框和保存按钮。
-"保存"按钮和数据加载功能在此阶段无需实现。

## 依赖关系
- 无，可与任务 `task-weekly-planning-01` 并行开发。 
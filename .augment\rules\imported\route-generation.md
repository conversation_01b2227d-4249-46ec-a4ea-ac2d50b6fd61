---
type: 'manual'
---

# 页面和路由自动化生成规则

## 页面创建规则

当需要在项目中创建新页面时，需要完成两个步骤：

1. 在指定目录下创建 Vue 页面文件
2. 在 pages.json 文件中添加对应的路由配置

### 页面文件命名规范

- 组件名称应该使用多词命名（避免单个单词）
- 使用小写字母和连字符（kebab-case）命名文件

### 页面文件结构

新创建的页面应包含以下基本结构：

```vue
<template>
  <view class="content">
    <!-- 页面内容 -->
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 数据定义
const data = ref([])

// 生命周期钩子
onMounted(() => {
  // 初始化逻辑
})
</script>

<style lang="scss">
.content {
  padding: 30rpx;
}
</style>
```

### 技术要求

- 使用 Vue 3 组合式 API 开发
- 使用 dayjs 库处理日期
- 使用 currency.js 库处理金额
- 使用 rpx 作为样式单位
- 使用 src/components 下以'z-'开头的组件（无需导入）
- 使用./components 下以'l-'开头的组件（无需导入）

## 路由配置规则

在[src/pages.json](mdc:src/pages.json)中添加路由配置时，需要遵循以下规则：

1. 路由配置应添加在相关页面的配置之后
2. 基本配置格式如下：

```json
{
  "path": "pages/目录名称/页面名称",
  "style": {
    "enablePullDownRefresh": true/false,
    "navigationBarTitleText": "页面标题"
  }
}
```

3. 对于需要自定义导航栏的页面，添加：`"navigationStyle": "custom"`
4. 对于需要下拉刷新的页面，设置：`"enablePullDownRefresh": true`

### 示例

在`src/pages/speak`目录下添加名为`news.vue`的页面，并在`pages.json`中添加如下配置：

```json
{
  "path": "pages/speak/news",
  "style": {
    "enablePullDownRefresh": true,
    "navigationBarTitleText": "新闻资讯"
  }
}
```

## 自动化步骤

1. 确定页面名称和所在目录
2. 创建页面文件，遵循命名规范和技术要求
3. 在 pages.json 的 pages 数组中找到相关位置
4. 添加对应路由配置
5. 确认配置无误后保存文件

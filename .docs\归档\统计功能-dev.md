## 3. 技术实现

### 3.1 数据获取与处理

#### 日视图数据

##### 3.1.1 数据获取流程

日视图数据基于`today.vue`页面中的`fetchTasksByDate`函数实现，主要执行以下步骤：

1. **获取指定日期的任务数据**
   - 查询条件：`type == 'kr' && startDate <= '${date}' && endDate >= '${date}'`
   - 获取所有符合日期条件的 KR 类型任务

2. **关联目标信息**
   - 提取任务中的目标 ID (`okrId`)
   - 查询相关目标详情
   - 将目标信息关联到任务对象

3. **过滤任务**
   - 处理重复任务（根据重复规则判断当天是否应显示）
   - 过滤暂停目标的任务

4. **计算任务进度数据**
   - 对于重复任务，使用`rrule.getTaskProgressData`计算当天进度
   - 对于非重复任务，创建基本进度数据对象

5. **格式化任务数据**
   - 转换为前端显示所需的数据格式

##### 3.1.2 今日生产力统计计算

基于获取的任务数据，今日生产力统计需要计算以下指标：

1. **任务进度统计**
   ```javascript
   // 统计当天任务总进度（如果当天所有任务完成，加起来的总进度）
   const taskTotalProgress = tasks.reduce((sum, task) => {
     // 获取任务的每日目标值或默认为 1
     const dailyTarget = task.dailyTarget || 1;
     return sum + dailyTarget;
   }, 0);
   
   // 统计当天任务实际进度（当天所有任务实际完成进度）
   const taskActualProgress = tasks.reduce((sum, task) => {
     // 从 progressData 中获取当天完成的进度
     const completedToday = task.progressData?.completedToday || 0;
     return sum + completedToday;
   }, 0);
   ```

2. **目标进度统计**
   ```javascript
   // 统计当天目标的总进度（如果当天所有任务完成之后，对目标贡献的总进度）
   const goalTotalProgress = tasks.reduce((sum, task) => {
     // 获取任务的每日目标值
     const dailyTarget = task.dailyTarget || 1;
     // 获取任务对目标的贡献比例
     const contributionRate = task.contributionRate || 1;
     return sum + (dailyTarget * contributionRate);
   }, 0);
   
   // 统计当天目标实际进度（当天所有任务实际完成进度，对目标贡献的总进度）
   const goalActualProgress = tasks.reduce((sum, task) => {
     // 从 progressData 中获取当天完成的进度
     const completedToday = task.progressData?.completedToday || 0;
     // 获取任务对目标的贡献比例
     const contributionRate = task.contributionRate || 1;
     return sum + (completedToday * contributionRate);
   }, 0);
   ```

3. **今日完成率计算**
   ```javascript
   // 当天任务实际进度/当天任务总进度 * 100%
   const completionRate = taskTotalProgress > 0 
     ? Math.round((taskActualProgress / taskTotalProgress) * 100) 
     : 0;
   ```

##### 3.1.3 数据结构

日视图数据最终生成的结构如下：

```javascript
const dayStats = {
  // 今日完成率
  completionRate: completionRate,
  
  // 目标进度
  goalProgress: {
    current: goalActualProgress,
    total: goalTotalProgress
  },
  
  // 任务进度
  taskProgress: {
    current: taskActualProgress,
    total: taskTotalProgress
  },
  
  // 统计摘要
  summary: [
    { value: incompleteTasks, label: '待完成任务' },
    { value: completedTasks, label: '完成任务' },
    { value: inProgressTasks, label: '进行中任务' },
    { value: `${goalActualProgress}/${goalTotalProgress}`, label: '目标进度' },
  ],
};
```

##### 3.1.4 实现注意事项

1. **重复任务处理**
   - 使用`rrule`工具计算重复任务在当天的进度
   - 判断当天是否为任务的发生日

2. **目标贡献率**
   - 每个任务对其关联目标的贡献率需要正确计算
   - 默认贡献率为 1，可根据任务设置调整

3. **数据边界处理**
   - 处理无任务情况下的默认值
   - 避免除零错误

4. **性能优化**
   - 减少不必要的 API 调用
   - 使用批量查询减少网络请求

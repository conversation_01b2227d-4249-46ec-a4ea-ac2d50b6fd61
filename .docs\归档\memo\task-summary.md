# 日记功能增强 - 任务拆分总结

## 需求概述
本次需求旨在增强现有日记功能，引入多次输入、问答式总结和智能回顾分析，以提升用户记录和反思的体验。核心是通过 AI 辅助，将碎片化记录转化为结构化思考。

## 任务拆分
1.  **task-diary-data-01**: 日记数据结构设计与实现
    - 调整 `Memo` 表的 `content` 字段以支持 JSON 格式，存储多次输入、问答及总结内容。
2.  **task-diary-entry-01**: 日记时间轴UI开发
    - 开发 `l-diary-timeline` 和 `l-diary-card` 组件，用于展示当天所有日记条目。
3.  **task-diary-entry-02**: 快捷输入UI开发
    - 开发 `l-quick-input` 浮动按钮组件，提供快速记录入口。
4.  **task-diary-entry-03**: 增强日记编辑器开发
    - 扩展 `z-editor` 为 `z-diary-editor`，支持多媒体、类型标记等功能。
5.  **task-diary-entry-04**: 多次输入功能集成
    - 整合UI组件，实现多次输入日记的完整流程，并集成AI即时回应。
6.  **task-diary-qa-01**: 问答交互UI开发
    - 开发 `z-question-answer` 组件，实现对话式问答界面。
7.  **task-diary-qa-02**: AI问答服务开发
    - 实现问题生成逻辑（`/api/diary/generateQuestions`），包括本地问题库和动态生成。
8.  **task-diary-qa-03**: AI总结服务开发
    - 实现日记总结生成逻辑（`/api/diary/generateSummary`），整合当日所有内容。
9.  **task-diary-qa-04**: 问答式总结功能集成
    - 整合问答UI与AI服务，实现完整的问答式日记总结流程。
10. **task-diary-review-01**: 情绪图表UI开发
    - 开发 `z-emotion-chart` 组件，用于情绪趋势可视化。
11. **task-diary-review-02**: 日记分析服务开发
    - 实现日记分析逻辑（`/api/diary/analyze`），提取情绪、主题等信息。
12. **task-diary-review-03**: 回顾分析功能集成
    - 开发回顾页面，整合图表与分析服务，提供周报、月报等功能。

## 任务依赖关系
```mermaid
graph TD
    subgraph "模块一：数据结构与基础"
        A[task-diary-data-01]
    end

    subgraph "模块二：多次输入功能"
        B[task-diary-entry-01]
        C[task-diary-entry-02]
        D[task-diary-entry-03]
        E[task-diary-entry-04]
    end

    subgraph "模块三：问答与总结"
        F[task-diary-qa-01]
        G[task-diary-qa-02]
        H[task-diary-qa-03]
        I[task-diary-qa-04]
    end

    subgraph "模块四：回顾与分析"
        J[task-diary-review-01]
        K[task-diary-review-02]
        L[task-diary-review-03]
    end

    A --> B
    A --> C
    A --> D
    A --> G
    A --> H
    A --> K

    B --> E
    C --> E
    D --> E

    E --> I
    F --> I
    G --> I
    H --> I

    J --> L
    K --> L
    E --> L
```

## 任务优先级
- **高优先级**: task-diary-data-01, task-diary-entry-04, task-diary-qa-04
- **中优先级**: task-diary-entry-01, task-diary-entry-02, task-diary-entry-03, task-diary-qa-01, task-diary-qa-02, task-diary-qa-03, task-diary-review-03
- **低优先级**: task-diary-review-01, task-diary-review-02

## 开发建议
1.  **数据先行**: 优先完成 `task-diary-data-01`，为所有后续开发提供数据基础。
2.  **UI并行**: 各UI组件（entry-01, 02, 03, qa-01, review-01）可以并行开发，使用模拟数据进行。
3.  **分步集成**: 先完成"多次输入"功能闭环（`task-diary-entry-04`），再集成"问答总结"功能（`task-diary-qa-04`），最后完成"回顾分析"功能（`task-diary-review-03`）。
4.  **后端服务**: AI相关的后端服务（qa-02, qa-03, review-02）可与前端并行开发，尽早联调。
5.  **兼容性**: 数据结构调整时，必须考虑对旧数据的兼容性处理方案。 
import request from '@/utils/request'
const speakAppid = '7492972882780995620'

/** 执行工作流 API
 * @param {string} workflow_id - 工作流 ID
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} 包含处理结果的响应对象
 */
const workflowApi = async (app_id, workflow_id, params) => {
  const res = await request.post(
    'https://api.coze.cn/v1/workflow/run',
    {
      workflow_id,
      app_id,
      parameters: params,
    },
    {
      timeout: 300000,
      header: {
        Authorization: `Bearer ${getApp().globalData.kzToken}`,
        'Content-Type': 'application/json',
      },
    }
  )
  return {
    ...res,
    data: JSON.parse(res.data),
  }
}
// 生成复述素材
export const generateSpeakConetnt = async () => {
  const params = {
    message: '你好',
    messages: [],
    model: 'deepseek-chat',
    system: 'You are a helpful assistant.',
  }
  const res = await request.post('/ds/chat', params)
  return {
    ...res,
    data: JSON.parse(res.data),
  }
}

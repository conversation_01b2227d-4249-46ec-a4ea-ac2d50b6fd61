# 任务：回顾分析功能集成

- **所属功能模块**: `diary-review`
- **任务ID**: `task-diary-review-03`
- **优先级**: 中

## 任务描述
创建日记回顾与分析的用户界面，并将 `z-emotion-chart` 组件和日记分析服务整合起来，为用户提供可视化的数据洞察，如周报、月报等。

## 技术实现详情
1.  **回顾页面开发**:
    - 创建一个新的页面用于日记回顾。
    - 页面布局应包含：
        - 时间范围选择器（如：本周、本月、自定义范围）。
        - 情绪曲线图表区域。
        - 关键主题展示区域。
        - （可选）AI生成的周期性文本摘要（周报/月报）。

2.  **数据整合与展示**:
    - 当用户选择一个时间范围后，从本地数据库获取该范围内的所有日记数据。
    - 调用日记分析服务（`task-diary-review-02`），传入日记数据。
    - 将服务返回的 `emotion_trend` 数据传递给 `z-emotion-chart` 组件进行渲染。
    - 将返回的 `key_topics` 展示在页面的相应区域。
    - 如果需要生成文本摘要，可额外调用AI服务，将分析结果作为输入，生成一段总结性文字。

3.  **状态处理**:
    - 在调用分析服务和等待图表渲染期间，需要有明确的加载中状态。
    - 对服务错误进行捕获和提示。
    - 对没有数据的空状态进行优雅处理。

## 验收标准
- 用户可以在回顾页面选择时间范围。
- 选择时间后，页面能正确调用分析服务并展示情绪曲线和关键主题。
- `z-emotion-chart` 组件能接收真实数据并正常工作。
- 页面在加载、空状态和错误状态下都有合适的UI表现。

## 依赖关系
- `task-diary-review-01`: 需要 `z-emotion-chart` 组件。
- `task-diary-review-02`: 需要日记分析服务。
- `task-diary-entry-04`: 依赖完整的日记数据作为分析基础。

## 状态追踪
- 未开始 
export function useKeyboard(key: string | string[], callback: () => void) {
  const keyEvent = ref(null)

  const eventHandler = (event: KeyboardEvent): void => {
    const keys = Array.isArray(key) ? key : [key]

    const allKeysPressed = keys.every((key) => {
      switch (key.toLowerCase()) {
        case 'ctrl':
          return event.ctrlKey
        case 'shift':
          return event.shiftKey
        case 'alt':
          return event.altKey
        default:
          return event.key.toLowerCase() === key
      }
    })

    if (allKeysPressed) {
      // event.preventDefault()
      callback()
    }
  }

  onMounted(() => {
    window.addEventListener('keydown', eventHandler)
  })

  onUnmounted(() => {
    window.removeEventListener('keydown', eventHandler)
  })

  return keyEvent
}

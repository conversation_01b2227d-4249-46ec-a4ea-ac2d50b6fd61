<template>
  <view class="test-page">
    <view class="header">
      <text class="title">键盘组件测试页面</text>
    </view>
    
    <view class="test-section">
      <view class="section-title">进度更新弹窗测试</view>
      <view class="test-buttons">
        <button @click="showModal = true" class="test-btn">打开进度更新弹窗</button>
      </view>
      
      <view class="test-info">
        <text>当前进度值: {{ testProgress }}</text>
      </view>
    </view>

    <!-- 进度更新弹窗 -->
    <z-update-progress-modal
      v-model:visible="showModal"
      :task-id="testTaskId"
      :initial-progress="testProgress"
      kr-title="测试关键结果"
      @save-success="onSaveSuccess"
    />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const showModal = ref(false)
const testProgress = ref(0)
const testTaskId = ref('test-task-id-123')

const onSaveSuccess = (addedValue: number) => {
  testProgress.value = addedValue
  uni.showToast({
    title: `进度更新成功: ${addedValue}`,
    icon: 'success'
  })
}
</script>

<style scoped lang="scss">
.test-page {
  padding: 40rpx;
  min-height: 100vh;
  background-color: #f8f9ff;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--color-gray-800);
}

.test-section {
  background-color: var(--color-white);
  border-radius: var(--rounded-lg);
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: var(--shadow-sm);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--color-gray-800);
  margin-bottom: 30rpx;
}

.test-buttons {
  margin-bottom: 30rpx;
}

.test-btn {
  background-color: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: var(--rounded-md);
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.test-info {
  padding: 20rpx;
  background-color: var(--color-gray-50);
  border-radius: var(--rounded-md);
  
  text {
    font-size: 28rpx;
    color: var(--color-gray-700);
  }
}
</style>

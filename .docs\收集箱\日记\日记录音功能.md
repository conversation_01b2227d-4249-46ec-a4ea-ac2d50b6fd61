# 日记录音功能需求

## 背景

为了让用户更方便快捷地记录日记，我们计划为日记功能增加录音输入方式。用户可以通过录音来创建日记，系统会自动将语音转换为文字，并保存录音文件。这可以降低用户记录的门槛，尤其是在不方便打字的情况下。

## 需求

### 功能需求

1.  **录音功能**：在日记编辑页面，提供一个录音按钮。
2.  **录音控制**：用户可以开始、停止录音。
3.  **音频播放**：录音结束后，在页面上显示一个音频播放器，用户可以播放、暂停、回听自己的录音。
4.  **语音转文字**：录音文件上传后，调用语音转文字服务，将语音内容自动填充到日记内容输入框中。
5.  **保存日记**：点击保存时，将文本内容和录音文件信息（如 URL）一同保存到数据库。
6.  **编辑已有录音日记**：打开包含录音的日记时，应能看到文本内容并能播放录音。

### 非功能需求

- **用户体验**: 录音和转写过程需要有明确的状态提示（如“正在录音...”、“正在转写...”）。
- **性能**: 语音文件上传和转写不应长时间阻塞 UI。

## 技术方案

### 实现思路

1.  **前端实现**:
    - 在 `src/pages/memo/diary_edit.vue` 页面中添加录音 UI。
    - 参考 `src/pages/speak/keyword-story-page.vue` 中使用的 `z-audio-player` 组件，实现播放和语音转文字功能。
    - 使用 `uni.getRecorderManager()` 实现录音功能。
    - 录音结束后，将音频文件上传到云存储，获取文件 URL。
    - 将音频 URL 传递给 `z-audio-player` 组件进行播放和转写。
    - `z-audio-player` 组件在转写完成后，通过事件将文字结果返回。
    - 将转写结果更新到日记内容的 `content` ref 中。
    - 修改 `onSubmit` 方法，在保存日记时，将 `audioURL` 等音频信息一并存入数据库。
2.  **后端实现**:
    - 无需后端修改。语音转文字服务由前端播放器组件或相关 SDK 直接调用。
3.  **数据库**:
    - 需要在 `memo` 表中增加字段来存储音频信息，例如 `audioURL` (字符串) 和 `audioDuration` (数字)。

### 架构设计

```mermaid
graph TD
    A[用户点击录音按钮] --> B{开始录音};
    B --> C[用户结束录音];
    C --> D[上传录音文件至云存储];
    D --> E[获取音频文件URL];
    E --> F[显示音频播放器 z-audio-player];
    F --> G[调用语音转文字服务];
    G --> H[将转写文本填充到输入框];
    A -- "UI更新" --> A;
    C -- "UI更新" --> C;
    G -- "UI更新" --> G;
    H --> I[用户点击保存];
    I --> J[保存文本和音频URL到数据库];
```

## 风险评估

- **语音转文字准确率**: 转写服务的准确率可能影响用户体验。可以选择更可靠的服务或提供手动编辑功能。
- **兼容性**: 录音 API 在不同平台（H5, 小程序, App）的兼容性问题。需要进行充分测试。


# 需求文档：工具返回值定义规范

## 1. 背景
当前系统的 AI 规划器 (`IntelligentExecutionPlanner`) 在生成多步骤计划时，依赖其内置知识来“猜测”上一步骤工具的返回数据结构，以便为下一步骤构建参数引用。这种“猜测”虽然在多数情况下有效，但缺乏确定性，是系统鲁棒性的一个主要隐患。当工具返回结构与 AI 的猜测不符时，会导致执行阶段出现致命的参数解析错误，进而导致任务失败。

## 2. 需求
### 功能需求
1.  **扩展工具注册规范**
    - 必须为 `TOOL_REGISTRY` 中的每一个工具定义，补充一个 `returns` 字段。
    - `returns` 字段必须使用 **JSON Schema** 格式，严格、清晰地描述该工具成功执行后返回的数据对象的结构、字段、类型和描述。
2.  **升级 AI 规划提示词**
    - 必须修改 `IntelligentExecutionPlanner` 中的 `buildAnalysisPrompt` 方法。
    - 在生成 `analysisPrompt` 时，必须将每个工具的 `returns` 定义也作为工具信息的一部分，包含在发送给大模型的提示词中。
3.  **确保 AI 遵从规范**
    - 提示词需引导和指示 AI 在规划后续步骤时，严格参照前置步骤的 `returns` 定义来构建动态参数引用 (`$step...`)。

## 3. 技术方案
### 实现思路
#### 1. 更新工具定义 (`config.js`)
遍历 `TOOL_REGISTRY` 中的所有工具对象，为其添加 `returns` 键值对。
**示例 - `getTasks` 工具:**
```javascript
// Before
getTasks: {
  cloudFunction: 'todolist-api',
  method: 'getTasks',
  description: '获取任务列表...',
  parameters: { ... }
}

// After
getTasks: {
  cloudFunction: 'todolist-api',
  method: 'getTasks',
  description: '获取任务列表...',
  parameters: { ... },
  returns: {
    type: 'object',
    properties: {
      tasks: {
        type: 'array',
        description: '任务对象数组',
        items: {
          type: 'object',
          properties: {
            _id: { type: 'string', description: '任务的唯一 ID' },
            title: { type: 'string', description: '任务标题' },
            completed: { type: 'boolean', description: '是否完成' }
          }
        }
      },
      total: { type: 'number', description: '任务总数' }
    }
  }
}
```

#### 2. 修改提示词生成器 (`planner.js`)
修改 `generateToolPrompt` 函数，在拼接每个工具的描述字符串时，附加上其返回值定义。
**示例 - 生成的提示词片段：**
```text
- getTasks: 获取任务列表，支持多种筛选条件。
  参数：{...}
  返回：{"type":"object","properties":{"tasks":{"type":"array",...},"total":{"type":"number",...}}}
```
这样，大模型在规划时就能确切地知道，如果它调用了 `getTasks`，后续可以通过 `$step.step_id.result.tasks[0]._id` 来安全地引用任务 ID。

## 4. 风险评估
- **潜在风险**:
    - **Token 消耗增加**: 提示词会显著变长，导致每次规划任务时的大模型 API 调用费用增加。
    - **维护成本**: 后端云函数的实际返回值必须与 `config.js` 中的 `returns` 定义严格保持同步。任何一方的修改都需要另一方同步更新，否则会引入新的、更隐蔽的错误。
- **应对策略**:
    - 建立代码规范和 CR（代码审查）流程，确保云函数接口变更时，必须同步更新 `TOOL_REGISTRY` 中的定义。
    - 可以考虑编写单元测试，自动化地校验工具的实际返回与 `returns` 定义是否匹配。

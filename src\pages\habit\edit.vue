<template>
  <view class="pages">
    <uni-forms :model-value="formData" label-position="top">
      <uni-forms-item label="习惯名" name="title">
        <uni-easyinput v-model="formData.title" type="text" placeholder="请输入习惯名字" />
      </uni-forms-item>
      <uni-forms-item label="类型">
        <view class="type-box">
          <view class="type-item" :class="[formData.type === 0 ? 'active' : '']" @click="formData.type = 0">
            按天
          </view>
          <view class="type-item" :class="[formData.type === 1 ? 'active' : '']" @click="formData.type = 1">
            按量
          </view>
        </view>
      </uni-forms-item>
      <uni-forms-item label="分类">
        <scroll-view scroll-x="true" scroll-left="120" class="scroll-y" show-scrollbar="false">
          <view class="label-list">
            <view class="label-box" @click="goPage('/pages/habit/label')"> + </view>
            <view
              v-for="(item, index) in habitLable"
              class="label-box"
              :class="[formData.label_id === item._id ? 'active' : '']"
              @click="formData.label_id = item._id"
            >
              {{ item.title }}
            </view>
          </view>
        </scroll-view>
      </uni-forms-item>
    </uni-forms>
    <button style="margin-top: 10px; width: 100%" type="primary" @click="formSubmit">保存</button>
    <button style="margin-top: 10px; width: 100%" type="warn" @click="onDelete">删除</button>
  </view>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue'
const db = uniCloud.database()

const formData = ref({
  title: '',
  type: 0,
  label_id: '',
})

const formSubmit = () => {
  // TODO: 为什么需要使用 _this
  const _this = this
  uni.showLoading({
    title: '提交中',
  })
  if (habitId) {
    db.collection('habit')
      .doc(habitId)
      .update({
        title: formData.value.title,
        type: formData.value.type,
        label_id: formData.value.label_id,
      })
      .then((res) => {
        uni.hideLoading()
        uni.showToast({
          title: '提交成功',
          success() {
            uni.switchTab({
              url: '/pages/habit/list',
            })
          },
        })
      })
      .catch((err) => {
        console.log(err.message)
      })
  } else {
    db.collection('habit')
      .add({
        title: formData.value.title,
        type: formData.value.type,
        label_id: formData.value.label_id,
      })
      .then((res) => {
        uni.showToast({
          title: '提交成功',
          success() {
            uni.switchTab({
              url: '/pages/habit/list',
            })
          },
        })
      })
      .catch((err) => {
        console.log(err.message)
      })
      .finally(() => {
        uni.hideLoading()
      })
  }
}
const goPage = (path) => {
  uni.navigateTo({
    url: path,
  })
}
const habitLable = ref([])
let habitId = ''
// 删除
const onDelete = async () => {
  uni.showLoading({
    title: '删除中',
  })
  await db.collection('habit').doc(habitId).remove()
  uni.hideLoading()
  uni.switchTab({
    url: '/pages/habit/list',
  })
}
onMounted(() => {
  const { id } = getApp().globalData.urlParams

  db.collection('habit-label')
    .where('user_id==$cloudEnv_uid')
    .get()
    .then(({ result }) => {
      habitLable.value = result.data
    })

  // 编辑状态
  if (id) {
    habitId = id
    db.collection('habit')
      .where({
        _id: id,
      })
      .get()
      .then(({ result }) => {
        console.log('result.data', result.data[0])
        formData.value = result.data[0]
      })
  }
})
</script>

<style lang="scss" scoped>
/* #ifndef APP-NVUE */
view {
  display: flex;
  box-sizing: border-box;
  flex-direction: column;
}

/* #endif */
.pages {
  padding: 10px;
  background-color: #ffffff;
}

.type-box {
  display: flex;
  align-items: center;
  flex-direction: inherit;
  // border: 1px solid #000;

  .type-item {
    padding: 5px 15px;
    border: 1px solid #000;

    &:first-child {
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
    }

    &:last-child {
      border-top-right-radius: 5px;
      border-bottom-right-radius: 5px;
    }

    &.active {
      background-color: #000;
      color: #fff;
    }
  }
}

.label-list {
  display: flex;
  align-items: center;
  flex-direction: inherit;

  .label-box {
    margin-right: 10px;
    padding: 5px 15px;
    border: 1px solid #000;
    border-radius: 20px;

    &.active {
      background-color: #000;
      color: #fff;
    }
  }
}

.scroll-y {
  white-space: nowrap;
}
</style>

/**
 * 数据库相关类型定义
 * 提供严格的类型检查，确保状态值类型一致
 */

import type { OkrStatus, TaskStatus } from '@/constants/status'

// 数据库查询条件类型
export type QueryCondition = string

// 数据库查询构建器类型
export interface QueryBuilder {
  // OKR状态查询
  okrStatus: {
    equals: (status: OkrStatus) => QueryCondition
    notEquals: (status: OkrStatus) => QueryCondition
    in: (statuses: OkrStatus[]) => QueryCondition
    notIn: (statuses: OkrStatus[]) => QueryCondition
  }
  
  // 任务状态查询
  taskStatus: {
    equals: (status: TaskStatus) => QueryCondition
    notEquals: (status: TaskStatus) => QueryCondition
    in: (statuses: TaskStatus[]) => QueryCondition
    notIn: (statuses: TaskStatus[]) => QueryCondition
  }
}

// 数据库实体基础接口
export interface BaseEntity {
  _id: string
  createTime: string
  updateTime: string
  deleteTime: string
}

// OKR实体接口（扩展原有定义）
export interface OkrEntity extends BaseEntity {
  title: string
  content: string
  color: string
  startDate: string
  endDate: string
  status: OkrStatus  // 使用严格的状态类型
  curVal?: number
  motivation: Array<{
    title: string
    content: string
  }>
  feasibility: Array<{
    title: string
    content: string
  }>
}

// 任务实体接口
export interface TaskEntity extends BaseEntity {
  title: string
  description?: string
  status: TaskStatus  // 使用严格的状态类型
  okrId: string
  parentId?: string
  type: 'kr' | 'todo' | 'habit'
  startDate: string
  endDate: string
  progress?: number
  curVal?: number
  tgtVal?: number
  weight?: number
  dailyTarget?: number
  repeatFlag?: string
  recList?: any[]
  compInfos?: any[]
  completeTime?: string
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  code?: number
}

// 分页查询参数
export interface PaginationParams {
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 分页查询结果
export interface PaginatedResult<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 状态更新参数类型
export interface OkrStatusUpdate {
  status: OkrStatus
}

export interface TaskStatusUpdate {
  status: TaskStatus
  completeTime?: string
}

// 类型守卫函数
export const isValidOkrStatus = (status: any): status is OkrStatus => {
  return typeof status === 'string' && 
    ['pending', 'inProgress', 'completed', 'paused', 'abandoned'].includes(status)
}

export const isValidTaskStatus = (status: any): status is TaskStatus => {
  return typeof status === 'number' && [0, 1, 2].includes(status)
}

// 查询条件验证函数
export const validateQueryCondition = (condition: string): boolean => {
  // 基础验证：检查是否包含潜在的SQL注入
  const dangerousPatterns = [
    /drop\s+table/i,
    /delete\s+from/i,
    /insert\s+into/i,
    /update\s+set/i,
    /--/,
    /\/\*/,
    /\*\//
  ]
  
  return !dangerousPatterns.some(pattern => pattern.test(condition))
}

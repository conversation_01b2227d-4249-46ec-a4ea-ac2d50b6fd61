<template>
  <view :class="!sys.isPC ? 'fixed-placeholder' : ''">
    <view :class="!sys.isPC ? 'fixed bg-white' : ''" class="w-full top-0 z-100">
      <view class="status_bar"></view>
      <view class="page-margin flex flex-1 h-80 justify-between items-center">
        <view>
          <text class="font-bold text-40">{{ dayText.month }}，</text>
          <text class="text-25">{{ dayText.diff }}</text>
        </view>

        <!-- <view v-show="showDateBtn" class="text-25 text-[#64b6f7]" @click="showDateBtn = false">统计</view>
        <view v-show="!showDateBtn" class="text-25 text-[#64b6f7]" @click="showDateBtn = true">日期</view> -->
      </view>
    </view>
    <view class="page-margin">
      <!-- 日历 -->
      <view style="margin: 0 -32rpx">
        <z-calendar ref="calendarRef" @on-change="init" @on-show-today="(isShow) => (showTodayBtn = isShow)" />
      </view>
      <l-statistics :compTaskList="compTaskList" :unCompTaskList="unCompTaskList"></l-statistics>
      <view class="pages">
        <uni-collapse>
          <uni-collapse-item :open="true">
            <template #title>
              <view class="z-collapse-title">
                <view>今天</view>
                <view class="z-collapse-number">{{ unCompTaskList.length }}</view>
              </view>
            </template>
            <template v-if="unCompTaskList.length > 0">
              <z-task-list :sort-key="orderByStr" :task-list="unCompTaskList" :day="curDate" @on-change="init" />
            </template>
            <view v-else class="py-10">
              <u-empty text="今日无任务" mode="list" />
            </view>
          </uni-collapse-item>
          <uni-collapse-item v-if="exTaskList.length > 0">
            <template #title>
              <view class="z-collapse-title">
                <view>过期</view>
                <view class="z-collapse-number">{{ exTaskList.length }}</view>
              </view>
            </template>
            <z-task-list :task-list="exTaskList" :day="curDate" @on-change="init" />
          </uni-collapse-item>
          <uni-collapse-item v-if="compTaskList.length > 0">
            <template #title>
              <view class="z-collapse-title">
                <view>已完成</view>
                <view class="z-collapse-number">{{ compTaskList.length }}</view>
              </view>
            </template>
            <z-task-list :task-list="compTaskList" :day="curDate" @on-change="init" />
          </uni-collapse-item>
        </uni-collapse>
        <view class="bg-white mt-2 p-2 rr-4" v-for="(item, index) in memoList" :key="index" @click="editMemo(item)">
          <view v-html="item.content"></view>
          <view class="text-16">{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</view>
        </view>
      </view>
    </view>
    <z-task-popup type="today" :today="curDate" v-model:show="taskPopupVisible" @on-submit="init" />
    <z-button type="plus" @click="taskPopupVisible = true" />
    <view
      v-if="showTodayBtn"
      @click="init('today')"
      class="flex justify-center items-center text-35 fixed right-50 color-white bg-[#64b6f7] wh-100 r-50 z-2"
      style="bottom: calc(var(--window-bottom) + 220rpx)"
    >
      今
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const sys = useIsPC()

const curDate = ref(dayjs().format('YYYY-MM-DD'))

// 显示切换日期按钮
const showDateBtn = ref(false)

const memoVisible = ref(false)
const taskPopupVisible = ref(false)
const showMemoPop = () => {
  memoVisible.value = true
}
const calendarRef = ref(null)

// 监听打开任务弹窗的快捷键
useKeyboard(['shift', 'n'], () => {
  taskPopupVisible.value = true
})

// 监听返回键
onBackPress(() => {
  if (taskPopupVisible.value) {
    taskPopupVisible.value = false
    return true
  } else {
    return false
  }
})

const dayText = computed(() => {
  const date = dayjs(curDate.value)
  let diff = date.diff(dayjs().format('YYYY-MM-DD'), 'day')

  if (diff === 0) diff = '今天'
  else if (diff === 1) diff = '明天'
  else if (diff === 2) diff = '后天'
  else if (diff === -1) diff = '昨天'
  else if (diff === -2) diff = '前天'
  else if (diff > 0) {
    diff = `${diff}天后`
  } else {
    diff = `${Math.abs(diff)}天前`
  }

  return {
    month: date.format('M') + '月',
    diff,
  }
})
const showTodayBtn = ref(false)

let orderByStr = ''

// const { data: taskList, run: getTaskList } = useRequest(getTaskListApi, {
//   async formatter(data) {
//     try {
//       const newList = []
//       for (let i = 0; i < data.length; i++) {
//         const item = data[i]

//         // 循环任务
//         if (item.repeatFlag) {
//           // 是否在重复规则中
//           try {
//             const option = {
//               ...parseRRULE(item.repeatFlag),
//               dtstart: dayjs(item.startDate).toDate(),
//               until: dayjs(item.endDate).toDate(),
//             }
//             let rule = new RRule(option)
//             const bb = dayjs(curDate.value).toDate()
//             const isShowToday = rule.between(bb, bb, true).length > 0
//             if (!isShowToday) continue
//             // 查询完成情况
//             item.status = item.compInfos.some((e) => e.date === curDate.value) ? 1 : 0
//           } catch (error) {
//             console.log('ggeeeee', error)
//           }
//         }
//         // 查询父任务 TODO 应该批量查询
//         if (item.parentId) {
//           const parentTask = await getTaskApi(item.parentId)
//           data[i].parentTask = parentTask
//         }
//         // 查询目标
//         if (item.okrId) {
//           const okr = await getOkrApi(item.okrId)
//           data[i].okr = okr
//         }

//         // 不展示暂停目标的任务
//         if (item.okr && item.okr.status === 2) continue

//         newList.push(data[i])
//       }
//       // 排序
//       orderByStr = `task_sort_${dayjs(curDate.value).format('YYYYMMDD')}`
//       let orderBy = await getConfigApi(orderByStr)

//       const newListByNoSort: any = []
//       const newListBySort: any = []
//       newList.forEach((e) => {
//         if (orderBy[e._id] !== undefined) {
//           newListBySort.push(e)
//         } else {
//           newListByNoSort.push(e)
//         }
//       })
//       newListBySort.sort((a, b) => orderBy[a._id] - orderBy[b._id])
//       return [...newListByNoSort, ...newListBySort]
//     } catch (e) {
//       console.error('报错！！！！')

//       console.error(e)
//       //TODO handle the exception
//     }
//   },
// })
const { data: tasksByDate, run: getTasksByDate } = useRequest(getTaskListByDateApi)
const { data: memoList, run: getMemoList } = useRequest(getMemoListApi, {})
const curMemo = ref({})
const editMemo = (item) => {
  curMemo.value = item
  showMemoPop()
}

const taskList = ref({
  compTaskList: [],
  unCompTaskList: [],
  exTaskList: [],
})
// TODO: init 这个方法会执行 3 次，useReload、onShow、z-calendar 的 onChange, 有待优化
const init = async (date?: string) => {
  try {
    if (date) curDate.value = date === 'today' ? dayjs().format('YYYY-MM-DD') : date
    if (date === 'today') calendarRef.value?.init()
    curMemo.value = {}
    taskList.value = await getTaskListByDateApi(curDate.value, { unComplete: true, expired: true })
  } catch (error) {
    console.error('报错！！！！')

    console.error(error)
  }
}
// 已完成的 todo
const compTaskList = computed(() => taskList.value.compTaskList)
// 未完成的 todo
const unCompTaskList = computed(() => taskList.value.unCompTaskList)

// 过期未完成的 todo
const exTaskList = computed(() => taskList.value.exTaskList)

useReload(init)
// onShow(() => {
//   setTimeout(init, 0)
// })

onMounted(() => {
  // // #ifdef APP-PLUS
  // // APP 端手动关闭启动页
  // plus.navigator.closeSplashscreen()
  // // #endif
})
</script>
<style scoped lang="scss">
.pages {
  // height: 100vh;
  background-color: hsl(205deg, 20%, 94%);
  // padding: 10px;
  margin-top: 20rpx;
}

.today-btn {
  positon: fixed;
  right: 50rpx;
  bottom: 50rpx;
  width: 100rpx;
}

.todo-box {
  background-color: #fff;
  padding: 10px;
}

.new-todo-btn {
  position: fixed;
  bottom: 50px;
  right: 50px;
  width: 40px;
  height: 40px;
  background-color: $uni-bg-color-grey;
  border-radius: 50%;
}

.edit-todo-box {
  padding: 10px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  background-color: #fff;

  .operation-panel {
    display: flex;

    .edit-btn {
      margin-left: auto;
      margin-right: 0;
    }
  }
}
.titlee {
  font-size: 30rpx;
  font-weight: bold;
  margin-top: 30rpx;
  margin-bottom: 20rpx;
}

.fixed-placeholder {
  padding-top: calc(var(--status-bar-height) + 80rpx);
}
</style>

<template>
  <view
    class="ml-auto border-3 border-gray rounded-2 b-solid flex flex-none justify-center items-center wh-40"
    :class="[todoStatus !== 0 ? 'bg-gray' : '', disabled ? 'op-40 bg-gray cursor-not-allowed' : '']"
    @click.stop="onChangeStatus"
  >
    <uni-icons
      v-if="todoStatus === 1"
      type="checkmarkempty"
      size="14"
      class="ckeckmark mb-[-4rpx] font-bold"
      color="#fff"
    />
    <uni-icons
      v-if="todoStatus === 2"
      type="closeempty"
      size="14"
      class="ckeckmark mb-[-4rpx] font-bold"
      color="#fff"
    />
  </view>
</template>
<script setup>
import { defineProps, defineEmits, watch, ref } from 'vue'
const props = defineProps({
  // 状态 0: 未完成 1: 已完成 2: 已删除
  status: {
    type: Number,
    default: 0,
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits('onChange')

const todoStatus = ref(props.status)

// 修改任务状态
const onChangeStatus = async () => {
  if (props.disabled) return

  uni.vibrateShort() // 震动
  todoStatus.value = props.status === 0 ? 1 : 0
  const timer = todoStatus.value === 1 ? 300 : 100
  // 打卡成功，等待动效结束后再刷新列表
  setTimeout(() => {
    emits('onChange', todoStatus.value)
  }, timer)
}
</script>
<style lang="scss" scope>
.ckeckmark {
  animation: checkmark 0.2s ease-out;
}
@keyframes checkmark {
  0% {
    transform: translateY(5px);
  }
  50% {
    transform: translateY(-2px);
  }
  100% {
    transform: translateY(0px);
  }
}
</style>

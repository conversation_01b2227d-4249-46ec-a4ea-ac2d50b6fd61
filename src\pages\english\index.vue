<template>
  <div class="p-[20px] relative h-[100vh]">
    <div v-show="pageType === 'init'">
      <div class="absolute top-[50%] left-[50%] text-80" style="cursor: point;transform: translate(-50%,-50%)"
        @click="onGenerate">生成句子
      </div>
      <div class="absolute top-[45%] left-[60%] text-40" style="cursor: point;transform: translate(-50%,-50%)"
        @click="pageType = 'enterWords'">录入</div>
    </div>
    <div v-show="pageType === 'enterWords'">
      <u-input v-model="wordsStr" type="textarea" />
      <u-button @click="enterWords">录入</u-button>
    </div>

    <div class="mt-[20px]" v-if="pageType === 'list'">
      <div class="mb-[20px]" v-for="(item, index) in sList" :key="index">
        <div class="text-50">{{ item.s }}</div>
        <div>
          <div v-if="!sList[index].isEn" @click="sList[index].isEn = true">查看原文</div>
          <div v-else><span class="color-gray">英文原文：</span>{{ item.en }}</div>
          <div v-if="!sList[index].isG" @click="sList[index].isG = true">查看语法</div>
          <div v-else><span class="color-gray">语法解析：</span>{{ item.grammar }}</div>
          <div v-if="!sList[index].isW" @click="sList[index].isW = true">查看单词</div>
          <div v-else><span class="color-gray">单词：</span>{{ item.words_info }}</div>
        </div>
        <div class="flex items-center">
          <u-button size="mini" @click="onCheck(index)" :loading="item.loading">检查</u-button>
          <u-input class="ml-[10px] w-[100px]" placeholder="请输入你的翻译" v-model="sInput[index]" />
        </div>
        <div v-if="sList[index].answer" class="" v-html="sList[index].answer"></div>
      </div>
    </div>

  </div>
</template>

<script setup>
const pageType = ref('init')
const wordsStr = ref('')
const bills = ref([])
const categories = ref({})
const sList = ref([
  {
    en: 'wwwwww',
    grammar: 'wwwwww',
    s: 'wwwwww',
    words_info: 'wwwwww',
  },
  {
    en: 'wwwwww',
    grammar: 'wwwwww',
    s: 'wwwwww',
    words_info: 'wwwwww',
  },
  {
    en: 'wwwwww',
    grammar: 'wwwwww',
    s: 'wwwwww',
    words_info: 'wwwwww',
  },
])
const sInput = ref([])

const workflowApi = async (workflow_id, params) => {
  const res = await request.post(
    'https://api.coze.cn/v1/workflow/run',
    {
      workflow_id,
      app_id: "7454761032381497359",
      parameters: params
    },
    {
      timeout: 300000,
      header: {
        "Authorization": "Bearer pat_UD2i5nlaSDUwFSmSOPbOxF8hJ23zT0sami2Qpde9k5qBiWcB5e4pZSN75UIM95di",
        "Content-Type": "application/json"
      },
    })
  return {
    ...res,
    data: JSON.parse(res.data)
  }
}

const onCheck = async (index) => {
  sList.value[index].loading = true

  const { data } = await workflowApi("7455162883808264233", {
    chinese: sList.value[index].s,
    english: sInput.value[index]
  })
  sList.value[index].loading = false
  sList.value[index].answer = data.output
}

const enterWords = async (words) => {
  uni.showLoading({
    title: '录入中'
  })
  let { data } = await workflowApi("7454930119828783130", {
    words: wordsStr.value
  })
  console.log(data);
  uni.hideLoading()
  uni.showToast({
    title: '录入成功',
    icon: 'success',
    duration: 2000
  })
  wordsStr.value = ''
  pageType.value = 'init'
}

const onGenerate = async () => {
  uni.showLoading({
    title: '生成中'
  })
  let { data } = await workflowApi("7454952481517748243", {})
  const list = JSON.parse(data.output)
  console.log(list);
  console.log(list);
  console.log(list);
  console.log(list);
  uni.hideLoading()
  uni.showToast({
    title: '生成成功',
    icon: 'success',
    duration: 2000
  })
  sList.value = list
  pageType.value = 'list'
}

const updateList = []
const bindPickerChange = (e, index) => {
  bills.value[index].index = e.detail.value
  bills.value[index].categoryId = categories.value[e.detail.value].categoryId
  updateList.push({
    ...bills.value[index],
    categoryName: categories.value[e.detail.value].categoryName
  })
}
// 保存到数据库
const save = async () => {
  const bill = bills.value.map((item) => {
    return {
      zcjKey: item.zcjKey,
      business_type: "Expense",
      account: { "id": "595573797774950481" },
      category: { "id": item.categoryId },
      amount: item.amount,
      remark: item.remark,
      transaction_time: dayjs(item.transaction_time).valueOf(),
      member: { "id": "912140037714137089" },
    }
  })

  const params = {
    bills: bill,
    httpParams: {
      url: "https://yun.feidee.net/cab-accounting-ws/v2/account-book/transaction/expense",
      method: "post",

    },
  }
  const { data } = await workflowApi('7452980046622752780', params)
  const failNum = data.failKeys.length
  if (failNum === 0) {
    uni.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 2000
    })
  } else {
    uni.showToast({
      title: `总共${failNum}条保存失败`,
      icon: 'none',
      duration: 2000
    })
  }
  bills.value = bills.value.filter((item) => {
    return data.failKeys.indexOf(item.zcjKey) !== -1
  })
  if (updateList.length > 0) updateKnowledge()
}
// 更新知识库
const updateKnowledge = async () => {
  const knowledge = updateList.map(e => {
    return {
      type: e.categoryName,
      name: e.product_name
    }
  })
  const params = {
    knowledge,
    isDelete: false,
  }
  console.log(params);
  const res = await workflowApi('7452980098364227638', params)
  console.log(res);
  console.log(res);
  console.log(res);
  console.log(res);

}

</script>
<style scoped>
.grid-table {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
}

.header {
  font-weight: bold;
}

.row {
  display: contents;
}

.uni-input {
  padding: 5px;
  border: 1px solid #ccc;
}
</style>
# 普通话训练页面数据源重构部署说明

## 重构概述

本次重构将普通话训练页面从传统接口请求改为云数据库存储方案，实现了：

- ✅ 云数据库统一数据管理
- ✅ 动态分类生成机制
- ✅ 内容随机选择逻辑
- ✅ 完善的错误处理
- ✅ 完全依赖云数据库，移除传统API

## 部署步骤

### 1. 云数据库配置

#### 1.1 创建数据表
在 uniCloud 控制台创建 `putonghua` 表，包含以下字段：

```javascript
{
  _id: String,           // 主键
  character: String,     // 汉字
  category: String,      // 分类（翘舌音、平舌音、前鼻音、后鼻音等）
  words: Array,          // 词语数组
  createTime: Date,      // 创建时间
  updateTime: Date       // 更新时间
}
```

#### 1.2 数据结构示例
```javascript
{
  "_id": "putonghua_001",
  "character": "这",
  "category": "翘舌音",
  "words": [
    {
      "text": "这个",
      "pinyin": "zhè ge",
      "sentences": [
        { "text": "这是一个美好的日子。", "pinyin": "zhè shì yí ge měi hǎo de rì zi." },
        { "text": "这个想法很不错。", "pinyin": "zhè ge xiǎng fǎ hěn bù cuò." }
      ]
    }
  ],
  "createTime": "2025-01-01T00:00:00.000Z",
  "updateTime": "2025-01-01T00:00:00.000Z"
}
```

#### 1.3 导入示例数据
使用 `.docs/语音训练/普通话训练示例数据.json` 中的数据导入到云数据库。

### 2. 云函数部署

#### 2.1 上传云函数
确保 `uniCloud-aliyun/cloudfunctions/speak/index.obj.js` 已包含 `getPutonghuaData` 方法。

#### 2.2 云函数权限配置
在 uniCloud 控制台配置云函数访问权限，确保可以读取 `putonghua` 表。

### 3. 前端代码部署

前端代码已完成重构，主要变更：

- 添加了云对象调用逻辑
- 实现了动态分类生成
- 添加了本地缓存机制
- 完善了错误处理

## 功能特性

### 1. 数据加载机制

- **实时加载**：每次页面加载时从云数据库获取最新数据
- **内存缓存**：页面期间数据常驻内存，提升操作响应速度
- **错误处理**：网络异常时提供友好提示和重试功能

### 2. 动态分类生成

- 自动从云数据库数据中收集 `category` 字段
- 去重并按汉字排序生成分类列表
- 支持任意分类名称，无需硬编码

### 3. 内容随机选择

- 点击汉字时从对应数据中随机选择词语
- 随机选择该词语的句子进行展示
- 每次点击都会产生不同的内容组合

### 4. 错误处理

- 网络异常时自动使用本地缓存
- 数据加载失败时提供重试功能
- 友好的错误提示和状态显示

## 使用说明

### 1. 管理员操作

#### 添加新汉字数据
在 uniCloud 控制台的 `putonghua` 表中添加新记录：

```javascript
{
  "character": "新字",
  "category": "分类名",
  "words": [
    {
      "text": "词语",
      "pinyin": "cí yǔ",
      "sentences": [
        { "text": "句子内容", "pinyin": "jù zi nèi róng" }
      ]
    }
  ]
}
```

#### 修改分类
直接修改数据记录的 `category` 字段，前端会自动更新分类显示。

### 2. 用户体验

#### 首次使用
- 页面加载时会显示"正在加载训练数据..."
- 加载完成后自动生成字库分类
- 选择汉字开始训练

#### 数据刷新
- 点击重新生成按钮可随机选择新内容
- 数据加载失败时可点击重试按钮重新从云数据库获取

## 性能优化

### 1. 数据管理策略
- 内存缓存：页面期间数据常驻内存，避免重复请求
- 实时数据：确保用户始终获取最新的训练内容

### 2. 加载优化
- 一次性加载所有数据，后续操作无网络延迟
- 异步加载，不阻塞页面渲染
- 加载状态提示，提升用户体验

## 故障排除

### 1. 数据加载失败
- 检查云函数是否正常部署
- 确认云数据库表是否存在
- 检查网络连接状态

### 2. 分类显示异常
- 确认数据中 `category` 字段格式正确
- 检查是否有空值或特殊字符

### 3. 内容显示问题
- 确认 `words` 数组结构正确
- 检查 `sentences` 数组是否为空

## 后续扩展

### 1. 数据管理后台
可开发专门的数据管理界面，方便批量添加和编辑训练数据。

### 2. 个性化推荐
基于用户练习记录，推荐需要重点练习的汉字。

### 3. 数据统计
添加练习数据统计，分析用户的学习进度和薄弱环节。

## 技术支持

如遇到问题，请检查：
1. uniCloud 服务状态
2. 云函数日志
3. 浏览器控制台错误信息
4. 网络连接状态

## 重构变更总结

### 移除的功能
- ❌ 本地存储缓存机制
- ❌ 离线数据支持
- ❌ 传统API回退逻辑

### 简化的架构
- ✅ 完全依赖云数据库
- ✅ 实时数据获取
- ✅ 简化的错误处理
- ✅ 更直接的数据流

重构完成后，普通话训练功能将更加简洁、可靠，完全基于云数据库提供稳定的学习体验。

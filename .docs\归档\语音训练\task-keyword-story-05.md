# 任务：实现从记录恢复训练场景

- **任务ID**: `task-keyword-story-05`
- **所属功能模块**: 语音训练 - 关键词讲故事
- **优先级**: 中
- **状态**: 未开始
- **依赖关系**: `task-keyword-story-04`

---

## 1. 任务描述
当用户在记录弹窗中选择一条记录后，需要将该记录的内容恢复到主页面上，让用户可以查看当时的关键词和AI反馈，并能在此基础上开始新的练习。

## 2. 技术实现详情
### 修改事件处理器
-   在 `keyword-story-page.vue` 中，修改 `handleSelectRecord` 函数（在任务2中已创建）。
    ```javascript
    const handleSelectRecord = (record) => {
      if (!record || !record.content) {
        uni.showToast({ title: '无效的记录', icon: 'none' });
        return;
      }

      try {
        const trainingData = JSON.parse(record.content);

        // 恢复页面状态
        keywords.value = trainingData.keywords;
        evaluationHistory.value = [trainingData.evaluation]; // 用选中的记录覆盖当前历史
        currentEvaluationId.value = trainingData.evaluation.id; // 设置当前选中ID

        // 清理输入框和音频状态
        userStory.value = trainingData.evaluation.storyText;
        inputMessage.value = '';
        currentAudioURL.value = trainingData.evaluation.audioURL || '';

        // 关闭弹窗
        showRecordPopup.value = false;

        uni.showToast({ title: '记录已恢复', icon: 'none' });
      } catch (error) {
        console.error('恢复记录失败:', error);
        uni.showToast({ title: '恢复失败，记录已损坏', icon: 'none' });
      }
    };
    ```

### 逻辑说明
-   **解析数据**: 从 `record.content` 中解析出 `TrainingRecord` 对象。
-   **恢复关键词**: 将 `trainingData.keywords` 赋值给页面的 `keywords` ref。
-   **恢复评价**: 将 `trainingData.evaluation` 作为一个**单项数组**赋值给 `evaluationHistory`。这很重要，因为它将AI反馈区更新为所选记录的内容，同时清除了之前的临时历史。
-   **设置当前ID**: 更新 `currentEvaluationId` 以确保 `computed` 属性 `currentEvaluation` 正确指向恢复的这条记录。
-   **清理状态**: 清空输入框，并根据记录中是否有音频URL来更新播放器状态。

## 3. 验收标准
-   [ ] `handleSelectRecord` 函数被更新为包含完整的恢复逻辑。
-   [ ] 在弹窗中选择一条记录后，弹窗关闭。
-   [ ] 主页面的关键词区域更新为所选记录的关键词。
-   [ ] 主页面的AI反馈区域更新为所选记录的评价内容。
-   [ ] 如果记录包含音频，音频播放器会显示并加载对应的音频。
-   [ ] 可以在恢复的场景下，使用相同的关键词开始一次新的复述，所有功能（如提交、评价、保存新记录）均正常。
-   [ ] 如果选择的记录 `content` 字段为空或JSON解析失败，会给出错误提示。 
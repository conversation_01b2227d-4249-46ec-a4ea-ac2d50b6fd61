# 任务：增强日记编辑器开发

- **所属功能模块**: `diary-entry`
- **任务ID**: `task-diary-entry-03`
- **优先级**: 中

## 任务描述
基于现有的 `z-editor` 组件，扩展其功能，创建一个新的全局组件 `z-diary-editor`，以满足日记功能的多样化输入需求。

## 技术实现详情
1.  **`z-diary-editor` 组件**:
    - 从 `z-editor` 派生或重构，保留其核心富文本编辑能力。
    - **新增功能**:
        - **类型标记**: 提供一组按钮或下拉菜单，允许用户标记当前内容的类型（如"思考"、"记事"、"感悟"）。
        - **多媒体支持**:
            - 集成图片上传功能。
            - 集成语音录制和输入功能。
        - **交互**:
            - 通常以弹窗或底部抽屉的形式出现。
            - 提供"保存"和"取消"按钮。
            - 保存时，通过 `emit` 事件将包含 `content`、`type`、`media_attachments` 的完整日记条目对象返回给父组件。

## 验收标准
- `z-diary-editor` 组件能被正常唤起和关闭。
- 用户可以输入和编辑富文本。
- 用户可以为日记条目标记类型。
- 用户可以成功上传图片和录入语音。
- 保存操作能正确返回一个结构化的日记条目对象。

## 依赖关系
- 无。可独立于其他UI组件先行开发。

## 状态追踪
- 未开始 
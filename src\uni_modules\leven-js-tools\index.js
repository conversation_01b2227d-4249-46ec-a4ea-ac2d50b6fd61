/**
 * 主管理容器
 */
// 数组管理器
import {
  ArrayManager
} from "./js_sdk/array/index.js"
// 加解密管理类
import {
  EncryptManager
} from "./js_sdk/encrypt/index.js"
// 数字管理类
import {
  NumberManager
} from "./js_sdk/number/index.js"
// 对象管理类
import {
  ObjectManager
} from "./js_sdk/object/index.js"
// 请求管理类
import {
  RequestManager
} from "./js_sdk/request/index.js"
// 字符串管理
import {
  StringManager
} from "./js_sdk/string/index.js"
// 验证管理
import {
  ValidateManager
} from "./js_sdk/validate/index.js"
// 定时任务管理
import {
  TimerManager
} from "./js_sdk/timer/index.js"
// 日期管理
import {
  DateManager
} from "./js_sdk/date/index.js"
// 颜色相关
import {
  ColorManager
} from "./js_sdk/color/index.js"
// 函数
import {
  FuncManager
} from "./js_sdk/func/funcManager.js"

// 数组管理
let arrayManager = new ArrayManager();
// 加解密管理
let encryptManager = new EncryptManager();
// 数字管理
let numberManager = new NumberManager();
// 对象管理类
let objectManager = new ObjectManager();
// 请求管理类
let requestManager = new RequestManager();
// 字符串管理
let stringManager = new StringManager();
// 验证管理
let validateManager = new ValidateManager();
// 定时任务管理
let timerManager = new TimerManager();
// 日期管理
let dateManager = new DateManager();
// 颜色相关
let colorManager = new ColorManager();
// 函数相关
let funcManager = new FuncManager();

const $lv = {
  array: arrayManager,
  encrypt: encryptManager,
  number: numberManager,
  object: objectManager,
  request: requestManager.request(),
  string: stringManager,
  validate: validateManager,
  timer: timerManager,
  date: dateManager,
  color: colorManager,
  func: funcManager
}

// 挂载到uni对象
uni.$lv = $lv;

const install = (Vue) => {
  Vue.prototype.$lv = $lv;
}

export default {
  install
}
# 关键词讲故事 - 复述记录功能需求文档

## 1. 背景
“关键词讲故事”是帮助用户提升创意表达和叙事能力的核心功能。当前版本中，用户的练习历史（包括使用的关键词和多次复述尝试）会在点击“重置”按钮后被清空，无法找回。这导致用户难以回顾和追踪自己长期的进步情况，也无法重新访问之前的练习素材进行巩固。

为了提升用户体验，满足长期训练和复盘的需求，我们计划引入“复述记录”功能。该功能允许用户将每一次**成功的练习（即获得AI反馈）**保存下来，形成一个可供回顾和恢复的训练档案。

## 2. 需求
### 2.1 功能需求
1.  **自动保存训练记录**：当AI对用户的复述**成功生成评价**后，系统应自动将当前这次练习（包含关键词和当次AI反馈）保存为一个独立的“复述记录”。
    - **保存触发条件**：`evaluateKeywordStory` 接口成功返回评价结果。
    - **保存内容**：一次独立的练习记录，包含使用的关键词和该次完整的 `evaluation` 对象。
    - **重置逻辑变更**：页面上的“重置”按钮仅用于清空当前训练状态，**不再触发**保存操作。
2.  **访问复述记录**：在页面上提供一个清晰的入口（如导航栏上的“复述记录”按钮），让用户可以方便地打开记录列表。
3.  **记录列表展示**：
    - 以弹窗或新页面的形式展示所有已保存的“复述记录”。
    - 列表应按**创建时间倒序**排列，最新的记录在最上方。
    - 每个列表项应清晰地展示核心信息，包括：
        - 训练日期
        - 使用的关键词（可从 `title` 字段获取）
        - 总体评价的摘要（可从 `content` JSON中获取）
4.  **恢复训练会话**：用户点击列表中的任意一条记录后，系统需要能够：
    - **恢复训练场景**：将所选记录的关键词填充到页面上。
    - **展示历史反馈**：将所选记录的评价详情展示在AI反馈区域。
    - 用户可以在此基础上，使用相同的关键词开始一次**新的**复述练习。
5.  **数据持久化**：所有“复述记录”需要使用 `chatRecord` 数据库表进行持久化存储。

### 2.2 非功能需求
1.  **性能**：保存和恢复操作应流畅，不影响用户体验。
2.  **易用性**：访问、查看和恢复记录的交互流程应直观、简单。
3.  **数据隔离**：保存到 `chatRecord` 表中的记录需要有明确的标识，以区别于其他聊天数据。

## 3. 技术方案
### 3.1 实现思路
我们将采用项目的 `chatRecord` 数据库表来持久化保存复述记录。整体流程将围绕 `evaluateKeywordStory` 接口的调用展开，在成功获取评价后，将记录写入数据库。

### 3.2 数据结构
**使用 `chatRecord` 表存储，单条记录结构如下：**
-   **`title`**: `string` - 用于存储当次训练的关键词，以逗号分隔。例如: `"创意, 宇宙, 冒险"`。
-   **`content`**: `string` - 存储一个JSON序列化后的 `TrainingRecord` 对象。
-   其他字段如 `createTime` 等将由数据库系统自动填充。

**`TrainingRecord` 对象 (存储在 `content` 字段中)**
```typescript
interface TrainingRecord {
  type: 'keywordStory'; // 用于区分其他聊天记录的标识
  keywords: string[]; // 当次训练使用的关键词
  evaluation: Evaluation; // 当次训练的完整评价对象
}

// Evaluation 类型 (沿用现有结构)
interface Evaluation {
  id: string;
  attempt: number;
  storyText: string;
  // ... 其他字段
}
```

### 3.3 架构流程
```mermaid
graph TD
    subgraph "关键词讲故事页面"
        A(用户提交故事) --> B(调用 aip/evaluateKeywordStory);
        B -- 成功返回评价 --> C[构建 TrainingRecord 对象];
        C --> D[将对象 JSON 序列化];
        D --> E[调用数据库接口];
        E --> F[将记录写入 chatRecord 表];
        
        subgraph "记录恢复流程"
            H(用户点击"复述记录") --> I[显示记录列表UI];
            I -- 读取 chatRecord 表 --> J[展示记录列表];
            J -- 用户选择一条记录 --> K[解析 content 字段];
            K --> L[恢复页面状态: 填充关键词和AI反馈];
        end
    end
```

### 3.4 核心实现步骤

1.  **修改评价处理逻辑**:
    - 在 `src/pages/speak/keyword-story-page.vue` 中，找到调用 `createEvaluation` 并获得成功返回的位置（`handleTextSubmit` 和 `handleTranscriptionEnd` 内部）。
    - 在成功创建 `newEvaluation` 对象后，调用一个新的函数 `saveTrainingRecord(keywords.value, newEvaluation)`。
2.  **实现 `saveTrainingRecord` 函数**:
    - 在 `keyword-story-page.vue` 中创建此函数。
    - 函数内部：
        - 构建一个 `TrainingRecord` 对象，包含 `type: 'keywordStory'`、关键词和评价数据。
        - 调用数据库API（例如 `import { db } from '@/api/database';`）来插入数据。
        - `db.add('chatRecord', { title: keywords.join(', '), content: JSON.stringify(record) })`
3.  **创建/修改“复述记录”列表组件 (`l-record-list-popup.vue`)**:
    - 在 `src/pages/speak/components/` 目录下创建或修改该组件。
    - 组件显示时，从 `chatRecord` 表中读取数据。
    - **查询逻辑**：需要一种方式来过滤出关键词故事的记录。可以在 `content` 字段中搜索 `"type":"keywordStory"`，或者如果数据库支持，直接查询JSON内容。
        - 示例查询: `db.getList('chatRecord', { where: "content LIKE '%\"type\":\"keywordStory\"%'" })`
    - 列表项绑定点击事件，并通过 `emit` 将该项的 `content` 内容（解析后的对象）传递给父组件。
4.  **实现 `handleRestoreRecord` 函数**:
    - 在 `keyword-story-page.vue` 中创建或修改此函数。
    - 该函数接收 `TrainingRecord` 对象。
    - **恢复逻辑**:
        - `keywords.value = record.keywords`
        - `evaluationHistory.value = [record.evaluation]` // 用单条记录覆盖历史
        - `currentEvaluationId.value = record.evaluation.id`
    - 关闭记录列表弹窗。

## 4. 风险评估
- **查询性能**：在 `chatRecord` 表中通过 `LIKE` 查询 `content` 字段可能会有效率问题，尤其是在记录数增多时。
    - **应对策略**：如果性能下降，未来可以考虑在 `chatRecord` 表中增加一个 `type` 或 `tag` 字段，并建立索引，以实现高效查询。
- **数据结构变更**：未来如果 `Evaluation` 的数据结构发生变化，可能导致旧的记录无法正确解析。
    - **应对策略**：在解析 `content` 时，可以增加一个版本号字段或进行数据迁移处理，以确保向后兼容性。 
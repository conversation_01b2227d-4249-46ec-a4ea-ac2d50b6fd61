<template>
  <view class="pages">
    <unicloud-db
      ref="udbRef"
      v-slot="{ data, loading, error, options }"
      loadtime="manual"
      where="user_id == $cloudEnv_uid"
      :page-size="100"
      orderby="create_date desc"
      collection="memos,memos-label"
    >
      <view v-if="error">{{ error.message }}</view>
      <view v-else>
        <view v-for="(item, index) in data" :key="index" class="memo-box" @click="onOption({ ...item })">
          <uni-dateformat class="memo-time" :date="item.create_date" format="yyyy-MM-dd hh:mm:ss"></uni-dateformat>
          <view class="whitespace-pre-wrap" v-html="item.content" />
          <image v-for="(img, index) in item.photo" class="memo-image" :src="img.url" mode="aspectFit" />
          <view v-if="item.label_id[0]" class="memo-label"> #{{ item.label_id[0].title }} </view>
        </view>
      </view>
    </unicloud-db>
  </view>
  <uni-popup ref="popup" @maskClick="closeP">
    <view class="memo-label-list">
      <view
        v-for="(item, index) in labelList"
        :key="index"
        class="memo-label-item"
        :class="{ active: curMemo.label_id === item._id }"
        @click="onSelectLabel(item)"
        >{{ item.title }}</view
      >
      <view class="memo-label-item" @click="onNewLabel('show')">添加标签</view>
    </view>
    <view class="popup-new">
      <textarea
        v-model="editData"
        placeholder="说点什么呢..."
        :maxlength="-1"
        class="textarea"
        :show-confirm-bar="false"
        auto-focus
        auto-height
        @input="onSaveDraft"
        @blur="bindTextAreaBlur"
      />
      <template v-show="showPooo">
        <uni-file-picker
          v-model="curMemo.photo"
          style="margin-top: 10px"
          file-mediatype="image"
          mode="grid"
          @select="onSelectImg"
          @progress="onProgressImg"
          @success="onSuccessImg"
          @fail="fail"
        />
      </template>
      <view class="new-memo-option">
        <uni-icons type="cloud-upload-filled" size="25" @click="showPooo = true"></uni-icons>
        <view class="save-btn" @click="onSaveMemo">
          <uni-icons type="paperplane-filled" color="#fff" size="20"></uni-icons>
        </view>
      </view>
    </view>
  </uni-popup>
  <view class="new-memo" @click="onShowEdit"></view>
  <uni-popup ref="optionRef" @maskClick="closeP">
    <view class="option-box">
      <view class="option-item" @click="edit"> 编辑 </view>
      <view class="option-item" @click="del"> 删除 </view>
    </view>
  </uni-popup>
  <uni-popup ref="labelRef" type="dialog">
    <uni-popup-dialog
      title="添加标签"
      mode="input"
      message="成功消息"
      :duration="2000"
      :before-close="true"
      @close="labelRef.close()"
      @confirm="onNewLabel"
    ></uni-popup-dialog>
  </uni-popup>
</template>
<script setup>
import { reactive, ref, onMounted, computed } from 'vue'
import { onPullDownRefresh } from '@dcloudio/uni-app'
const udbRef = ref()
import statusBar from '@/uni_modules/uni-nav-bar/components/uni-nav-bar/uni-status-bar'

import Gps from '@/uni_modules/json-gps/js_sdk/gps.js'
const gps = new Gps(),
  db = uniCloud.database()
const popup = ref()
const showPooo = ref(false)
const labelRef = ref()
const optionRef = ref()
const editorCtx = ref()
const curMemo = ref({})
const editData = ref('')
const onOption = (item) => {
  console.log(item)
  curMemo.value = {
    ...item,
    label_id: item.label_id[0],
  }
  editData.value = item.content
  optionRef.value.open('bottom')
}
const onSelectImg = (e) => {
  console.log(e)
}
const onProgressImg = (e) => {
  console.log(e)
}
const onSuccessImg = (e) => {
  console.log(e)
}

const onSaveDraft = (e) => {
  console.log(curMemo.value)
  const { content, _id } = curMemo.value
  if (_id) {
    uni.setStorageSync(`draft:memo_${_id}`, e.detail.value)
  } else {
    uni.setStorageSync(`draft:memo`, e.detail.value)
  }
}

const closeP = () => {
  optionRef.value.close()
  popup.value.close()
  curMemo.value = {}
  editData.value = ''
}
// 添加标签
const onNewLabel = (value) => {
  if (value === 'show') {
    labelRef.value.open()
  } else {
    uni.showLoading()
    db.collection('memos-label')
      .add({
        title: value,
      })
      .then((res) => {
        uni.hideLoading()
        console.log(res)
        getLabelList()
        labelRef.value.close()
      })
  }
}
const labelList = ref([])
const getLabelList = async () => {
  const { result } = await db.collection('memos-label').where(`user_id==$cloudEnv_uid`).get()
  labelList.value = result.data || []
}
const onSelectLabel = (item) => {
  curMemo.value.label_id = item._id
}
// 删除
const del = async () => {
  uni.showLoading({
    title: '删除中',
  })
  await db.collection('memos').where(`_id == '${curMemo.value._id}'`).remove()
  optionRef.value.close()
  uni.hideLoading()
  uni.startPullDownRefresh()
}
// 编辑
const edit = () => {
  popup.value.open('bottom')
  optionRef.value.close()
  const draft = uni.getStorageSync(`draft:memo_${curMemo.value._id}`)
  if (draft) {
    uni.showModal({
      title: '草稿',
      content: '你有之前尚未保存的草稿，是否恢复？',
      success(val) {
        if (val.confirm) {
          editData.value = draft
        } else if (val.cancel) {
          uni.removeStorageSync(`draft:memo_${curMemo.value._id}`)
        }
      },
    })
  }
}
const onShowEdit = () => {
  popup.value.open('bottom')
  const draft = uni.getStorageSync(`draft:memo`)
  console.log(draft)
  if (draft) {
    editData.value = draft
  }
}
// 保存 memo
const onSaveMemo = () => {
  uni.showLoading({
    title: '提交中',
  })
  const { content, _id, label_id, photo } = curMemo.value
  if (content) {
    db.collection('memos')
      .where(`_id == '${_id}'`)
      .update({
        content: editData.value,
        label_id,
        photo: photo || [],
      })
      .then((res) => {
        uni.hideLoading()
        // res 为数据库查询结果
        uni.startPullDownRefresh()
        popup.value.close()
        uni.removeStorageSync(`draft:memo_${_id}`)
      })
  } else {
    db.collection('memos')
      .add({
        content: editData.value,
        label_id,
        photo: photo || [],
      })
      .then((res) => {
        // res 为数据库查询结果
        uni.hideLoading()
        uni.startPullDownRefresh()
        popup.value.close()
        uni.removeStorageSync('draft:memo')
      })
      .catch((err) => {
        console.log(err.message)
      })
  }
}

onMounted(() => {
  uni.startPullDownRefresh({})
})
// 刷新
onPullDownRefresh(() => {
  curMemo.value = {}
  editData.value = ''
  getLabelList()
  udbRef.value.loadData(
    {
      clear: true,
    },
    () => {
      uni.stopPullDownRefresh()
    }
  )
})
</script>

<style lang="scss" scoped>
/* #ifndef APP-NVUE */
view {
  display: flex;
  box-sizing: border-box;
  flex-direction: column;
}

/* #endif */
.pages {
  background-color: #ffffff;
}

.avatar {
  width: 200rpx;
  height: 200rpx;
  margin-right: 10rpx;
}

.main {
  justify-content: space-between;
  flex: 1;
}

.title {
  font-size: 16px;
}

.info {
  flex-direction: row;
  justify-content: space-between;
}

.author,
.last_modify_date {
  font-size: 14px;
  color: #999999;
}

.uni-search-box {
  background-color: #ffffff;
  position: sticky;
  height: 50px;
  top: 0;
  left: 0;
  /* #ifndef APP-PLUS */
  z-index: 9;
  /* #endif */
  /* #ifdef MP-WEIXIN */
  width: 580rpx;
  /* #endif */
}

.popup-new {
  /* #ifdef H5 */
  margin-bottom: 50px;
  /* #endif */
  background-color: #fff;
  padding: 10px;

  // position: relative; `
  .textarea {
    width: 100%;
    // padding-bottom: 40px;
  }

  .new-memo-option {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    flex-direction: row;

    .save-btn {
      width: 48px;
      height: 24px;
      border-radius: 4px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      background-color: $uni-color-success;
      margin-left: auto;
    }
  }
}

.container {
  padding: 10px;
}

#editor {
  width: 100%;
  height: 10px;
  background-color: #eee;
}

button {
  margin-top: 10px;
}

.new-memo {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: aquamarine;
}

.memo-box {
  margin: 10px;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #e0e0e0;

  .memo-image {
    width: 50px;
    height: 50px;
    margin-top: 10px;
  }

  .memo-time {
    font-size: 12px;
    margin-bottom: 5px;
  }

  .memo-label {
    margin-top: 4px;
    font-size: 12px;
    color: skyblue;
  }
}

.option-box {
  /* #ifdef H5 */
  margin-bottom: 50px;

  /* #endif */
  .option-item {
    background-color: #fff;
    line-height: 50px;
    text-align: center;
  }
}

.memo-label-list {
  display: flex;
  flex-direction: row-reverse;
  flex-wrap: wrap-reverse;

  .memo-label-item {
    background-color: #fff;
    border-radius: 10px;
    margin: 6px;
    padding: 4px 8px;
    font-size: 14px;

    &.active {
      background-color: aquamarine;
      color: #fff;
    }
  }
}
</style>

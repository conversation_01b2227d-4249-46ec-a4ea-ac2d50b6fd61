export class AppDB {
  private DBName: string
  private DBPath: string
  constructor(dataConfig: any, tableSchema: any) {
    this.DBName = dataConfig.databaseName
    this.DBPath = `_doc/${this.DBName}.db`
    this.init(tableSchema)
  }

  /** 初始化
   *  TODO 初始化不一定每次都需要，后面封装到升级模块里去
   */
  private async init(tableSchema: any) {
    try {
      // 打开数据库
      await this.openDB()
      // 创建表
      const sqlArr = Object.keys(tableSchema).map((tbName) => {
        // 遍历当前表对象的所有键（字段名），拼接成创建表的 sql 语句
        const fields = tableSchema[tbName]
        const fieldArr = this.buildFieldArr(fields)
        const sql = `create table if not exists ${tbName} (${fieldArr.join(', ')})`
        return sql
      })
      // 创建表
      const res = await this.executeSQL(sqlArr)
      // console.log('创建表：', JSON.stringify(res), JSON.stringify(sqlArr))
    } catch (error) {
      console.error('Database initialization failed:', error)
    }
  }
  private buildFieldArr(fields: any) {
    return Object.keys(fields).map((field) => {
      const { aType, primaryKey, defaultVal } = fields[field]
      let str = `"${field}" ${aType}`
      if (primaryKey) str += ` PRIMARY KEY`
      if (defaultVal) str += ` DEFAULT ${defaultVal}`
      return str
    })
  }

  /** 打开数据库 */
  openDB() {
    return new Promise((resolve, reject) => {
      if (!this.isOpenDatabase()) {
        plus.sqlite.openDatabase({
          name: this.DBName,
          path: this.DBPath,
          success(res) {
            console.log('openDatabase success!', res)
            resolve(res)
          },
          fail(e) {
            console.log('openDatabase failed: ' + JSON.stringify(e))
            reject(e)
          },
        })
      } else {
        console.log('Database already open!')
        resolve({
          isOk: true,
          msg: 'Database already open!',
        })
      }
    })
  }

  /** 关闭数据库 */
  closeDatabase() {
    return new Promise((resolve, reject) => {
      if (this.isOpenDatabase()) {
        plus.sqlite.closeDatabase({
          name: this.DBName,
          success(res) {
            console.log('closeDatabase success!', res)
            resolve(res)
          },
          fail(e) {
            console.log('closeDatabase failed: ' + JSON.stringify(e))
            reject(e)
          },
        })
      } else {
        resolve({
          isOk: true,
          msg: '已关闭',
        })
      }
    })
  }

  /**
   * 执行查寻 sql 语句
   * @param {String} sql
   */
  selectSQL(sql: string) {
    return new Promise((resolve, reject) => {
      plus.sqlite.selectSql({
        name: this.DBName,
        sql,
        success(data) {
          resolve(data)
        },
        fail(e) {
          reject(e) //回失败调
        },
      })
    })
  }

  /**
   * 执行 sql 语句
   * @param {String|Array} sql 语句或者语句组，支持批量，但是每次批量不要超过 500，超过 500 的自己拆分多次
   * @param {Bool} useTran 是否使用事务
   */
  executeSQL(sql: string | Array<string>, useTran = false) {
    //执行语句
    const _this = this
    if (useTran) {
      _this
        .transaction('begin')
        .then(() => {
          return this._exec(sql)
        })
        .then((sqldata) => {
          return this.transaction('commit')
        })
        .catch((err) => {
          return this.transaction('rollback')
        })
    } else {
      return this._exec(sql)
    }
  }

  /**
   * 删除表
   * @param {String} 表名
   */
  dropTable(tbname: string) {
    var sql = `drop table if exists  ${tbname}`
    return this.executeSQL(sql)
  }
  /**
   * 清空表
   * @param {String} 表名
   */
  clearTable(tbname: string) {
    var sql = `DELETE FROM ${tbname}`
    return this.executeSQL(sql)
  }
  /** 检查数据库是否打开 */
  isOpenDatabase() {
    // 检查数据库是否打开
    return plus.sqlite.isOpenDatabase({
      name: this.DBName,
      path: this.DBPath,
    })
  }
  /**
   * 执行 sql
   * @param {String|Array} sql 语句或者语句组，支持批量，但是每次批量不要超过 500，超过 500 的自己拆分多次
   */
  private _exec(sql: string | Array<string>): Promise<any> {
    return new Promise((resolve, reject) => {
      plus.sqlite.executeSql({
        name: this.DBName,
        sql,
        success(e: any) {
          resolve(e) // 成功回调
        },
        fail(e) {
          reject(e) // 失败回调  TODO 查看文档错误信息有哪些？做相应处理
        },
      })
    })
  }
  /**
   * 事务状态控制
   * @param {Object} operation  需要执行的事务操作 begin（开始事务）、commit（提交）、rollback（回滚）。
   */
  transaction(operation: 'begin' | 'commit' | 'rollback') {
    return new Promise((resolve, reject) => {
      plus.sqlite.transaction({
        name: this.DBName,
        operation,
        success(e: unknown) {
          resolve(e) // 成功回调
        },
        fail(e: any) {
          reject(e) // 失败回调
        },
      })
    })
  }
}

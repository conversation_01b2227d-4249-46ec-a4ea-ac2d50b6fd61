/**
 * 项目管理模块
 * 处理项目相关功能
 */

const {
	DIDA_API_BASE,
	DIDA_PROJECT_APIS,
	REQUEST_CONFIG,
	buildDidaApiUrl,
} = require("../config");

const {
	logInfo,
	logError,
	createSuccessResponse,
	createErrorResponse,
	validateRequired,
	validateType,
	buildAuthHeaders,
	buildAuthCookies,
	safeJsonParse,
} = require("../utils");

/**
 * 获取所有项目列表
 * @param {string} authToken - 认证令牌
 * @param {string} csrfToken - CSRF令牌
 * @returns {object} 项目列表响应
 */
async function getProjects(authToken, csrfToken) {
	const methodName = "getProjects";
	logInfo(methodName, "开始获取项目列表");

	try {
		// 参数验证
		const authValidation = validateRequired(authToken, "authToken");
		if (authValidation) return authValidation;

		const csrfValidation = validateRequired(csrfToken, "csrfToken");
		if (csrfValidation) return csrfValidation;

		// 构建请求URL
		const apiUrl = buildDidaApiUrl(DIDA_PROJECT_APIS.get_projects);

		// 构建请求头和cookies
		const headers = buildAuthHeaders(authToken, csrfToken);
		const cookies = buildAuthCookies(authToken, csrfToken);

		logInfo(methodName, "发起获取项目列表请求", { apiUrl });

		// 发起请求
		const response = await uniCloud.httpclient.request(apiUrl, {
			method: "GET",
			headers: headers,
			cookies: cookies,
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`获取项目列表失败，状态码: ${response.status}`);
		}

		// 解析响应数据
		const responseData = safeJsonParse(response.data, null);

		if (!responseData) {
			throw new Error("无法解析项目列表数据");
		}

		logInfo(methodName, "成功获取项目列表", {
			projectCount: responseData.length || 0,
		});

		// 处理项目数据
		const projects = Array.isArray(responseData) ? responseData : [];
		const processedProjects = projects.map((project) => ({
			id: project.id,
			name: project.name,
			color: project.color,
			icon: project.icon,
			sort_order: project.sortOrder,
			is_inbox: project.isInbox || false,
			is_closed: project.isClosed || false,
			is_archived: project.isArchived || false,
			task_count: project.taskCount || 0,
			completed_count: project.completedCount || 0,
			created_time: project.createdTime,
			modified_time: project.modifiedTime,
			// 项目统计信息
			statistics: {
				total_tasks: project.taskCount || 0,
				completed_tasks: project.completedCount || 0,
				pending_tasks: (project.taskCount || 0) - (project.completedCount || 0),
				completion_rate:
					project.taskCount > 0
						? Math.round(
								((project.completedCount || 0) / project.taskCount) * 100
						  )
						: 0,
			},
		}));

		// 按类型分组项目
		const groupedProjects = {
			active: processedProjects.filter((p) => !p.is_closed && !p.is_archived),
			closed: processedProjects.filter((p) => p.is_closed),
			archived: processedProjects.filter((p) => p.is_archived),
			inbox: processedProjects.filter((p) => p.is_inbox),
		};

		// 计算总体统计
		const totalStatistics = {
			total_projects: processedProjects.length,
			active_projects: groupedProjects.active.length,
			closed_projects: groupedProjects.closed.length,
			archived_projects: groupedProjects.archived.length,
			total_tasks: processedProjects.reduce((sum, p) => sum + p.task_count, 0),
			total_completed: processedProjects.reduce(
				(sum, p) => sum + p.completed_count,
				0
			),
		};

		return createSuccessResponse("获取项目列表成功", {
			projects: processedProjects,
			grouped_projects: groupedProjects,
			statistics: totalStatistics,
		});
	} catch (error) {
		logError(methodName, "获取项目列表失败", error);
		throw error;
	}
}

// 导出项目管理模块函数
module.exports = {
	getProjects,
};

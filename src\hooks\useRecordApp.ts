import { ref, onUnmounted, Ref } from 'vue'
import { UseRecordOptions, UseRecordReturn, RecordResult } from './types/record'
import { RecordError } from './utils/recordErrors'

/**
 * App环境下的录音Hook实现
 * 基于uni.getRecorderManager()
 *
 * @param options 录音配置选项
 * @returns 录音控制对象
 */
export default function useRecordApp(options?: UseRecordOptions): UseRecordReturn {
  // 默认录音配置
  const defaultOptions: UseRecordOptions = {
    maxDuration: 60000, // 默认 60 秒
    disableLogs: true,
    appOptions: {
      format: 'mp3', // App默认格式
    },
  }

  const mergedOptions = { ...defaultOptions, ...options }

  // 获取录音管理器实例
  const recorderManager: any = uni.getRecorderManager()

  // 录音状态
  const isRecording = ref<boolean>(false)
  const isPaused = ref<boolean>(false)
  const duration = ref<number>(0)
  const volume = ref<number>(0)
  const recordBlob = ref<string | null>(null) // App端是文件路径
  const recordURL = ref<string>('')

  // 定时器和音量分析
  let durationTimer: any = null
  let volumeUpdateTimer: any = null
  let waveformDataArray: Uint8Array | null = null

  // --- 事件处理函数 ---
  const handleOnStart = () => {
    isRecording.value = true
    isPaused.value = false
    startTimers()
    if (!mergedOptions.disableLogs) {
      console.log('录音开始')
    }
  }

  const handleOnPause = () => {
    isPaused.value = true
    stopTimers()
    if (!mergedOptions.disableLogs) {
      console.log('录音暂停')
    }
  }

  const handleOnResume = () => {
    isPaused.value = false
    startTimers()
    if (!mergedOptions.disableLogs) {
      console.log('录音继续')
    }
  }

  const handleOnStop = (res: any) => {
    const { tempFilePath, duration: recordDuration } = res
    isRecording.value = false
    isPaused.value = false
    recordBlob.value = tempFilePath
    recordURL.value = tempFilePath
    if (recordDuration) {
      duration.value = recordDuration
    }
    stopTimers()
    if (!mergedOptions.disableLogs) {
      console.log('录音结束，文件路径：', tempFilePath)
    }
  }

  const handleOnError = (res: { errMsg: string }) => {
    const { errMsg } = res
    resetState()
    console.error('录音错误：', errMsg)
    throw RecordError.recordingFailed(errMsg)
  }
  // --- 事件处理函数结束 ---

  // 监听录音开始事件
  const setupEventListeners = () => {
    recorderManager.onStart(handleOnStart)
    recorderManager.onPause(handleOnPause)
    recorderManager.onInterruptionEnd(handleOnResume as any)
    recorderManager.onStop(handleOnStop)
    recorderManager.onError(handleOnError)
  }

  // 开始录音
  const startRecording = async (): Promise<void> => {
    try {
      // 如果已经在录音，先停止之前的录音
      if (isRecording.value) {
        await stopRecording()
      }

      // 重置状态
      resetState()

      // 确保事件监听已设置
      setupEventListeners()

      // 构建录音选项
      const recordOptions: UniApp.RecorderManagerStartOptions = {
        duration: mergedOptions.maxDuration,
        format: mergedOptions.appOptions?.format || 'mp3',
        numberOfChannels: mergedOptions.numberOfAudioChannels || 1,
        sampleRate: mergedOptions.sampleRate || 44100,
        frameSize: mergedOptions.appOptions?.frameSize,
      }

      // 开始录音
      recorderManager.start(recordOptions)

      // 模拟波形数据
      initWaveformData()
    } catch (error: any) {
      console.error('启动录音失败：', error)
      throw error instanceof RecordError ? error : RecordError.initializationFailed(error.message)
    }
  }

  // 暂停录音
  const pauseRecording = (): void => {
    if (isRecording.value && !isPaused.value) {
      recorderManager.pause()
    }
  }

  // 继续录音
  const resumeRecording = (): void => {
    if (isRecording.value && isPaused.value) {
      recorderManager.resume()
    }
  }

  // 停止录音
  const stopRecording = (): Promise<string> => {
    return new Promise((resolve, reject) => {
      if (!isRecording.value) {
        reject(RecordError.recordingFailed('没有正在进行的录音'))
        return
      }

      const onStopCallback = (res: any) => {
        const filePath = res.tempFilePath
        recordBlob.value = filePath
        recordURL.value = filePath
        resolve(filePath)
      }
      recorderManager.onStop(onStopCallback)

      // 停止录音
      recorderManager.stop()
    })
  }

  // 播放录音
  const playRecording = (): void => {
    if (recordURL.value) {
      const innerAudioContext = uni.createInnerAudioContext()
      innerAudioContext.src = recordURL.value
      innerAudioContext.autoplay = true
      innerAudioContext.onError((err) => {
        console.error('播放录音失败：', err)
      })
    }
  }

  // 初始化模拟波形数据
  const initWaveformData = (): void => {
    // 创建一个固定大小的数组用于模拟波形数据
    const size = 128
    waveformDataArray = new Uint8Array(size)

    // 初始化为静音状态
    for (let i = 0; i < size; i++) {
      waveformDataArray[i] = 128 // 静音波形中点值
    }
  }

  // 更新模拟波形数据
  const updateWaveformData = (): void => {
    if (!waveformDataArray || !isRecording.value) return

    // 根据当前音量生成随机波形数据
    const volumeValue = volume.value
    const baseAmplitude = Math.max(5, volumeValue / 2) // 确保即使在静音时也有一些波动

    for (let i = 0; i < waveformDataArray.length; i++) {
      // 添加一些随机波动，音量越大波动越大
      const randomAmplitude = Math.random() * baseAmplitude * 2 - baseAmplitude

      // 波形值范围保持在 0-255 之间，中心在 128
      waveformDataArray[i] = Math.max(0, Math.min(255, 128 + randomAmplitude))
    }
  }

  // 获取音频波形数据 (App端通过模拟生成)
  const getAudioWaveform = (): Uint8Array | null => {
    if (!waveformDataArray || !isRecording.value) return null

    // 更新波形数据
    updateWaveformData()
    return waveformDataArray
  }

  // 取消录音
  const cancelRecording = (): void => {
    if (isRecording.value) {
      // 停止录音
      recorderManager.stop()

      // 重置状态
      resetState()
    }
  }

  // 重置状态
  const resetState = (): void => {
    isRecording.value = false
    isPaused.value = false
    duration.value = 0
    volume.value = 0
    recordBlob.value = null
    recordURL.value = ''
    stopTimers()
  }

  // 开始计时器
  const startTimers = (): void => {
    // 录音时长计时器
    if (!durationTimer) {
      durationTimer = setInterval(() => {
        duration.value += 100
      }, 100)
    }

    // 模拟音量更新计时器
    if (!volumeUpdateTimer) {
      volumeUpdateTimer = setInterval(() => {
        // 生成0-100之间的随机音量值，但保持一定平滑度
        const newVolume = Math.min(100, Math.max(0, volume.value + (Math.random() * 30 - 15)))
        volume.value = Math.round(newVolume)
      }, 100)
    }
  }

  // 停止计时器
  const stopTimers = (): void => {
    if (durationTimer) {
      clearInterval(durationTimer)
      durationTimer = null
    }

    if (volumeUpdateTimer) {
      clearInterval(volumeUpdateTimer)
      volumeUpdateTimer = null
    }
  }

  // 清理资源
  const destroy = (): void => {
    // 停止录音
    if (isRecording.value) {
      cancelRecording()
    }

    // 停止计时器
    stopTimers()

    // 移除事件监听
    recorderManager.offStart(handleOnStart)
    recorderManager.offPause(handleOnPause)
    recorderManager.offInterruptionEnd(handleOnResume)
    recorderManager.offStop(handleOnStop)
    recorderManager.offError(handleOnError)
  }

  // 组件卸载时自动清理
  onUnmounted(() => {
    destroy()
  })

  return {
    // 状态
    isRecording: isRecording as Readonly<Ref<boolean>>,
    isPaused: isPaused as Readonly<Ref<boolean>>,
    duration: duration as Readonly<Ref<number>>,
    volume: volume as Readonly<Ref<number>>,
    recordBlob: recordBlob as Readonly<Ref<RecordResult | null>>,
    recordURL: recordURL as Readonly<Ref<string>>,

    // 方法
    startRecording,
    pauseRecording,
    resumeRecording,
    stopRecording,
    playRecording,
    getAudioWaveform,
    cancelRecording,
    destroy,
  }
}

<template>
  <view class="overlay" :class="{ active: show }" @click="closeColorModal"></view>
  <view class="color-picker-modal" :class="{ active: show }">
    <view class="modal-header">
      <view class="modal-title">选择颜色</view>
      <view class="modal-close" @click="closeColorModal">
        <i class="fas fa-times"></i>
      </view>
    </view>
    <view class="color-grid">
      <view
        v-for="color in colorList"
        :key="color"
        class="color-option"
        :class="{ selected: selectedColor === color }"
        :style="{ backgroundColor: color }"
        @click="selectColor(color)"
      ></view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  selectedColor: {
    type: String,
    default: '#4361ee',
  },
})

const emits = defineEmits(['update:show', 'onSubmit'])

const colorList = ref([
  // 蓝色系 - 参考海洋主题
  '#3498db',
  '#5e6ad2',
  '#607d8b',

  // 绿色系 - 参考森林和薄荷主题
  '#4caf50',
  '#009688',
  '#2ecc71',

  // 橙红色系 - 参考日落主题
  '#e77e23',
  '#d35400',
  '#e67e22',

  // 紫色系 - 参考莓果和紫晶主题
  '#c35b80',
  '#9b59b6',
  '#8d6e63',

  // 暖色系 - 参考阳光和咖啡主题
  '#D4AC6E',
  '#795548',

  // 青色系
  '#1abc9c',
  '#00bfa5',

  // 中性色
  '#546e7a',
  '#34495e',
])

// 关闭颜色选择器
const closeColorModal = () => {
  emits('update:show', false)
}

// 选择颜色
const selectColor = (color: string) => {
  emits('onSubmit', color)
  setTimeout(() => {
    closeColorModal()
  }, 300)
}
</script>
<style scoped lang="scss">
/* 颜色选择器弹窗 */
.color-picker-modal {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: var(--color-white);
  border-radius: 48rpx 48rpx 0 0;
  padding: 48rpx 40rpx;
  box-shadow: var(--shadow-lg);
  z-index: 100;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  &.active {
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 36rpx;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color-primary, var(--color-gray-800));
}

.modal-close {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background: var(--color-gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);

  i {
    font-size: 14px;
    color: var(--color-gray-600);
  }
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
    gap: 24rpx 20rpx;
  padding: 12rpx 0 28rpx;
  padding-bottom: 28rpx;
  max-width: 720rpx;
  margin: 0 auto;
}

.color-option {
  aspect-ratio: 1/1;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  position: relative;
  cursor: pointer;
  transition: var(--transition-normal);
  border: 1.5px solid rgba(var(--color-white-rgb, 255, 255, 255), 0.8);
  width: 100%;
  margin: 0 auto;

  &.selected {
    transform: scale(1.1);
    border-color: var(--color-white);
    box-shadow: 0 0 0 2px var(--color-primary);

    &:after {
      content: '✓';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: var(--color-white);
      font-weight: bold;
      font-size: 14px;
    }
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 90;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.active {
    opacity: 1;
    pointer-events: all;
  }
}

// Font Awesome Class (basic for this example)
.fas {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

// Specific icons used
.fa-times:before {
  content: '\f00d';
}
</style>

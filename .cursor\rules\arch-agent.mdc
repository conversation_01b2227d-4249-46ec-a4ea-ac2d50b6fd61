---
description: 
globs: 
alwaysApply: false
---
# 架构师规则与流程指南

## 使用场景
- 当需要将产品需求转化为技术架构设计时
- 当需要制定详细的技术实现计划时
- 当需要确保技术决策与产品愿景保持一致时
- 当开发团队需要明确的技术指导时
- 当需要将大型需求分解为多个可管理的任务文件时

## 关键规则

### 任务分解与规划
1. 将架构设计分解为可执行的工程任务。
2. 为每个任务提供明确的技术规范和验收标准。
3. 确定任务之间的依赖关系和优先级。
4. 创建合理的技术里程碑。
5. **为每个主要功能模块创建独立的任务文件**，采用 `./task-<模块名>-<序号>.md` 格式命名。
6. **创建任务总览文件**，使用 `task-summary.md` 命名，作为所有任务的概览文档。

### 任务文件生成与管理
1. **任务文件命名规范**：
   - 使用 `./task-<功能模块>-<序号>.md` 格式
   - 例如：`./task-authentication-01.md`、`./task-dashboard-01.md`
   - 序号从01开始，按功能模块递增

2. **任务文件内容结构**：
   - 任务标题和描述
   - 所属功能模块
   - 技术实现详情
   - 验收标准
   - 依赖关系
   - 优先级
   - 状态追踪

3. **任务分解原则**：
   - 每个任务应该是可独立完成的工作单元
   - 相关功能应归类到同一模块下的连续任务
   - 遵循技术依赖顺序安排任务序列
   - **前端任务应遵循先UI后数据的顺序**：先开发静态UI和交互，使用模拟数据；再对接真实数据和API。

4. **任务文件间的关联**：
   - 在每个任务文件中明确说明与其他任务的依赖关系
   - 使用任务ID进行精确引用
   - 建立任务依赖图，确保开发顺序合理

5. **任务文件位置**：
   - 任务文件应创建在相关需求文档的同级目录下
   - 例如：如果需求文档在 `.docs/功能A/需求.md`，则任务文件应位于 `.docs/功能A/task-功能A-01.md`
   - 这样确保任务文件和需求文档保持关联，方便查阅和管理

6. **前端任务开发流程顺序**：
   - 第一阶段：创建组件基础结构和模拟数据展示，确保组件能正常渲染和交互
   - 第二阶段：集成真实数据，实现数据处理和业务逻辑
   - 第三阶段：UI优化和交互完善
   - 第四阶段：测试、文档和上线准备
   - 这种顺序确保开发过程中能尽早发现UI和交互问题，避免在真实数据集成后才发现基础问题

7. **任务总览文件(task-summary.md)**：
   - 在每个功能模块的任务拆分完成后，必须创建一个任务总览文件
   - 文件应包含以下内容：
     - 需求概述：简要描述功能需求和目标
     - 任务拆分列表：列出所有任务及其主要内容
     - 任务依赖关系：使用mermaid图表展示任务间的依赖关系
     - 任务优先级：明确标注各任务的优先级
     - 开发建议：提供实施过程中的注意事项和建议
   - 该文件作为整个功能模块的开发指南和进度跟踪参考

### 技术决策与文档
1. 做出的每个技术决策都必须有明确的理由和权衡分析。
2. 记录所有重要的架构决策和设计模式选择。
3. 提供足够详细的技术文档，使开发团队能够无障碍实施。
4. 使用图表和图解说明复杂的系统交互和数据流。

### 质量保障与最佳实践
1. 确保架构设计考虑了测试策略。
2. 遵循行业标准和最佳实践。
3. 考虑代码可维护性、可读性和可扩展性。
4. 定义适当的技术债务管理策略。

## 多任务文件生成流程
1. 在架构文档获得批准后，开始任务分解过程。
2. 根据架构文档中的组件划分，确定主要功能模块。
3. 为每个功能模块创建单独的任务文件夹或文件系列。
4. 按照实现顺序和依赖关系，为每个模块创建多个连续的任务文件。
5. 创建任务总览文件(task-summary.md)，汇总所有任务信息。
6. 在每个任务文件中详细描述：
   - 实现目标和范围
   - 技术要求和规范
   - 验收标准
7. 确保任务文件之间的依赖关系清晰并保持一致性。

## 示例
<example>
### 将用户认证需求分解为多个任务文件

**需求**: 实现完整的用户认证系统，包括注册、登录、密码重置和权限管理。

**架构设计中的相关部分**:
```
// 用户数据模型
interface User {
  _id: string;
  username: string;
  email: string;
  passwordHash: string;
  role: 'admin' | 'user';
  lastLogin: string;
  createTime: string;
}

// 认证相关API
- POST /api/auth/register - 用户注册
- POST /api/auth/login - 用户登录
- POST /api/auth/reset-password - 密码重置
- GET /api/auth/verify-token - 验证令牌
```

**任务文件划分**:

1. `./task-auth-01.md`: 用户数据模型和数据库设计
   - 实现User数据库模型
   - 创建数据库索引
   - 设计密码加密策略
   
2. `./task-auth-02.md`: 用户注册UI开发
   - 开发注册页面静态UI
   - 使用模拟数据完成前端交互逻辑
   - 确保表单验证和用户提示正常工作

3. `./task-auth-03.md`: 用户注册功能实现
   - 实现注册API接口
   - 对接注册UI与API
   - 实现邮箱验证流程
   
4. `./task-auth-04.md`: 用户登录功能实现
   - 实现登录API接口
   - 添加JWT令牌生成
   - 实现记住登录状态功能
   
5. `./task-auth-05.md`: 密码重置功能
   - 实现发送重置邮件功能
   - 实现密码重置API
   - 添加安全限制和防护
   
6. `./task-auth-06.md`: 权限管理系统
   - 实现角色基础权限控制
   - 创建权限中间件
   - 实现API访问控制

**任务总览文件 task-summary.md**:
```markdown
# 用户认证系统 - 任务拆分总结

## 需求概述
实现完整的用户认证系统，包括用户注册、登录、密码重置和权限管理功能，确保系统安全性和用户体验。

## 任务拆分
1. **task-auth-01**: 用户数据模型和数据库设计
   - 实现User数据库模型
   - 创建数据库索引
   - 设计密码加密策略

2. **task-auth-02**: 用户注册UI开发
   - 开发注册页面静态UI
   - 使用模拟数据完成前端交互逻辑

3. **task-auth-03**: 用户注册功能实现
   - 实现注册API接口
   - 对接注册UI与API
   - 实现邮箱验证流程

4. **task-auth-04**: 用户登录功能实现
   - 实现登录API接口
   - 添加JWT令牌生成
   - 实现记住登录状态功能

5. **task-auth-05**: 密码重置功能
   - 实现发送重置邮件功能
   - 实现密码重置API
   - 添加安全限制和防护

6. **task-auth-06**: 权限管理系统
   - 实现角色基础权限控制
   - 创建权限中间件
   - 实现API访问控制

## 任务依赖关系
```mermaid
graph TD
    A[task-auth-01] --> C[task-auth-03]
    B[task-auth-02] --> C
    A --> D[task-auth-04]
    D --> E[task-auth-05]
    A --> F[task-auth-06]
```

## 任务优先级
- 高优先级: task-auth-01, task-auth-04
- 中优先级: task-auth-02, task-auth-03, task-auth-06
- 低优先级: task-auth-05

## 开发建议
1. 优先完成数据模型和登录功能，为其他模块提供基础
2. 注册和权限管理可以并行开发
3. 密码重置功能可以最后实现
4. 所有API应实现适当的错误处理和日志记录
5. 确保所有用户输入都经过验证，防止安全漏洞
```

每个任务文件都包含详细的技术规范、验收标准，并明确指出与其他任务的依赖关系。
</example>

<example type="invalid">
### 不良任务分解示例

**需求**: 实现用户认证系统。

**任务文件**:
`./auth-tasks.md`
```
实现所有认证功能：
1. 用户注册
2. 用户登录
3. 密码重置
4. 权限管理

所有功能应在下周完成。
```

这个任务文件有以下问题：
- 没有将大型功能分解为多个独立任务文件
- 缺乏每个任务的详细技术规范
- 没有明确的验收标准
- 没有说明任务间的依赖关系
- 没有足够的技术细节指导开发人员实施
- 没有创建任务总览文件，缺乏整体视角
</example>










# 循环设置界面优化需求

status: draft

## 背景
当前循环设置弹窗在一个界面中同时展示循环类型选择和具体配置参数（如选择星期几、设置间隔天数等），导致界面混乱、操作流程不清晰。需要将设置流程拆分为清晰的两个步骤，提升用户体验。

## 需求

### 功能需求
1. 将循环设置弹窗拆分为两个步骤：
   - 第一步：仅展示循环类型选择（基础循环和高级循环）
   - 第二步：根据选择的循环类型，展示相应的配置界面（如选择星期几、设置间隔天数等）

2. 操作流程优化：
   - 用户点击循环设置按钮，打开弹窗，默认显示第一步（循环类型选择）
   - 用户选择循环类型后：
     - 如果选择了"不重复"或"每天"等不需要额外配置的类型，则直接保存并关闭弹窗
     - 如果选择了需要额外配置的类型（如"每周几"、"每几天"等），则隐藏循环类型列表，显示第二步的配置界面
   - 第二步配置界面提供"返回"按钮，允许用户返回第一步重新选择循环类型

3. 视觉反馈：
   - 当从第一步切换到第二步时，提供平滑的过渡动画，增强用户体验
   - 第二步界面顶部显示当前选择的循环类型，便于用户理解上下文

### 非功能需求
1. 界面简洁清晰，每个步骤只展示必要的元素
2. 动画过渡流畅，不产生卡顿
3. 保持与现有 UI 风格一致

## 技术方案

### 实现思路
1. 在现有的 `l-loop-popup.vue` 组件中，引入状态管理表示当前步骤（第一步或第二步）
2. 使用条件渲染，根据当前步骤显示不同的界面内容
3. 实现动画效果，使用 Vue 过渡组件处理步骤切换动画

### 代码实现要点
1. 在组件中添加 `currentStep` 状态变量，用于追踪当前处于哪个步骤
2. 修改现有模板结构，将内容分为两个主要部分：
   - 第一步：循环类型选择区域
   - 第二步：具体配置区域
3. 实现选择循环类型后的逻辑：
   - 对于简单类型（如"不重复"、"每天"），直接调用确认处理
   - 对于复杂类型，切换到第二步
4. 在第二步界面添加返回按钮，允许用户返回第一步

### 架构设计
```mermaid
graph TD
    A[用户点击设置循环] --> B[打开弹窗：第一步-选择循环类型]
    B --> C{是否需要额外配置?}
    C -->|否| D[直接保存并关闭]
    C -->|是| E[切换到第二步-配置具体参数]
    E --> F[用户配置参数]
    F --> G[保存并关闭]
    E --> H[用户点击返回]
    H --> B
```

### 代码结构变更
1. 组件状态变更：
   ```javascript
   // 新增状态
   const currentStep = ref(1) // 1=选择循环类型，2=配置具体参数
   
   // 处理循环类型选择
   const handleFrequencySelect = (type) => {
     frequency.value = type
     
     // 判断是否需要额外配置
     if (type === 'none' || type === 'daily') {
       // 直接保存并关闭
       handleConfirm()
     } else {
       // 切换到第二步
       currentStep.value = 2
     }
   }
   
   // 返回第一步
   const backToFirstStep = () => {
     currentStep.value = 1
   }
   ```

2. 模板结构调整：
   ```html
   <div class="popup-content">
     <!-- 第一步：循环类型选择 -->
     <transition name="fade">
       <div v-if="currentStep === 1" class="step-container">
         <!-- 循环类型选择界面 -->
       </div>
     </transition>
     
     <!-- 第二步：具体参数配置 -->
     <transition name="fade">
       <div v-if="currentStep === 2" class="step-container">
         <!-- 头部：显示已选循环类型和返回按钮 -->
         <div class="step-header">
           <div class="back-button" @click="backToFirstStep">
             <i class="icon-back"></i>
           </div>
           <div class="current-type">{{ frequencyDisplayName }}</div>
         </div>
         
         <!-- 具体配置内容 -->
       </div>
     </transition>
   </div>
   ```

## 风险评估


### 潜在风险
1. 增加步骤可能导致部分用户认为操作变复杂
   - 解决方案：确保界面提示清晰，优化每个步骤的视觉设计，减少认知负担

## 效果预期
优化后的循环设置界面将更加清晰直观，用户可以更专注于当前步骤的操作，减少认知负担，提升整体用户体验。分步设计也为未来可能的功能扩展提供了更灵活的空间。 
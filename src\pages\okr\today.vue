<template>
  <view class="content">
    <l-progress-contribution-tip v-model="showProgressTip" :progress-info="progressTipInfo" />
    <view class="status-bar-placeholder"></view>
    <!-- 日历周视图 -->
    <l-week-calendar :pendingTasksCount="pendingTasks.length" :monthYearText="monthYearText" :currentDate="currentDate"
      @selectDay="handleSelectDay" @navigateWeek="handleNavigateWeek" @showWeekOverview="showWeekOverview"
      @dateChange="handleDateChange" />

    <!-- 动机展示卡片 -->
    <view class="motivation-card" v-if="currentMotivation" @click="refreshMotivation">
      <view class="motivation-content">
        <i class="fas fa-quote-left motivation-quote-icon"></i>
        <text class="motivation-text">{{ currentMotivation.content }}</text>
        <i class="fas fa-quote-right motivation-quote-icon right"></i>
      </view>
    </view>

    <!-- 今日任务列表 -->
    <view class="task-container">
      <view class="task-container-title" @click="togglePendingTasks">
        <text>{{ selectedDateText }}待办 ({{ pendingTasks.length }})</text>
        <i class="fas" :class="showPendingTasks ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
      </view>

      <!-- 未完成任务列表 -->
      <view class="task-list" v-show="showPendingTasks">
        <view v-for="task in pendingTasks" :key="task._id" class="task-item" :style="{
  background: `linear-gradient(to right, ${task.target?.color ? `${task.target.color}33` : 'var(--color-primary-transparent-20)'} ${task._progressPercentCache || 0
            }%, transparent ${task._progressPercentCache || 0}%)`,
        }" @click="goToKrDetail(task)">
          <view class="task-header">
            <div class="add-progress-btn" @click.stop="showUpdateProgress(task)" :style="{
              backgroundColor: task.target?.color || 'var(--color-primary)'
            }">
              <i class="fas fa-plus"></i>
            </div>
            <view class="task-title">{{ task.title }}</view>
          </view>
          <view class="task-details">
            <view class="task-info-row">
              <view class="task-key-result" @click.stop="goToOkrDetail(task)" v-if="task.objective">
                <text class="key-result-text" :style="{
                  color: task.target?.color || 'var(--color-primary)',
                  backgroundColor: task.target?.color ? `${task.target.color}33` : 'var(--color-primary-transparent-20)'
                }">目标：{{ task.objective }}</text>
              </view>
              <view class="task-deadline">
                <!-- 任务进度显示 -->
                <span class="repeat-progress-text" v-if="task._progressText" :style="{
                  color: task.target?.color || 'var(--color-primary)'
                }">{{ task._progressText }}</span>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 已完成任务列表 -->
      <view class="task-container-title completed-title" v-if="completedTasks.length > 0" @click="toggleCompletedTasks">
        <text>已完成 ({{ completedTasks.length }})</text>
        <i class="fas" :class="showCompletedTasks ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
      </view>
      <view class="task-list completed-list" v-if="completedTasks.length > 0" v-show="showCompletedTasks">
        <view v-for="task in completedTasks" :key="task._id" class="task-item completed-item" :style="{
          background: `linear-gradient(to right, ${task.target?.color ? `${task.target.color}33` : 'var(--color-primary-transparent-20)'} ${task._progressPercentCache || 0
            }%, transparent ${task._progressPercentCache || 0}%)`,
        }" @click="goToKrDetail(task)">
          <view class="task-header">
            <div class="completed-icon" :style="{
              backgroundColor: task.target?.color || 'var(--color-success)'
            }">
              <i class="fas fa-check"></i>
            </div>
            <view class="task-title completed">{{ task.title }}</view>
          </view>
          <view class="task-details">
            <view class="task-info-row">
              <view class="task-key-result" @click.stop="goToOkrDetail(task)" v-if="task.objective">
                <text class="key-result-text" :style="{
                  color: task.target?.color || 'var(--color-primary)',
                  backgroundColor: task.target?.color ? `${task.target.color}33` : 'var(--color-primary-transparent-20)'
                }">目标：{{ task.objective }}</text>
              </view>
              <view class="task-deadline">
                <!-- 任务进度显示 -->
                <span class="repeat-progress-text" v-if="task._progressText" :style="{
                  color: task.target?.color || 'var(--color-primary)'
                }">{{ task._progressText }}</span>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 周概览弹窗 -->
    <l-week-overview-modal v-model:visible="showWeekOverviewModal" :current-date="currentDate" />

    <!-- 更新进度弹窗 -->
    <z-update-progress-modal v-model:visible="showProgressModal" :task-id="currentTaskId" :kr-title="currentTaskTitle"
      :initial-progress="currentTaskProgress" :initial-date="currentDate" @save-success="handleProgressUpdateSuccess" />
  </view>
</template>

<script setup>
import { ref, onMounted, computed, defineComponent, inject, watch } from 'vue'
import { router, getUrlParams } from '@/utils/tools'
import * as rrule from '@/utils/rrrrule'
import ZTaskModal from '@/components/z-task-modal.vue'
import ZUpdateProgressModal from '@/components/z-update-progress-modal.vue'
import LWeekCalendar from './components/l-week-calendar.vue'
import LProgressContributionTip from './components/l-progress-contribution-tip.vue';
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { updateTaskApi, getTaskApi, addTaskApi, getTaskListApi } from '@/api/task'
import { getOkrListApi, getOkrApi } from '@/api/okr'
import { getConfigApi } from '@/api/okrConfig'
import { onShow } from '@dcloudio/uni-app'
import ZTaskCheckbox from '@/components/z-task-checkbox.vue'
import LWeekOverviewModal from './components/l-week-overview-modal.vue'
import { StatusUtils } from '@/constants/status'
import { buildOkrQuery } from '@/utils/queryBuilder'

// 注入刷新触发器
const reloadTrigger = inject('reloadTrigger', ref({ count: 0, tabKey: '' }))

// 设置 dayjs 为中文
dayjs.locale('zh-cn')

defineComponent({
  name: 'OkrToday',
})

// 判断当前页面是否激活
const isActiveTab = computed(() => {
  return reloadTrigger.value.tabKey === "today"
})

// 动机相关数据
const allMotivations = ref([])
const currentMotivation = ref(null)

// 获取随机动机
const fetchRandomMotivation = async () => {
  try {
    console.log('[调试] 开始获取动机，当前动机列表长度：', allMotivations.value.length)

    // 如果已有动机列表，则从中随机选择一条
    if (allMotivations.value.length > 0) {
      const randomIndex = Math.floor(Math.random() * allMotivations.value.length)
      currentMotivation.value = allMotivations.value[randomIndex]
      console.log('[调试] 从已有列表中选择动机：', currentMotivation.value)
      return
    }

    // 否则获取所有目标的动机
    console.log('[调试] 正在获取目标动机...')

    // 先获取所有目标，然后在代码中过滤
    const allOkrs = await getOkrListApi() // 获取所有目标
    console.log('[调试] 所有目标：', allOkrs.map(okr => ({ id: okr._id, title: okr.title, status: okr.status })))

    // 在代码中过滤进行中的目标，避免数据库索引问题
    const inProgressOkrs = allOkrs.filter(okr => okr.status === 'inProgress')
    console.log('[调试] 进行中的目标数量：', inProgressOkrs.length, inProgressOkrs)

    // 如果没有进行中的目标，则使用所有目标
    let okrs = inProgressOkrs.length > 0 ? inProgressOkrs : allOkrs
    console.log('[调试] 最终使用的目标数量：', okrs.length)

    // 收集所有动机
    const motivations = []
    for (const okr of okrs) {
      console.log('[调试] 处理目标：', okr.title, '动机数据：', okr.motivation)
      if (okr.motivation && Array.isArray(okr.motivation)) {
        // 过滤掉空内容的动机
        const validMotivations = okr.motivation.filter((m) => m && m.content && m.content.trim() !== '')
        console.log('[调试] 有效动机数量：', validMotivations.length, validMotivations)
        motivations.push(...validMotivations)
      }
    }

    console.log('[调试] 收集到的总动机数量：', motivations.length)

    // 保存所有动机
    allMotivations.value = motivations

    // 如果有动机，随机选择一条
    if (motivations.length > 0) {
      const randomIndex = Math.floor(Math.random() * motivations.length)
      currentMotivation.value = motivations[randomIndex]
      console.log('[调试] 选择的动机：', currentMotivation.value)
    } else {
      console.log('[调试] 没有找到任何动机')
    }
  } catch (error) {
    console.error('获取动机失败', error)
  }
}

// 刷新动机
const refreshMotivation = () => {
  if (allMotivations.value.length > 0) {
    // 获取当前动机索引
    const currentIndex = allMotivations.value.findIndex((m) => m.content === currentMotivation.value.content)

    // 选择下一个动机，如果是最后一个则回到第一个
    const nextIndex = (currentIndex + 1) % allMotivations.value.length
    currentMotivation.value = allMotivations.value[nextIndex]
  } else {
    // 如果没有动机，尝试重新获取
    fetchRandomMotivation()
  }
}

// 当前日期
const currentDate = ref(dayjs().format('YYYY-MM-DD'))
const selectedDate = ref(dayjs())

// 月份年份显示文本
const monthYearText = computed(() => {
  const result = selectedDate.value.format('YYYY 年 M 月')
  console.log('[调试] monthYearText 计算属性被触发', {
    结果: result,
    当前日期: currentDate.value,
    选中日期: selectedDate.value ? selectedDate.value.format('YYYY-MM-DD') : null,
  })
  return result
})

// 选中日期显示文本
const selectedDateText = computed(() => {
  const today = dayjs().format('YYYY-MM-DD')
  const selected = selectedDate.value.format('YYYY-MM-DD')
  let result

  if (today === selected) {
    result = '今日'
  } else if (dayjs(today).add(1, 'day').format('YYYY-MM-DD') === selected) {
    result = '明日'
  } else if (dayjs(today).subtract(1, 'day').format('YYYY-MM-DD') === selected) {
    result = '昨日'
  } else {
    result = selectedDate.value.format('MM 月 DD 日')
  }

  console.log('[调试] selectedDateText 计算属性被触发', {
    结果: result,
    当前日期: currentDate.value,
    选中日期: selected,
  })
  return result
})

// 任务数据
const tasks = ref([])
const loading = ref(false)
const showWeekOverviewModal = ref(false)
const showTaskModal = ref(false)
const modalTitle = ref('添加新任务')
const isTaskEdit = ref(false)
const currentTaskId = ref('')
const currentTaskData = ref({})

// 更新进度弹窗相关
const showProgressModal = ref(false)
const currentTaskTitle = ref('')
const currentTaskProgress = ref(0)
const progressTipInfo = ref({})
const showProgressTip = ref(false)

// 控制任务列表显示/隐藏
const showPendingTasks = ref(true)
const showCompletedTasks = ref(true)

// 根据完成状态筛选任务
const pendingTasks = computed(() => {
  console.log('[调试] pendingTasks 计算属性被触发')
  const result = tasks.value.filter((task) => {
    // 对于重复任务，判断今日完成情况
    if (task.repeatFlag && task.progressData) {
      // 使用 isCompletedToday 判断是否已完成
      const isCompleted = task.progressData.isCompletedToday
      return !isCompleted
    }
    // 对于非重复任务，使用任务状态判断
    return !task.completed
  })
  console.log(`[调试] pendingTasks 计算结果：${result.length}个待办任务`)
  return result
})

const completedTasks = computed(() => {
  console.log('[调试] completedTasks 计算属性被触发')
  const result = tasks.value.filter((task) => {
    // 对于重复任务，判断今日完成情况
    if (task.repeatFlag && task.progressData) {
      // 使用 isCompletedToday 判断是否已完成
      const isCompleted = task.progressData.isCompletedToday
      return isCompleted
    }
    // 对于非重复任务，使用任务状态判断
    return task.completed
  })
  console.log(`[调试] completedTasks 计算结果：${result.length}个已完成任务`)
  return result
})

// 跟踪最后一次加载的日期
const lastLoadedDate = ref('')

/**
 * 获取指定日期的任务数据
 *
 * 该函数执行以下操作：
 * 1. 获取指定日期的所有 KR 类型任务
 * 2. 查询并关联目标信息
 * 3. 过滤任务（重复任务和暂停目标）
 * 4. 计算每个任务在选定日期的完成情况
 * 5. 格式化任务数据用于显示
 *
 * @param {string} date - 要获取任务的日期，格式为 YYYY-MM-DD
 * @param {boolean} force - 是否强制刷新，默认为 false
 */
const fetchTasksByDate = async (date, force = false) => {
  console.log('[调试] fetchTasksByDate 开始', {
    请求日期: date,
    当前日期: currentDate.value,
    loading状态: loading.value,
    最后加载日期: lastLoadedDate.value,
    强制刷新: force,
  })

  // 避免重复加载相同日期的任务（除非强制刷新）
  if (!force && date === lastLoadedDate.value) {
    console.log('[调试] fetchTasksByDate 跳过重复加载相同日期', date)
    return
  }

  if (loading.value) {
    console.log('[调试] fetchTasksByDate 已在加载中，跳过')
    return
  }

  loading.value = true
  try {
    console.log('[调试] fetchTasksByDate 正在获取任务...')
    // 1. 获取所有 KR 类型任务
    let allTasks = []
    try {
      allTasks = await getTaskListApi(`type == 'kr' && startDate <= '${date}' && endDate >= '${date}'`)
      console.log(`[调试] 获取到 ${allTasks.length} 个原始任务`)
    } catch (apiError) {
      console.error('[错误] 获取任务列表失败', apiError)
      allTasks = [] // 保证流程继续
    }

    // 2. 查询目标信息
    const okrIds = allTasks.map((item) => item.okrId).filter((id) => id && id !== '')
    const uniqueOkrIds = [...new Set(okrIds)]

    let targetList = []
    if (uniqueOkrIds.length > 0) {
      try {
        const idsQuery = uniqueOkrIds.map((id) => `"${id}"`).join(',')
        targetList = await getOkrListApi(`_id, anyOf(${idsQuery})`)
      } catch (targetError) {
        console.error('[错误] 获取目标失败', targetError)
        targetList = []
      }
    }

    // 关联目标信息到任务对象
    const tasksWithRelations = allTasks.map((item) => {
      try {
        const target = targetList.find((t) => t._id === item.okrId) || null
        return { ...item, target }
      } catch (relationError) {
        console.error(`[错误] 关联任务时出错:`, relationError)
        return { ...item, target: null }
      }
    })

    // 3. 过滤任务（重复任务和暂停目标）
    const filteredTasks = tasksWithRelations.filter((item) => {
      try {
        // 检查是否为重复任务，如果是则判断当前选中日期是否应该显示
        if (item.repeatFlag) {
          try {
            const progressData = rrule.getTaskProgressData(date, item.repeatFlag, item.recList || [], {
              totalRequired: item.dailyTarget || 1,
              startDate: item.startDate,
              endDate: item.endDate,
            })
            // 保存 progressData 到任务对象中
            item.progressData = progressData
            // 通过 isOccurrenceToday 判断是否是发生日
            const shouldShow = progressData.isOccurrenceToday
            if (!shouldShow) {
              return false // 不符合重复规则，过滤掉
            }
          } catch (error) {
            console.error(`[错误] 判断重复任务规则出错:`, error)
            // 出错时不过滤
          }
        } else {
          // 对于非重复任务，创建一个基本的 progressData
          item.progressData = {
            completedToday: 0,
            isOccurrenceToday: true,
          }
        }

        // 检查目标是否为活跃状态（只显示进行中的目标）
        const isHide = !item.target || !StatusUtils.isOkrActive(item.target.status)
        return !isHide
      } catch (filterError) {
        console.error(`[错误] 过滤任务时出错:`, filterError)
        return false
      }
    })

    // 4. 预先计算进度文本和进度百分比 - 避免在模板渲染时重复计算
    const processedTasks = filteredTasks.map((task) => {
      // 创建任务对象的深拷贝，避免修改原始数据
      const processedTask = JSON.parse(
        JSON.stringify({
          _id: task._id,
          title: task.title || '无标题',
          objective: task.target ? task.target.title : '',
          deadline: task.endDate ? formatDeadline(task.endDate) : '无截止日期',
          completed: task.status === 1,
          progress: task.progress || 0,
          okrId: task.okrId || '',
          type: task.type || 'todo',
          dailyTarget: task.dailyTarget || 0,
          repeatFlag: task.repeatFlag || '',
          recList: task.recList || [],
          progressData: task.progressData || { completedToday: 0, isOccurrenceToday: true },
          unit: task.unit || '',
          target: task.target || null, // 保留 target 信息用于颜色显示
        })
      )

      // 初始化进度百分比为 0
      let progressPercent = 0

      if (processedTask.repeatFlag && processedTask.progressData) {
        const rule =
          typeof processedTask.repeatFlag === 'string'
            ? rrule.parseRule(processedTask.repeatFlag)
            : processedTask.repeatFlag
        const progressData = processedTask.progressData
        const dailyTarget = processedTask.dailyTarget || 0

        // 基础循环类型：DAILY, WEEKLY, MONTHLY, INTERVAL_DAILY
        if (['DAILY', 'WEEKLY', 'MONTHLY', 'INTERVAL_DAILY'].includes(rule.type)) {
          if (progressData.isOccurrenceToday) {
            // 计算当天进度百分比
            progressPercent = Math.min((progressData.completedToday / dailyTarget) * 100, 100)
            processedTask._progressText = `今天:${progressData.completedToday}/${dailyTarget}${processedTask.unit || ''
              }`
          }
        }
        // 周期内完成类型 - WEEKLY_N_TIMES
        else if (rule.type === 'WEEKLY_N_TIMES') {
          if (progressData.isOccurrenceToday) {
            // 计算本周进度百分比
            progressPercent = Math.min((progressData.completedDays / progressData.totalDays) * 100, 100)
            processedTask._progressText = `本周:${progressData.completedDays}/${progressData.totalDays}${processedTask.unit || ''
              }  今天:${progressData.completedToday}/${dailyTarget}${processedTask.unit || ''}`
          }
        }
        // 周期内完成类型 - MONTHLY_N_TIMES
        else if (rule.type === 'MONTHLY_N_TIMES') {
          if (progressData.isOccurrenceToday) {
            // 计算本月进度百分比
            progressPercent = Math.min((progressData.completedDays / progressData.totalDays) * 100, 100)
            processedTask._progressText = `本月：${progressData.completedDays}/${progressData.totalDays}${processedTask.unit || ''
              }  今天：${progressData.completedToday}/${dailyTarget}${processedTask.unit || ''}`
          }
        }
        // N_DAYS
        else if (rule.type === 'N_DAYS') {
          // 计算 N 天内的进度百分比
          progressPercent = Math.min((progressData.completedCount / progressData.totalCount) * 100, 100)
          processedTask._progressText = `第${progressData.dayOfPeriod}天  ${progressData.totalDays}天内：${progressData.completedCount
            }/${progressData.totalCount}${processedTask.unit || ''}`
        }
        // N_WEEKS
        else if (rule.type === 'N_WEEKS') {
          // 计算 N 周内的进度百分比
          progressPercent = Math.min((progressData.completedCount / progressData.totalCount) * 100, 100)
          processedTask._progressText = `第${progressData.weekOfPeriod}周  ${progressData.totalWeeks}周内：${progressData.completedCount
            }/${progressData.totalCount}${processedTask.unit || ''}`
        }
        // N_MONTHS
        else if (rule.type === 'N_MONTHS') {
          // 计算 N 月内的进度百分比
          progressPercent = Math.min((progressData.completedCount / progressData.totalCount) * 100, 100)
          processedTask._progressText = `第${progressData.monthOfPeriod}月  ${progressData.totalMonths}月内：${progressData.completedCount
            }/${progressData.totalCount}${processedTask.unit || ''}`
        }
      } else if (!processedTask.repeatFlag) {
        // 对于非重复任务，使用总体进度
        progressPercent = processedTask.progress || 0
      }

      // 存储计算好的进度百分比
      processedTask._progressPercentCache = progressPercent

      return processedTask
    })

    // 5. 更新任务列表
    tasks.value = processedTasks

    lastLoadedDate.value = date // 更新最后一次加载的日期
    console.log('[调试] 任务数据处理完成，更新最后加载日期', date)
  } catch (error) {
    console.error('[错误] 获取任务失败：', error)
    uni.showToast({
      title: '获取任务失败',
      icon: 'none',
    })
  } finally {
    console.log('[调试] fetchTasksByDate 完成', {
      结束日期: date,
      当前日期: currentDate.value,
    })
    loading.value = false
  }
}

// 格式化截止日期显示
const formatDeadline = (dateString) => {
  const today = dayjs().format('YYYY-MM-DD')

  if (dateString === today) {
    return '今天 23:59'
  } else if (dayjs(today).add(1, 'day').format('YYYY-MM-DD') === dateString) {
    return '明天 23:59'
  } else {
    return dayjs(dateString).format('MM-DD HH:mm')
  }
}

// 处理日期选择
const handleSelectDay = (data) => {
  // 确保 date 参数有值
  if (!data || !data.date) {
    console.error('[调试] handleSelectDay: 收到无效数据', data)
    return
  }

  console.log('[调试] handleSelectDay 被调用', {
    传入日期: data.date,
    当前日期: currentDate.value,
    选中日期: selectedDate.value ? selectedDate.value.format('YYYY-MM-DD') : null,
    数据源: data.source || '未知',
  })

  // 避免不必要的更新：如果日期没变，不重新设置
  if (data.date === currentDate.value) {
    console.log('[调试] handleSelectDay: 日期未变化，跳过更新', data.date)
    return
  }

  selectedDate.value = dayjs(data.date)
  currentDate.value = data.date

  console.log('[调试] handleSelectDay 设置后', {
    传入日期: data.date,
    当前日期: currentDate.value,
    选中日期: selectedDate.value ? selectedDate.value.format('YYYY-MM-DD') : null,
  })

  // 根据选中日期获取对应的任务
  fetchTasksByDate(data.date)
}

// 处理周导航
const handleNavigateWeek = (data) => {
  console.log('[调试] handleNavigateWeek 被调用', data)
  // 可以在这里更新任务列表或其他状态
}

// 处理日期变更
const handleDateChange = (data) => {
  // 确保 date 参数有值
  if (!data || !data.date) {
    console.error('[调试] handleDateChange: 收到无效数据', data)
    return
  }

  console.log('[调试] handleDateChange 被调用', {
    传入日期: data.date,
    当前日期: currentDate.value,
    选中日期: selectedDate.value ? selectedDate.value.format('YYYY-MM-DD') : null,
    数据源: data.source || '未知',
  })

  // 避免不必要的更新：如果日期没变，不重新设置
  if (data.date === currentDate.value) {
    console.log('[调试] handleDateChange: 日期未变化，跳过更新', data.date)
    return
  }

  // 如果是初始挂载触发的事件，我们可能需要采取不同的处理方式
  if (data.source === 'initial-mount' && currentDate.value) {
    console.log('[调试] handleDateChange: 初始挂载事件，但 currentDate 已存在，跳过', currentDate.value)
    return
  }

  selectedDate.value = dayjs(data.date)
  currentDate.value = data.date

  console.log('[调试] handleDateChange 设置后', {
    传入日期: data.date,
    当前日期: currentDate.value,
    选中日期: selectedDate.value ? selectedDate.value.format('YYYY-MM-DD') : null,
  })

  // 根据变更后的日期获取任务
  fetchTasksByDate(data.date)
}

// 显示周概览
const showWeekOverview = () => {
  showWeekOverviewModal.value = true
}

// 跳转到 KR 详情
const goToKrDetail = (task) => {
  console.log('[调试] 跳转到详情页', { taskId: task._id, okrId: task.okrId })
  router.push(`/pages/okr/krDetail?okrId=${task.okrId}&id=${task._id}`)
}

// 跳转到目标详情页
const goToOkrDetail = (task) => {
  if (task.target && task.target._id) {
    console.log('[调试] 跳转到目标详情页', { okrId: task.target._id, title: task.objective })
    router.push(`/pages/okr/okrDetail?id=${task.target._id}`)
  } else {
    uni.showToast({
      title: '无法获取目标信息',
      icon: 'none',
    })
  }
}

// 显示更新进度弹窗
const showUpdateProgress = (task) => {
  console.log('[调试] 显示更新进度弹窗', { taskId: task._id, title: task.title })
  currentTaskId.value = task._id
  currentTaskTitle.value = task.title
  currentTaskProgress.value = task.progress || 0
  // 使用选中的日期而不是系统当前日期
  currentDate.value = selectedDate.value.format('YYYY-MM-DD')
  // 阻止事件冒泡，避免触发 goToKrDetail
  showProgressModal.value = true
}

// 处理进度更新成功
const handleProgressUpdateSuccess = async (addedValue) => {
  // 1. 强制刷新任务列表以获取最新数据
  await fetchTasksByDate(currentDate.value, true);

  // 2. 找到刚刚更新过的任务
  const updatedTask = tasks.value.find(t => t._id === currentTaskId.value);
  if (!updatedTask) return;

  // 3. 收集今日所有相关目标 - 改为基于 KR 完成比例的加权平均计算
  const objectives = new Map();
  tasks.value.forEach(task => {
    if (task.target && task.progressData && task.progressData.isOccurrenceToday) {
      if (!objectives.has(task.target._id)) {
        objectives.set(task.target._id, {
          id: task.target._id,
          title: task.target.title,
          krs: [], // 存储该目标下的所有 KR 信息
          totalKrCount: 0, // 该目标下 KR 的总数
          completedKrProgress: 0, // 已完成的 KR 进度总和（基于比例）
          unit: '', // 显示单位（使用第一个 KR 的单位）
          color: task.target.color || 'var(--color-primary)'
        });
      }
      const obj = objectives.get(task.target._id);

      // 计算当前 KR 的完成比例（0-1 之间）
      const krCompletionRatio = task.dailyTarget > 0
        ? Math.min((task.progressData.completedToday || 0) / task.dailyTarget, 1)
        : 0;

      // 添加 KR 信息
      obj.krs.push({
        id: task._id,
        title: task.title,
        dailyTarget: task.dailyTarget || 0,
        completedToday: task.progressData.completedToday || 0,
        completionRatio: krCompletionRatio
      });

      obj.totalKrCount += 1;
      obj.completedKrProgress += krCompletionRatio;

      // 使用第一个 KR 的单位作为显示单位
      if (!obj.unit && task.unit) {
        obj.unit = task.unit;
      }
    }
  });

  // 转换为进度提示组件需要的格式
  const processedObjectives = new Map();
  objectives.forEach((obj, objId) => {
    // 计算目标的整体完成百分比：每个 KR 贡献相等权重
    const overallCompletionPercentage = obj.totalKrCount > 0
      ? (obj.completedKrProgress / obj.totalKrCount) * 100
      : 0;

    processedObjectives.set(objId, {
      id: obj.id,
      title: obj.title,
      total: 100, // 目标总是 100%
      completed: Math.round(overallCompletionPercentage), // 当前完成的百分比
      added: 0, // 稍后计算
      unit: '%', // 目标层面使用百分比
      color: obj.color,
      krs: obj.krs // 保留 KR 详细信息用于计算增量
    });
  });

  // 4. 计算更新前的进度和本次新增进度
  const progressList = Array.from(processedObjectives.values()).map(obj => {
    if (obj.id === updatedTask.target._id) {
      // 找到被更新的 KR
      const updatedKr = obj.krs.find(kr => kr.id === updatedTask._id);
      if (updatedKr) {
        // 计算更新前该 KR 的完成比例
        const previousKrCompletedToday = updatedKr.completedToday - addedValue;
        const previousKrCompletionRatio = updatedKr.dailyTarget > 0
          ? Math.min(Math.max(previousKrCompletedToday, 0) / updatedKr.dailyTarget, 1)
          : 0;

        // 计算更新后该 KR 的完成比例
        const currentKrCompletionRatio = updatedKr.completionRatio;

        // 计算 KR 完成比例的增量（0-1 之间）
        const krCompletionRatioIncrease = currentKrCompletionRatio - previousKrCompletionRatio;

        // 将 KR 完成比例增量转换为目标百分比增量
        // 每个 KR 对目标贡献的权重是 1/totalKrCount
        const objectivePercentageIncrease = obj.totalKrCount > 0
          ? (krCompletionRatioIncrease / obj.totalKrCount) * 100
          : 0;

        return {
          ...obj,
          initial: Math.round(obj.completed - objectivePercentageIncrease),
          added: Math.round(objectivePercentageIncrease),
        };
      }
    }
    return {
      ...obj,
      initial: obj.completed,
      added: 0,
    };
  });

  // 5. 设置提示信息并显示
  progressTipInfo.value = progressList;
  showProgressTip.value = true;
};

// 切换待办任务显示/隐藏
const togglePendingTasks = () => {
  showPendingTasks.value = !showPendingTasks.value
}

// 切换已完成任务显示/隐藏
const toggleCompletedTasks = () => {
  showCompletedTasks.value = !showCompletedTasks.value
}
const onInit = () => {
  // 只有在首次加载时才设置为当前日期
  if (!currentDate.value) {
    currentDate.value = dayjs().format('YYYY-MM-DD')
    selectedDate.value = dayjs()
  }

  // 获取随机动机
  fetchRandomMotivation()

  // 如果已经在加载中，则不要重复请求
  if (!loading.value) {
    fetchTasksByDate(currentDate.value)
  }
}

// 监听刷新触发和激活状态
watch(() => [reloadTrigger.value.count, isActiveTab.value], ([count, isActive]) => {
  console.log('今日页面监听到刷新触发：', count, '当前页是否激活：', isActive, '当前 tab:', reloadTrigger.value.tabKey)
  if (isActive) {
    console.log('今日页面接到了刷新通知，且当前为激活页面')
    onInit()
  } else {
    console.log('今日页面接到了刷新通知，但当前不是激活页面')
  }
}, { deep: true })

// 添加 onShow 生命周期钩子，确保每次页面显示时都刷新任务数据
onShow(onInit)
</script>

<style lang="scss">
.content {
  padding: 30rpx;
  position: relative;
  background-color: var(--color-bg);
  min-height: 100vh;
}

// 动机卡片样式
.motivation-card {
  margin: 20rpx 0rpx 30rpx;
  background-color: var(--color-white);
  border-radius: var(--rounded-lg);
  padding: 30rpx;
  box-shadow: var(--shadow-sm);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: var(--transition-fast);
  text-align: center;

  &:active {
    background-color: var(--color-gray-50);
  }
}

.motivation-content {
  flex: 1;
  position: relative;
  padding: 10rpx 40rpx;
}

.motivation-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: var(--color-gray-700);
  font-weight: 500;
}

.motivation-quote-icon {
  color: var(--color-primary);
  opacity: 0.3;
  font-size: 24rpx;
  position: absolute;

  &.right {
    bottom: 0;
    right: 0;
  }

  &:not(.right) {
    top: 0;
    left: 0;
  }
}

.motivation-refresh {
  width: 60rpx;
  height: 60rpx;
  border-radius: var(--rounded-full);
  background-color: var(--color-primary-transparent-10);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;

  i {
    font-size: 24rpx;
    color: var(--color-primary);
  }
}

// 任务列表
.task-container {
  padding: 0 10rpx;
}

.task-container-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--color-gray-800);
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  user-select: none;

  i {
    font-size: 24rpx;
    color: var(--color-gray-500);
  }
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.task-item {
  background-color: var(--color-white);
  border-radius: var(--rounded-lg);
  padding: 24rpx;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  /* 确保渐变背景不超出卡片边界 */
  background-repeat: no-repeat;
  transition: background 0.3s ease;
}

.task-header {
  position: relative;
  display: flex;
  align-items: flex-start;
}

.task-details {
  position: relative;
  margin-left: 56rpx;
}

.add-progress-btn {
  width: 40rpx;
  height: 40rpx;
  background-color: var(--color-primary);
  border-radius: var(--rounded-sm);
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  .fas {
    color: var(--color-white);
    font-size: 24rpx;
  }
}

.task-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid var(--color-gray-300);
  border-radius: var(--rounded-sm);
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-title {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--color-gray-800);
  flex: 1;

  &.completed {
    text-decoration: line-through;
    color: var(--color-gray-500);
  }
}

.task-daily-target {
  font-size: 24rpx;
  color: var(--color-primary);
  background-color: var(--color-primary-transparent-10);
  padding: 2rpx 16rpx;
  border-radius: var(--rounded-sm);
  margin-left: 16rpx;
  white-space: nowrap;
}

.task-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin: 8rpx 0;
}

.task-key-result {
  margin-right: 16rpx;
}

.key-result-text {
  display: inline-block;
  font-size: 24rpx;
  color: var(--color-primary);
  background-color: var(--color-primary-transparent-20);
  padding: 4rpx 24rpx;
  border-radius: var(--rounded-sm);
}

.task-deadline {
  display: flex;
  align-items: center;

  .far,
  .fas {
    font-size: 24rpx;
    color: var(--color-gray-400);
    margin-right: 8rpx;
  }
}

.deadline-text {
  font-size: 24rpx;
  color: var(--color-gray-500);
}

.progress-indicator {
  margin-left: 24rpx;
  color: var(--color-primary) !important;
}

.progress-value {
  font-size: 24rpx;
  color: var(--color-primary);
  margin-left: 4rpx;
  font-weight: 500;
}

.repeat-progress-text {
  font-size: 24rpx;
  color: var(--color-primary);
  padding: 2rpx 16rpx;
  background-color: var(--color-primary-transparent-10);
  border-radius: var(--rounded-sm);
  margin-left: 8rpx;
}

// 已完成任务样式
.completed-title {
  margin-top: 40rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: var(--color-gray-800);
}

.completed-list {
  opacity: 0.8;
}

.completed-item {
  background-color: var(--color-gray-50);
}

.completed-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: var(--color-success);
  border-radius: var(--rounded-sm);
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  .fas {
    color: var(--color-white);
    font-size: 24rpx;
  }
}
</style>

/**
 * 滴答清单API云对象工具函数模块
 * 提供通用的工具函数和辅助方法
 */

const { ERROR_CODES, REQUEST_CONFIG } = require("./config");

// ================================
// 日志记录工具
// ================================

/**
 * 统一日志记录函数
 * @param {string} level - 日志级别 (info, warn, error)
 * @param {string} methodName - 方法名称
 * @param {string} message - 日志消息
 * @param {any} data - 附加数据
 */
function log(level, methodName, message, data = null) {
	const timestamp = new Date().toISOString();
	const logMessage = `[${timestamp}] [${methodName}] ${message}`;

	switch (level) {
		case "info":
			if (data) {
				console.log(logMessage, data);
			} else {
				console.log(logMessage);
			}
			break;
		case "warn":
			if (data) {
				console.warn(logMessage, data);
			} else {
				console.warn(logMessage);
			}
			break;
		case "error":
			if (data) {
				console.error(logMessage, data);
			} else {
				console.error(logMessage);
			}
			break;
		default:
			console.log(logMessage, data);
	}
}

/**
 * 记录信息日志
 * @param {string} methodName - 方法名称
 * @param {string} message - 日志消息
 * @param {any} data - 附加数据
 */
function logInfo(methodName, message, data = null) {
	log("info", methodName, message, data);
}

/**
 * 记录警告日志
 * @param {string} methodName - 方法名称
 * @param {string} message - 日志消息
 * @param {any} data - 附加数据
 */
function logWarn(methodName, message, data = null) {
	log("warn", methodName, message, data);
}

/**
 * 记录错误日志
 * @param {string} methodName - 方法名称
 * @param {string} message - 日志消息
 * @param {any} data - 附加数据
 */
function logError(methodName, message, data = null) {
	log("error", methodName, message, data);
}

// ================================
// 错误处理工具
// ================================

/**
 * 统一错误处理函数
 * @param {Error} error - 错误对象
 * @param {string} methodName - 方法名称
 * @returns {object} 标准错误响应
 */
function handleError(error, methodName) {
	logError(methodName, "操作失败", error);

	// 根据错误类型返回相应的错误响应
	if (error.code === "TIMEOUT") {
		return {
			errCode: ERROR_CODES.TIMEOUT_ERROR,
			errMsg: "请求超时，请稍后重试",
		};
	}

	if (error.code === "ENOTFOUND" || error.code === "ECONNREFUSED") {
		return {
			errCode: ERROR_CODES.NETWORK_ERROR,
			errMsg: "网络连接失败，请检查网络设置",
		};
	}

	return {
		errCode: ERROR_CODES.UNKNOWN_ERROR,
		errMsg: error.message || "操作失败",
		details: error,
	};
}

/**
 * 创建成功响应
 * @param {string} message - 成功消息
 * @param {any} data - 返回数据
 * @returns {object} 标准成功响应
 */
function createSuccessResponse(message, data = null) {
	const response = {
		errCode: null,
		errMsg: message,
		timestamp: Date.now(),
	};

	if (data !== null) {
		response.data = data;
	}

	return response;
}

/**
 * 创建错误响应
 * @param {string} errCode - 错误代码
 * @param {string} errMsg - 错误消息
 * @param {any} details - 错误详情
 * @returns {object} 标准错误响应
 */
function createErrorResponse(errCode, errMsg, details = null) {
	const response = {
		errCode,
		errMsg,
		timestamp: Date.now(),
	};

	if (details !== null) {
		response.details = details;
	}

	return response;
}

// ================================
// 参数验证工具
// ================================

/**
 * 验证必需参数
 * @param {any} param - 参数值
 * @param {string} paramName - 参数名称
 * @returns {object|null} 如果验证失败返回错误响应，否则返回null
 */
function validateRequired(param, paramName) {
	if (param === null || param === undefined || param === "") {
		return createErrorResponse(
			ERROR_CODES.PARAM_IS_NULL,
			`参数 ${paramName} 不能为空`
		);
	}
	return null;
}

/**
 * 验证参数类型
 * @param {any} param - 参数值
 * @param {string} expectedType - 期望的类型
 * @param {string} paramName - 参数名称
 * @returns {object|null} 如果验证失败返回错误响应，否则返回null
 */
function validateType(param, expectedType, paramName) {
	if (typeof param !== expectedType) {
		return createErrorResponse(
			ERROR_CODES.PARAM_TYPE_ERROR,
			`参数 ${paramName} 类型错误，期望 ${expectedType}，实际 ${typeof param}`
		);
	}
	return null;
}

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @param {string} paramName - 参数名称
 * @returns {object|null} 如果验证失败返回错误响应，否则返回null
 */
function validateEmail(email, paramName) {
	const emailRegex = /^[\w\.-]+@[\w\.-]+\.\w+$/;
	if (!emailRegex.test(email)) {
		return createErrorResponse(
			ERROR_CODES.PARAM_FORMAT_ERROR,
			`参数 ${paramName} 邮箱格式不正确`
		);
	}
	return null;
}

// ================================
// HTTP请求工具
// ================================

/**
 * 构建认证请求头
 * @param {string} authToken - 认证令牌
 * @param {string} csrfToken - CSRF令牌
 * @returns {object} 请求头对象
 */
function buildAuthHeaders(authToken, csrfToken) {
	const traceid = `${Date.now().toString(16)}${Math.random()
		.toString(16)
		.substr(2, 8)}`;

	return {
		Accept: "application/json, text/plain, */*",
		"Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7",
		"Cache-Control": "no-cache",
		Origin: "https://dida365.com",
		Pragma: "no-cache",
		Referer: "https://dida365.com/",
		"Sec-Ch-Ua":
			'"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
		"Sec-Ch-Ua-Mobile": "?0",
		"Sec-Ch-Ua-Platform": '"Windows"',
		"Sec-Fetch-Dest": "empty",
		"Sec-Fetch-Mode": "cors",
		"Sec-Fetch-Site": "same-site",
		"User-Agent": REQUEST_CONFIG.user_agent,
		"X-Csrftoken": csrfToken,
		"X-Device": REQUEST_CONFIG.device_info,
		Hl: REQUEST_CONFIG.language,
		"X-Tz": REQUEST_CONFIG.timezone,
		Traceid: traceid,
	};
}

/**
 * 构建认证cookies
 * @param {string} authToken - 认证令牌
 * @param {string} csrfToken - CSRF令牌
 * @returns {object} cookies对象
 */
function buildAuthCookies(authToken, csrfToken) {
	return {
		t: authToken,
		_csrf_token: csrfToken,
	};
}

// ================================
// 时间处理工具
// ================================

/**
 * 将时间字符串转换为时间戳（毫秒）
 * @param {string} timeStr - 时间字符串，格式如 "2025-04-22T08:43:31.000+0000"
 * @returns {number} 毫秒时间戳
 */
function convertTimeToTimestamp(timeStr) {
	try {
		// 解析时间字符串
		const dt = new Date(timeStr.replace("Z", "+00:00"));

		// 转换为中国时间（UTC+8）
		const chinaTime = new Date(dt.getTime() + 8 * 60 * 60 * 1000);

		// 转换为时间戳（毫秒）
		return chinaTime.getTime();
	} catch (error) {
		throw new Error(`时间转换失败: ${error.message}`);
	}
}

/**
 * 格式化时长显示
 * @param {number} seconds - 秒数
 * @returns {string} 格式化的时长字符串
 */
function formatDuration(seconds) {
	try {
		if (seconds < 60) {
			return `${seconds}秒`;
		} else if (seconds < 3600) {
			const minutes = Math.floor(seconds / 60);
			const remainingSeconds = seconds % 60;
			if (remainingSeconds === 0) {
				return `${minutes}分钟`;
			} else {
				return `${minutes}分${remainingSeconds}秒`;
			}
		} else {
			const hours = Math.floor(seconds / 3600);
			const remainingMinutes = Math.floor((seconds % 3600) / 60);
			const remainingSeconds = seconds % 60;

			let result = `${hours}小时`;
			if (remainingMinutes > 0) {
				result += `${remainingMinutes}分`;
			}
			if (remainingSeconds > 0) {
				result += `${remainingSeconds}秒`;
			}
			return result;
		}
	} catch (error) {
		return `${seconds}秒`;
	}
}

// ================================
// 数据处理工具
// ================================

/**
 * 深度克隆对象
 * @param {any} obj - 要克隆的对象
 * @returns {any} 克隆后的对象
 */
function deepClone(obj) {
	if (obj === null || typeof obj !== "object") {
		return obj;
	}

	if (obj instanceof Date) {
		return new Date(obj.getTime());
	}

	if (obj instanceof Array) {
		return obj.map((item) => deepClone(item));
	}

	if (typeof obj === "object") {
		const cloned = {};
		for (const key in obj) {
			if (obj.hasOwnProperty(key)) {
				cloned[key] = deepClone(obj[key]);
			}
		}
		return cloned;
	}

	return obj;
}

/**
 * 安全的JSON解析
 * @param {string} jsonStr - JSON字符串
 * @param {any} defaultValue - 解析失败时的默认值
 * @returns {any} 解析结果或默认值
 */
function safeJsonParse(jsonStr, defaultValue = null) {
	try {
		return JSON.parse(jsonStr);
	} catch (error) {
		return defaultValue;
	}
}

// ================================
// 导出工具函数
// ================================

module.exports = {
	// 日志工具
	log,
	logInfo,
	logWarn,
	logError,

	// 错误处理工具
	handleError,
	createSuccessResponse,
	createErrorResponse,

	// 参数验证工具
	validateRequired,
	validateType,
	validateEmail,

	// HTTP请求工具
	buildAuthHeaders,
	buildAuthCookies,

	// 时间处理工具
	convertTimeToTimestamp,
	formatDuration,

	// 数据处理工具
	deepClone,
	safeJsonParse,
};

<template>
  <div class="db-container">
    <!-- 表格选择和数据显示 -->
    <div class="db-content">
      <!-- 表格选择区域 (下拉菜单) -->
      <div class="table-selector-header">
        <div class="selector-header">
          <div class="header-title">数据库表查看</div>
          <div class="table-info" v-if="tableData.length > 0">
            共 <span class="count">{{ tableData.length }}</span> 条记录
          </div>
        </div>
        <div class="table-dropdown">
          <div class="table-tags">
            <div
              v-for="(table, index) in tableList"
              :key="index"
              class="table-tag"
              :class="{ active: currentTable === table }"
              @click="selectTable(table)"
            >
              {{ table }}
            </div>
          </div>
        </div>
      </div>

      <!-- 数据显示区域 -->
      <div class="data-display">
        <div class="table-actions" v-if="currentTable">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" placeholder="搜索数据..." v-model="searchKeyword" @input="handleSearch" />
          </div>

          <!-- 显示隐藏列按钮 -->
          <div class="hidden-columns-action" v-if="hiddenColumns.length > 0">
            <div class="hidden-columns-info">当前隐藏了 {{ hiddenColumns.length }} 列</div>
            <button class="reset-columns-btn" @click="resetColumns"><i class="fas fa-columns"></i> 显示全部列</button>
          </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-state" v-if="loading">
          <div class="loading-spinner"></div>
          <div class="loading-text">加载数据中...</div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" v-else-if="!currentTable">
          <div class="empty-icon">
            <i class="fas fa-database"></i>
          </div>
          <div class="empty-text">请选择一个数据表查看详细数据</div>
        </div>

        <!-- 无数据状态 -->
        <div class="empty-state" v-else-if="currentTable && tableData.length === 0">
          <div class="empty-icon">
            <i class="fas fa-table"></i>
          </div>
          <div class="empty-text">当前表无数据</div>
        </div>

        <!-- 数据表格 -->
        <div class="table-container" v-else-if="currentTable && tableData.length > 0">
          <div class="table-wrapper">
            <table class="data-table">
              <thead>
                <tr>
                  <th
                    v-for="(field, index) in tableFields"
                    :key="index"
                    @click="toggleColumnVisibility(field)"
                    :class="{ 'column-hidden': isColumnHidden(field) }"
                    :title="isColumnHidden(field) ? '点击显示此列' : '点击隐藏此列'"
                    v-show="!isColumnHidden(field)"
                  >
                    {{ field }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(item, rowIndex) in displayedData"
                  :key="rowIndex"
                  :class="{ 'row-highlighted': selectedRowIndex === rowIndex }"
                  @click="selectRow(rowIndex)"
                >
                  <td
                    v-for="(field, fieldIndex) in tableFields"
                    :key="fieldIndex"
                    class="table-cell"
                    v-show="!isColumnHidden(field)"
                  >
                    <div class="cell-content" :title="formatCellValueForTitle(item[field])">
                      {{ formatCellValue(item[field]) }}
                    </div>
                    <div
                      class="cell-expand"
                      v-if="isCellExpandable(item[field])"
                      @click.stop="toggleCellExpand(rowIndex, fieldIndex)"
                    >
                      <i class="fas fa-expand-alt"></i>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 分页 -->
          <div class="pagination">
            <div
              class="page-info"
              @click="
                filteredData.length > pageSize && ((showAllData = !showAllData), !showAllData && (currentPage = 1))
              "
            >
              共
              <span class="count" :class="{ clickable: filteredData.length > pageSize }">{{
                filteredData.length
              }}</span>
              条记录
              <span class="toggle-hint" v-if="filteredData.length > pageSize">
                {{ showAllData ? '(点击分页显示)' : '(点击显示全部)' }}
              </span>
            </div>

            <div class="page-controls" v-if="totalPages > 1 && !showAllData">
              <div
                class="page-btn"
                :class="{ disabled: currentPage === 1 }"
                @click="currentPage !== 1 && goToPage(currentPage - 1)"
              >
                <i class="fas fa-chevron-left"></i>
              </div>

              <div class="page-numbers">
                <!-- 首页按钮 -->
                <div class="page-number" :class="{ active: currentPage === 1 }" @click="goToPage(1)">1</div>

                <!-- 省略号 -->
                <div class="page-ellipsis" v-if="currentPage > 4">...</div>

                <!-- 中间的页码 -->
                <div
                  v-for="page in middlePages"
                  :key="page"
                  class="page-number"
                  :class="{ active: currentPage === page }"
                  @click="goToPage(page)"
                >
                  {{ page }}
                </div>

                <!-- 省略号 -->
                <div class="page-ellipsis" v-if="totalPages > 5 && currentPage < totalPages - 3">...</div>

                <!-- 末页按钮 -->
                <div
                  v-if="totalPages > 1"
                  class="page-number"
                  :class="{ active: currentPage === totalPages }"
                  @click="goToPage(totalPages)"
                >
                  {{ totalPages }}
                </div>
              </div>

              <div
                class="page-btn"
                :class="{ disabled: currentPage === totalPages }"
                @click="currentPage !== totalPages && goToPage(currentPage + 1)"
              >
                <i class="fas fa-chevron-right"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 单元格展开弹窗 -->
    <div class="cell-modal" v-if="expandedCell.visible" @click.self="expandedCell.visible = false">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">单元格内容详情</div>
          <div class="modal-close" @click="expandedCell.visible = false">
            <i class="fas fa-times"></i>
          </div>
        </div>
        <div class="modal-body">
          <div v-if="expandedCell.isJson" class="json-content">
            <div v-for="(item, index) in expandedCell.jsonData" :key="index" class="json-item">
              <div class="json-key" :title="item.key">{{ item.key }}</div>
              <div class="json-value" :class="{ 'json-nested': item.isObject }">
                <template v-if="item.isObject">
                  <div class="json-toggle" @click="toggleJsonNode(index)">
                    <i :class="item.expanded ? 'fas fa-caret-down' : 'fas fa-caret-right'"></i>
                    {{ item.expanded ? '{...}' : getObjectPreview(item.value) }}
                  </div>
                  <div v-if="item.expanded" class="json-nested-content">
                    {{ formatJsonValue(item.value) }}
                  </div>
                </template>
                <template v-else>
                  {{ formatJsonSimpleValue(item.value) }}
                </template>
              </div>
            </div>
          </div>
          <pre v-else class="expanded-content">{{ expandedCell.content }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import db from '@/api/database'
import { tableSchema } from '@/api/dataSchema'
import { ref, computed, onMounted, watch, reactive } from 'vue'

// 数据状态
const tableList = ref<string[]>([])
const currentTable = ref<string>('')
const tableData = ref<any[]>([])
const filteredData = ref<any[]>([])
const loading = ref<boolean>(false)
const searchKeyword = ref<string>('')
const currentPage = ref<number>(1)
const pageSize = ref<number>(20)
const showAllData = ref<boolean>(false) // 是否显示所有数据

// 隐藏列状态
const hiddenColumns = ref<string[]>([])

// 单元格展开状态
const expandedCell = reactive({
  visible: false,
  content: '',
  isJson: false,
  jsonData: [] as Array<{ key: string; value: any; isObject: boolean; expanded: boolean }>,
})

// 选中行状态
const selectedRowIndex = ref<number | null>(null)

// 表格字段
const tableFields = computed(() => {
  if (!tableData.value.length) return []
  return Object.keys(tableData.value[0]).filter(
    (key) => key !== '__rowid__' && typeof tableData.value[0][key] !== 'function'
  )
})

// 计算属性：分页后的数据
const displayedData = computed(() => {
  if (showAllData.value) {
    return filteredData.value // 显示所有数据
  }

  const startIndex = (currentPage.value - 1) * pageSize.value
  const endIndex = startIndex + pageSize.value
  return filteredData.value.slice(startIndex, endIndex)
})

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(filteredData.value.length / pageSize.value)
})

// 计算中间显示的页码
const middlePages = computed(() => {
  const result = []
  if (totalPages.value <= 7) {
    // 总页数较少时显示所有页码
    for (let i = 2; i < totalPages.value; i++) {
      result.push(i)
    }
  } else {
    // 总页数较多时，显示当前页附近的页码
    const min = Math.max(2, currentPage.value - 2)
    const max = Math.min(totalPages.value - 1, currentPage.value + 2)

    for (let i = min; i <= max; i++) {
      result.push(i)
    }
  }
  return result
})

// 检查列是否被隐藏
const isColumnHidden = (columnName: string): boolean => {
  return hiddenColumns.value.includes(columnName)
}

// 切换列可见性
const toggleColumnVisibility = (columnName: string) => {
  const index = hiddenColumns.value.indexOf(columnName)
  if (index >= 0) {
    // 如果列已经被隐藏，则显示它
    hiddenColumns.value.splice(index, 1)
  } else {
    // 如果列可见，则隐藏它
    hiddenColumns.value.push(columnName)
  }
}

// 重置所有列可见性
const resetColumns = () => {
  hiddenColumns.value = []
}

// 处理表格选择变化
const handleTableChange = () => {
  if (currentTable.value) {
    loadTableData(currentTable.value)
  }
}

// 选择数据表
const selectTable = async (tableName: string) => {
  currentTable.value = tableName
  currentPage.value = 1
  searchKeyword.value = ''
  selectedRowIndex.value = null
  hiddenColumns.value = [] // 重置隐藏的列
  await loadTableData(tableName)
}

// 选择行
const selectRow = (rowIndex: number) => {
  selectedRowIndex.value = selectedRowIndex.value === rowIndex ? null : rowIndex
}

// 加载表数据
const loadTableData = async (tableName: string) => {
  loading.value = true
  tableData.value = []
  filteredData.value = []
  selectedRowIndex.value = null
  hiddenColumns.value = [] // 重置隐藏的列

  try {
    const res = await db.table(tableName).where().toArray()
    tableData.value = res
    filteredData.value = [...res]
  } catch (error) {
    console.error(`获取${tableName}表数据失败:`, error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    filteredData.value = [...tableData.value]
  } else {
    const keyword = searchKeyword.value.toLowerCase()
    filteredData.value = tableData.value.filter((item) => {
      return Object.values(item).some((val) => {
        if (val === null || val === undefined) return false
        return String(val).toLowerCase().includes(keyword)
      })
    })
  }
  currentPage.value = 1
  selectedRowIndex.value = null
}

// 页面导航
const goToPage = (page: number) => {
  if (page < 1 || page > totalPages.value) return
  currentPage.value = page
  selectedRowIndex.value = null
}

// 判断单元格内容是否可展开
const isCellExpandable = (value: any): boolean => {
  if (value === null || value === undefined) return false

  if (typeof value === 'object') return true

  if (typeof value === 'string' && value.length > 50) return true

  return false
}

// 判断是否是 JSON 格式的字符串
const isJsonString = (str: string): boolean => {
  if (typeof str !== 'string') return false
  try {
    const obj = JSON.parse(str)
    return typeof obj === 'object' && obj !== null
  } catch (e) {
    return false
  }
}

// 获取对象预览
const getObjectPreview = (obj: any): string => {
  if (Array.isArray(obj)) {
    return `Array(${obj.length})`
  }

  const keys = Object.keys(obj)
  if (keys.length === 0) return '{}'

  return `{${keys[0]}: ${formatJsonSimpleValue(obj[keys[0]])}, ...}`
}

// 处理 JSON 对象格式化
const processJsonForDisplay = (json: any): Array<{ key: string; value: any; isObject: boolean; expanded: boolean }> => {
  const result = []

  for (const key of Object.keys(json)) {
    const value = json[key]
    const isObject = value !== null && typeof value === 'object'

    result.push({
      key,
      value,
      isObject,
      expanded: false,
    })
  }

  return result
}

// 切换 JSON 节点展开状态
const toggleJsonNode = (index: number) => {
  if (expandedCell.jsonData[index]) {
    expandedCell.jsonData[index].expanded = !expandedCell.jsonData[index].expanded
  }
}

// 格式化 JSON 简单值显示
const formatJsonSimpleValue = (value: any): string => {
  if (value === null) return 'null'
  if (value === undefined) return 'undefined'
  if (typeof value === 'string') return `"${value}"`
  return String(value)
}

// 格式化 JSON 对象/数组值
const formatJsonValue = (value: any): string => {
  try {
    return JSON.stringify(value, null, 2)
  } catch (e) {
    return String(value)
  }
}

// 切换单元格展开状态
const toggleCellExpand = (rowIndex: number, fieldIndex: number) => {
  const field = tableFields.value[fieldIndex]
  const value = displayedData.value[rowIndex][field]

  expandedCell.isJson = false
  expandedCell.jsonData = []

  if (typeof value === 'object' && value !== null) {
    // 处理对象类型
    expandedCell.isJson = true
    expandedCell.jsonData = processJsonForDisplay(value)
    expandedCell.content = JSON.stringify(value, null, 2)
  } else if (typeof value === 'string' && isJsonString(value)) {
    // 处理 JSON 字符串
    try {
      const jsonObj = JSON.parse(value)
      expandedCell.isJson = true
      expandedCell.jsonData = processJsonForDisplay(jsonObj)
      expandedCell.content = value
    } catch (e) {
      expandedCell.content = String(value)
    }
  } else {
    // 处理其他类型
    expandedCell.content = String(value)
  }

  expandedCell.visible = true
}

// 格式化单元格值（展示用）
const formatCellValue = (value: any): string => {
  if (value === undefined || value === null) {
    return ''
  }

  // 处理对象或数组
  if (typeof value === 'object') {
    return JSON.stringify(value).substring(0, 50) + (JSON.stringify(value).length > 50 ? '...' : '')
  }

  // 处理日期（ISO 格式）
  if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
    return value.replace('T', ' ').substring(0, 19)
  }

  // 处理长字符串
  if (typeof value === 'string' && value.length > 50) {
    return value.substring(0, 50) + '...'
  }

  return String(value)
}

// 格式化单元格值（用于悬停提示）
const formatCellValueForTitle = (value: any): string => {
  if (value === undefined || value === null) {
    return ''
  }

  // 处理对象或数组
  if (typeof value === 'object') {
    try {
      return JSON.stringify(value)
    } catch (e) {
      return String(value)
    }
  }

  return String(value)
}

// 初始化
const init = async () => {
  const params = getRoute.params()
  console.log('页面加载参数：', params)

  // 获取所有表名
  tableList.value = Object.keys(tableSchema)

  // 页面加载时默认选中第一个数据表
  if (tableList.value.length > 0) {
    selectTable(tableList.value[0])
  }
}

// 监听数据表变化
watch(currentTable, () => {
  searchKeyword.value = ''
  currentPage.value = 1
  selectedRowIndex.value = null
})

// 生命周期钩子
onMounted(init)
</script>

<style scoped lang="scss">
.db-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding: 20rpx;
}

.db-content {
  display: flex;
  flex-direction: column;
}

.table-selector-header {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .header-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .table-info {
      font-size: 24rpx;
      color: #999;

      .count {
        color: #5cadff;
        font-weight: 600;
      }
    }
  }

  .table-dropdown {
    width: 100%;

    .table-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
      margin-top: 10rpx;

      .table-tag {
        padding: 12rpx 20rpx;
        background-color: #f5f7fa;
        border-radius: 8rpx;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 26rpx;
        border: 1px solid #eee;
        text-align: center;
        min-width: 120rpx;

        &.active {
          background-color: #5cadff;
          color: white;
          border-color: #5cadff;
          box-shadow: 0 2rpx 8rpx rgba(92, 173, 255, 0.3);
        }

        &:hover {
          background-color: #e9f4ff;
          transform: translateY(-2rpx);
        }
      }
    }
  }
}

.data-display {
  background-color: white;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .table-actions {
    padding: 20rpx 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 20rpx;
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    justify-content: space-between;
    align-items: center;

    .search-box {
      display: flex;
      align-items: center;
      background-color: #f5f7fa;
      border-radius: 8rpx;
      padding: 0 20rpx;
      height: 70rpx;
      flex: 1;
      min-width: 250rpx;

      i {
        color: #999;
        font-size: 28rpx;
        margin-right: 16rpx;
      }

      input {
        flex: 1;
        height: 100%;
        border: none;
        background: transparent;
        font-size: 26rpx;
        color: #333;

        &:focus {
          outline: none;
        }
      }
    }

    .hidden-columns-action {
      display: flex;
      align-items: center;

      .hidden-columns-info {
        font-size: 24rpx;
        color: #999;
        margin-right: 16rpx;
      }

      .reset-columns-btn {
        height: 60rpx;
        padding: 0 20rpx;
        background-color: #f0f7ff;
        border: 1px solid #d0e8ff;
        color: #5cadff;
        border-radius: 6rpx;
        font-size: 24rpx;
        display: flex;
        align-items: center;
        cursor: pointer;

        i {
          margin-right: 8rpx;
        }

        &:hover {
          background-color: #e0f0ff;
        }
      }
    }
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid rgba(92, 173, 255, 0.3);
    border-top-color: #5cadff;
    border-radius: 50%;
    animation: spin 1s infinite linear;
  }

  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-icon {
    font-size: 80rpx;
    color: #ddd;
    margin-bottom: 20rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.table-container {
  height: calc(100vh - 300rpx);
  display: flex;
  flex-direction: column;
}

.table-wrapper {
  flex: 1;
  overflow: auto;
  border: 1px solid #eee;
  border-radius: 8rpx;

  .data-table {
    width: 100%;
    border-collapse: collapse;

    th,
    td {
      padding: 20rpx;
      text-align: left;
      font-size: 24rpx;
      border-bottom: 1px solid #eee;
      min-width: 150rpx;
    }

    th {
      background-color: #f5f7fa;
      font-weight: 600;
      color: #333;
      position: sticky;
      top: 0;
      z-index: 10;
      cursor: pointer;
      user-select: none;
      transition: background-color 0.2s;
      position: relative;
      padding-right: 40rpx;

      &:hover {
        background-color: #e9f4ff;
      }

      &.column-hidden {
        background-color: #f0f0f0;
        color: #999;
        font-style: italic;
        border-bottom-style: dashed;
      }
    }

    tr {
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f9f9f9;
      }

      &.row-highlighted {
        background-color: #ecf6ff;

        .table-cell {
          border-color: #d0e8ff;
        }
      }
    }

    .table-cell {
      position: relative;
      max-width: 300rpx;
      transition: all 0.2s;

      .cell-content {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-right: 30rpx;
      }

      .cell-expand {
        position: absolute;
        right: 10rpx;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        color: #5cadff;
        font-size: 22rpx;
        opacity: 0.7;

        &:hover {
          opacity: 1;
        }
      }
    }
  }
}

.pagination {
  margin-top: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20rpx;

  .page-info {
    font-size: 24rpx;
    color: #999;
    margin-right: 16rpx;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6rpx;
    padding: 6rpx 12rpx;
    border-radius: 6rpx;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f5f7fa;
    }

    .count {
      color: #5cadff;
      font-weight: 600;

      &.clickable {
        text-decoration: underline;
        cursor: pointer;
      }
    }

    .toggle-hint {
      color: #999;
      font-size: 22rpx;
    }
  }

  .page-controls {
    display: flex;
    align-items: center;
    gap: 16rpx;

    .page-btn {
      width: 70rpx;
      height: 70rpx;
      border-radius: 8rpx;
      border: 1px solid #ddd;
      background-color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s;
      color: #666;

      &:hover:not(.disabled) {
        background-color: #f5f7fa;
        border-color: #5cadff;
        color: #5cadff;
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .page-numbers {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .page-number {
        min-width: 60rpx;
        height: 60rpx;
        border-radius: 6rpx;
        border: 1px solid #eee;
        background-color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 24rpx;
        transition: all 0.2s;
        padding: 0 10rpx;

        &:hover:not(.active) {
          border-color: #5cadff;
          color: #5cadff;
          transform: translateY(-2rpx);
        }

        &.active {
          background-color: #5cadff;
          color: white;
          border-color: #5cadff;
          font-weight: bold;
          box-shadow: 0 2rpx 8rpx rgba(92, 173, 255, 0.3);
        }
      }

      .page-ellipsis {
        min-width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        font-size: 24rpx;
        letter-spacing: 2rpx;
      }
    }
  }
}

.cell-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;

  .modal-content {
    width: 90%;
    max-width: 800rpx;
    max-height: 80vh;
    background-color: white;
    border-radius: 16rpx;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .modal-header {
      padding: 20rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #eee;

      .modal-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
      }

      .modal-close {
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #999;

        &:hover {
          color: #333;
        }
      }
    }

    .modal-body {
      padding: 20rpx;
      overflow-y: auto;
      max-height: calc(80vh - 80rpx);

      .expanded-content {
        white-space: pre-wrap;
        word-break: break-word;
        font-size: 26rpx;
        line-height: 1.6;
        color: #333;
        margin: 0;
      }

      .json-content {
        font-size: 26rpx;
        line-height: 1.6;
        color: #333;
        width: 100%;
        display: table;
        table-layout: fixed; /* 固定表格布局 */
        border-collapse: collapse;

        .json-item {
          padding: 6rpx 0;
          display: table-row;

          .json-key {
            font-weight: 600;
            color: #0b6efd;
            display: table-cell;
            width: 25%; /* 减小 key 宽度占比 */
            max-width: 200rpx; /* 设置最大宽度 */
            padding: 8rpx 16rpx 8rpx 0;
            vertical-align: top;
            text-align: right;
            border-right: 1px solid #f0f0f0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: default; /* 显示提示鼠标样式 */
          }

          .json-value {
            color: #333;
            display: table-cell;
            padding: 8rpx 0 8rpx 16rpx;
            vertical-align: top;
            width: 75%; /* 增加 value 宽度占比 */
            word-break: break-all; /* 确保长单词也会断行 */

            &.json-nested {
              cursor: pointer;
            }
          }

          .json-toggle {
            cursor: pointer;
            color: #666;

            i {
              margin-right: 6rpx;
              font-size: 20rpx;
            }
          }

          .json-nested-content {
            margin-top: 10rpx;
            margin-left: 20rpx;
            padding: 10rpx;
            border-left: 2px solid #eee;
            white-space: pre-wrap;
            word-break: break-word;
          }

          &:nth-child(odd) {
            background-color: #f9f9f9;
          }

          &:hover {
            background-color: #f0f7ff;
          }
        }
      }
    }
  }
}
</style>

<template>
  <view v-if="visible" class="week-overview-modal" @click="closeModal">
    <view class="week-overview-content" @click.stop>
      <text class="overview-title">本周任务概览</text>

      <!-- 标签页组件 -->
      <view class="tabs-container">
        <u-tabs
          :list="displayTabList"
          :is-scroll="false"
          :current="currentTab"
          @change="handleTabChange"
          active-color="var(--color-primary)"
          inactive-color="var(--color-gray-600)"
          bar-width="60"
        ></u-tabs>
      </view>

      <!-- 标签页内容区域 -->
      <view class="tab-content">
        <!-- 周概览标签页 -->
        <view v-if="currentTab === 0 && showOverview" class="tab-pane">
          <view class="overview-stats">
            <text class="stat-value">{{ weekOverviewData.totalTasks }}</text>
            <text class="stat-label">总任务</text>
          </view>
          <view class="overview-stats">
            <view class="stat-item">
              <text class="stat-value">{{ weekOverviewData.completedTasks }}</text>
              <text class="stat-label">已完成</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{ weekOverviewData.pendingTasks }}</text>
              <text class="stat-label">待完成</text>
            </view>
          </view>

          <view class="overview-by-group">
            <text class="overview-subtitle">按目标分类</text>
            <view class="overview-group-list">
              <view class="overview-group-item" v-for="(group, index) in weekOverviewData.groupStats" :key="index">
                <view class="overview-group-left">
                  <view class="group-color" :style="{ backgroundColor: group.color }"></view>
                  <text class="overview-group-title">{{ group.title }}</text>
                </view>
                <text class="overview-group-count">{{ group.count }} 项</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 周目标标签页 -->
        <view v-if="(currentTab === 1 && showOverview) || (currentTab === 0 && !showOverview)" class="tab-pane">
          <view class="content-area">
            <text v-if="weekGoal" class="content-text">{{ weekGoal }}</text>
            <text v-else class="placeholder-text">暂无周目标，点击下方按钮添加</text>
          </view>
          <view class="edit-btn" @click="handleEdit('goal')">
            <i class="fas fa-edit"></i>
            <text>编辑</text>
          </view>
        </view>

        <!-- 周复盘标签页 -->
        <view v-if="(currentTab === 2 && showOverview) || (currentTab === 1 && !showOverview)" class="tab-pane">
          <view class="content-area">
            <text v-if="weekReview" class="content-text">{{ weekReview }}</text>
            <text v-else class="placeholder-text">暂无周复盘，点击下方按钮添加</text>
          </view>
          <view class="edit-btn" @click="handleEdit('review')">
            <i class="fas fa-edit"></i>
            <text>编辑</text>
          </view>
        </view>
      </view>

      <view class="close-btn" @click="closeModal">关闭</view>
    </view>
  </view>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted, watch, computed } from 'vue'
import dayjs from 'dayjs'
import { getTaskListApi } from '@/api/task'
import { getOkrListApi } from '@/api/okr'
import { getMemo } from '@/api/memo'
import { router, getCurrentWeek } from '@/utils/tools'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  currentDate: {
    type: String,
    default: () => dayjs().format('YYYY-MM-DD')
  },
  showOverview: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible'])

// 标签页数据
const tabList = ref([
  { name: '周概览' },
  { name: '周目标' },
  { name: '周复盘' }
])

// 根据 showOverview 属性动态计算要显示的标签页
const displayTabList = computed(() => {
  if (props.showOverview) {
    return tabList.value
  } else {
    return tabList.value.filter((_, index) => index > 0)
  }
})

const currentTab = ref(0) // 默认选中"周概览"标签页

// 周目标和周复盘数据
const weekGoal = ref('')
const weekReview = ref('')
const goalLoading = ref(false)
const reviewLoading = ref(false)
const currentWeek = ref('')


// 标签页切换处理
const handleTabChange = (index) => {
  currentTab.value = index
  
  // 根据是否显示概览标签页调整加载逻辑
  if (props.showOverview) {
    // 切换到周目标标签页时加载周目标数据
    if (index === 1 && !weekGoal.value) {
      loadWeekGoal()
    }
    
    // 切换到周复盘标签页时加载周复盘数据
    if (index === 2 && !weekReview.value) {
      loadWeekReview()
    }
  } else {
    // 不显示概览标签页时，索引需要调整
    // 切换到周目标标签页时加载周目标数据
    if (index === 0 && !weekGoal.value) {
      loadWeekGoal()
    }
    
    // 切换到周复盘标签页时加载周复盘数据
    if (index === 1 && !weekReview.value) {
      loadWeekReview()
    }
  }
}

// 获取当前周数
const updateCurrentWeek = () => {
  // 根据传入的日期计算所在周数
  const date = dayjs(props.currentDate)
  const year = date.year()
  const week = date.week().toString().padStart(2, '0')
  currentWeek.value = `${year}-${week}`
}

// 处理编辑按钮点击
const handleEdit = (type) => {
  // 跳转到编辑页面，传递类型参数和当前周数
  router.push('/pages/okr/weekEdit', {
    type: type, // 'goal' 或 'review'
    date: currentWeek.value
  })
  
  // 关闭当前弹窗
  closeModal()
}

// 加载周目标数据
const loadWeekGoal = async () => {
  try {
    goalLoading.value = true
    const query = `type == "weekGoal" && date == "${currentWeek.value}"`
    const result = await getMemo(query)
    
    if (result && result.length > 0) {
      weekGoal.value = result[0].content
    } else {
      weekGoal.value = ''
    }
  } catch (error) {
    console.error('加载周目标失败：', error)
  } finally {
    goalLoading.value = false
  }
}

// 加载周复盘数据
const loadWeekReview = async () => {
  try {
    reviewLoading.value = true
    const query = `type == "weekReview" && date == "${currentWeek.value}"`
    const result = await getMemo(query)
    
    if (result && result.length > 0) {
      weekReview.value = result[0].content
    } else {
      weekReview.value = ''
    }
  } catch (error) {
    console.error('加载周复盘失败：', error)
  } finally {
    reviewLoading.value = false
  }
}

// 周概览数据
const weekOverviewData = ref({
  startDate: '',
  endDate: '',
  totalTasks: 0,
  completedTasks: 0,
  pendingTasks: 0,
  groupStats: [],
})

// 关闭弹窗
const closeModal = () => {
  emit('update:visible', false)
  // 重置标签页状态
  currentTab.value = 0 // 重置为默认选中"周概览"或"周目标"
  // 重置周概览数据
  weekOverviewData.value = {
    startDate: '',
    endDate: '',
    totalTasks: 0,
    completedTasks: 0,
    pendingTasks: 0,
    groupStats: [],
  }
  // 重置周目标和周复盘数据
  weekGoal.value = ''
  weekReview.value = ''
}

// 根据字符串生成一致的颜色
const getRandomColor = (str) => {
  // 预定义的颜色列表
  const colors = [
    'var(--color-primary)',
    'var(--color-success)',
    'var(--color-warning)',
    'var(--color-danger)',
    'var(--color-info)',
  ]

  // 使用字符串生成一个一致的索引
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash)
  }

  // 使用哈希值选择颜色
  const index = Math.abs(hash) % colors.length
  return colors[index]
}

// 加载周概览数据
const loadWeekOverviewData = async () => {
  if (!props.visible || !props.showOverview) return
  
  console.log(`[DEBUG] 加载周概览数据`)
  try {
    // 计算当前周的开始和结束日期
    const currentWeekStart = dayjs(props.currentDate).startOf('week')
    const currentWeekEnd = dayjs(props.currentDate).endOf('week')

    weekOverviewData.value.startDate = currentWeekStart.format('YYYY 年 MM 月 DD 日')
    weekOverviewData.value.endDate = currentWeekEnd.format('MM 月 DD 日')

    const startDateStr = currentWeekStart.format('YYYY-MM-DD')
    const endDateStr = currentWeekEnd.format('YYYY-MM-DD')
    const allWeekTasks = await getTaskListApi(`type == 'kr' && startDate <= '${endDateStr}'`)
    console.log(`[DEBUG] 获取到本周原始任务：${allWeekTasks.length} 个`)

    // 直接使用所有任务，不进行循环筛选
    const weekTasks = allWeekTasks
    console.log(`[DEBUG] 本周所有任务数量 (含重复): ${weekTasks.length}`)

    // 去重（同一个任务可能在多天都出现）
    const uniqueTasks = []
    const taskIds = new Set()
    weekTasks.forEach((task) => {
      if (!taskIds.has(task._id)) {
        taskIds.add(task._id)
        uniqueTasks.push(task)
      }
    })
    console.log(`[DEBUG] 去重后本周任务数量：${uniqueTasks.length}`)

    // 查询父任务和目标信息
    const parentIdsWeek = uniqueTasks.map((item) => item.parentId).filter((id) => id && id !== '')
    const uniqueParentIdsWeek = [...new Set(parentIdsWeek)]

    const okrIdsWeek = uniqueTasks.map((item) => item.okrId).filter((id) => id && id !== '')
    const uniqueOkrIdsWeek = [...new Set(okrIdsWeek)]

    let parentListWeek = []
    if (uniqueParentIdsWeek.length > 0) {
      try {
        const idsQuery = uniqueParentIdsWeek.map((id) => `"${id}"`).join(',')
        parentListWeek = await getTaskListApi(`_id, anyOf(${idsQuery})`)
      } catch (parentError) {
        console.error('[ERROR] 获取周概览父任务失败', parentError)
        parentListWeek = []
      }
    }

    let targetListWeek = []
    if (uniqueOkrIdsWeek.length > 0) {
      try {
        const idsQuery = uniqueOkrIdsWeek.map((id) => `"${id}"`).join(',')
        targetListWeek = await getOkrListApi(`_id, anyOf(${idsQuery})`)
      } catch (targetError) {
        console.error('[ERROR] 获取周概览目标失败', targetError)
        targetListWeek = []
      }
    }

    const tasksWithRelationsWeek = uniqueTasks.map((item) => {
      try {
        const parent = parentListWeek.find((p) => p._id === item.parentId) || null
        const target = targetListWeek.find((t) => t._id === item.okrId) || null
        return { ...item, parent, target }
      } catch (relationError) {
        console.error(`[ERROR] 关联周概览任务时出错:`, relationError)
        return { ...item, parent: null, target: null }
      }
    })

    // 统计任务数据
    weekOverviewData.value.totalTasks = tasksWithRelationsWeek.length
    weekOverviewData.value.completedTasks = tasksWithRelationsWeek.filter((task) => task.status === 1).length
    weekOverviewData.value.pendingTasks = tasksWithRelationsWeek.filter((task) => task.status === 0).length

    // 按目标分组统计
    const groupStats = {}
    tasksWithRelationsWeek.forEach((task) => {
      const groupTitle = task.target ? task.target.title : '未分组'
      if (!groupStats[groupTitle]) {
        groupStats[groupTitle] = {
          title: groupTitle,
          color: getRandomColor(groupTitle),
          count: 0,
        }
      }
      groupStats[groupTitle].count++
    })
    weekOverviewData.value.groupStats = Object.values(groupStats)
  } catch (error) {
    console.error('[ERROR] 获取周概览数据失败：', error)
    uni.showToast({
      title: '获取周概览数据失败',
      icon: 'none',
    })
  }
}

// 监听弹窗显示状态变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    updateCurrentWeek()
    if (props.showOverview) {
      loadWeekOverviewData()
    }
    loadWeekGoal()
    loadWeekReview()
  }
})

// 监听当前日期变化
watch(() => props.currentDate, (newVal) => {
  if (props.visible) {
    updateCurrentWeek()
    if (props.showOverview) {
      loadWeekOverviewData()
    }
    loadWeekGoal()
    loadWeekReview()
  }
})

// 监听 showOverview 属性变化
watch(() => props.showOverview, (newVal) => {
  if (props.visible) {
    // 如果从不显示概览变为显示概览，需要加载数据
    if (newVal) {
      loadWeekOverviewData()
    }
    // 重置当前标签页索引，确保显示正确的标签页
    currentTab.value = 0
  }
})
</script>

<style lang="scss">
// 周概览弹窗
.week-overview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.week-overview-content {
  width: 600rpx;
  height: 900rpx; /* 设置固定高度 */
  background-color: var(--color-white);
  border-radius: var(--rounded-lg);
  padding: 40rpx;
  display: flex;
  flex-direction: column;
}

.overview-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--color-gray-800);
  text-align: center;
  margin-bottom: 20rpx;
}

.overview-stats {
  display: flex;
  justify-content: space-around;
  margin: 30rpx 0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--color-primary);
}

.stat-label {
  font-size: 24rpx;
  color: var(--color-gray-600);
  margin-top: 8rpx;
}

.overview-by-group {
  margin-top: 24rpx;
}

.overview-subtitle {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--color-gray-700);
  margin-bottom: 16rpx;
}

.overview-group-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.overview-group-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.overview-group-left {
  display: flex;
  align-items: center;
}

.group-color {
  width: 16rpx;
  height: 16rpx;
  border-radius: var(--rounded-full);
  margin-right: 12rpx;
}

.overview-group-title {
  font-size: 28rpx;
  color: var(--color-gray-700);
}

.overview-group-count {
  font-size: 28rpx;
  color: var(--color-gray-600);
}

.close-btn {
  margin-top: 40rpx;
  padding: 16rpx;
  background-color: var(--color-gray-100);
  border-radius: var(--rounded-md);
  text-align: center;
  color: var(--color-gray-700);
  font-size: 28rpx;
  font-weight: 500;
}

// 标签页组件样式
.tabs-container {
  margin: 20rpx 0;
  border-bottom: 1px solid var(--color-gray-200);
}

.tab-content {
  margin-top: 20rpx;
  flex: 1;
  overflow-y: auto; /* 内容超出时显示滚动条 */
}

.tab-pane {
  padding: 10rpx 0;
}

.content-area {
  background-color: var(--color-gray-50);
  border-radius: var(--rounded-md);
  padding: 30rpx;
  min-height: 200rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-text {
  color: var(--color-gray-400);
  font-size: 28rpx;
  font-style: italic;
  text-align: center;
}

.content-text {
  color: var(--color-gray-800);
  font-size: 28rpx;
  text-align: left;
  white-space: pre-wrap;
  width: 100%;
}

.edit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 24rpx;
  background-color: var(--color-primary);
  border-radius: var(--rounded-md);
  color: var(--color-white);
  font-size: 28rpx;
  font-weight: 500;
  width: 160rpx;
  margin: 0 auto;
  transition: opacity 0.2s;

  &:active {
    opacity: 0.8;
  }

  i {
    margin-right: 8rpx;
    font-size: 24rpx;
  }
}
</style>

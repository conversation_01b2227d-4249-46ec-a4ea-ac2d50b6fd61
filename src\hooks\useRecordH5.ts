import { ref, onUnmounted, Ref } from 'vue'
import RecordRTC, { StereoAudioRecorder } from 'recordrtc'
import { UseRecordOptions, UseRecordReturn, RecordResult } from './types/record'
import { RecordError, RecordErrorType } from './utils/recordErrors'

/**
 * H5环境下的录音Hook实现
 * @param options 录音配置选项
 * @returns 录音控制对象
 */
export default function useRecordH5(options?: UseRecordOptions): UseRecordReturn {
  // 默认录音配置
  const defaultOptions: UseRecordOptions = {
    sampleRate: 44100,
    numberOfAudioChannels: 1,
    desiredSampleRate: 16000,
    mimeType: 'audio/wav',
    maxDuration: 60000, // 默认 60 秒
    checkForInactiveTracks: true,
    disableLogs: true,
  }

  const mergedOptions = { ...defaultOptions, ...options }

  // 录音状态
  const isRecording = ref<boolean>(false)
  const isPaused = ref<boolean>(false)
  const duration = ref<number>(0)
  const volume = ref<number>(0)
  const recordBlob = ref<Blob | null>(null)
  const recordURL = ref<string>('')

  // 录音器和音频流
  let recorder: RecordRTC | null = null
  let audioStream: MediaStream | null = null
  let audioContext: AudioContext | null = null
  let analyser: AnalyserNode | null = null
  let durationTimer: number | null = null
  let volumeTimer: number | null = null

  // 开始录音
  const startRecording = async (): Promise<void> => {
    try {
      // 如果已经在录音，先停止之前的录音
      if (isRecording.value) {
        await stopRecording()
      }

      // 重置状态
      resetState()

      // 请求音频流
      audioStream = await navigator.mediaDevices.getUserMedia({ audio: true }).catch((error) => {
        if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
          throw RecordError.permissionDenied(error.message)
        }
        throw RecordError.initializationFailed(error.message)
      })

      // 创建录音实例
      recorder = new RecordRTC(audioStream, {
        type: 'audio',
        mimeType: mergedOptions.mimeType,
        sampleRate: mergedOptions.sampleRate,
        desiredSampleRate: mergedOptions.desiredSampleRate,
        numberOfAudioChannels: mergedOptions.numberOfAudioChannels,
        checkForInactiveTracks: mergedOptions.checkForInactiveTracks,
        disableLogs: mergedOptions.disableLogs,
        recorderType: StereoAudioRecorder,
        timeSlice: 1000, // 每秒触发一次 ondataavailable 事件
        ondataavailable: () => {
          // 可以在这里获取实时数据
        },
      })

      // 设置音频分析器
      setupAudioAnalyser()

      // 开始录音
      recorder.startRecording()

      // 更新状态
      isRecording.value = true
      isPaused.value = false

      // 开始计时
      startTimers()

      // 设置最大录音时长
      if (mergedOptions.maxDuration) {
        setTimeout(() => {
          if (isRecording.value && !isPaused.value) {
            stopRecording().catch(console.error)
          }
        }, mergedOptions.maxDuration)
      }
    } catch (error: any) {
      console.error('启动录音失败：', error)
      throw error instanceof RecordError ? error : RecordError.initializationFailed(error.message)
    }
  }

  // 暂停录音
  const pauseRecording = (): void => {
    if (recorder && isRecording.value && !isPaused.value) {
      recorder.pauseRecording()
      isPaused.value = true

      // 暂停计时器
      stopTimers()
    }
  }

  // 继续录音
  const resumeRecording = (): void => {
    if (recorder && isRecording.value && isPaused.value) {
      recorder.resumeRecording()
      isPaused.value = false

      // 继续计时
      startTimers()
    }
  }

  // 停止录音
  const stopRecording = (): Promise<string> => {
    return new Promise((resolve, reject) => {
      if (!recorder || !isRecording.value) {
        reject(new RecordError(RecordErrorType.RECORDING_FAILED, '没有正在进行的录音'))
        return
      }

      // 停止计时器
      stopTimers()

      const recorderInstance = recorder // 保存当前recorder引用

      // 停止录音
      recorderInstance.stopRecording(() => {
        try {
          // 获取录音结果
          const blob = recorderInstance.getBlob()
          recordBlob.value = blob

          // 创建音频URL
          if (recordURL.value) {
            URL.revokeObjectURL(recordURL.value)
          }
          const fileURL = URL.createObjectURL(blob)
          recordURL.value = fileURL

          // 更新状态
          isRecording.value = false
          isPaused.value = false

          // 停止音频流
          stopAudioStream()

          // 返回文件URL而不是Blob
          resolve(fileURL)
        } catch (error: any) {
          reject(RecordError.recordingFailed(error.message))
        }
      })
    })
  }

  // 播放录音
  const playRecording = (): void => {
    if (recordURL.value) {
      const audio = new Audio(recordURL.value)
      audio.play().catch((error) => {
        console.error('播放录音失败：', error)
      })
    }
  }

  // 获取音频波形数据
  const getAudioWaveform = (): Uint8Array | null => {
    if (!analyser) return null

    const dataArray = new Uint8Array(analyser.frequencyBinCount)
    analyser.getByteTimeDomainData(dataArray)
    return dataArray
  }

  // 取消录音
  const cancelRecording = (): void => {
    if (recorder && isRecording.value) {
      // 停止计时器
      stopTimers()

      // 停止录音但不保存
      recorder.stopRecording(() => {
        // 重置状态
        resetState()

        // 停止音频流
        stopAudioStream()
      })
    }
  }

  // 重置状态
  const resetState = (): void => {
    isRecording.value = false
    isPaused.value = false
    duration.value = 0
    volume.value = 0
    recordBlob.value = null

    if (recordURL.value) {
      URL.revokeObjectURL(recordURL.value)
      recordURL.value = ''
    }
  }

  // 设置音频分析器
  const setupAudioAnalyser = (): void => {
    if (!audioStream) return

    try {
      // 创建音频上下文
      audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

      // 创建分析器
      analyser = audioContext.createAnalyser()
      analyser.fftSize = 256

      // 连接音频流
      const source = audioContext.createMediaStreamSource(audioStream)
      source.connect(analyser)

      // 不连接到扬声器，避免回声
      // analyser.connect(audioContext.destination)
    } catch (error) {
      console.error('设置音频分析器失败：', error)
    }
  }

  // 停止音频流
  const stopAudioStream = (): void => {
    if (audioStream) {
      audioStream.getTracks().forEach((track) => track.stop())
      audioStream = null
    }

    if (audioContext) {
      audioContext.close().catch(console.error)
      audioContext = null
      analyser = null
    }
  }

  // 开始计时器
  const startTimers = (): void => {
    // 录音时长计时器
    if (!durationTimer) {
      durationTimer = window.setInterval(() => {
        duration.value += 100
      }, 100)
    }

    // 音量计算计时器
    if (!volumeTimer && analyser) {
      volumeTimer = window.setInterval(() => {
        if (analyser) {
          const dataArray = new Uint8Array(analyser.frequencyBinCount)
          analyser.getByteFrequencyData(dataArray)

          // 计算平均音量
          const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length

          // 将音量范围从 0-255 映射到 0-100
          volume.value = Math.round((average / 255) * 100)
        }
      }, 100)
    }
  }

  // 停止计时器
  const stopTimers = (): void => {
    if (durationTimer) {
      clearInterval(durationTimer)
      durationTimer = null
    }

    if (volumeTimer) {
      clearInterval(volumeTimer)
      volumeTimer = null
    }
  }

  // 清理资源
  const destroy = (): void => {
    // 停止录音
    if (isRecording.value) {
      cancelRecording()
    }

    // 停止计时器
    stopTimers()

    // 清理 URL
    if (recordURL.value) {
      URL.revokeObjectURL(recordURL.value)
      recordURL.value = ''
    }

    // 停止音频流
    stopAudioStream()

    // 销毁录音器
    if (recorder) {
      recorder.destroy()
      recorder = null
    }
  }

  // 组件卸载时自动清理
  onUnmounted(() => {
    destroy()
  })

  return {
    // 状态
    isRecording: isRecording as Readonly<Ref<boolean>>,
    isPaused: isPaused as Readonly<Ref<boolean>>,
    duration: duration as Readonly<Ref<number>>,
    volume: volume as Readonly<Ref<number>>,
    recordBlob: recordBlob as Readonly<Ref<RecordResult | null>>,
    recordURL: recordURL as Readonly<Ref<string>>,

    // 方法
    startRecording,
    pauseRecording,
    resumeRecording,
    stopRecording,
    playRecording,
    getAudioWaveform,
    cancelRecording,
    destroy,
  }
}

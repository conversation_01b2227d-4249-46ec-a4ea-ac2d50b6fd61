import pyautogui
import os
import uuid
# pip install pyperclip
import pyperclip      
import time
import sys

# 检查当前激活窗口是否是 hbuilder x
title = pyautogui.getActiveWindow().title
print(title)


if '- HBuilder X' not in title:
    print('dsfsdfsdf')
    # 如果当前激活的窗口不是我们期望的窗口，我们将焦点切换回我们的应用
    my_app_window = pyautogui.getWindowsWithTitle('HBuilder X')[0]
    print(my_app_window)
    my_app_window.activate()
# 运行启动命令
pyautogui.hotkey('ctrl', 'r')
pyautogui.hotkey('1')
# time.sleep(0.1)
# if '- HBuilder X' in pyautogui.getActiveWindow().title:
#         pyautogui.hotkey('alt', 'tab')

time.sleep(5)
pyperclip.copy('http://localhost:5000/')

while True:
    active_window = pyautogui.getActiveWindow()
    if '0.0' in active_window.title or '要恢复页面吗' in active_window.title:
        pyautogui.hotkey('alt', 'tab')
        sys.exit()
    time.sleep(1)

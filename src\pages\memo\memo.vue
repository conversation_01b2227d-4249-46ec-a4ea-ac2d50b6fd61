<template>
  <view>
    <view class="bg-white p-2 mb-2 rr-4" v-for="item in memoList" :key="item.id">
      <rich-text :nodes="item.content"></rich-text>
      <view class="text-16">{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</view>
    </view>
    <view @click="memoVisible = true" class="fixed bottom-200 right-50 wh-30 bg-blue"></view>
    <z-memo-popup v-model:show="memoVisible" @onSubmit="onSubmit" />
  </view>
</template>
<script setup>
const memoVisible = ref(false)

const { data: memoList, run: getMemoList } = useRequest(getMemoListApi, {})
const onSubmit = () => {
  init()
}
const init = async () => {
  memoVisible.value = false
  await getMemoList('type == 0')
  console.log('memoList===')
  console.log(memoList.value)
}
useReload(init)
</script>

<style lang="scss" scoped></style>

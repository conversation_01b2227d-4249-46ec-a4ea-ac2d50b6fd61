---
type: 'agent_requested'
description: '当需要使用到接口时，应该遵循这个规则'
---

# API 使用规范

## 网络接口使用规范

项目中所有的 API 接口都定义在 `src/api` 目录下，按功能模块进行分类。当需要使用接口时，应从对应的模块文件中导入。

### 主要 API 模块：

1. [okr.ts](mdc:src/api/okr.ts) - OKR 相关接口
2. [workflow.ts](mdc:src/api/workflow.ts) - 工作流相关接口
3. [okrMd.ts](mdc:src/api/okrMd.ts) - OKR 元数据相关接口
4. [chatRecord.ts](mdc:src/api/chatRecord.ts) - 聊天记录相关接口
5. [syncServer.ts](mdc:src/api/syncServer.ts) - 同步服务相关接口
6. [task.ts](mdc:src/api/task.ts) - 任务相关接口
7. [okrConfig.ts](mdc:src/api/okrConfig.ts) - OKR 配置相关接口
8. [memo.ts](mdc:src/api/memo.ts) - 备忘录相关接口
9. [habit.ts](mdc:src/api/habit.ts) - 习惯相关接口
10. [bill.ts](mdc:src/api/bill.ts) - 账单相关接口
11. [database/index.ts](mdc:src/api/database/index.ts) - 数据库操作接口

### 接口类型定义：

接口相关的类型定义位于 [typings.d.ts](mdc:src/api/typings.d.ts)

### 网络接口使用示例：

```typescript
import { getOkrList } from '@/api/okr'
import { getTaskList } from '@/api/task'

// 使用接口
getOkrList().then((res) => {
  // 处理响应数据
})
```

请始终从对应的 API 模块导入接口，而不是创建重复的接口调用代码。

## 数据库 API 使用规范

### 基本概念

本项目使用自封装的数据库 API（`src/api/database/index.ts`），支持 H5 和 App 两个平台，底层实现分别基于 Dexie.js（H5）和 SQLite（App）。API 设计统一，可在不同平台无缝使用。

### 数据库实例

项目中使用默认导出的数据库实例：

```typescript
import db from '@/api/database'
```

### 主要操作方法

#### 表操作

通过`db.table(tableName)`方法获取对应表的操作对象：

```typescript
const userTable = db.table('user')
```

#### 添加数据

```typescript
// 添加单条数据
const id = await userTable.add({
  name: '张三',
  age: 20,
})

// 批量添加数据
await userTable.bulkAdd([
  { name: '张三', age: 20 },
  { name: '李四', age: 25 },
])
```

#### 更新数据

```typescript
// 更新单条数据（根据_id）
await userTable.update('user_id_xxx', {
  name: '张三改',
  age: 21,
})

// 更新/替换整条数据
await userTable.put({
  _id: 'user_id_xxx',
  name: '张三改',
  age: 21,
})

// 批量更新
await userTable.bulkUpdate([
  { key: 'user_id_1', changes: { name: '张三改' } },
  { key: 'user_id_2', changes: { name: '李四改' } },
])
```

#### 查询数据

```typescript
// 根据_id查询单条数据
const user = await userTable.get('user_id_xxx')

// 批量获取多条数据
const users = await userTable.bulkGet(['user_id_1', 'user_id_2'])

// 条件查询
const results = await userTable.where('name == "张三" && age >= 20').toArray()

// 高级查询（链式调用）
const results = await userTable
  .where('name == "张三" || age >= 20')
  .orderBy('createTime', 'desc')
  .limit(10)
  .offset(0)
  .toArray()

// 查询并返回第一条数据
const firstUser = await userTable.where('age >= 20').first()

// 查询并返回最后一条数据
const lastUser = await userTable.where('age >= 20').last()

// 统计符合条件的数量
const count = await userTable.where('age >= 20').count()
```

#### 查询语法

支持两种查询语法：

1. 字符串方式：

```typescript
// 使用 == != >= <= > < 等运算符，支持 && 和 || 组合
userTable.where('name == "张三" && age >= 20')
userTable.where('(name == "张三" && age >= 20) || (name == "李四" && age < 30)')
```

2. 函数方式：

```typescript
// 使用逗号分隔字段名和操作函数
userTable.where('age, above(20)')
userTable.where('age, between(20, 30)')
userTable.where('name, equals("张三")')
```

支持的函数查询操作符：

- `equals`: 等于 - 示例: `userTable.where('name, equals("张三")')`
- `above`: 大于 - 示例: `userTable.where('age, above(20)')`
- `aboveOrEqual`: 大于等于 - 示例: `userTable.where('age, aboveOrEqual(20)')`
- `below`: 小于 - 示例: `userTable.where('age, below(20)')`
- `belowOrEqual`: 小于等于 - 示例: `userTable.where('age, belowOrEqual(20)')`
- `between`: 在两个值之间（包含边界）- 示例: `userTable.where('age, between(20, 30)')`
- `anyOf`: 在指定值列表中 - 示例: `userTable.where('age, anyOf(18, 20, 22)')`
- `noneOf`: 不在指定值列表中 - 示例: `userTable.where('age, noneOf(18, 20, 22)')`
- `notEqual`: 不等于 - 示例: `userTable.where('age, notEqual(20)')`
- `inAnyRange`: 在多个范围内 - 示例: `userTable.where('age, inAnyRange([[18, 30], [40, 50]])')`

注意：函数方式查询必须使用**逗号**分隔字段名和操作函数，而不是使用点号。正确格式是 `字段名, 函数名(参数)`。

#### 删除操作

```typescript
// 删除满足条件的数据
await userTable.where('age < 20').delete()
```

#### 修改操作

```typescript
// 修改满足条件的数据
await userTable.where('age < 20').modify({ status: 'inactive' })
```

### 数据同步

数据修改后会自动同步到服务器（通过`syncToServer`函数），无需手动处理。

### 注意事项

1. 数据库主键使用`_id`字段，创建数据时会自动生成 UUID
2. 默认会过滤已删除的数据（`deleteTime`字段不为空的数据）
3. 数据操作会自动处理`createTime`、`updateTime`等时间字段
4. 请始终使用本 API 进行数据操作，不要直接操作底层数据库

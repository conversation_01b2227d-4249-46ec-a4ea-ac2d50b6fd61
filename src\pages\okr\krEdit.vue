<template>
  <!-- 
  页面入参说明：
  1. okrId: 必传参数，关联的 OKR 目标 ID
  2. id: 可选参数，关键结果 ID，存在则为编辑模式，不存在则为新增模式
  
  使用示例：
  - 新增关键结果：/pages/okr/krEdit?okrId=123456
  - 编辑关键结果：/pages/okr/krEdit?okrId=123456&id=789012
  -->
  <div class="page-container">
    <!-- 导航栏 -->
    <z-page-navbar
      :title="isEdit ? '编辑关键结果' : '新增关键结果'"
      rightButtonType="save"
      @back="goBack"
      @rightButtonClick="handleSave"
    />

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 加载指示器 -->
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
      </div>

      <div class="container">
        <form id="kr-form" @submit.prevent="handleSave">
          <!-- 关键结果标题和描述部分 -->
          <div class="form-section">
            <div class="section-label">基本信息</div>
            <div class="input-card">
              <div class="input-field">
                <div class="input-label" for="kr-title">关键结果标题</div>
                <input
                  type="text"
                  id="kr-title"
                  class="input-text"
                  placeholder="例如：完成APP新版本UI设计"
                  v-model="krTitle"
                  :disabled="loading"
                />
                <div class="input-hint">明确、可衡量的结果描述</div>
              </div>
              <div class="input-field">
                <div class="input-label" for="kr-description">详细描述</div>
                <textarea
                  id="kr-description"
                  class="input-text"
                  rows="3"
                  placeholder="添加更详细的说明..."
                  v-model="krDescription"
                  :disabled="loading"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- 任务日期和循环 -->
          <div class="form-section">
            <div class="section-label">任务日期和循环</div>
            <div class="input-card">
              <div class="date-range-display" @click="toggleDateLoopContent">
                <span class="date-icon">📅</span>
                <span class="date-range-text-display">{{ dateRangeText }}</span>
                <div v-if="areDatesSet" class="clear-button date-action-right" @click.stop="clearSelectedDates">
                  清除
                </div>
                <span v-else class="chevron-icon date-action-icon date-action-right">❯</span>
              </div>
              <div v-if="isDateLoopContentVisible">
                <div class="tabs">
                  <div class="tab" :class="{ active: dateLoopTab === 'single' }" @click="switchToSingleMode">单日</div>
                  <div class="tab" :class="{ active: dateLoopTab === 'range' }" @click="switchToRangeMode">
                    区间/循环
                  </div>
                </div>
                <div v-if="dateLoopTab === 'range'">
                  <div class="date-details">
                    <uni-datetime-picker
                      v-model="dateRange"
                      type="daterange"
                      rangeSeparator="至"
                      @change="handleDateRangeChange"
                    >
                      <div class="date-range-container">
                        <div class="date-item-label">日期区间</div>
                        <div class="date-range-value">{{ startDateDisplay }} 至 {{ endDateDisplay }}</div>
                        <div class="date-range-duration">
                          {{ startDayDisplay }} - {{ endDayDisplay }} (持续 {{ durationDays }} 天)
                        </div>
                      </div>
                    </uni-datetime-picker>
                  </div>
                  <div class="input-field row-field arrow-field">
                    <div class="input-label">设置循环</div>
                    <div class="loop-setting-value" @click="openLoopSettingModal">
                      <span>{{ loopSettingText }}</span>
                      <span class="chevron-icon">❯</span>
                    </div>
                  </div>
                </div>
                <div v-if="dateLoopTab === 'single'">
                  <div class="input-field">
                    <div class="input-label">选择日期</div>
                    <uni-datetime-picker v-model="singleDate" type="date">
                      <div class="date-input-container">
                        <input
                          type="text"
                          class="input-text"
                          :placeholder="'年/月/日'"
                          :value="singleDateDisplay"
                          readonly
                        />
                        <span class="calendar-icon">📅</span>
                      </div>
                    </uni-datetime-picker>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 目标值设置 -->
          <div class="form-section">
            <div class="section-label">量化进度</div>
            <div class="input-card">
              <!-- 量化表单 -->
              <div>
                <div class="input-field">
                  <div class="numeric-inputs-container">
                    <div class="numeric-input-group">
                      <div class="input-label-small" for="start-value">起始值</div>
                      <input
                        type="number"
                        id="start-value"
                        class="input-text"
                        v-model.number="startValue"
                        step="0.1"
                        min="0"
                        placeholder="例如: 0"
                        :disabled="loading"
                      />
                    </div>
                    <div class="numeric-input-group">
                      <div class="input-label-small" for="target-value">目标值</div>
                      <div class="input-with-unit">
                        <input
                          type="number"
                          id="target-value"
                          class="input-text"
                          v-model.number="targetValue"
                          step="0.1"
                          min="0"
                          placeholder="例如: 100"
                          :disabled="loading"
                        />
                        <input
                          type="text"
                          id="unit-value"
                          class="input-text unit-input"
                          v-model="unitValue"
                          placeholder="单位"
                          :disabled="loading"
                          maxlength="10"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="input-field daily-target-field" v-if="areDatesSet && isValidDateRange">
                  <div class="daily-target-container">
                    <div class="input-label">
                      每日目标量：<span class="daily-target-value">{{ dailyTargetAmount }}</span>
                    </div>
                    <div class="adjust-btn" @click="showDailyTargetAdjust = true">调整</div>
                  </div>
                  <div class="input-hint">根据日期范围和循环规则自动计算</div>
                </div>
                <div class="input-field">
                  <div class="input-label">进度计算方式</div>
                  <div class="radio-group">
                    <div
                      class="radio-option"
                      :class="{ 'radio-selected': progressType === 'sum', 'radio-disabled': loading }"
                      @click="!loading && (progressType = 'sum')"
                    >
                      <div class="radio-text">求和</div>
                    </div>
                    <div
                      class="radio-option"
                      :class="{ 'radio-selected': progressType === 'latest', 'radio-disabled': loading }"
                      @click="!loading && (progressType = 'latest')"
                    >
                      <div class="radio-text">最新</div>
                    </div>
                  </div>
                  <div class="input-hint">
                    {{ progressTypeHint }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
    <!-- 标签栏 placeholder -->
    <div id="tab-bar-container"></div>

    <!-- 在页面底部添加循环设置弹窗 -->
    <l-loop-popup
      v-model:show="showLoopSettingModal"
      :initialValue="loopSetting"
      :startDate="startDate"
      @confirm="handleLoopSettingConfirm"
    />

    <!-- 添加每日目标量调整弹窗 -->
    <u-popup v-model="showDailyTargetAdjust" mode="center" width="80%" borderRadius="10">
      <div class="daily-target-adjust-popup">
        <div class="popup-header">
          <div class="popup-title">调整每日目标量</div>
        </div>
        <div class="popup-content">
          <div class="adjust-input-field">
            <div class="adjust-input-label">自定义每日目标量</div>
            <input
              type="number"
              class="input-text"
              v-model.number="dailyTarget"
              step="0.1"
              placeholder="请输入调整后的值"
            />
            <div class="input-hint">原计算值：{{ dailyTargetAmount }}</div>
          </div>
          <div class="smart-calc-button" @click="handleSmartCalculate">
            <div class="smart-calc-icon">🧠</div>
            <div class="smart-calc-text">智能计算总量</div>
          </div>
        </div>
        <div class="popup-footer">
          <div type="button" class="cancel-btn" @click="handleCancelAdjust">取消</div>
          <div type="button" class="confirm-btn" @click="handleConfirmAdjust">确定</div>
        </div>
      </div>
    </u-popup>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { router, getUrlParams, generateUUID } from '../../utils/tools'
import { getTaskApi, addTaskApi, updateTaskApi } from '@/api/task'
import ZPageNavbar from '../../components/z-page-navbar.vue'
import LLoopPopup from './components/l-loop-popup.vue'
import uSwitch from '../../uni_modules/vk-uview-ui/components/u-switch/u-switch.vue'
import * as rrule from '@/utils/rrrrule'

// 表单数据
const krTitle = ref('')
const krDescription = ref('')
const startValue = ref(0)
const targetValue = ref(100)
const unitValue = ref('') // 添加单位值
const progressType = ref('sum') // 'sum' or 'latest' or 'max' or 'avg'
const krWeight = ref(1)
const krId = ref(null) // 用于存储编辑时的关键结果 ID
const okrId = ref(null) // 存储关联的 OKR ID
const loading = ref(false) // 加载状态
// 量化功能始终开启
const isQuantifiable = ref(true) // 始终启用量化功能

// 新增：每日目标量调整相关
const showDailyTargetAdjust = ref(false)
const dailyTarget = ref(0)
const useCustomDailyTarget = ref(false)

// 新增：任务日期和循环相关状态
const dateLoopTab = ref('range') // 'single' or 'range'
const isDateLoopContentVisible = ref(true) // New state for collapsible section
const startDate = ref('')
const endDate = ref('')
const startDateDisplay = ref('')
const endDateDisplay = ref('')
const startDayDisplay = ref('')
const endDayDisplay = ref('')
const singleDate = ref('') // 新增：单日选择器的日期
const singleDateDisplay = ref('') // 新增：单日选择器的日期显示
// 新增：日期区间选择器数据
const dateRange = ref([])

// 新增：循环设置相关状态 - 修改默认值为新格式
const showLoopSettingModal = ref(false)
const loopSetting = ref('type=DAILY') // 直接存储新格式的循环规则

// Helper computed property to check if any date is set
const areDatesSet = computed(() => {
  return !!(startDate.value || endDate.value || singleDate.value)
})

// 循环设置文本显示 - 使用新的 rrule.toText 方法
const loopSettingText = computed(() => {
  console.log('loopSettingText', loopSetting.value)
  if (!loopSetting.value) return '不重复'
  return rrule.toText(loopSetting.value)
})

// 计算显示的日期范围文本
const dateRangeText = computed(() => {
  if (dateLoopTab.value === 'single' && singleDateDisplay.value) {
    return singleDateDisplay.value
  } else if (dateLoopTab.value === 'range' && startDateDisplay.value && endDateDisplay.value) {
    return `${startDateDisplay.value} ~ ${endDateDisplay.value}`
  } else if (dateLoopTab.value === 'range' && startDateDisplay.value) {
    return startDateDisplay.value // Only start date selected
  }
  return '日期和循环' // Changed placeholder
})

// 在组件挂载时初始化日期
const initDates = () => {
  // 设置默认日期
  const today = new Date()
  startDate.value = formatDateValue(today)

  // 根据当前模式决定是否设置结束日期
  if (dateLoopTab.value === 'range') {
    const defaultEndDate = new Date(today)
    defaultEndDate.setDate(today.getDate() + 7) // 默认持续 7 天
    endDate.value = formatDateValue(defaultEndDate)
    // 设置日期区间
    dateRange.value = [startDate.value, endDate.value]
  } else {
    // 单日模式下清空结束日期
    endDate.value = ''
    dateRange.value = []
  }

  // 单日模式默认为今天
  singleDate.value = formatDateValue(today)
  // 立即更新日期显示
  updateDateDisplays()
}

// 计算持续天数
const durationDays = computed(() => {
  if (!startDate.value || !endDate.value) return 0
  const startDt = new Date(startDate.value)
  const endDt = new Date(endDate.value)
  const diffTime = Math.abs(endDt.getTime() - startDt.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1 // 包含首尾两天
  return diffDays
})

// 计算当前选中计算方式的说明文本
const progressTypeHint = computed(() => {
  const hints = {
    sum: '求和：累计所有进度记录',
    latest: '最新：仅使用最新记录',
    max: '最大值：使用最大进度记录',
    avg: '平均值：计算所有进度记录的平均值',
  }
  return hints[progressType.value] || ''
})

// 判断当前是编辑状态还是新增状态
const isEdit = computed(() => !!krId.value)

// 日期格式化辅助函数
const formatDateDisplay = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${year}/${month}/${day}`
}

const formatDayOfWeek = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const days = ['周日', '周一', '周二', '周三', '周四', '周五', '六']
  return days[date.getDay()]
}

const formatDateValue = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 更新日期显示
const updateDateDisplays = () => {
  if (startDate.value) {
    startDateDisplay.value = formatDateDisplay(startDate.value)
    startDayDisplay.value = formatDayOfWeek(startDate.value)
  } else {
    startDateDisplay.value = ''
    startDayDisplay.value = ''
  }
  if (endDate.value) {
    endDateDisplay.value = formatDateDisplay(endDate.value)
    endDayDisplay.value = formatDayOfWeek(endDate.value)
  } else {
    endDateDisplay.value = ''
    endDayDisplay.value = ''
  }
  // 更新单日日期显示
  if (singleDate.value) {
    singleDateDisplay.value = formatDateDisplay(singleDate.value)
  } else {
    singleDateDisplay.value = ''
  }
  console.log('日期已更新：', {
    startDate: startDate.value,
    endDate: endDate.value,
    startDateDisplay: startDateDisplay.value,
    endDateDisplay: endDateDisplay.value,
    singleDate: singleDate.value,
    singleDateDisplay: singleDateDisplay.value,
  })
}

// 监听日期变化
watch(
  [startDate, endDate, singleDate],
  () => {
    updateDateDisplays()
  },
  { immediate: true }
)

const validateWeight = () => {
  // 只在保存或初始化时校验
  if (krWeight.value === '' || krWeight.value === null || isNaN(krWeight.value)) {
    krWeight.value = 1
  }
}

const goBack = () => {
  // 使用封装的 router 工具进行返回
  router.back()
}

// 量化功能始终开启，无需监听状态变化

// 根据 ID 获取关键结果详情
const fetchKrDetail = async (id) => {
  try {
    loading.value = true
    const taskData = await getTaskApi(id)

    if (taskData) {
      // 初始化表单数据前，根据任务日期情况判断应该使用哪种模式
      if (taskData.startDate && !taskData.endDate) {
        dateLoopTab.value = 'single'
        console.log('检测到单日任务，切换到单日模式')
      } else if (taskData.startDate && taskData.endDate) {
        dateLoopTab.value = 'range'
        console.log('检测到区间任务，切换到区间模式')
      }
      // 初始化表单数据
      initFormData(taskData)
    } else {
      uni.showToast({
        title: '未找到关键结果数据',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('获取关键结果详情失败：', error)
    uni.showToast({
      title: '获取关键结果详情失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 初始化表单数据
const initFormData = (data) => {
  if (data) {
    krId.value = data._id
    krTitle.value = data.title || ''
    krDescription.value = data.content || '' // 使用 content 字段作为描述
    startValue.value = data.initVal || 0 // 使用 initVal 字段作为起始值
    targetValue.value = data.tgtVal || 100 // 使用 tgtVal 字段作为目标值
    unitValue.value = data.unit || '' // 加载单位值
    progressType.value = data.valType || 'sum' // 使用 valType 字段作为进度计算方式
    krWeight.value = data.weight
    validateWeight()

    // 初始化自定义每日目标量
    if (data.dailyTarget !== undefined) {
      dailyTarget.value = data.dailyTarget
      useCustomDailyTarget.value = true
    } else {
      useCustomDailyTarget.value = false
    }

    // 量化功能始终开启
    isQuantifiable.value = true

    // 初始化循环设置 (直接使用 repeatFlag)
    loopSetting.value = data.repeatFlag || 'type=DAILY' // 默认每天，如果为空则不重复，弹窗会处理

    // 初始化日期
    if (data.startDate) {
      startDate.value = data.startDate
      // 单日模式下，将单日日期设置为开始日期
      if (dateLoopTab.value === 'single') {
        singleDate.value = data.startDate
        // 确保结束日期为空
        endDate.value = ''
        dateRange.value = []
        console.log('单日模式：设置单日日期', singleDate.value)
      }
      // 区间模式下，设置开始和结束日期
      else {
        if (data.endDate) {
          endDate.value = data.endDate
        } else {
          // 如果没有结束日期，设置为开始日期后 7 天
          const defaultEndDate = new Date(data.startDate)
          defaultEndDate.setDate(defaultEndDate.getDate() + 7)
          endDate.value = formatDateValue(defaultEndDate)
        }
        // 设置日期区间
        dateRange.value = [startDate.value, endDate.value]
        console.log('区间模式：设置开始日期', startDate.value, '结束日期', endDate.value)
      }
    } else {
      // If no start date from data, clear dates initially so "日期和循环" shows
      startDate.value = ''
      endDate.value = ''
      singleDate.value = ''
      dateRange.value = []
      isDateLoopContentVisible.value = false // Collapse if no dates initially
    }

    updateDateDisplays()
  } else {
    // 新增状态的默认值
    krId.value = null
    krTitle.value = ''
    krDescription.value = ''
    startValue.value = 0
    targetValue.value = 100
    unitValue.value = '' // 单位默认为空
    progressType.value = 'sum'
    krWeight.value = 1
    loopSetting.value = 'type=DAILY' // 新增时默认每天
    // For new KRs, start with dates cleared and section collapsed
    startDate.value = ''
    endDate.value = ''
    singleDate.value = ''
    dateRange.value = []
    isDateLoopContentVisible.value = false // Start collapsed for new KRs
    updateDateDisplays() // Ensure displays are also empty
  }
}

// New method to toggle date loop content visibility
const toggleDateLoopContent = () => {
  isDateLoopContentVisible.value = !isDateLoopContentVisible.value
  // If expanding and no dates are set, initialize them
  if (isDateLoopContentVisible.value && !areDatesSet.value) {
    initDates() // This will set default dates
  }
}

// 切换到单日模式
const switchToSingleMode = () => {
  dateLoopTab.value = 'single'
  // 当切换到单日模式时，清空结束日期
  endDate.value = '' // 清空 endDate，因为单日模式不需要
  // 更新日期显示，确保 UI 上的结束日期也被清空
  updateDateDisplays()
  console.log('切换到单日模式，已清空结束日期')
}

// 切换到区间/循环模式
const switchToRangeMode = () => {
  dateLoopTab.value = 'range'
  // 如果之前清空了结束日期 (例如从单日切换过来)，需要重新设置
  if (!endDate.value && startDate.value) {
    const startDt = new Date(startDate.value)
    const defaultEndDate = new Date(startDt)
    defaultEndDate.setDate(startDt.getDate() + 7) // 默认持续 7 天
    endDate.value = formatDateValue(defaultEndDate)
  } else if (!startDate.value) {
    // 如果开始日期也没有，则重新初始化日期
    initDates()
  }
  // 更新日期显示
  updateDateDisplays()
  console.log('切换到区间模式，已重新设置结束日期')
}

// 打开循环设置弹窗
const openLoopSettingModal = () => {
  showLoopSettingModal.value = true
}

// 处理循环设置确认
const handleLoopSettingConfirm = (rruleString) => {
  loopSetting.value = rruleString
  console.log('循环设置规则已更新：', rruleString)
}

const handleSave = async () => {
  // 表单校验
  if (!krTitle.value || krTitle.value.trim() === '') {
    uni.showToast({ title: '请输入关键结果标题', icon: 'none' })
    return
  }

  // 校验量化相关字段
  if (startValue.value === null || startValue.value === '' || isNaN(startValue.value)) {
    uni.showToast({ title: '请输入起始值', icon: 'none' })
    return
  }
  if (targetValue.value === null || targetValue.value === '' || isNaN(targetValue.value)) {
    uni.showToast({ title: '请输入目标值', icon: 'none' })
    return
  }

  validateWeight()

  // 检查日期是否已选择 - 仅当有部分日期被设置但不完整时才提示
  // 如果所有日期都被清除，则允许保存
  const allDatesCleared = !startDate.value && !endDate.value && !singleDate.value

  if (!allDatesCleared) {
    // 只有在有部分日期被设置的情况下才进行验证
    if (dateLoopTab.value === 'single' && !singleDate.value) {
      uni.showToast({ title: '请选择日期', icon: 'none' })
      return
    }
    if (dateLoopTab.value === 'range' && (!startDate.value || !endDate.value)) {
      uni.showToast({ title: '请选择开始和结束日期', icon: 'none' })
      return
    }
  }

  try {
    loading.value = true
    console.log('保存前的循环规则：', loopSetting.value)

    const now = new Date().toISOString()
    const krData = {
      title: krTitle.value,
      content: krDescription.value,
      // 任务类型始终为 kr
      type: 'kr',
      okrId: okrId.value,
      parentId: '',
      // 量化相关字段
      initVal: startValue.value,
      tgtVal: targetValue.value,
      valType: progressType.value,
      weight: 1,
      status: 0,
      unit: unitValue.value, // 添加单位字段
      repeatFlag: loopSetting.value, // 直接使用新格式的循环规则字符串
      // 如果设置了日期范围，始终保存 dailyTarget 值
      ...(isValidDateRange.value && {
        dailyTarget: useCustomDailyTarget.value ? dailyTarget.value : parseFloat(dailyTargetAmount.value),
      }),
    }

    // 根据选择的标签页确定使用哪个日期
    // 如果所有日期都被清除，则设置为空字符串
    if (allDatesCleared) {
      krData.startDate = ''
      krData.endDate = ''
    } else if (dateLoopTab.value === 'single') {
      // 单日模式：只使用 startDate 字段
      krData.startDate = singleDate.value
      // 单日模式下不设置 endDate，确保清空
      krData.endDate = '' // 明确设置为空字符串
    } else {
      // 区间/循环模式：使用 startDate 和 endDate
      krData.startDate = startDate.value
      krData.endDate = endDate.value
    }

    // 打印保存接口字段
    console.log('保存到数据库的数据：', JSON.stringify(krData, null, 2))

    if (isEdit.value) {
      // 编辑模式：更新现有关键结果
      await updateTaskApi(krId.value, krData)
    } else {
      // 新增模式：创建新的关键结果
      krData._id = generateUUID() // 生成新的 ID
      krData.createTime = now
      krData.recList = [] // 只在新增时设置空的进度记录
      await addTaskApi(krData)
    }

    // 显示保存成功提示
    uni.showToast({
      title: isEdit.value ? '关键结果更新成功' : '关键结果创建成功',
      icon: 'none',
    })

    // 打印保存后的数据
    console.log('保存成功，数据：', JSON.stringify(krData, null, 2))

    // 保存后返回上一页
    setTimeout(() => {
      router.back()
    }, 500)
  } catch (error) {
    console.error('保存关键结果失败：', error)
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

const clearSelectedDates = () => {
  startDate.value = ''
  endDate.value = ''
  singleDate.value = ''
  dateRange.value = []
  isDateLoopContentVisible.value = false // Collapse the section
  // The watch on [startDate, endDate, singleDate] will automatically call updateDateDisplays()
  // and thus clear startDateDisplay, endDateDisplay, singleDateDisplay.
  console.log('所有日期已清除，日期选择器已收起')
}

const isValidDateRange = computed(() => {
  // 检查日期范围是否有效
  if (dateLoopTab.value === 'range') {
    return startDate.value && endDate.value && new Date(startDate.value) <= new Date(endDate.value)
  } else if (dateLoopTab.value === 'single') {
    return !!singleDate.value
  }
  return false
})

// 计算每日目标量
const dailyTargetAmount = computed(() => {
  // 如果使用自定义的每日目标量
  if (useCustomDailyTarget.value) {
    return dailyTarget.value.toFixed(2)
  }

  if (!isValidDateRange.value) return '0'

  let taskCount = 1 // 默认至少执行一次

  // 计算任务执行次数
  if (dateLoopTab.value === 'range' && startDate.value && endDate.value) {
    // 如果有循环设置，根据循环规则计算
    if (loopSetting.value && loopSetting.value !== '不重复') {
      taskCount = calculateTaskOccurrences(loopSetting.value, startDate.value, endDate.value)
    } else {
      // 没有循环规则，只执行一次
      taskCount = 1
    }
  }

  // 计算每日目标量 = (目标值 - 起始值) / 任务次数
  const targetDiff = Math.abs(targetValue.value - startValue.value)
  const dailyAmount = taskCount > 0 ? (targetDiff / taskCount).toFixed(2) : '0'

  return dailyAmount
})

// 修改：计算任务在指定日期范围内的发生次数
const calculateTaskOccurrences = (ruleString, startDateStr, endDateStr) => {
  // 默认至少执行一次
  if (!ruleString || !startDateStr || !endDateStr) return 1

  try {
    // 使用新的 rrrrule.getOccurrences 获取日期范围内的所有事件
    const result = rrule.getOccurrences(ruleString, startDateStr, endDateStr)

    // 至少返回 1，表示任务至少执行一次
    return Math.max(1, result.count)
  } catch (error) {
    console.error('计算任务循环次数出错：', error)
    return 1 // 出错时返回默认值
  }
}

// 处理取消调整
const handleCancelAdjust = () => {
  showDailyTargetAdjust.value = false
  // 如果之前没有使用自定义值，重置自定义值为计算值
  if (!useCustomDailyTarget.value) {
    dailyTarget.value = parseFloat(dailyTargetAmount.value)
  }
}

// 处理确认调整
const handleConfirmAdjust = () => {
  if (dailyTarget.value === null || isNaN(dailyTarget.value)) {
    uni.showToast({
      title: '请输入有效的数值',
      icon: 'none',
    })
    return
  }

  useCustomDailyTarget.value = true
  showDailyTargetAdjust.value = false

  uni.showToast({
    title: '每日目标量已调整',
    icon: 'none',
  })
}

// 处理智能计算总量
const handleSmartCalculate = () => {
  console.log('执行智能计算总量')

  // 检查每日目标量是否有效
  if (dailyTarget.value === null || isNaN(dailyTarget.value) || dailyTarget.value <= 0) {
    uni.showToast({
      title: '请先输入有效的每日目标量',
      icon: 'none',
    })
    return
  }

  // 检查日期范围是否有效
  if (!isValidDateRange.value) {
    uni.showToast({
      title: '请先设置有效的日期范围',
      icon: 'none',
    })
    return
  }

  // 计算任务执行次数
  let taskCount = 1
  if (dateLoopTab.value === 'range' && startDate.value && endDate.value) {
    // 如果有循环设置，根据循环规则计算
    if (loopSetting.value && loopSetting.value !== '不重复') {
      taskCount = calculateTaskOccurrences(loopSetting.value, startDate.value, endDate.value)
    } else {
      // 没有循环规则，只执行一次
      taskCount = 1
    }
  }

  // 计算总量 = 每日目标量 * 任务次数
  const totalAmount = dailyTarget.value * taskCount

  // 更新目标值 = 起始值 + 总量
  targetValue.value = startValue.value + totalAmount

  uni.showToast({
    title: `目标值已更新为 ${targetValue.value}`,
    icon: 'none',
  })

  // 关闭弹窗
  showDailyTargetAdjust.value = false

  // 设置为使用自定义每日目标量
  useCustomDailyTarget.value = true
}

// 监听调整弹窗显示状态变化
watch(showDailyTargetAdjust, (newValue) => {
  if (newValue) {
    // 打开弹窗时，初始化自定义值为当前计算值或已保存的自定义值
    if (!useCustomDailyTarget.value) {
      // 如果没有使用自定义值，则设置为计算值
      const calculatedValue = dailyTargetAmount.value
      dailyTarget.value = parseFloat(calculatedValue)
    }
    // 如果已经有自定义值，保持不变
  }
})

// 监听目标值和起始值变化，如果没有使用自定义每日目标量，则重置
watch(
  [targetValue, startValue, startDate, endDate, loopSetting],
  () => {
    if (!useCustomDailyTarget.value) {
      // 参数变化时，不需要操作，因为 dailyTargetAmount 会自动重新计算
      // 但可以在此添加其他逻辑
    }
  },
  { deep: true }
)

// 单独监听日期和循环规则变化，这些变化会影响计算次数
watch(
  [startDate, endDate, loopSetting, dateLoopTab],
  () => {
    if (useCustomDailyTarget.value) {
      // 如果日期或循环规则变化，重置自定义每日目标量
      useCustomDailyTarget.value = false

      // 静默更新，不显示提示
    }
  },
  { deep: true }
)

// 处理日期区间变化
const handleDateRangeChange = (e) => {
  console.log('日期区间变化：', e)
  if (Array.isArray(e) && e.length === 2) {
    startDate.value = e[0]
    endDate.value = e[1]
    // 更新日期显示会通过 watch 自动触发
  }
}

// 监听日期变化，同步 dateRange 值
watch(
  [startDate, endDate],
  ([newStart, newEnd]) => {
    if (newStart && newEnd) {
      dateRange.value = [newStart, newEnd]
    }
    updateDateDisplays()
  },
  { immediate: true }
)

onMounted(() => {
  // 状态栏和标签栏初始化
  if (typeof loadStatusBar === 'function') {
    loadStatusBar('status-bar-container')
  }
  if (typeof loadTabBar === 'function') {
    loadTabBar('tab-bar-container', 'home')
  }

  // 初始化默认日期
  initDates()

  // 获取 URL 参数
  const params = getUrlParams()

  // 获取 okrId（必传）
  if (params.okrId) {
    okrId.value = params.okrId
  } else {
    // okrId 是必传参数，如果没有则提示错误并返回
    uni.showToast({
      title: '缺少必要参数',
      icon: 'none',
    })
    setTimeout(() => {
      router.back()
    }, 500)
    return
  }

  // 获取 id（可选，有则为编辑模式）
  if (params.id) {
    krId.value = params.id
    // 获取关键结果详情 - initFormData will handle initial date display based on fetched data
    fetchKrDetail(params.id)
  } else {
    // 新增模式，初始化表单，日期部分会根据新的 initFormData 逻辑处理（默认收起，无日期）
    initFormData(null)
  }
})
</script>

<style scoped lang="scss">
@import '../../styles/reset.css';
// 基本页面布局
.page-container {
  font-family: var(--font-sans, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif);
  background: var(--color-bg);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  color: var(--color-gray-800);
  overflow-x: hidden;
}

.content-area {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px 90px; // 90px 底部间距为悬浮按钮留出空间
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  position: relative; // 添加相对定位，用于加载指示器定位
}

.navbar {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);
  position: sticky;
  // top: 44px; // 假设状态栏高度，如果状态栏是真实元素则不需要，或者用 JS 动态计算
  top: 0; // 如果 #status-bar-container 是固定高度的占位
  z-index: 40; // uni-app 中 navbar 通常层级较高
}

.navbar-title {
  font-weight: 600;
  font-size: 18px;
  margin-left: 15px;
  color: var(--color-primary);
  flex-grow: 1;
  text-align: center; // 使标题居中
}

.navbar-back {
  color: var(--color-primary);
  font-size: 16px; // 图标大小
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--rounded-sm, 8px);
  background: var(--color-gray-100);
  cursor: pointer;
  position: absolute; // 确保返回按钮在左侧
  left: 20px;
}
.navbar-action {
  margin-left: auto;
  color: var(--color-primary);
  font-weight: 500;
  font-size: 15px;
  padding: 8px 16px;
  border-radius: var(--rounded-sm, 6px); // variables.css 有 --rounded-sm (8px), 原来是 6px
  background: var(--color-primary-transparent-20); // 使用 variables.css 中的变量
  cursor: pointer;
  position: absolute; // 确保保存在右侧
  right: 20px;
  transition: all 0.3s ease;

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.form-section {
  margin-bottom: 24px;
}

.section-label {
  font-size: 14px;
  color: var(--color-gray-600);
  margin-bottom: 12px;
  margin-left: 5px;
  font-weight: 500;
}

.input-card {
  background-color: var(--color-white);
  border-radius: var(--rounded-lg, 16px);
  padding: 16px;
  box-shadow: var(--shadow-sm);
  margin-bottom: 5px;
  border: 1px solid var(--color-gray-200);
  box-sizing: border-box;
}

.input-field {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
  &:last-child {
    margin-bottom: 0;
  }
}

.input-field.row-field {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.input-field.arrow-field {
  cursor: pointer;
}

.input-label {
  font-size: 14px;
  color: var(--color-gray-600);
  margin-bottom: 8px;
}

.input-label-small {
  font-size: 13px; // 稍小一点的字体
  color: var(--color-gray-600);
  margin-bottom: 6px; // 标签和输入框之间的间距
  display: block; // 确保标签在输入框上方
}

.input-text,
input[type='number'],
textarea {
  width: 100%;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--rounded-sm, 8px);
  padding: 12px;
  font-size: 16px;
  color: var(--color-gray-800);
  background: var(--color-white);
  transition: var(--transition-fast, all 0.2s);
  box-sizing: border-box;
  height: inherit;
  &:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px var(--color-primary-transparent-20);
  }
  &:disabled {
    background-color: var(--color-gray-100);
    opacity: 0.8;
    cursor: not-allowed;
  }
}
textarea.input-text {
  min-height: 80px; // 给 textarea 一个初始高度
}

.input-hint {
  font-size: 13px;
  color: var(--color-gray-500);
  margin-top: 6px;
}

// 添加每日目标量样式
.daily-target-value {
  font-weight: 600;
  color: var(--color-primary);
  font-size: 16px;
}

// 调整按钮样式
.adjust-btn {
  display: inline-block;
  padding: 3px 8px;
  margin-left: 8px;
  background-color: var(--color-primary-transparent-10);
  color: var(--color-primary);
  border-radius: var(--rounded-sm, 4px);
  font-size: 12px;
  cursor: pointer;
  vertical-align: middle;
}

// 调整弹窗样式
.daily-target-adjust-popup {
  background: var(--color-white);
  border-radius: var(--rounded-lg, 16px);
  padding: 20px 16px; // 减少左右 padding
  width: 100%;
}

.popup-header {
  margin-bottom: 16px; // 减少 margin
}

.popup-title {
  font-size: 16px; // 减小字体
  font-weight: 600;
  color: var(--color-gray-800);
  text-align: center;
}

.popup-content {
  margin-bottom: 20px; // 减少 margin
}

.adjust-input-field {
  margin-bottom: 16px;
}

.adjust-input-label {
  font-size: 14px;
  color: var(--color-gray-600);
  margin-bottom: 8px;
  display: block;
}

.popup-footer {
  display: flex;
  justify-content: center; // 改为居中对齐
  gap: 16px;
}

// 修改选择器，使样式适用于 div 标签
.popup-footer .cancel-btn,
.popup-footer .confirm-btn {
  padding: 8px 0;
  border-radius: var(--rounded-md, 8px);
  font-size: 14px;
  cursor: pointer;
  border: none;
  width: 80px; // 固定宽度代替 max-width
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.cancel-btn {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
}

.confirm-btn {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.numeric-inputs-container {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.numeric-input-group {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.input-with-unit {
  display: flex;
  gap: 16rpx;
}

.unit-input {
  width: 30%;
  min-width: 120rpx;
}

.weight-input-container {
  position: relative;
  display: flex;
  align-items: center;
  .input-text {
    padding-right: 40px; // 为 "%" 单位留空间
  }
  .weight-unit {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--color-gray-500);
    font-weight: 500;
    font-size: 16px;
  }
}

.radio-group {
  display: flex;
  gap: 10px;
  margin-top: 8px;
  flex-wrap: nowrap; /* 禁止换行 */
  justify-content: space-between; /* 均匀分布 */
}

.radio-option {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: var(--rounded-sm, 8px);
  transition: background-color 0.2s;
  flex: 1; /* 均等分配空间 */
  text-align: center;

  &:hover:not(.radio-disabled) {
    background-color: var(--color-gray-100);
  }

  &.radio-selected {
    background-color: var(--color-primary-transparent-10, rgba(59, 130, 246, 0.1));
    color: var(--color-primary);
    font-weight: 500;
  }

  &.radio-disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.radio-text {
  font-size: 14px;
  color: var(--color-gray-700);
}

/* 底部保存按钮 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: var(--color-white);
  padding: 16px;
  // box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  box-shadow: var(--shadow-sm); // 使用 variables.css
  z-index: 50; // uni-app 中 tabbar 通常是原生组件，层级很高，需注意
  // 如果有原生 tabbar，此处的 bottom 应为 tabbar 高度
  // padding-bottom: calc(16px + constant(safe-area-inset-bottom)); /* 适配 iPhone X 等机型 */
  // padding-bottom: calc(16px + env(safe-area-inset-bottom));
}

.save-btn {
  width: 100%;
  background: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: var(--rounded-md, 12px);
  padding: 15px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast, all 0.2s);
  &:hover {
    background: var(--color-primary-dark);
  }
  &:active {
    opacity: 0.9;
  }
}

// 加载状态样式
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 30;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-gray-200);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 确保 #status-bar-container 和 #tab-bar-container 有实际高度，否则 sticky 定位可能不准确
// 例如，如果它们是动态加载的组件，需要确保它们渲染并占据空间
#status-bar-container {
  // height: 44px; /* 示例状态栏高度，根据实际情况调整 */
  // background-color: var(--color-bg-statusBar, transparent); /* 可选：状态栏背景色 */
}
#tab-bar-container {
  // height: 50px; /* 示例标签栏高度，根据实际情况调整 */
}

/* 新增：任务日期和循环样式 */
.date-range-display {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--color-gray-700);
  // padding: 10px 0; // Removed for finer control with internal elements
  cursor: pointer;
  position: relative; // Added for absolute positioning of action items
}

.date-range-text-display {
  flex-grow: 1; // Allows text to take available space before the action item
  // Potentially add margin-right if needed to prevent overlap with absolutely positioned items
}

.date-icon {
  font-size: 18px;
  margin-right: 8px;
  color: var(--color-primary);
}

.clear-button {
  // margin-left: auto; // Replaced by absolute positioning logic
  font-size: 13px;
  color: var(--color-primary);
  background-color: var(--color-primary-transparent-10);
  cursor: pointer;
  padding: 6px 12px;
  border-radius: var(--rounded-sm, 8px);
  font-weight: 500;
}

.date-action-icon {
  // margin-left: auto; // Replaced by absolute positioning logic
  color: var(--color-gray-500);
  font-size: 16px; // Ensure chevron icon size is consistent
}

.date-action-right {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.tabs {
  display: flex;
  margin-top: 16px;
  margin-bottom: 16px;
  gap: 10px;
  justify-content: center; /* Ensure tabs are centered */
}
.tab {
  padding: 8px 12px;
  font-size: 14px;
  color: var(--color-gray-700);
  cursor: pointer;
  border-radius: var(--rounded-sm, 8px);
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-white); /* Default background for non-active tabs */

  &:hover:not(.disabled) {
    background-color: var(--color-gray-100); /* Hover for non-active tabs */
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}
.tab.active {
  color: var(--color-primary); /* Text color for active tab */
  background-color: var(--color-primary-transparent-10, rgba(59, 130, 246, 0.1)); /* Background for active tab */
  font-weight: 500;
}

.date-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  gap: 16px;
}
.date-item {
  flex: 1;
  padding: 12px;
  background-color: var(--color-gray-50);
  border-radius: var(--rounded-md, 12px);
  border: 1px solid var(--color-gray-200);
}

/* 新增：日期区间容器样式 */
.date-range-container {
  width: 100%;
  padding: 16px;
  background-color: var(--color-gray-50);
  border-radius: var(--rounded-md, 12px);
  border: 1px solid var(--color-gray-200);
}
.date-range-value {
  font-size: 16px;
  color: var(--color-gray-800);
  font-weight: 500;
  margin: 6px 0;
}
.date-range-duration {
  font-size: 13px;
  color: var(--color-gray-500);
  margin-top: 4px;
}

.date-item-label {
  font-size: 12px;
  color: var(--color-gray-500);
  margin-bottom: 4px;
}
.date-item-value {
  font-size: 14px;
  color: var(--color-gray-800);
  font-weight: 500;
}
.date-item-duration {
  font-size: 12px;
  color: var(--color-gray-500);
  margin-top: 2px;
}

.switch-control {
  position: relative;
  display: inline-block;
  width: 44px; /* 调整宽度以匹配图像 */
  height: 24px; /* 调整高度 */
  cursor: pointer; /* 确保鼠标显示为可点击状态 */
}

.switch-input {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute; /* 确保不占用空间但仍保持可访问性 */
}

.switch-label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-gray-300); /* 关闭状态颜色 */
  transition: 0.4s;
  border-radius: 24px; /* 圆角 */
  box-sizing: border-box; /* 确保边框不会影响尺寸 */
  margin: 0; /* 重置任何可能的外边距 */
}

.switch-label:before {
  position: absolute;
  content: '';
  height: 20px; /* 滑块高度 */
  width: 20px; /* 滑块宽度 */
  left: 2px; /* 滑块初始位置 */
  bottom: 2px; /* 滑块初始位置 */
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

.switch-input:checked + .switch-label {
  background-color: var(--color-primary); /* 打开状态颜色 */
}

.switch-input:checked + .switch-label:before {
  transform: translateX(20px); /* 滑块移动距离，等于宽度 - 左边距*2 - 滑块宽度 */
}

.switch-input:disabled + .switch-label {
  opacity: 0.6;
  cursor: not-allowed;
}

.loop-setting-value {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--color-gray-700);
}
.chevron-icon {
  margin-left: 8px;
  font-size: 16px;
  color: var(--color-gray-400);
}

/* Chevron icon for collapsible section START */
.chevron-icon {
  margin-left: 10px;
  font-size: 16px;
  color: var(--color-gray-500);
  transition: transform 0.3s ease;
}

.chevron-icon.rotated {
  transform: rotate(90deg);
}
/* Chevron icon for collapsible section END */

/* Styles for collapsed state of input-card - REMOVED
.input-card.is-collapsed {
  padding-top: 8px; 
  padding-bottom: 8px; 
}

.input-card.is-collapsed .date-range-display {
  margin-bottom: 0; 
}
*/

// TailwindCSS 的一些常用类，如果项目中没有配置 Tailwind，这些会失效
// 如果需要，可以手动转换为 SCSS
// .flex { display: flex; }
// .items-center { align-items: center; }
// .justify-center { justify-content: center; }
// .ml-auto { margin-left: auto; }
// ...等等

.date-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.calendar-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: var(--color-gray-500);
  pointer-events: none;
}

// 添加每日目标量容器样式
.daily-target-field {
  padding: 8px 0;
  border-top: 1px solid var(--color-gray-200);
}

.daily-target-container {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.daily-target-container .input-label {
  margin-bottom: 0;
  display: flex;
  align-items: center;
}

/* 智能计算按钮样式 */
.smart-calc-button {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
  padding: 10px;
  background-color: var(--color-primary-transparent-10);
  border-radius: var(--rounded-md, 8px);
  cursor: pointer;
  transition: all 0.2s;
}

.smart-calc-button:hover {
  background-color: var(--color-primary-transparent-20);
}

.smart-calc-icon {
  font-size: 18px;
  margin-right: 8px;
}

.smart-calc-text {
  font-size: 14px;
  color: var(--color-primary);
  font-weight: 500;
}
</style>

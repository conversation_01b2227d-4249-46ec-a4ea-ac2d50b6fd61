import db from './database'

type OkrConfig = Omit<DB.OkrConfig, 'createTime' | 'updateTime'>

/**
 * 添加一条 config 记录
 * @param params 包含添加 config 所需的参数，具体包括：
 * - key: config 的唯一标识符
 * - value: config 的值
 */
export const addConfigApi = async (params: API.NewOkrConfig) => {
  await db.table('okrConfig').add({ key: params.key, value: JSON.stringify(params.value) })
}
/**
 * 更新一条 config 记录
 * @param key config 的唯一标识符
 * @param value config 的值
 */
export const updateConfigApi = async (key: string, value: API.EditOkrConfig) => {
  await db.table('okrConfig').update(key, { value: JSON.stringify(value) })
}
/**
 * 获取一条 config 记录
 * @returns 返回一条 config 记录 || null
 */
export const getConfigApi = async (key: string) => {
  try {
    const res = await db.table('okrConfig').get(key)
    if (res && res.value !== '{}') {
      return JSON.parse(res.value)
    } else {
      return {}
    }
  } catch (error) {
    console.error(error)
    throw new Error(String(error))
  }
}

/**
 * 删除 config
 * @key config 的唯一标识符
 */
export const delConfigApi = async (key: string) => {
  try {
    await db.table('okrConfig').update(key, {
      value: '{}',
    })
  } catch (error) {
    console.error(error)
    throw new Error(String(error))
  }
}

/**
 * 获取排序
 * @type 排序类型 today: 今日任务排序，okr: 目标列表排序，okrDetail: okr 详情任务排序，krDetail: 任务详情任务排序
 * @keyStr 排序 key
 * @orderByVal 排序值，传就是设置，不传就是获取
 */
export const orderByApi = async ({
  type,
  keyStr,
  orderByVal,
}: {
  type: 'today' | 'okr' | 'okrDetail' | 'krDetail'
  keyStr: string
  orderByVal?: any
}) => {
  try {
    switch (type) {
      case 'today':
        keyStr = `task_orderBy_${dayjs(keyStr).format('YYYYMMDD')}`
        break
      default:
        console.error('未知排序类型')
        return
    }
    const orderBy = await db.table('okrConfig').get(keyStr)

    if (orderByVal) {
      // 设置排序
      if (orderBy) {
        await db.table('okrConfig').update(keyStr, { value: JSON.stringify(orderByVal) })
        return orderBy._id
      } else {
        const _id = await db.table('okrConfig').add({ key: keyStr, value: JSON.stringify(orderByVal) })
        return _id
      }
    } else {
      // 获取排序
      if (orderBy) {
        return JSON.parse(orderBy.value)
      }
      return {}
    }
  } catch (error) {
    console.error(error)
    throw new Error(String(error))
  }
}

# z-audio-player 音频播放器组件

音频播放器组件，支持播放/暂停、进度条调整等功能。

## 使用方法

```vue
<template>
  <z-audio-player 
    :src="audioSrc" 
    :autoplay="false"
    :loop="false"
    title="音频标题"
    :showTitle="true"
    themeColor="#2979ff"
  />
</template>

<script setup>
import { ref } from 'vue'

const audioSrc = ref('https://example.com/audio.mp3')
</script>
```

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| src | String | - | 音频资源地址（必填） |
| autoplay | Boolean | false | 是否自动播放 |
| loop | Boolean | false | 是否循环播放 |
| title | String | '' | 音频标题 |
| showTitle | Boolean | false | 是否显示标题 |
| showTime | Boolean | true | 是否显示时间 |
| themeColor | String | '' | 主题颜色，不传使用默认主题 |

## 事件

| 事件名 | 说明 | 返回参数 |
| --- | --- | --- |
| play | 开始播放时触发 | - |
| pause | 暂停播放时触发 | - |
| ended | 播放结束时触发 | - |
| timeupdate | 播放进度更新时触发 | {currentTime, duration} |
| error | 播放出错时触发 | 错误信息 |

## 方法

通过 ref 获取组件实例后，可以调用以下方法：

```vue
<template>
  <z-audio-player ref="audioPlayer" :src="audioSrc" />
  <view class="controls">
    <button @click="playAudio">播放</button>
    <button @click="pauseAudio">暂停</button>
    <button @click="stopAudio">停止</button>
    <button @click="seekAudio">跳转到30秒</button>
  </view>
</template>

<script setup>
import { ref } from 'vue'

const audioPlayer = ref(null)
const audioSrc = ref('https://example.com/audio.mp3')

const playAudio = () => {
  audioPlayer.value.play()
}

const pauseAudio = () => {
  audioPlayer.value.pause()
}

const stopAudio = () => {
  audioPlayer.value.stop()
}

const seekAudio = () => {
  audioPlayer.value.seek(30) // 跳转到30秒
}

// 获取播放状态
const getAudioStatus = () => {
  const status = audioPlayer.value.getStatus()
  console.log('播放状态:', status.isPlaying)
  console.log('总时长:', status.duration)
  console.log('当前时间:', status.currentTime)
}
</script>
```

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| play | 播放音频 | - |
| pause | 暂停播放 | - |
| stop | 停止播放 | - |
| seek | 跳转到指定时间 | position (秒) |
| getDuration | 获取音频总时长 | - |
| getCurrentTime | 获取当前播放时间 | - |
| getStatus | 获取播放状态 | - | 
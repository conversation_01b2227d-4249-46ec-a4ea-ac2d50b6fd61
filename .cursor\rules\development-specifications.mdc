---
description: 
globs: 
alwaysApply: true
---
# 项目开发规范

## 使用场景
- 开发新页面或组件时
- 修改现有代码时
- 实现页面导航功理时间日期数据时
- 封装API接口时
- 使用图标时
- 命名组件和文件时

## 关键规则

### UI开发规则
- 始终使用view/text标签代替button/label等标签
- 页面跳转必须使用router工具函数，不使用uni原生导航API

### 数据库规则
- 始终使用_id作为主键，不重命名为id
- 修改数据库字段时必须同时更新typings.d.ts和index.ts
- 时间日期字段必须遵循指定格式规范

### 组件开发规则
- API接口调用必须封装在组件内部
- 通过props接收参数，通过事件通知父组件操作结果
- 组件内部处理加载状态和错误提示

### 图标使用规则
- 统一使用Font Awesome图标库
- 遵循指定的图标风格前缀规范
- 通过CSS控制图标大小和颜色

### 命名规范
- 组件名称必须是多词的，避免单个单词
- 文件命名使用kebab-case格式# 页面导航

### 路由中定义的router工具函数
- 可用方法：push、redirect、reLaunch、switchTab、back

<example>
// 正确的页面跳转方式
import { router } from '@/utils/tools'

// 保留当前页面，跳转到应用内的某个页面
router.push('/pages/home/<USER>', { id: 123 })

// 关闭当前页面，跳转到应用内的某个页面
router.redirect('/pages/user/profile')
</example>

<example type="invalid">
// 错误的页面跳转方式
uni.navigateTo({
  url: '/pages/home/<USER>'
})

// 错误的页面跳转方式
uni.redirectTo({
  url: '/pages/user/profile'
})
</example>

## 数据库操作

### 主键命名
- 数据库中使用_id作为主键
- 前端渲染时直接使用_id，不重命名为id

<example>
// 正确的主键使用方式
<view v-for="item in list" :key="item._id">
  {{ item.name }}
</view>
</example>

<example type="invalid">
// 错误的主键使用方式
const processedList = list.map(item => ({
  id: item._id,
  ...item
}))

<view v-for="item in processedList" :key="item.id">
  {{ item.name }}
</view>
</example>

### 数据库字段修改
- 添加或修改字段时必须同时更新两个文件：
  - src/api/dataSchema/typings.d.ts
  - src/api/dataSchema/index.ts

<example>
// typings.d.ts中添加字段
export interface Task {
  _id: string
  title: string
  description?: string // 新增字段
  createTime: string
}

// index.ts中添加对应配置
export const tableSchema = {
  task: {
    _id: { type: 'string', primary: true },
    title: { type: 'string' },
    description: { type: 'string', default: '' }, // 新增字段配置
    createTime: { type: 'string' }
  }
}
</example>

<example type="invalid">
// 只在一个文件中添加字段，缺少同步更新
// typings.d.ts中添加字段
export interface Task {
  _id: string
  title: string
  description?: string // 新增字段
  createTime: string
}

// index.ts中未添加对应配置
export const tableSchema = {
  task: {
    _id: { type: 'string', primary: true },
    title: { type: 'string' },
    createTime: { type: 'string' }
  }
}
</example>

### 时间日期格式
- 日期时间字段：ISO 8601格式字符串，如`2025-05-24T02:26:08.286Z`
- 纯日期字段：`YYYY-MM-DD`格式字符串，如`2025-05-24`
- 时间排序：先将字符串转换为时间戳再比较

<example>
// 正确的时间排序方式
items.sort((a, b) => {
  const aTime = a.dateField ? new Date(a.dateField).getTime() : 0
  const bTime = b.dateField ? new Date(b.dateField).getTime() : 0
  return bTime - aTime // 降序排列
})
</example>

<example type="invalid">
// 错误的时间排序方式
items.sort((a, b) => {
  return b.dateField.localeCompare(a.dateField) // 直接比较字符串
})
</example>

## 组件接口封装

### API接口封装原则
- 组件内部引入并调用所需的API
- 通过props接收必要参数
- 通过事件通知父组件操作结果
- 组件内部处理加载状态和错误提示

<example>
// 正确的组件接口封装方式
// TaskEditor.vue
<script setup>
import { ref } from 'vue'
import { saveTask } from '@/api/task'

const props = defineProps({
  taskId: String,
  initialData: Object
})

const emit = defineEmits(['save-success'])
const loading = ref(false)

const handleSave = async () => {
  loading.value = true
  try {
    const result = await saveTask(formData.value)
    uni.showToast({ title: '保存成功' })
    emit('save-success', result)
  } catch (error) {
    uni.showToast({ title: '保存失败', icon: 'none' })
  } finally {
    loading.value = false
  }
}
</script>
</example>

<example type="invalid">
// 错误的组件接口封装方式
// 父组件中
<script setup>
import { ref } from 'vue'
import { saveTask } from '@/api/task'
import TaskEditor from './TaskEditor.vue'

const taskData = ref(null)
const loading = ref(false)

const handleSaveTask = async (formData) => {
  loading.value = true
  try {
    const result = await saveTask(formData)
    taskData.value = result
    uni.showToast({ title: '保存成功' })
  } catch (error) {
    uni.showToast({ title: '保存失败', icon: 'none' })
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <TaskEditor :data="taskData" @save="handleSaveTask" />
</template>
</example>

## 图标使用

### Font Awesome图标库
- 项目已全局引入，无需在组件中单独引入
- 使用`<i>`标签并添加相应的类名
- 图标风格前缀：fas(实心)、far(线框)、fab(品牌)

<example>
// 正确的图标使用方式
<template>
  <view class="icon-container">
    <i class="fas fa-bullseye"></i>
    <i :class="iconClass"></i>
  </view>
</template>

<script setup>
import { ref } from 'vue'
const iconClass = ref('fas fa-chart-pie')
</script>

<style>
.icon-container i {
  font-size: 22px;
  color: var(--color-primary);
}
</style>
</example>

<example type="invalid">
// 错误的图标使用方式
<template>
  <view class="icon-container">
    <!-- 使用图片代替图标 -->
    <image src="/static/icons/bullseye.png"></image>
    
    <!-- 错误的类名前缀 -->
    <i class="fa fa-chart-pie"></i>
  </view>
</template>
</example>

## 命名规范

### 组件命名
- 组件名称必须是多词的
- 文件命名使用kebab-case格式
- 全局通用组件以z-开头，局部功能组件以l-开头

<example>
// 正确的组件命名方式
// 全局通用组件
// src/components/z-task-list/z-task-list.vue

// 局部功能组件
// src/pages/okr/components/l-kr-progress/l-kr-progress.vue
</example>

<example type="invalid">
// 错误的组件命名方式
// 单词命名
// src/components/task.vue

// 不符合前缀规范
// src/components/task-list.vue
// src/pages/okr/components/kr-progress.vue
</example>


# 任务：AI总结服务开发

- **所属功能模块**: `diary-qa`
- **任务ID**: `task-diary-qa-03`
- **优先级**: 中

## 任务描述
实现AI总结服务，该服务负责将用户当天的所有日记条目和问答记录整合成一篇连贯、结构化的日记总结。这将通过一个API端点（或客户端等效逻辑）`/api/diary/generateSummary` 来实现。

## 技术实现详情
1.  **总结生成逻辑**:
    - 实现 `/api/diary/generateSummary` 的逻辑。
    - **输入**:
        - 当日的所有日记条目（`entries`）。
        - 所有的问答记录（`questions_answers`）。
    - **处理**:
        - 将所有输入内容整合成一个结构化的文本，作为提示（Prompt）提交给通用AI模型。
        - **提示词设计**: 指导AI模型完成以下任务：
            - "你是一位专业的日记助手。"
            - "请根据以下用户的日记片段和问答记录，为他生成一篇第一人称的日记总结。"
            - "总结应保留用户的原始情感和主要观点，但语言更流畅、结构更清晰。"
            - "可以适当添加小标题，如'今日成就'、'一点思考'、'待办事项'等。"
            - "总结的结尾可以附上一句简短的鼓励或启发性评论。"
    - **输出**: 一个包含日记总结的字符串。

## 验收标准
- 调用总结服务/函数并传入模拟的日记和问答数据后，能返回一篇格式良好、内容相关的日记总结。
- AI生成的总结能够忠实地反映输入内容，而不是凭空捏造。
- 总结的语气和风格符合第一人称日记的特点。
- 服务/函数有良好的错误处理和超时机制。

## 依赖关系
- `task-diary-data-01`: 依赖其定义的 `entries` 和 `questions_answers` 数据结构作为输入。

## 状态追踪
- 未开始 
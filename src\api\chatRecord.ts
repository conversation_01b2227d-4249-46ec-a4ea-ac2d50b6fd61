import db from './database'

/**
 * 添加一条聊天记录
 * @param params 聊天记录参数
 * @returns 新增聊天记录的 ID
 */
export const addChatRecordApi = async (params: API.CreateParams<DB.ChatRecord>): Promise<string> => {
  return await db.table('chatRecord').add(params)
}

/**
 * 修改一条聊天记录
 * @param id 更新 id
 * @param params 更新参数
 * @returns 无返回值
 */
export const updateChatRecordApi = async (id: string, params: API.UpdateParams<DB.ChatRecord>): Promise<void> => {
  return await db.table('chatRecord').update(id, params)
}

/**
 * 获取一条聊天记录
 * @param _id 查询 id
 * @returns 聊天记录对象，未找到返回空对象
 */
export const getChatRecordApi = async (_id: string): Promise<DB.ChatRecord | Record<string, never>> => {
  try {
    const res = await db.table('chatRecord').get(_id)
    return res || {}
  } catch (error) {
    console.error(error)
    throw new Error(String(error))
  }
}

/**
 * 获取聊天记录列表
 * @param params 查询参数
 * @returns 聊天记录数组
 */
export const getChatRecordListApi = async (params?: string): Promise<DB.ChatRecord[]> => {
  try {
    const res = await db.table('chatRecord').where(params).toArray()
    return res
  } catch (error) {
    console.error(error)
    throw new Error(String(error))
  }
}

/**
 * 删除聊天记录
 * @param params 传入 _id 或者 筛选条件
 * @returns void
 */
export const delChatRecordApi = async (params: string): Promise<void> => {
  try {
    if (isUUID(params)) {
      await db.table('chatRecord').update(params, { deleteTime: new Date().toISOString() })
    } else {
      await db.table('chatRecord').where(params).modify({ deleteTime: new Date().toISOString() })
    }
  } catch (error) {
    console.error(error)
    throw new Error(String(error))
  }
}

<template>
  <view class="keyword-story-page">
    <z-page-navbar title="关键词讲故事">
      <template #right>
        <div class="action-btn" @click="showRecordListPopup = true" aria-label="训练记录">
          <i class="fas fa-archive"></i>
        </div>
      </template>
    </z-page-navbar>

    <view class="content-container">
      <!-- 关键词展示 -->
      <view class="keywords-container">
        <view class="keywords-header">
          <i class="keywords-icon fas fa-tags"></i>
          <view class="keywords-title">关键词</view>
          <i class="fas fa-sync-alt reset-keywords-btn" @click="handleReset" aria-label="重置"></i>
        </view>
        <view v-if="keywords.length > 0" class="floating-keywords">
          <view
            v-for="(keyword, index) in keywords"
            :key="keyword"
            class="floating-keyword-tag"
            :class="`animation-delay-${index % 5}`"
            :style="{
              animationDuration: `${6 + (index % 3)}s`,
            }"
          >
            {{ keyword }}
          </view>
        </view>
        <view v-else class="keywords-placeholder">
          <button v-if="!isGenerating" class="generate-btn" @click="generateKeywords">生成关键词</button>
          <z-loading v-else text="正在生成关键词" :font-size="32" padding="20px 0" />
        </view>
      </view>

      <!-- 用户故事 -->
      <view class="card story-card">
        <view class="card-header" @click="isStoryCollapsed = !isStoryCollapsed">
          <i class="card-icon fas fa-microphone"></i>
          <view class="card-title"
            >你的故事
            <text v-if="currentEvaluation" class="attempt-label">(第 {{ currentEvaluation.attempt }} 次)</text></view
          >
          <view
            v-if="evaluationHistory.length > 1 && !isRecording"
            class="history-btn"
            @click.stop="showHistoryPopup = true"
          >
            <i class="fas fa-history"></i>
            <text style="margin-left: 4px">复述历史</text>
          </view>
        </view>
        <view v-show="!isStoryCollapsed" class="story-content">
          <z-audio-player
            v-if="currentAudioURL"
            :src="currentAudioURL"
            :showTime="true"
            :themeColor="'var(--color-primary, #007aff)'"
            class="story-audio-player"
            ref="audioPlayerRef"
            @play="onAudioPlay"
            @pause="onAudioPause"
            @ended="onAudioEnded"
            :enableTranscription="true"
            :initial-sentences="currentEvaluation && currentEvaluation.sentences"
            @transcription-start="handleTranscriptionStart"
            @transcription-end="handleTranscriptionEnd"
          />
          <view v-else class="story-text">{{
            userStory || '请使用下方的语音或文本输入框开始讲述你的故事...'
          }}</view>
        </view>
      </view>

      <!-- AI 反馈 -->
      <view class="card feedback-card" v-if="(currentEvaluation || isEvaluating) && !isRecording">
        <view class="card-header" @click="isFeedbackCollapsed = !isFeedbackCollapsed">
          <i class="card-icon fas fa-comment-dots"></i>
          <view class="card-title">AI 反馈</view>
        </view>
        <view v-show="!isFeedbackCollapsed" class="feedback-content">
          <z-loading v-if="isEvaluating" text="正在评价中" :font-size="32" padding="40rpx 0" />
          <template v-else-if="currentEvaluation">
            <view class="feedback-summary">{{ currentEvaluation.summaryText }}</view>
            <view class="feedback-details">
              <view v-for="item in currentEvaluation.details" :key="item.metric" class="detail-item">
                <div class="detail-item-header">
                  <i :class="['metric-icon', item.icon]"></i>
                  <h4 class="metric-name">{{ item.metric }}</h4>
                </div>
                <p class="metric-feedback">{{ item.feedback }}</p>
              </view>
            </view>
          </template>
        </view>
      </view>

      <!-- 输入控制区域 -->
      <view class="input-container">
        <z-message-input
          v-model="inputMessage"
          placeholder="输入你的故事或点击麦克风图标录音..."
          @send="handleTextSubmit"
          @send-audio="handleAudioSubmit"
          @upload-progress="handleUploadProgress"
          @error="handleRecordError"
          :max-duration="180000"
          audio-format="mp3"
          cloud-path="speak/"
        />
      </view>
    </view>

    <l-history-popup
      v-model:show="showHistoryPopup"
      title="复述历史"
      :history-list="evaluationHistory"
      :current-item-id="currentEvaluation?.id"
      @select-item="handleSelectHistoryItem"
    />

    <!-- 训练记录弹窗 (内置实现) -->
    <uni-popup ref="recordListPopupRef" type="bottom" @change="handleRecordListPopupChange" style="z-index: 10000">
      <view class="record-popup-content">
        <view class="record-popup-header">
          <text class="record-popup-title">训练记录</text>
          <view class="header-actions">
            <text class="clear-all-btn" @click="handleClearAllRecords">全部清空</text>
            <view class="record-popup-close-icon" @click="closeRecordListPopup">
              <uni-icons type="closeempty" size="20"></uni-icons>
            </view>
          </view>
        </view>
        <scroll-view scroll-y class="record-popup-scroll-view">
          <z-loading v-if="isLoadingRecords" text="正在加载..." :font-size="32" padding="40rpx 0" />
          <view v-else-if="!parsedRecords.length" class="record-popup-empty-state">
            <text>暂无记录</text>
          </view>
          <view v-else class="record-popup-list">
            <view
              v-for="record in parsedRecords"
              :key="record._id"
              :class="['record-popup-item', { active: record._id === currentRecordId }]"
              @click="handleSelectRecordAndClosePopup(record)"
            >
              <view class="record-popup-item-main">
                <view class="record-popup-item-header">
                  <text class="record-popup-date">{{ formatDateForRecord(record.createTime) }}</text>
                </view>
                <view class="record-popup-keywords">
                  <uni-tag
                    v-for="(keyword, index) in record.keywords"
                    :key="index"
                    :text="keyword"
                    type="primary"
                    :inverted="true"
                    size="mini"
                    class="record-popup-keyword-tag"
                  />
                </view>
                <text class="record-popup-summary">{{ record.summaryText }}</text>
              </view>
              <view class="record-popup-item-delete" @click.stop="handleDeleteRecord(record._id)">
                <i class="fas fa-trash-alt"></i>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>

    <!-- Debug Modal -->
    <z-debug-modal
      v-model:show="debugModalVisible"
      :title="debugModalData.title"
      :error-type="debugModalData.errorType"
      :error-message="debugModalData.errorMessage"
      :error-details="debugModalData.errorDetails"
      @close="debugModalVisible = false"
    />
  </view>
</template>

<script setup>
import { ref, computed, watch, onUnmounted, nextTick } from 'vue'
import ZMessageInput from '@/components/z-message-input/z-message-input.vue'
import ZAudioPlayer from '@/components/z-audio-player/z-audio-player.vue'
import ZDebugModal from '@/components/z-debug-modal.vue'
import { getRandomKeywords } from '@/utils/keywords'
import { evaluateKeywordStory } from '@/api/speak'
import { addChatRecordApi, getChatRecordListApi, updateChatRecordApi, delChatRecordApi } from '@/api/chatRecord'
import ZLoading from '@/components/z-loading/index.vue'

const recordListPopupRef = ref(null)

// Debug Modal 状态
const debugModalVisible = ref(false)
const debugModalData = ref({
  title: 'Debug 信息',
  errorType: '',
  errorMessage: '',
  errorDetails: null,
})

// 显示 Debug 弹窗
const showDebugModal = (type, message, details) => {
  debugModalData.value = {
    title: '处理错误详情',
    errorType: type || '未知错误类型',
    errorMessage: message || '发生错误',
    errorDetails: details || {},
  }
  debugModalVisible.value = true
}

// 状态变量
const isStoryCollapsed = ref(false)
const isFeedbackCollapsed = ref(false)
const keywords = ref([])
const userStory = ref('')
const inputMessage = ref('')
const isGenerating = ref(false)
const isRecording = ref(false)
const isEvaluating = ref(false) // 控制评价加载状态
const evaluationHistory = ref([])
const currentEvaluationId = ref(null)
const currentRecordId = ref(null) // 新增：当前训练记录的 ID
const isTranscribing = ref(false)
const showHistoryPopup = ref(false)
const showRecordListPopup = ref(false)
const records = ref([])
const isLoadingRecords = ref(false)

const fetchRecords = async () => {
  isLoadingRecords.value = true
  try {
    const allRecords = await getChatRecordListApi()
    records.value = allRecords
      .filter((r) => {
        try {
          // 确保 content 是一个有效的 JSON 字符串
          if (typeof r.content === 'string' && r.content.trim().startsWith('{')) {
            const content = JSON.parse(r.content)
            return content.type === 'keywordStory'
          }
          return false
        } catch {
          return false
        }
      })
      .sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
  } catch (error) {
    console.error('Failed to fetch records', error)
    uni.showToast({ title: '加载记录失败', icon: 'none' })
    records.value = []
  } finally {
    isLoadingRecords.value = false
  }
}

watch(showRecordListPopup, (newValue) => {
  if (newValue) {
    fetchRecords()
    recordListPopupRef.value?.open()
  } else {
    recordListPopupRef.value?.close()
  }
})

const handleSelectRecordAndClosePopup = (record) => {
  handleSelectRecord(record)
  closeRecordListPopup()
}

const handleRecordListPopupChange = (e) => {
  if (!e.show) {
    showRecordListPopup.value = false
  }
}

const closeRecordListPopup = () => {
  showRecordListPopup.value = false
}

const parsedRecords = computed(() => {
  if (!records.value || records.value.length === 0) {
    return []
  }
  return records.value
    .map((record) => {
      try {
        const content = JSON.parse(record.content)
        // Adapt to new data structure where history is an array of evaluations
        const history = content.history || []
        return {
          _id: record._id,
          createTime: record.createTime,
          keywords: content.keywords,
          history: history,
          // For display in the list, we can show a summary of the last attempt
          summaryText: history.length > 0 ? history[history.length - 1].summaryText : '暂无评价',
        }
      } catch (e) {
        console.error('Failed to parse record content:', e)
        return null
      }
    })
    .filter(Boolean)
})

const formatDateForRecord = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(
    2,
    '0'
  )} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

const handleSelectRecord = (selectedRecord) => {
  console.log('Restoring from record:', selectedRecord)

  // The full evaluation data is now a history array
  const restoredHistory = selectedRecord.history || []

  // Reconstruct the full evaluation history for the component's state
  evaluationHistory.value = restoredHistory.map((item, index) => ({
    ...item, // Spread all persistent data
    // Add back the transient properties for UI display
    attempt: index + 1,
    // Use the timestamp from the item if it exists, otherwise fall back to the record's creation time
    timestamp: new Date(item.timestamp || selectedRecord.createTime),
  }))

  if (evaluationHistory.value.length > 0) {
    // Set current evaluation to the last one in the history
    currentEvaluationId.value = evaluationHistory.value[evaluationHistory.value.length - 1].id
  } else {
    currentEvaluationId.value = null
  }

  // Restore keywords and the record ID for future updates
  keywords.value = selectedRecord.keywords
  currentRecordId.value = selectedRecord._id

  uni.showToast({
    title: '记录已恢复',
    icon: 'none',
  })
}

// 删除单条训练记录
const handleDeleteRecord = async (recordId) => {
  try {
    await delChatRecordApi(recordId)
    uni.showToast({ title: '删除成功', icon: 'none' })

    // 从本地列表中移除
    records.value = records.value.filter((r) => r._id !== recordId)

    // 如果删除的是当前活动记录，则重置视图
    if (recordId === currentRecordId.value) {
      handleReset(false)
    }
  } catch (error) {
    console.error('Failed to delete record:', error)
    uni.showToast({ title: '删除失败', icon: 'none' })
  }
}

// 清空所有训练记录
const handleClearAllRecords = async () => {
  if (records.value.length === 0) {
    uni.showToast({ title: '没有可清空的记录', icon: 'none' })
    return
  }

  isLoadingRecords.value = true
  try {
    const recordIdsToDelete = records.value.map((r) => r._id)
    await Promise.all(recordIdsToDelete.map((id) => delChatRecordApi(id)))

    uni.showToast({ title: '已清空所有记录', icon: 'none' })

    // 清空本地数据
    records.value = []
    if (currentRecordId.value) {
      handleReset(false)
    }
  } catch (error) {
    console.error('Failed to clear records:', error)
    uni.showToast({ title: '清空失败', icon: 'none' })
  } finally {
    isLoadingRecords.value = false
  }
}

// 保存训练记录
const saveRecord = async () => {
  if (evaluationHistory.value.length === 0) return

  // Create a clean evaluation history for persistence
  const persistentHistory = evaluationHistory.value.map((e) => {
    const persistentItem = { ...e }
    delete persistentItem.attempt
    // timestamp can be kept if it's already a string, or converted.
    // Let's ensure it's a string for JSON.
    persistentItem.timestamp = new Date(persistentItem.timestamp).toISOString()
    return persistentItem
  })

  try {
    const recordPayload = {
      title: keywords.value.join(', '),
      content: JSON.stringify({
        type: 'keywordStory',
        keywords: keywords.value,
        history: persistentHistory, // Save the whole history
      }),
    }

    if (currentRecordId.value) {
      // Update existing record
      await updateChatRecordApi(currentRecordId.value, recordPayload)
      console.log('训练记录已更新：', currentRecordId.value)
    } else {
      // Add new record and store its ID
      const newId = await addChatRecordApi(recordPayload)
      currentRecordId.value = newId
      console.log('新训练记录已保存：', newId)
    }
  } catch (error) {
    console.error('保存训练记录失败：', error)
    // 静默失败，或只在开发模式下提示
  }
}

// 音频播放器相关状态
const currentAudioURL = ref('')
const isPlaying = ref(false)
const audioPlayerRef = ref(null)

// 当前选中的评价记录
const currentEvaluation = computed(() => {
  if (!currentEvaluationId.value && evaluationHistory.value.length > 0) {
    return evaluationHistory.value[evaluationHistory.value.length - 1]
  }
  return evaluationHistory.value.find((item) => item.id === currentEvaluationId.value) || null
})

// 监听当前评价变化，更新显示的故事内容和音频
watch(currentEvaluation, (newVal) => {
  if (newVal) {
    userStory.value = newVal.storyText
    if (newVal.audioURL) {
      currentAudioURL.value = newVal.audioURL
    } else {
      currentAudioURL.value = ''
    }
  }
})

// 生成关键词的函数
const generateKeywords = async (e) => {
  e && e.stopPropagation()
  isGenerating.value = true
  try {
    const newKeywords = getRandomKeywords(3)
    // This is a new session, so reset all relevant state
    handleReset(false) // Call reset without showing toast
    keywords.value = newKeywords
  } catch (error) {
    console.error('生成关键词失败：', error)
    uni.showToast({ title: '生成失败，请重试', icon: 'none' })
  } finally {
    isGenerating.value = false
  }
}

// 创建评价记录
const createEvaluation = async (attemptNumber, submittedText, sentences = null, audioData = null) => {
  if (keywords.value.length === 0) {
    uni.showToast({ title: '请先生成关键词', icon: 'none' })
    return null
  }

  try {
    const evaluationResult = await evaluateKeywordStory(submittedText, keywords.value)
    console.log('评价结果：', evaluationResult)
    // 接口请求完成后震动提示
    uni.vibrateShort()

    return {
      id: `attempt-${Date.now()}-${attemptNumber}`,
      attempt: attemptNumber,
      storyText: submittedText,
      sentences: sentences, // 保存详细文案
      timestamp: new Date(),
      summaryText: evaluationResult['总体评价'] || '分析完成',
      details: [
        { metric: '关键词使用', icon: 'fas fa-check-double', feedback: evaluationResult['关键词使用'] },
        { metric: '故事创意', icon: 'fas fa-lightbulb', feedback: evaluationResult['故事创意'] },
        { metric: '表达流畅度', icon: 'fas fa-wind', feedback: evaluationResult['表达流畅度'] },
        { metric: '语法和词汇', icon: 'fas fa-spell-check', feedback: evaluationResult['语法和词汇'] },
        {
          metric: 'AI 示例故事',
          icon: 'fas fa-robot',
          feedback: evaluationResult['AI 示例故事'] || '无法生成示例故事',
        },
        {
          metric: '故事改良版',
          icon: 'fas fa-magic',
          feedback: evaluationResult['故事改良版'] || '无法生成改良版故事',
        },
      ],
      audioURL: audioData ? audioData.tempFileURL || '' : '',
      audioDuration: audioData ? audioData.duration : 0,
      fileID: audioData ? audioData.fileID : '',
    }
  } catch (error) {
    console.error('评价失败：', error)
    showDebugModal('评价错误', '获取评价失败，请重试', error)
    uni.showToast({ title: '评价失败，请重试', icon: 'none' })
    return {
      id: `attempt-${Date.now()}-${attemptNumber}`,
      attempt: attemptNumber,
      storyText: submittedText,
      sentences: sentences, // 即使失败也保存
      timestamp: new Date(),
      summaryText: '评价请求失败，请重试',
      details: [
        { metric: '关键词使用', icon: 'fas fa-check-double', feedback: '无法获取评价' },
        { metric: '故事创意', icon: 'fas fa-lightbulb', feedback: '无法获取评价' },
        { metric: '表达流畅度', icon: 'fas fa-wind', feedback: '无法获取评价' },
        { metric: '语法和词汇', icon: 'fas fa-spell-check', feedback: '无法获取评价' },
        { metric: 'AI 示例故事', icon: 'fas fa-robot', feedback: '无法获取评价' },
        { metric: '故事改良版', icon: 'fas fa-magic', feedback: '无法获取评价' },
      ],
      audioURL: audioData ? audioData.tempFileURL || '' : '',
      audioDuration: audioData ? audioData.duration : 0,
      fileID: audioData ? audioData.fileID : '',
    }
  }
}

// 处理文本提交
const handleTextSubmit = async () => {
  if (!inputMessage.value.trim()) return
  userStory.value = inputMessage.value

  isEvaluating.value = true
  try {
    const attemptNumber = evaluationHistory.value.length + 1
    const newEvaluation = await createEvaluation(attemptNumber, inputMessage.value, null)
    if (newEvaluation) {
      evaluationHistory.value.push(newEvaluation)
      currentEvaluationId.value = newEvaluation.id
      inputMessage.value = ''
      uni.vibrateShort()
      // 保存记录
      saveRecord()
    }
  } catch (error) {
    console.error('文本评价失败：', error)
  } finally {
    isEvaluating.value = false
  }
}

// 存储当前音频数据
const currentAudioData = ref(null)

// 处理音频提交
const handleAudioSubmit = async (audioData) => {
  console.log('收到音频', audioData)
  isRecording.value = true
  currentAudioData.value = audioData
  if (audioData && audioData.tempFileURL) {
    currentAudioURL.value = audioData.tempFileURL
  }

  // 使用 nextTick 确保 DOM 更新后，player 组件的 ref 可用
  nextTick(() => {
    if (audioPlayerRef.value) {
      // 显式调用子组件的转写方法
      audioPlayerRef.value.transcribe()
    }
  })
}

// 处理转写
const handleTranscriptionStart = () => {
  console.log('开始转写')
  isTranscribing.value = true
}

const handleTranscriptionEnd = async (result) => {
  console.log('转写结束', result)
  isTranscribing.value = false
  isRecording.value = false

  if (result.success && result.transcript) {
    userStory.value = result.transcript
    isEvaluating.value = true
    try {
      const attemptNumber = evaluationHistory.value.length + 1
      const newEvaluation = await createEvaluation(attemptNumber, result.transcript, result.sentences, {
        tempFileURL: currentAudioData.value.tempFileURL,
        duration: currentAudioData.value.duration,
        fileID: currentAudioData.value.fileID,
      })
      if (newEvaluation) {
        evaluationHistory.value.push(newEvaluation)
        currentEvaluationId.value = newEvaluation.id
        uni.vibrateShort()
        // 保存记录
        saveRecord()
      }
      currentAudioData.value = null
    } catch (error) {
      console.error('转写评价失败：', error)
    } finally {
      isEvaluating.value = false
    }
  } else if (result.error) {
    showDebugModal('转写失败', '语音转写失败，请重试', result.error)
    uni.showToast({ title: '转写失败，请重试', icon: 'none' })
  }
}

// 其他事件处理
const onAudioPlay = () => (isPlaying.value = true)
const onAudioPause = () => (isPlaying.value = false)
const onAudioEnded = () => (isPlaying.value = false)
const handleUploadProgress = (progress) => console.log('上传进度', progress)

const handleRecordError = (error) => {
  console.error('录音错误', error)
  isRecording.value = false
  isTranscribing.value = false
  showDebugModal('录音错误', '录音或上传过程出错', error)
  uni.showToast({ title: '录音出错，请重试', icon: 'none', duration: 3000 })
}

const handleSelectHistoryItem = (id) => {
  currentEvaluationId.value = id
}

const formatTimestamp = (date) => {
  if (!date) return ''
  const d = new Date(date)
  return `${d.getHours().toString().padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}`
}

// 重置页面状态
const handleReset = (showToast = true) => {
  // 清理关键词和用户故事
  keywords.value = []
  userStory.value = ''
  inputMessage.value = ''

  // 清理评价历史和当前记录 ID
  evaluationHistory.value = []
  currentEvaluationId.value = null
  currentRecordId.value = null

  // 清理音频相关状态
  if (currentAudioURL.value && currentAudioURL.value.startsWith('blob:')) {
    URL.revokeObjectURL(currentAudioURL.value)
  }
  currentAudioURL.value = ''
  currentAudioData.value = null

  // 停止任何正在播放的音频
  if (audioPlayerRef.value) {
    audioPlayerRef.value.pause()
  }

  // 重置状态标记
  isRecording.value = false
  isTranscribing.value = false
  isGenerating.value = false

  if (showToast) {
    uni.showToast({
      title: '已重置',
      icon: 'none',
      duration: 1500,
    })
  }
}

// 组件卸载时清理资源
onUnmounted(() => {
  if (currentAudioURL.value && currentAudioURL.value.startsWith('blob:')) {
    URL.revokeObjectURL(currentAudioURL.value)
  }
  evaluationHistory.value.forEach((item) => {
    if (item.audioURL && item.audioURL.startsWith('blob:')) {
      URL.revokeObjectURL(item.audioURL)
    }
  })
})
</script>

<style scoped>
/* General Page Style */
.keyword-story-page {
  background-color: var(--color-bg-gray, #f4f5f7);
  min-height: 100vh;
  font-family: var(--font-sans);
}

.content-container {
  padding: 16px;
  padding-bottom: 110px; /* 为底部输入框留出空间 */
}

/* Input Container */
.input-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--color-white, #fff);
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.06);
  z-index: 100;
}

/* Custom Reset Button Style */
.reset-btn {
  margin-left: 10px;
  border-color: var(--color-primary-light-1, #a0cfff);
  color: var(--color-primary, #007aff);
}

/* Card Styles */
.card {
  background-color: var(--color-white, #fff);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
  border: 1px solid var(--color-border, #e9e9e9);
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--color-border, #f0f0f0);
  position: relative;
  cursor: pointer;
}

.card-icon {
  font-size: 20px;
  color: var(--color-primary, #007aff);
  margin-right: 12px;
  width: 24px;
  text-align: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary, #333);
}

.card-header .history-btn {
  margin-left: auto;
  font-size: 14px;
  color: var(--color-text-secondary, #666);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
  border: 1px solid var(--color-border-light, #e0e0e0);
}

.card-header .history-btn:hover {
  background-color: var(--color-bg-hover, #f0f1f2);
}

.attempt-label {
  font-size: 14px;
  color: var(--color-text-secondary, #666);
  font-weight: normal;
  margin-left: 8px;
}

/* Keywords Section */
.keywords-container {
  margin-bottom: 20px;
  padding: 10px 0;
}

.keywords-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--color-border, #f0f0f0);
}

.reset-keywords-btn {
  margin-left: auto;
  font-size: 18px;
  color: var(--color-text-secondary, #666);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.reset-keywords-btn:hover {
  background-color: var(--color-bg-hover, #f0f1f2);
  color: var(--color-primary, #007aff);
}

.keywords-icon {
  font-size: 20px;
  color: var(--color-primary, #007aff);
  margin-right: 12px;
  width: 24px;
  text-align: center;
}

.keywords-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary, #333);
}

.floating-keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 30px;
  padding: 0 10px;
  justify-content: center;
  min-height: 120px;
}

.floating-keyword-tag {
  background-color: var(--color-primary, #007aff);
  color: var(--color-white, #fff);
  padding: 8px 18px;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
  animation: floatAnimation 8s ease-in-out infinite;
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes floatAnimation {
  0%,
  100% {
    transform: translateY(0) rotate(-1deg);
  }
  25% {
    transform: translateY(-8px) rotate(1deg);
  }
  50% {
    transform: translateY(-4px) rotate(-2deg);
  }
  75% {
    transform: translateY(-12px) rotate(0deg);
  }
}

.animation-delay-0 {
  animation-delay: 0s;
}
.animation-delay-1 {
  animation-delay: 1s;
}
.animation-delay-2 {
  animation-delay: 2s;
}
.animation-delay-3 {
  animation-delay: 1.5s;
}
.animation-delay-4 {
  animation-delay: 0.5s;
}

.keywords-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

.generate-btn {
  background-color: var(--color-primary, #007aff);
  color: var(--color-white, #fff);
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 122, 255, 0.2);
}

.generate-btn:hover {
  background-color: var(--color-primary-dark, #0062cc);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 122, 255, 0.3);
}

/* Story Section */
.story-content {
  transition: all 0.3s ease-in-out;
}

.story-text {
  font-size: 16px;
  line-height: 1.7;
  color: var(--color-text-main, #444);
  min-height: 60px;
  font-style: italic;
  padding-top: 12px;
}

.story-audio-player {
  margin-bottom: 16px;
}

/* Feedback Card */
.feedback-content {
  transition: all 0.3s ease-in-out;
}

.feedback-card .feedback-content {
  padding-top: 8px;
}

.feedback-summary {
  font-size: 16px;
  line-height: 1.6;
  color: var(--color-text-primary, #333);
  margin-bottom: 24px;
  padding: 12px;
  background-color: var(--color-bg-light, #f9fafb);
  border-radius: 8px;
}

.feedback-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-item {
  position: relative;
  padding-left: 36px;
}

.detail-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.metric-icon {
  position: absolute;
  left: 0;
  top: 2px;
  font-size: 18px;
  color: var(--color-primary-light-2, #5c94ff);
  width: 24px;
  text-align: center;
}

.metric-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary, #333);
}

.metric-feedback {
  font-size: 14px;
  color: var(--color-text-secondary, #666);
  line-height: 1.6;
}

/* History Card */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.history-item {
  background-color: var(--color-bg-light, #f9fafb);
  padding: 12px 16px;
  border-radius: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid var(--color-border, #eee);
  transition: all 0.2s ease;
  cursor: pointer;
}

.history-item.active {
  background-color: var(--color-primary-light-hover, #e8f4ff);
  border-color: var(--color-primary, #b3d8ff);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
}

.history-item:hover:not(.active) {
  background-color: var(--color-bg-hover, #f0f1f2);
  border-color: var(--color-border-hover, #ddd);
}

.history-item-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.history-item-info .attempt-number {
  font-weight: 600;
  color: var(--color-text-primary, #333);
  font-size: 15px;
}

.history-item-info .timestamp {
  color: var(--color-text-secondary, #888);
  font-size: 13px;
}

.history-item-icon {
  color: var(--color-icon-secondary, #bbb);
  font-size: 14px;
  transition: color 0.2s ease;
}

.history-item.active .history-item-icon,
.history-item:hover .history-item-icon {
  color: var(--color-primary, #007aff);
}

/* History Popup */
.history-popup-container {
  padding: 16px;
  padding-bottom: 32px; /* For safe area */
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary, #333);
}

.popup-close-icon {
  font-size: 20px;
  color: var(--color-text-secondary, #666);
  cursor: pointer;
}

.history-list-scroll {
  max-height: 50vh;
}

/* Styles for the integrated Record List Popup */
.record-popup-content {
  background-color: white;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
.record-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 1;
}
.record-popup-title {
  font-size: 16px;
  font-weight: bold;
}
.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}
.clear-all-btn {
  font-size: 14px;
  color: var(--color-danger, #f56c6c);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}
.clear-all-btn:hover {
  background-color: var(--color-danger-light-2, #fde2e2);
}
.record-popup-close-icon {
  cursor: pointer;
}
.record-popup-scroll-view {
  height: 50vh;
}
.record-popup-empty-state {
  text-align: center;
  padding: 40px;
  color: #999;
}
.record-popup-list {
  padding: 0 15px;
}
.record-popup-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 5px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}
.record-popup-item.active {
  background-color: var(--color-primary-light-hover, #e8f4ff);
}
.record-popup-item:hover {
  background-color: var(--color-bg-hover, #f5f5f5);
}
.record-popup-item:last-child {
  border-bottom: none;
}
.record-popup-item-main {
  flex: 1;
  min-width: 0;
}
.record-popup-item-delete {
  padding: 8px;
  color: var(--color-text-secondary, #999);
  border-radius: 50%;
  transition: all 0.2s ease;
}
.record-popup-item-delete:hover {
  background-color: var(--color-danger-light-2, #fde2e2);
  color: var(--color-danger, #f56c6c);
}
.record-popup-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.record-popup-date {
  font-size: 12px;
  color: #999;
}
.record-popup-keywords {
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}
.record-popup-keyword-tag {
  margin-right: 5px;
}
.record-popup-summary {
  font-size: 14px;
  color: #333;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>

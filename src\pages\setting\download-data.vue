<template>
  <div class="download-data-container">
    <z-page-navbar title="数据初始化" />

    <div class="box-content">
      <div class="sync-progress">
        <div class="sync-icon" :class="{ success: syncStatus === '已完成' }">
          <i v-if="syncStatus !== '已完成'" class="fas fa-cloud-download-alt"></i>
          <i v-else class="fas fa-check-circle"></i>
        </div>
        <div class="sync-text">{{ statusText }}</div>
        <div v-if="syncStatus !== '已完成'" class="loading-spinner"></div>
      </div>

      <div class="sync-info">
        <div class="info-item">
          <div class="info-label">同步状态</div>
          <div class="info-value">{{ syncStatus }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">网络状态</div>
          <div class="info-value">{{ networkStatus }}</div>
        </div>
      </div>

      <div class="tip-box">
        <i class="fas fa-info-circle"></i>
        <span>首次使用需要同步数据，完成后将自动返回</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { syncToServer } from '@/api/syncServer'
import { toolGetNetworkType } from '@/utils/tools'
import { ref, onMounted, onBeforeUnmount } from 'vue'

const statusText = ref('正在初始化数据...')
const syncStatus = ref('同步中')
const networkStatus = ref('检测中...')

// 监听同步完成事件
const handleUpdateCloud = ({ isError }: { isError: boolean }) => {
  if (isError) {
    statusText.value = '数据同步失败'
    syncStatus.value = '失败'
    return
  } else {
    statusText.value = '数据同步完成！'
    syncStatus.value = '已完成'
  }

  // 延迟返回，给用户一个视觉反馈
  setTimeout(() => {
    router.back()
  }, 1500)
}

// 检查网络状态
const checkNetwork = async () => {
  try {
    const type = await toolGetNetworkType()
    networkStatus.value = type === 'none' ? '未连接' : `已连接 (${type})`

    // 如果没有网络，显示提示
    if (type === 'none') {
      statusText.value = '网络连接失败，请检查网络设置'
      syncStatus.value = '等待网络'
    }
  } catch (error) {
    networkStatus.value = '检测失败'
  }
}

onMounted(async () => {
  // 添加事件监听
  uni.$on('updateCloud', handleUpdateCloud)

  // 检查网络
  await checkNetwork()
  syncToServer()
})

onBeforeUnmount(() => {
  // 组件卸载前移除事件监听
  uni.$off('updateCloud', handleUpdateCloud)
})
</script>

<style scoped lang="scss">
.download-data-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20rpx;
}

.content-box {
  background-color: white;
  border-radius: 16rpx;
  padding: 50rpx 30rpx;
  margin: 30rpx 0;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  text-align: center;
}

.sync-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;

  .sync-icon {
    font-size: 100rpx;
    color: #5cadff;
    margin-bottom: 30rpx;

    &.success {
      color: #4cd964;
    }
  }

  .sync-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
  }

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 6rpx solid rgba(92, 173, 255, 0.3);
    border-top-color: #5cadff;
    border-radius: 50%;
    animation: spin 1s infinite linear;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.sync-info {
  margin: 40rpx 0;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;

  .info-item {
    display: flex;
    justify-content: space-between;
    padding: 16rpx 0;
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
    }

    .info-label {
      font-size: 28rpx;
      color: #666;
    }

    .info-value {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
  }
}

.tip-box {
  margin-top: 40rpx;
  font-size: 24rpx;
  color: #999;

  i {
    margin-right: 8rpx;
    color: #5cadff;
  }
}
</style>

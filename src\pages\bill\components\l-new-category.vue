<template>
  <view>
    <!-- 添加账单记录 -->
    <uni-popup ref="popRef" type="bottom" background-color="rgba(0,0,0,0)" @change="onChange">
      <view class="bg-white rounded-t-2 p-4 flex justify-center items-center flex-col">
        <view class="mb-4">{{ editId ? '修改分类' : '添加分类' }}</view>
        <uni-easyinput v-model="name" type="text" placeholder="" @confirm="sumbit" />
        <u-button class="mt-8" @click="sumbit" type="success" size="medium">确定</u-button>
      </view>
      <view class="z-bottom"></view>
    </uni-popup>
  </view>
</template>

<script setup>
const popRef = ref(null)
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  editId: {
    type: String,
    default: '',
  },
})
const emits = defineEmits(['update:open', 'submit'])
const name = ref('')

const sumbit = () => {
  const p = {
    name: name.value,
  }

  if (props.editId) {
    updateCategoryApi(props.editId, p)
      .then(() => {
        emits('update:open', false)
        emits('submit')
        // Handle success for update
      })
      .catch((error) => {
        // Handle error for update
      })
  } else {
    addCategoryApi(p)
      .then(() => {
        emits('update:open', false)
        emits('submit')
        // Handle success for add
      })
      .catch((error) => {
        // Handle error for add
      })
  }
}

watch(
  () => props.open,
  (val) => {
    if (val) {
      getCategoryApi().then((res) => {
        name.value = ''
        if (props.editId) {
          const item = res.find((item) => item._id === props.editId)
          name.value = item.name
        }
      })

      popRef.value.open('bottom')
    } else {
      popRef.value.close()
    }
  }
)
const onChange = (e) => {
  emits('update:open', e.show)
}
</script>

<style lang="scss"></style>

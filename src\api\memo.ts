import db from './database'
import dayjs from 'dayjs'
import { isUUID } from '@/utils/tools'

/**
 * 添加一条数据
 * @param params 参数
 */
export const addDiaryApi = async (params: API.NewMemo) => {
  await db.table('memo').add({
    ...params,
    type: params.type || 'diary',
  })
}
/**
 * 修改一条数据
 * @param {string} id 更新 id
 * @param {object} params 更新参数
 */
export const updateDiaryApi = async (id: string, params: API.EditOkr) => {
  return await db.table('memo').update(id, params)
}
// 获取 tag 列表
export const getTagListApi = async () => {
  const res = await db.table('memo').where(`type == "tag"`).toArray()
  return res
}
// 获取一条日回顾
export const getDiaryApi = async (date: string) => {
  date = dayjs(date).format('YYYY-MM-DD')
  const res = await db.table('memo').where(`type == "diary" && date == "${date}"`).toArray()
  return res[0]
}
// 按日期获取日回顾
export const getDiaryListApi = async (params: { startDate: string; endDate: string }) => {
  const res = await db
    .table('memo')
    .where(`type == "diary" && date >= "${params.startDate}" && date <= "${params.endDate}"`)
    .toArray()
  return res
}
// 获取日回顾 ai 总结列表
export const getDiaryAiListApi = async (date: string) => {
  const res = await db.table('memo').where(`type == "aiDiary" && date == "${date}"`).toArray()
  return res
}
/**
 * 删除
 * @params 传入 _id 或者 筛选条件
 */
export const delMemoApi = async (params: string) => {
  try {
    if (isUUID(params)) {
      await db.table('memo').update(params, { deleteTime: new Date().toISOString() })
    } else {
      await db.table('memo').where(params).modify({ deleteTime: new Date().toISOString() })
    }
  } catch (error) {
    console.error(error)
    throw new Error(String(error))
  }
}

/**
 * 通用查询备忘录方法
 * @param query 查询条件字符串，例如：`type == "weekGoal" && date == "2023-25"`
 * @returns 查询结果数组
 */
export const getMemo = async (query: string) => {
  try {
    const memos = await db.table('memo').where(query).toArray()
    return memos
  } catch (error) {
    console.error(error)
    throw new Error(String(error))
  }
}

/**
 * 添加备忘录
 * @param data 备忘录数据
 */
export const addMemo = async (data: API.NewMemo) => {
  try {
    await db.table('memo').add({
      ...data,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    })
  } catch (error) {
    console.error(error)
    throw new Error(String(error))
  }
}

/**
 * 更新备忘录
 * @param data 更新数据（包含 _id）
 */
export const updateMemo = async (data: { _id: string } & API.EditMemo) => {
  try {
    const { _id, ...updateData } = data
    return await db.table('memo').update(_id, {
      ...updateData,
      updateTime: new Date().toISOString()
    })
  } catch (error) {
    console.error(error)
    throw new Error(String(error))
  }
}

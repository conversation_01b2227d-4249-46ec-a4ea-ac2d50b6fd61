<template>
  <view class="transfer-info-container" :class="{'transfer-info-container-pc': sys.isPC}">
    <view class="transfer-info-content" :class="{'transfer-info-content-pc': sys.isPC}">
      <!-- 顶部：匹配码展示区域 -->
      <view class="match-code-area">
        <view class="match-code-header" @click="toggleMatchCodeVisible">
          <view class="header-left">
            <view class="match-code-title">匹配码</view>
            <view class="collapsed-match-code" v-if="!isMatchCodeVisible">
              <text>{{ matchCode }}</text>
            </view>
          </view>
          <view class="toggle-icon">
            <i :class="isMatchCodeVisible ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
          </view>
        </view>
        <view class="match-code-body" v-show="isMatchCodeVisible">
          <view class="match-code-content">
            <text class="match-code" v-if="!isEditingMatchCode">{{ matchCode }}</text>
            <input v-else class="match-code-input" v-model="newMatchCode" maxlength="6" placeholder="输入6位匹配码" />
            <view class="match-code-actions">
              <view class="action-btn edit-btn" @click="toggleEditMatchCode" v-if="!isEditingMatchCode">
                <i class="fas fa-edit"></i>
                <text>修改</text>
              </view>
              <view class="action-btn confirm-btn" @click="updateMatchCode" v-else>
                <i class="fas fa-check"></i>
                <text>确认</text>
              </view>
              <view class="action-btn copy-btn" @click="copyMatchCode" v-if="!isEditingMatchCode">
                <i class="fas fa-copy"></i>
                <text>复制</text>
              </view>
              <view class="action-btn cancel-btn" @click="cancelEditMatchCode" v-else>
                <i class="fas fa-times"></i>
                <text>取消</text>
              </view>
              <view class="action-btn refresh-btn" @click="regenerateMatchCode" v-if="!isEditingMatchCode">
                <i class="fas fa-sync-alt"></i>
                <text>重新生成</text>
              </view>
            </view>
          </view>
          <view class="match-code-tip">使用相同匹配码可在不同设备间传递信息</view>

          <!-- PC 端显示二维码 -->
          <view class="qrcode-section" @mouseenter="showQrcode = true" @mouseleave="showQrcode = false">
            <view class="qrcode-btn" @click="copyQrcodeUrl">
              <i class="fas fa-qrcode"></i>
              <text>扫码分享</text>
            </view>
            <view class="qrcode-container" v-if="showQrcode">
              <view class="qrcode-title">扫描二维码在移动设备上使用</view>
              <view class="qrcode-wrapper">
                <l-qrcode :value="qrcodeUrl" size="200" icon="" color="#007AFF" />
              </view>
              <view class="qrcode-tip">扫描后将自动填入当前匹配码</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 中部：信息列表区域 -->
      <view class="message-list-area">
        <view class="message-list-title">
          <text>信息列表</text>
          <text class="message-count" v-if="messageList.length > 0">({{ messageList.length }})</text>
          <view class="sync-btn" @click="fetchMessages">
            <i class="fas fa-sync-alt" :class="{'rotating': isSyncing}"></i>
            <text>同步</text>
          </view>
        </view>
        <view class="message-list" v-if="messageList.length > 0">
          <view class="message-item" v-for="(item, index) in messageList" :key="index">
            <view class="message-content">{{ item.content }}</view>
            <view class="message-footer">
              <text class="message-time">{{ formatTime(item.createTime) }}</text>
              <view class="message-copy" @click="copyMessage(item.content)">
                <i class="fas fa-copy"></i>
                <text>复制</text>
              </view>
            </view>
          </view>
        </view>
        <view class="empty-list" v-else>
          <i class="fas fa-inbox"></i>
          <text>暂无信息</text>
        </view>
      </view>

      <!-- 底部：信息输入与提交区域 -->
      <view class="message-input-area">
        <view class="input-container">
          <textarea class="message-input" v-model="messageInput" placeholder="输入要传递的信息..." maxlength="500"></textarea>
          <view class="input-counter">{{ messageInput.length }}/500</view>
        </view>
        <view class="submit-btn" @click="submitFromClipboard">
          <i class="fas fa-paper-plane" :class="{'rotating': isSending}"></i>
          <text>{{ isSending ? '发送中' : '发送' }}</text>
        </view>

      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted } from 'vue'
import useIsPC from '@/hooks/useIsPC'
import request from '@/utils/request'

// 导入二维码组件
import LQrcode from '@/uni_modules/lime-qrcode/components/l-qrcode/l-qrcode.vue'

// 定义组件名称
defineOptions({
  name: 'z-transfer-info-page'
})

// 判断是否是 PC 端
const sys = useIsPC()

// 匹配码
const matchCode = ref('')

// 信息列表
const messageList = ref([])

// 信息输入
const messageInput = ref('')

// 同步状态
const isSyncing = ref(false)

// 发送状态
const isSending = ref(false)

// 编辑匹配码状态
const isEditingMatchCode = ref(false)

// 新匹配码
const newMatchCode = ref('')

// 二维码显示状态
const showQrcode = ref(false)

// 匹配码显示状态
const isMatchCodeVisible = ref(false)

// 二维码 URL
const qrcodeUrl = computed(() => {
  // 获取当前 URL 并添加匹配码参数
  const baseUrl = window.location.href.split('?')[0]
  return `${baseUrl}?matchCode=${matchCode.value}`
})

// 生成随机匹配码（6 位字母数字组合）
const generateMatchCode = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let code = ''
  for (let i = 0; i < 6; i++) {
    code += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return code
}

// 保存匹配码到本地存储
const saveMatchCode = (code) => {
  try {
    uni.setStorageSync('transfer_match_code', code)
    return true
  } catch (e) {
    console.error('保存匹配码失败：', e)
    uni.showToast({
      title: '保存匹配码失败',
      icon: 'none'
    })
    return false
  }
}

// 从本地存储读取匹配码
const getMatchCodeFromStorage = () => {
  try {
    return uni.getStorageSync('transfer_match_code')
  } catch (e) {
    console.error('读取匹配码失败：', e)
    return null
  }
}

// 重新生成匹配码
const regenerateMatchCode = () => {
  const newCode = generateMatchCode()
  if (saveMatchCode(newCode)) {
    matchCode.value = newCode
    
    // 清空消息列表
    messageList.value = []
    
    // 重新获取数据
    fetchMessages()
    
    uni.showToast({
      title: '匹配码已更新',
      icon: 'success'
    })
  }
}

// 复制匹配码
const copyMatchCode = () => {
  uni.setClipboardData({
    data: matchCode.value,
    success: () => {
      uni.showToast({
        title: '匹配码已复制',
        icon: 'none'
      })
    }
  })
}

// 复制信息
const copyMessage = (content) => {
  uni.setClipboardData({
    data: content,
    success: () => {
      uni.showToast({
        title: '内容已复制',
        icon: 'none'
      })
    }
  })
}

// 复制二维码链接
const copyQrcodeUrl = () => {
  uni.setClipboardData({
    data: qrcodeUrl.value,
    success: () => {
      uni.showToast({
        title: '链接已复制',
        icon: 'none'
      })
    }
  })
}

// 格式化时间
const formatTime = (isoString) => {
  const date = new Date(isoString)
  const now = new Date()
  const isToday = date.toDateString() === now.toDateString()
  
  const padZero = (num) => num.toString().padStart(2, '0')
  const hours = padZero(date.getHours())
  const minutes = padZero(date.getMinutes())
  
  if (isToday) {
    return `今天 ${hours}:${minutes}`
  } else {
    const month = padZero(date.getMonth() + 1)
    const day = padZero(date.getDate())
    return `${month}-${day} ${hours}:${minutes}`
  }
}

// 提交信息
const submitMessage = async () => {
  if (!messageInput.value.trim() || isSending.value) return
  
  const params = {
    matchCode: matchCode.value,
    content: messageInput.value,
    createTime: new Date().toISOString()
  }
  
  try {
    isSending.value = true
    
    // 调用后端接口上传信息到云存储
    const serverinfo = await request.post('/okr/uploadInfoToCloudStorage', params)
    
    if (serverinfo) {
      // 添加到本地消息列表
      messageList.value.unshift(params)
      
      // 清空输入框
      messageInput.value = ''
      
      uni.showToast({
        title: '信息已发送',
        icon: 'success'
      })
    } else {
      uni.showToast({
        title: '发送失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('发送信息失败：', error)
    uni.showToast({
      title: '发送失败',
      icon: 'none'
    })
  } finally {
    isSending.value = false
  }
}

// 从输入框或剪贴板获取内容并发送
const submitFromClipboard = async () => {
  if (isSending.value) return
  
  // 如果输入框有内容，直接调用 submitMessage 发送
  if (messageInput.value.trim()) {
    await submitMessage()
    return
  }
  
  try {
    isSending.value = true
    
    // 获取剪贴板内容
    const clipboardData = await new Promise((resolve, reject) => {
      uni.getClipboardData({
        success: (res) => resolve(res.data),
        fail: (err) => reject(err)
      })
    })
    
    // 检查剪贴板是否有内容
    if (!clipboardData || !clipboardData.trim()) {
      uni.showToast({
        title: '剪贴板为空',
        icon: 'none'
      })
      isSending.value = false
      return
    }
    
    // 使用剪贴板内容
    const content = clipboardData.trim().substring(0, 500)
    
    const params = {
      matchCode: matchCode.value,
      content: content,
      createTime: new Date().toISOString()
    }
    
    // 调用后端接口上传信息到云存储
    const serverinfo = await request.post('/okr/uploadInfoToCloudStorage', params)
    
    if (serverinfo) {
      // 添加到本地消息列表
      messageList.value.unshift(params)
      
      uni.showToast({
        title: '信息已发送',
        icon: 'success'
      })
    } else {
      uni.showToast({
        title: '发送失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('发送信息失败：', error)
    uni.showToast({
      title: '发送失败',
      icon: 'none'
    })
  } finally {
    isSending.value = false
  }
}

// 从云端获取信息列表
const fetchMessages = async () => {
  try {
    // 设置同步状态
    isSyncing.value = true
    
    const params = {
      matchCode: matchCode.value
    }
    
    // 调用后端接口获取信息列表
    const result = await request.post('/okr/getInfoList', params)
    
    if (result && result.data) {
      // 更新本地消息列表
      messageList.value = result.data.sort((a, b) => {
        return new Date(b.createTime) - new Date(a.createTime)
      })
      
    } else {
      uni.showToast({
        title: '暂无数据',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取信息失败：', error)
    uni.showToast({
      title: '同步失败',
      icon: 'none'
    })
  } finally {
    // 重置同步状态
    isSyncing.value = false
  }
}

// 设置自动同步定时器
let syncTimer = null
let switchToSlowSyncTimer = null

// 页面加载时初始化匹配码
onMounted(() => {
  // 优先从 URL 参数中获取匹配码
  const params = getUrlParams()
  
  // 如果是 H5 环境且 URL 中有匹配码参数，优先使用 URL 中的匹配码
  if (params.matchCode) {
    code = params.matchCode
    // 保存到本地存储
    saveMatchCode(code)
    // 如果从 URL 获取了匹配码，则自动展开匹配码区域
    isMatchCodeVisible.value = true
  } else {
    // 否则从本地存储获取
    code = getMatchCodeFromStorage()
    
    // 如果本地存储也没有，则生成新的
    if (!code) {
      code = generateMatchCode()
      saveMatchCode(code)
    }
    
    // 默认收起匹配码区域
    isMatchCodeVisible.value = false
  }
  
  matchCode.value = code
  
  // 初始加载数据
  fetchMessages()
  
  // 初始阶段：每秒同步一次
  syncTimer = setInterval(() => {
    fetchMessages()
  }, 1000)
  
  // 一分钟后，切换到每 10 秒同步一次
  switchToSlowSyncTimer = setTimeout(() => {
    if (syncTimer) {
      clearInterval(syncTimer)
    }
    syncTimer = setInterval(() => {
      fetchMessages()
    }, 10000)
  }, 60000)
})

// 页面卸载时清除定时器
onUnmounted(() => {
  if (syncTimer) {
    clearInterval(syncTimer)
  }
  if (switchToSlowSyncTimer) {
    clearTimeout(switchToSlowSyncTimer)
  }
})

// 切换匹配码编辑状态
const toggleEditMatchCode = () => {
  isEditingMatchCode.value = true
  newMatchCode.value = matchCode.value
}

// 更新匹配码
const updateMatchCode = () => {
  // 验证匹配码格式
  const matchCodeRegex = /^[A-Z0-9]{6}$/;
  
  if (!matchCodeRegex.test(newMatchCode.value)) {
    uni.showToast({
      title: '请输入 6 位大写字母或数字',
      icon: 'none'
    })
    return;
  }
  
  // 更新匹配码
  matchCode.value = newMatchCode.value.toUpperCase();
  isEditingMatchCode.value = false;
  saveMatchCode(matchCode.value);
  
  // 清空消息列表
  messageList.value = [];
  
  // 重新获取数据
  fetchMessages();
  
  uni.showToast({
    title: '匹配码已更新',
    icon: 'success'
  })
}

// 取消匹配码编辑
const cancelEditMatchCode = () => {
  isEditingMatchCode.value = false
}

// 切换匹配码显示状态
const toggleMatchCodeVisible = () => {
  isMatchCodeVisible.value = !isMatchCodeVisible.value
}
</script>

<style>
.transfer-info-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8fafc;
  padding: 20rpx;
  box-sizing: border-box;
}

.transfer-info-container-pc {
  padding: 40rpx;
  align-items: center;
  justify-content: center;
}

.transfer-info-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.transfer-info-content-pc {
  max-width: 1000rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 122, 255, 0.08);
  background-color: #fff;
  padding: 40rpx;
}

/* 匹配码区域样式 */
.match-code-area {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.06);
  border: 1rpx solid rgba(0, 122, 255, 0.1);
}

.match-code-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 4rpx 0;
  transition: all 0.3s;
}

.match-code-header:hover {
  opacity: 0.8;
}

.header-left {
  display: flex;
  align-items: center;
}

.match-code-title {
  font-size: 28rpx;
  color: #555;
  margin-bottom: 0;
  font-weight: 600;
}

.collapsed-match-code {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #4361ee;
  background-color: rgba(67, 97, 238, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
  font-weight: 500;
  letter-spacing: 2rpx;
}

.collapsed-match-code text {
  background: linear-gradient(135deg, #4361ee, #3a86ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.toggle-icon {
  font-size: 24rpx;
  color: #888;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(67, 97, 238, 0.1);
  border-radius: 50%;
  transition: all 0.3s;
}

.toggle-icon i {
  transition: transform 0.3s;
}

.match-code-body {
  margin-top: 20rpx;
  opacity: 1;
  max-height: 500rpx;
  transition: all 0.3s ease;
  /* 移除 overflow: hidden 以防止二维码被裁剪 */
}

.match-code-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.match-code {
  font-size: 48rpx;
  font-weight: bold;
  letter-spacing: 4rpx;
  color: #4361ee;
  background: linear-gradient(135deg, #4361ee, #3a86ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.match-code-input {
  width: 200rpx;
  height: 80rpx;
  font-size: 48rpx;
  font-weight: bold;
  letter-spacing: 4rpx;
  color: #4361ee;
  border: 2rpx solid #4361ee;
  border-radius: 12rpx;
  padding: 0 20rpx;
  text-align: center;
}

.match-code-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #555;
  padding: 12rpx 24rpx;
  border-radius: 12rpx;
  background-color: #f0f7ff;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn i {
  margin-right: 10rpx;
  font-size: 24rpx;
}

.edit-btn {
  color: #4361ee;
  background-color: rgba(67, 97, 238, 0.1);
}

.confirm-btn {
  color: #10b981;
  background-color: rgba(16, 185, 129, 0.1);
}

.copy-btn {
  color: #6366f1;
  background-color: rgba(99, 102, 241, 0.1);
}

.cancel-btn {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
}

.refresh-btn {
  color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.1);
}

.match-code-tip {
  font-size: 24rpx;
  color: #888;
}

/* 二维码区域样式 */
.qrcode-section {
  margin-top: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.qrcode-btn {
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 24rpx;
  color: #4361ee;
  padding: 12rpx 24rpx;
  border-radius: 12rpx;
  background-color: rgba(67, 97, 238, 0.1);
  cursor: pointer;
  transition: all 0.3s;
}

.qrcode-btn:hover {
  background-color: rgba(67, 97, 238, 0.2);
}

.qrcode-btn i {
  font-size: 28rpx;
  color: #4361ee;
}

.qrcode-container {
  position: absolute;
  top: 60rpx;
  z-index: 1000;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: opacity 0.3s, transform 0.3s;
  border: 1rpx solid rgba(0, 122, 255, 0.1);
}

.qrcode-title {
  font-size: 28rpx;
  color: #555;
  margin-bottom: 20rpx;
  font-weight: 600;
}

.qrcode-wrapper {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.qrcode-tip {
  font-size: 24rpx;
  color: #888;
  margin-top: 20rpx;
}

.qrcode-copy {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  font-size: 24rpx;
  color: #4361ee;
  cursor: pointer;
  margin-top: 16rpx;
  padding: 10rpx 20rpx;
  background-color: rgba(67, 97, 238, 0.1);
  border-radius: 12rpx;
  transition: all 0.3s;
}

.qrcode-copy:hover {
  background-color: rgba(67, 97, 238, 0.2);
}

.qrcode-copy i {
  margin-right: 6rpx;
}

/* 信息列表区域样式 */
.message-list-area {
  flex: 1;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.06);
  border: 1rpx solid rgba(0, 122, 255, 0.1);
}

.message-list-title {
  font-size: 28rpx;
  color: #555;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  font-weight: 600;
}

.message-count {
  font-size: 24rpx;
  color: #888;
  margin-left: 10rpx;
}

.sync-btn {
  margin-left: auto;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #4361ee;
  padding: 12rpx 24rpx;
  border-radius: 12rpx;
  background-color: rgba(67, 97, 238, 0.1);
  cursor: pointer;
  transition: all 0.2s;
}

.sync-btn:hover {
  background-color: rgba(67, 97, 238, 0.2);
}

.sync-btn i {
  margin-right: 8rpx;
}

/* 添加旋转动画 */
.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.message-list {
  flex: 1;
  overflow-y: auto;
}

.message-item {
  padding: 24rpx;
  border-radius: 12rpx;
  background-color: #f8fafc;
  margin-bottom: 16rpx;
  transition: transform 0.2s;
}

.message-item:hover {
  transform: translateY(-2rpx);
}

.message-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 16rpx;
  word-break: break-all;
}

.message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-time {
  font-size: 24rpx;
  color: #888;
}

.message-copy {
  font-size: 24rpx;
  color: #4361ee;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background-color: rgba(67, 97, 238, 0.1);
  transition: all 0.2s;
}

.message-copy:hover {
  background-color: rgba(67, 97, 238, 0.2);
}

.message-copy i {
  margin-right: 6rpx;
}

.empty-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #888;
}

.empty-list i {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  color: #4361ee;
  opacity: 0.5;
}

/* 信息输入区域样式 */
.message-input-area {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.06);
  border: 1rpx solid rgba(0, 122, 255, 0.1);
}

.input-container {
  flex: 1;
  position: relative;
}

.message-input {
  width: 100%;
  height: 160rpx;
  background-color: #f8fafc;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  border: 1rpx solid rgba(0, 122, 255, 0.1);
}

.input-counter {
  position: absolute;
  right: 20rpx;
  bottom: 10rpx;
  font-size: 24rpx;
  color: #888;
}

.submit-btn {
  width: 120rpx;
  height: 160rpx;
  background: linear-gradient(135deg, #4361ee, #3a86ff);
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4rpx 12rpx rgba(67, 97, 238, 0.3);
}

.submit-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(67, 97, 238, 0.4);
}

.submit-btn i {
  font-size: 36rpx;
  margin-bottom: 10rpx;
}

.submit-btn text {
  font-size: 24rpx;
}

/* PC 端特有样式 */
@media screen and (min-width: 768px) {
  .match-code, .match-code-input {
    font-size: 56rpx;
  }
  
  .match-code-title, .message-list-title {
    font-size: 32rpx;
  }
  
  .message-content {
    font-size: 32rpx;
  }
  
  .action-btn, .message-copy {
    transition: all 0.3s;
  }
  
  .action-btn:hover, .message-copy:hover {
    transform: translateY(-2rpx);
  }
  
  .submit-btn:not(.submit-btn-disabled):hover {
    background: linear-gradient(135deg, #3a56e4, #2978ff);
  }
  
  .message-input {
    font-size: 32rpx;
    height: 200rpx;
  }
  
  .submit-btn {
    width: 160rpx;
    height: 200rpx;
  }
  
  .submit-btn i {
    font-size: 44rpx;
  }
  
  .submit-btn text {
    font-size: 28rpx;
  }
}
</style>

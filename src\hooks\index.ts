// 导出录音Hook
export { default as useRecord } from './useRecord'

// 按需导出各平台实现
// #ifdef H5
export { default as useRecordH5 } from './useRecordH5'
// #endif

// #ifdef APP-PLUS
export { default as useRecordApp } from './useRecordApp'
// #endif

// 导出工具函数
export { default as useRecordCompat } from './utils/useRecordCompat'
export { createRecord } from './utils/recordFactory'

// 导出类型定义
export * from './types/record'
export * from './utils/recordErrors'

<template>
  <div class="p-4">
    <view class="mb-10 mt-10 text-center text-40">分类管理</view>
    <view class="grid grid-cols-4 gap-4">
      <view
        @click="edit(item)"
        class="mb-4 flex justify-center items-center flex-col"
        v-for="(item, index) in classList"
        :key="index"
      >
        <view class="flex justify-center items-center color-white bg-[#3eb575] w-70 h-70 rounded-[50%] mb-2">{{
          item.name[0]
        }}</view>
        <view v-if="item.name !== '+'">{{ item.name }}</view>
      </view>
    </view>
  </div>
  <l-new-category v-model:open="showPop" :editId="editId" @submit="init"></l-new-category>
  <u-action-sheet :list="options" @click="clickOption" v-model="showOption"></u-action-sheet>
</template>

<script setup lang="ts">
const classList = ref([])
const showPop = ref(false)
const showOption = ref(false)
const editId = ref('')
const options = [
  {
    text: '删除',
    color: 'red',
  },
  {
    text: '修改',
  },
]
const clickOption = (index: number) => {
  if (index === 0) {
    delCategoryApi(editId.value).then(() => {
      init()
    })
  } else {
    showPop.value = true
  }
  showOption.value = false
}
const edit = (item) => {
  editId.value = ''
  if (item.name !== '+') {
    showOption.value = true
    editId.value = item._id
  } else {
    showPop.value = true
  }
}

const init = () => {
  getCategoryApi().then((res) => {
    // 按照创建时间排序
    res.sort((a, b) => {
      return new Date(a.createTime).getTime() - new Date(b.createTime).getTime()
    })

    res.push({
      _id: '',
      name: '+',
    })
    classList.value = res
  })
}
onMounted(init)
</script>

<style scoped lang="scss"></style>

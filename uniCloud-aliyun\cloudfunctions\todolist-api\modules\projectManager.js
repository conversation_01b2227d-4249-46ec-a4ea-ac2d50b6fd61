/**
 * 项目管理模块
 * 提供项目的 CRUD 操作和相关功能
 */

const { API_CONFIG, PROJECT_CONFIG, ERROR_CODES } = require('../config.js')
const { 
	createSuccessResponse, 
	createErrorResponse, 
	validateParams,
	simplifyProjectData,
	removeEmptyFields
} = require('../utils.js')

/**
 * 创建项目管理器
 * @param {object} context - 云对象上下文
 * @param {object} authManager - 认证管理器实例
 * @returns {object} 项目管理器实例
 */
function createProjectManager(context, authManager) {
	return {
		/**
		 * 获取项目列表
		 * @param {object} [options={}] - 查询参数对象
		 * @param {string} [options.keyword] - 关键词筛选
		 * @param {boolean} [options.includeClosed=false] - 是否包含已关闭的项目
		 * @returns {object} 项目列表
		 */
		async getProjects(options = {}) {
			const { keyword = null, includeClosed = false } = options;
			console.log('[ProjectManager] [getProjects] - 开始获取项目列表，过滤条件:', { keyword, includeClosed });
			try {
				const batchResult = await authManager.getBatchData()
				if (batchResult.errCode) {
					console.error('[ProjectManager] [getProjects] - 获取基础数据失败:', batchResult);
					return batchResult
				}

				let { projects } = batchResult.data
				let filteredProjects = []
				console.log(`[ProjectManager] [getProjects] - 原始项目数: ${projects.length}`);

				for (const project of projects) {
					// 是否包含已关闭的项目
					if (!includeClosed && project.closed) continue

					// 关键词筛选
					if (keyword) {
						const searchText = `${project.name || ''}`.toLowerCase()
						if (!searchText.includes(keyword.toLowerCase())) continue
					}

					// 简化项目数据
					const simplifiedProject = simplifyProjectData(project)
					filteredProjects.push(simplifiedProject)
				}

				console.log(`[ProjectManager] [getProjects] - 筛选后项目数: ${filteredProjects.length}`);
				return createSuccessResponse('获取项目列表成功', filteredProjects)

			} catch (error) {
				console.error('获取项目列表异常：', error)
				return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取项目列表失败', error)
			}
		},

		/**
		 * 创建项目
		 * @param {object} options - 项目数据对象
		 * @param {string} options.name - 项目名称 (必填)
		 * @param {string} [options.color="#3498db"] - 项目颜色
		 * @param {string} [options.kind="TASK"] - 项目类型（TASK/NOTE）
		 * @returns {object} 创建结果
		 */
		async createProject(options = {}) {
			const { name, color = "#3498db", kind = "TASK" } = options;
			console.log(`[ProjectManager] [createProject] - 开始创建项目，名称: ${name}`);
			// 参数校验
			const validation = validateParams({ name }, ['name'])
			if (validation) {
				console.warn('[ProjectManager] [createProject] - 参数校验失败:', validation);
				return validation
			}

			try {
				// 准备项目数据
				const projectData = {
					name: name,
					color: color,
					kind: kind,
					closed: false
				}

				// 移除空值字段
				const cleanProjectData = removeEmptyFields(projectData)
				if(context.debug) console.log('[ProjectManager] [createProject] - 准备发送的项目数据:', cleanProjectData);

				// 发送创建请求
				const result = await authManager._request('POST', API_CONFIG.PROJECT_URL, cleanProjectData)
				if (result.errCode) {
					console.error('[ProjectManager] [createProject] - API 请求创建项目失败:', result);
					return result
				}

				// 返回简化后的项目数据
				const simplifiedProject = simplifyProjectData(result.data)
				console.log(`[ProjectManager] [createProject] - 项目创建成功，ID: ${simplifiedProject.id}`);
				
				return createSuccessResponse('项目创建成功', simplifiedProject)

			} catch (error) {
				console.error('创建项目异常：', error)
				return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '创建项目失败', error)
			}
		},

		/**
		 * 更新项目
		 * @param {string} projectId - 项目 ID
		 * @param {object} updateData - 更新数据
		 * @returns {object} 更新结果
		 */
		async updateProject(projectId, updateData) {
			console.log(`[ProjectManager] [updateProject] - 开始更新项目 ID: ${projectId}`, { updateData });
			// 参数校验
			const validation = validateParams({ projectId }, ['projectId'])
			if (validation) {
				console.warn('[ProjectManager] [updateProject] - 参数校验失败:', validation);
				return validation
			}

			if (!updateData || typeof updateData !== 'object') {
				console.error('[ProjectManager] [updateProject] - 更新数据无效:', updateData);
				return createErrorResponse(ERROR_CODES.PARAM_INVALID, '更新数据不能为空')
			}

			try {
				// 移除空值字段
				const cleanUpdateData = removeEmptyFields(updateData)
				if(context.debug) console.log('[ProjectManager] [updateProject] - 准备发送的更新数据:', cleanUpdateData);

				// 发送更新请求
				const result = await authManager._request('PUT', `${API_CONFIG.PROJECT_URL}/${projectId}`, cleanUpdateData)
				if (result.errCode) {
					console.error(`[ProjectManager] [updateProject] - API 请求更新项目失败 (ID: ${projectId}):`, result);
					return result
				}

				// 返回简化后的项目数据
				const simplifiedProject = simplifyProjectData(result.data)
				console.log(`[ProjectManager] [updateProject] - 项目更新成功，ID: ${projectId}`);
				
				return createSuccessResponse('项目更新成功', simplifiedProject)

			} catch (error) {
				console.error('更新项目异常：', error)
				return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '更新项目失败', error)
			}
		},

		/**
		 * 删除项目
		 * @param {string} projectId - 项目 ID
		 * @returns {object} 删除结果
		 */
		async deleteProject(projectId) {
			console.log(`[ProjectManager] [deleteProject] - 开始删除项目 ID: ${projectId}`);
			// 参数校验
			const validation = validateParams({ projectId }, ['projectId'])
			if (validation) {
				console.warn('[ProjectManager] [deleteProject] - 参数校验失败:', validation);
				return validation
			}

			try {
				// 发送删除请求
				const result = await authManager._request('DELETE', `${API_CONFIG.PROJECT_URL}/${projectId}`)
				if (result.errCode) {
					console.error(`[ProjectManager] [deleteProject] - API 请求删除项目失败 (ID: ${projectId}):`, result);
					return result
				}

				console.log(`[ProjectManager] [deleteProject] - 项目删除成功, ID: ${projectId}`);
				return createSuccessResponse('项目删除成功', { projectId })

			} catch (error) {
				console.error('删除项目异常：', error)
				return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '删除项目失败', error)
			}
		},

		/**
		 * 关闭项目
		 * @param {string} projectId - 项目 ID
		 * @returns {object} 关闭结果
		 */
		async closeProject(projectId) {
			console.log(`[ProjectManager] [closeProject] - 标记项目为已关闭, ID: ${projectId}`);
			return await this.updateProject(projectId, { closed: true })
		},

		/**
		 * 重新打开项目
		 * @param {string} projectId - 项目 ID
		 * @returns {object} 重新打开结果
		 */
		async reopenProject(projectId) {
			console.log(`[ProjectManager] [reopenProject] - 重新打开项目, ID: ${projectId}`);
			return await this.updateProject(projectId, { closed: false })
		},

		/**
		 * 获取单个项目详情
		 * @param {string} projectId - 项目 ID
		 * @returns {object} 项目详情
		 */
		async getProject(projectId) {
			console.log(`[ProjectManager] [getProject] - 开始获取单个项目详情, ID: ${projectId}`);
			// 参数校验
			const validation = validateParams({ projectId }, ['projectId'])
			if (validation) {
				console.warn('[ProjectManager] [getProject] - 参数校验失败:', validation);
				return validation
			}

			try {
				// 发送获取请求
				const result = await authManager._request('GET', `${API_CONFIG.PROJECT_URL}/${projectId}`)
				if (result.errCode) {
					console.error(`[ProjectManager] [getProject] - API 请求获取项目详情失败 (ID: ${projectId}):`, result);
					return result
				}

				// 返回简化后的项目数据
				const simplifiedProject = simplifyProjectData(result.data)
				console.log(`[ProjectManager] [getProject] - 获取项目详情成功, ID: ${projectId}`);
				
				return createSuccessResponse('获取项目详情成功', simplifiedProject)

			} catch (error) {
				console.error('获取项目详情异常：', error)
				return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取项目详情失败', error)
			}
		}
	}
}

module.exports = createProjectManager

<template>
  <view>
    <!-- 添加账单记录 -->
    <uni-popup ref="popRef" type="bottom" background-color="rgba(0,0,0,0)" @change="onChange">
      <view class="bg-white rounded-t-2 px-4">
        <view class="py-6">{{ dayjs(props.date).format('YYYY 年 M 月 D 日') }}共支出￥{{ totalAmount }}</view>
        <scroll-view :scroll-top="scrollTop" scroll-y="true" class="h-600 pb-4">
          <view v-for="(item, i) in dayBills" :key="item._id">
            <view class="flex items-center px-4">
              <view class="t-30 text-gray w-60">{{ i + 1 }}</view>
              <view class="bg-[#3eb575] mr-4 w-70 h-70 rounded-[50%] color-white flex justify-center items-center">
                {{ categoryMap[item.category] ? categoryMap[item.category][0] : '无' }}
              </view>
              <view class="flex-1 py-4" style="border-bottom: 1px solid #eee">
                <view class="flex justify-between font-medium">
                  <text>{{ categoryMap[item.category] }}</text>
                  <text class="font-semibold">-{{ item.amount.toFixed(2) }}</text>
                </view>
                <view class="mt-1 flex justify-between items-center text-gray text-25">
                  <view>{{ item.remark || item.merchants }}</view>
                  <view>{{ dayjs(item.date).format('HH:mm') }}</view>
                </view>
              </view>
            </view>
          </view>
          <view class="text-center color-gray my-4">----- end -----</view>
        </scroll-view>
        <view class="py-6 text-center">
          <u-button plain size="medium" @click="emits('update:open', false)" type="success">知道了</u-button>
        </view>
      </view>
      <view class="z-bottom"></view>
    </uni-popup>
  </view>
</template>

<script setup>
import currency from 'currency.js'

const popRef = ref(null)
const dayBills = ref([])
const categoryMap = ref({})
const totalAmount = ref(0)

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  date: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['update:open'])

// 获取分类数据
const getCategoryData = async () => {
  const categories = await getCategoryApi()
  const map = {}
  categories.forEach(category => {
    map[category._id] = category.name
  })
  categoryMap.value = map
}

// 获取当天账单
const getDayBills = async () => {
  const bills = await getBillListApi()
  dayBills.value = bills.filter(bill => 
    dayjs(bill.date).format('YYYY-MM-DD') === props.date
  ).sort((a, b) => b.amount - a.amount)
  totalAmount.value = dayBills.value.reduce((acc, cur) => currency(acc).add(cur.amount), 0).value
  console.log(dayBills.value)
}

watch(() => props.open, async (val) => {
  if (val) {
    await getCategoryData()
    await getDayBills()
    popRef.value.open()
  } else {
    popRef.value.close()
  }
})

const onChange = (e) => {
  emits('update:open', e.show)
}
</script>

<style lang="scss"></style>

<!-- l-trend-chart.vue -->
<template>
  <div class="trend-chart-container">
    <!-- 时间维度切换 -->
    <div class="time-dimension-tabs">
      <div class="time-tab" :class="{ active: activeDimension === 'day' }" @click="handleDimensionChange('day')">
        日
      </div>
      <div class="time-tab" :class="{ active: activeDimension === 'week' }" @click="handleDimensionChange('week')">
        周
      </div>
      <div class="time-tab" :class="{ active: activeDimension === 'month' }" @click="handleDimensionChange('month')">
        月
      </div>
    </div>

    <!-- 图表容器 -->
    <div class="chart-area" ref="chartContainerRef">
      <div class="chart-container" v-if="hasData" :style="{ width: `${chartTotalWidth}px` }">
        <!-- Y轴刻度 -->
        <div class="y-axis">
          <div
            v-for="(tick, index) in yAxisTicks"
            :key="index"
            class="y-axis-tick"
            :style="{ bottom: `${tick.position}%` }"
          >
            <span class="y-axis-label">{{ tick.value }}</span>
          </div>
        </div>

        <!-- X轴刻度 -->
        <div class="x-axis">
          <div
            v-for="(tick, index) in currentData"
            :key="index"
            class="x-axis-tick"
            :style="{ left: `${index * pointSpacing}px` }"
            v-show="shouldShowLabel(index)"
          >
            <span class="x-axis-label">{{ tick.label }}</span>
          </div>
        </div>

        <!-- 折线图表 -->
        <div class="chart-content">
          <!-- 网格线 -->
          <div class="grid-lines">
            <div
              v-for="(tick, index) in yAxisTicks"
              :key="index"
              class="grid-line"
              :style="{ bottom: `${tick.position}%` }"
            ></div>
          </div>

          <!-- 折线和数据点 -->
          <div class="chart-line">
            <svg :width="chartTotalWidth" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
              <polyline
                :points="chartLinePoints"
                fill="none"
                stroke="var(--color-primary)"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>

          <!-- 数据点 -->
          <div
            v-for="(point, index) in currentData"
            :key="index"
            class="data-point"
            :class="{ active: hoveredPointIndex === index }"
            :style="{
              left: `${index * pointSpacing}px`,
              bottom: `${calculatePointPosition(point.value)}%`,
            }"
            @mouseover="hoveredPointIndex = index"
            @mouseleave="hoveredPointIndex = null"
          >
            <div class="data-point-tooltip" v-if="hoveredPointIndex === index">
              <div class="tooltip-date">{{ point.date }}</div>
              <div class="tooltip-value">{{ point.value }}{{ unit }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="empty-state" v-else>
        <i class="fas fa-chart-line"></i>
        <p>暂无进度数据</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, PropType, watch } from 'vue'
import dayjs from 'dayjs'

/**
 * 数据点接口定义
 */
interface DataPoint {
  date: string
  value: number
  label: string
}

/**
 * 定义组件属性
 */
const props = defineProps({
  // KR数据，包含进度记录等
  krData: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
  // 计量单位
  unit: {
    type: String,
    default: '',
  },
})

// 引用图表容器元素
const chartContainerRef = ref<HTMLElement | null>(null)

// 活跃的时间维度
const activeDimension = ref('day') // 'day', 'week', 'month'

// 当前悬停的数据点索引
const hoveredPointIndex = ref<number | null>(null)

// 拖动相关状态
const isDragging = ref(false)
const startX = ref(0)
const scrollLeft = ref(0)

// 数据点之间的间距（像素）
const pointSpacing = 40 // 每个数据点占据的宽度

// 图表总宽度，根据数据点数量动态计算
const chartTotalWidth = computed(() => {
  const minWidth = 300 // 最小宽度
  const dataLength = currentData.value.length
  const calculatedWidth = dataLength * pointSpacing
  return Math.max(minWidth, calculatedWidth)
})

// 模拟数据 - 日维度
const mockDailyData = ref<DataPoint[]>([
  { date: '2023-05-01', value: 10, label: '5-01' },
  { date: '2023-05-02', value: 15, label: '5-02' },
  { date: '2023-05-03', value: 12, label: '5-03' },
  { date: '2023-05-04', value: 18, label: '5-04' },
  { date: '2023-05-05', value: 22, label: '5-05' },
  { date: '2023-05-06', value: 20, label: '5-06' },
  { date: '2023-05-07', value: 25, label: '5-07' },
  { date: '2023-05-08', value: 28, label: '5-08' },
  { date: '2023-05-09', value: 30, label: '5-09' },
  { date: '2023-05-10', value: 35, label: '5-10' },
  { date: '2023-05-11', value: 32, label: '5-11' },
  { date: '2023-05-12', value: 38, label: '5-12' },
  { date: '2023-05-13', value: 42, label: '5-13' },
  { date: '2023-05-14', value: 40, label: '5-14' },
  { date: '2023-05-15', value: 45, label: '5-15' },
  { date: '2023-05-16', value: 48, label: '5-16' },
  { date: '2023-05-17', value: 50, label: '5-17' },
  { date: '2023-05-18', value: 53, label: '5-18' },
  { date: '2023-05-19', value: 55, label: '5-19' },
  { date: '2023-05-20', value: 58, label: '5-20' },
  { date: '2023-05-21', value: 56, label: '5-21' },
  { date: '2023-05-22', value: 60, label: '5-22' },
  { date: '2023-05-23', value: 65, label: '5-23' },
  { date: '2023-05-24', value: 68, label: '5-24' },
  { date: '2023-05-25', value: 70, label: '5-25' },
  { date: '2023-05-26', value: 72, label: '5-26' },
  { date: '2023-05-27', value: 75, label: '5-27' },
  { date: '2023-05-28', value: 78, label: '5-28' },
  { date: '2023-05-29', value: 80, label: '5-29' },
  { date: '2023-05-30', value: 82, label: '5-30' },
  { date: '2023-05-31', value: 85, label: '5-31' },
  { date: '2023-06-01', value: 87, label: '6-01' },
  { date: '2023-06-02', value: 90, label: '6-02' },
  { date: '2023-06-03', value: 88, label: '6-03' },
  { date: '2023-06-04', value: 92, label: '6-04' },
  { date: '2023-06-05', value: 95, label: '6-05' },
  { date: '2023-06-06', value: 98, label: '6-06' },
  { date: '2023-06-07', value: 100, label: '6-07' },
])

// 模拟数据 - 周维度
const mockWeeklyData = ref<DataPoint[]>([
  { date: '2023-05-01', value: 12, label: '第1周' },
  { date: '2023-05-08', value: 18, label: '第2周' },
  { date: '2023-05-15', value: 25, label: '第3周' },
  { date: '2023-05-22', value: 30, label: '第4周' },
  { date: '2023-05-29', value: 38, label: '第5周' },
  { date: '2023-06-05', value: 45, label: '第6周' },
  { date: '2023-06-12', value: 52, label: '第7周' },
  { date: '2023-06-19', value: 58, label: '第8周' },
  { date: '2023-06-26', value: 65, label: '第9周' },
  { date: '2023-07-03', value: 70, label: '第10周' },
  { date: '2023-07-10', value: 78, label: '第11周' },
  { date: '2023-07-17', value: 85, label: '第12周' },
  { date: '2023-07-24', value: 92, label: '第13周' },
  { date: '2023-07-31', value: 100, label: '第14周' },
])

// 模拟数据 - 月维度
const mockMonthlyData = ref<DataPoint[]>([
  { date: '2023-01-01', value: 8, label: '1月' },
  { date: '2023-02-01', value: 15, label: '2月' },
  { date: '2023-03-01', value: 25, label: '3月' },
  { date: '2023-04-01', value: 35, label: '4月' },
  { date: '2023-05-01', value: 48, label: '5月' },
  { date: '2023-06-01', value: 60, label: '6月' },
  { date: '2023-07-01', value: 75, label: '7月' },
  { date: '2023-08-01', value: 85, label: '8月' },
  { date: '2023-09-01', value: 92, label: '9月' },
  { date: '2023-10-01', value: 96, label: '10月' },
  { date: '2023-11-01', value: 98, label: '11月' },
  { date: '2023-12-01', value: 100, label: '12月' },
])

// 当前展示的数据
const currentData = computed<DataPoint[]>(() => {
  switch (activeDimension.value) {
    case 'day':
      return mockDailyData.value
    case 'week':
      return mockWeeklyData.value
    case 'month':
      return mockMonthlyData.value
    default:
      return mockDailyData.value
  }
})

// 是否显示标签
const shouldShowLabel = (index: number): boolean => {
  const total = currentData.value.length

  if (total <= 12) {
    // 数据点较少时显示全部标签
    return true
  } else if (total <= 24) {
    // 数据中等时，每隔一个显示标签
    return index % 2 === 0 || index === total - 1
  } else {
    // 数据较多时，按一定间隔显示标签
    return index % 5 === 0 || index === total - 1
  }
}

// 是否有数据
const hasData = computed(() => {
  // 使用模拟数据，所以始终为true
  // 实际应用中，应检查 props.krData?.recList?.length > 0
  return true
})

// 计算图表中的最大值和最小值
const dataRange = computed(() => {
  if (currentData.value.length === 0) return { min: 0, max: 100 }

  const values = currentData.value.map((item) => item.value)
  const min = Math.floor(Math.min(...values) * 0.8) // 为了视觉效果，最小值略小于实际值
  const max = Math.ceil(Math.max(...values) * 1.2) // 为了视觉效果，最大值略大于实际值

  return { min, max }
})

// 计算Y轴刻度值
const yAxisTicks = computed(() => {
  const { min, max } = dataRange.value
  const tickCount = 5 // 显示5个刻度
  const ticks = []

  for (let i = 0; i < tickCount; i++) {
    const value = min + (max - min) * (i / (tickCount - 1))
    ticks.push({
      value: Math.round(value),
      position: (i / (tickCount - 1)) * 100,
    })
  }

  return ticks
})

// 计算折线图的点坐标
const chartLinePoints = computed(() => {
  if (currentData.value.length === 0) return ''

  const totalPoints = currentData.value.length
  const width = 100

  return currentData.value
    .map((point, index) => {
      const x = (index / (totalPoints - 1)) * width
      const y = 100 - calculatePointPosition(point.value)
      return `${x},${y}`
    })
    .join(' ')
})

/**
 * 计算数据点在Y轴上的位置（百分比）
 */
const calculatePointPosition = (value: number): number => {
  const { min, max } = dataRange.value
  return ((value - min) / (max - min || 1)) * 100
}

/**
 * 切换时间维度
 */
const handleDimensionChange = (dimension: string) => {
  activeDimension.value = dimension

  // 当切换维度时，重置滚动位置
  if (chartContainerRef.value) {
    chartContainerRef.value.scrollLeft = 0
  }
}

// 拖动相关方法
const startDrag = (e: MouseEvent | TouchEvent) => {
  isDragging.value = true
  const container = chartContainerRef.value
  if (!container) return

  const clientX = e instanceof MouseEvent ? e.clientX : e.touches[0].clientX
  startX.value = clientX - container.offsetLeft
  scrollLeft.value = container.scrollLeft
}

const onDrag = (e: MouseEvent | TouchEvent) => {
  if (!isDragging.value) return
  e.preventDefault()

  const container = chartContainerRef.value
  if (!container) return

  const clientX = e instanceof MouseEvent ? e.clientX : e.touches[0].clientX
  const x = clientX - container.offsetLeft
  const walk = (x - startX.value) * 2 // 滚动速度因子
  container.scrollLeft = scrollLeft.value - walk
}

const endDrag = () => {
  isDragging.value = false
}

onMounted(() => {
  // 设置图表容器的鼠标滚轮事件，支持横向滚动
  const container = chartContainerRef.value
  if (container) {
    container.addEventListener(
      'wheel',
      (e) => {
        e.preventDefault()
        container.scrollLeft += e.deltaY > 0 ? 30 : -30
      },
      { passive: false }
    )
  }

  // 可以添加一些初始化逻辑，例如根据数据密度自动选择合适的时间维度
  if (mockDailyData.value.length > 10) {
    activeDimension.value = 'week'
  } else if (mockWeeklyData.value.length > 8) {
    activeDimension.value = 'month'
  }
})

// 实际应用中，当 krData 变化时需要更新图表数据
watch(
  () => props.krData,
  (newVal) => {
    if (newVal?.recList?.length) {
      // 处理真实数据，转换为图表所需格式
      // 本演示版本直接使用模拟数据
    }
  },
  { deep: true }
)
</script>

<style scoped>
.trend-chart-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 8px;
}

.time-dimension-tabs {
  display: flex;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 16px;
}

.time-tab {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
}

.time-tab.active {
  color: var(--color-primary);
  border-bottom: 2px solid var(--color-primary);
}

.chart-area {
  flex: 1;
  padding: 16px;
  min-height: 250px;
  position: relative;
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: thin;
}

/* 滚动条样式 */
.chart-area::-webkit-scrollbar {
  height: 6px;
}

.chart-area::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: 3px;
}

.chart-area::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: 3px;
}

.chart-area::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

.chart-container {
  height: 220px;
  position: relative;
  min-width: 100%;
  padding-left: 40px; /* 为Y轴标签留出空间 */
}

.chart-content {
  width: 100%;
  height: 180px;
  position: relative;
  border-left: 1px solid var(--color-gray-200);
  border-bottom: 1px solid var(--color-gray-200);
  margin-bottom: 20px;
}

.y-axis {
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 180px;
  z-index: 10;
}

.y-axis-tick {
  position: absolute;
  width: 100%;
  height: 1px;
  transform: translateY(50%);
  z-index: 10;
}

.y-axis-label {
  position: absolute;
  left: 0;
  transform: translateY(-50%);
  font-size: 12px;
  color: var(--color-text-secondary);
}

.x-axis {
  position: absolute;
  bottom: 0;
  left: 40px; /* 与y轴对齐 */
  width: calc(100% - 40px);
  height: 20px;
  z-index: 10;
}

.x-axis-tick {
  position: absolute;
  width: 1px;
  height: 20px;
  transform: translateX(-50%);
}

.x-axis-label {
  position: absolute;
  top: 0;
  transform: translateX(-50%);
  font-size: 12px;
  color: var(--color-text-secondary);
  white-space: nowrap;
}

.grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.grid-line {
  position: absolute;
  width: 100%;
  height: 1px;
  background-color: var(--color-gray-200);
  opacity: 0.5;
  transform: translateY(50%);
}

.chart-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.data-point {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: var(--color-white);
  border: 2px solid var(--color-primary);
  border-radius: 50%;
  transform: translate(-50%, 50%);
  z-index: 3;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.data-point.active {
  transform: translate(-50%, 50%) scale(1.5);
  z-index: 100;
}

.data-point-tooltip {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 101;
  pointer-events: none;
}

.data-point-tooltip:after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid rgba(0, 0, 0, 0.7);
}

.tooltip-date {
  font-size: 10px;
  opacity: 0.8;
}

.tooltip-value {
  font-weight: bold;
}

.empty-state {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  min-height: 180px;
}

.empty-state i {
  font-size: 32px;
  margin-bottom: 8px;
}
</style>

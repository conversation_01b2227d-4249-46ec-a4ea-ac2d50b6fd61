# 循环任务显示规范

根据任务循环类型，完善这个 getCustomRepeatProgressText 函数的返回：

## 基础循环类型

### 每天 (DAILY)

- **显示文本**：`今天：<今天已完成>/<今日完成量>`
- **例子**：`今天：20/100`
- **实现逻辑**：
  - 今天已完成：从当天的进度记录中获取完成量总和
  - 今日完成量：从任务的 dailyTarget 字段获取

### 每周几 (WEEKLY)

- **显示文本**：`今天：<今天已完成>/<今日完成量>`
- **例子**：`今天：20/100`
- **实现逻辑**：
  - 今天已完成：从当天的进度记录中获取完成量总和
  - 今日完成量：从任务的 dailyTarget 字段获取
  - 仅在规则指定的星期几显示

### 每月几号 (MONTHLY)

- **显示文本**：`今天：<今天已完成>/<今日完成量>`
- **例子**：`今天：20/100`
- **实现逻辑**：
  - 今天已完成：从当天的进度记录中获取完成量总和
  - 今日完成量：从任务的 dailyTarget 字段获取
  - 仅在规则指定的日期显示

### 每隔几天 (INTERVAL_DAILY)

- **显示文本**：`今天：<今天已完成>/<今日完成量>`
- **例子**：`今天：20/100`
- **实现逻辑**：
  - 今天已完成：从当天的进度记录中获取完成量总和
  - 今日完成量：从任务的 dailyTarget 字段获取
  - 仅在符合间隔规则的日期显示

## 周期内完成类型

### 每周几天 (WEEKLY_N_TIMES)

- **显示文本**：`本周：<本周完成天数>/<本周需完成天数> 今天：<今天已完成>/<今日完成量>`
- **例子**：`本周：2/3 今天：20/100`
- **实现逻辑**：
  - 本周完成天数：统计本周内已完成目标量的天数
  - 本周需完成天数：从规则的 count 字段获取
  - 今天已完成：从当天的进度记录中获取完成量总和
  - 今日完成量：从任务的 dailyTarget 字段获取

### 每月几天 (MONTHLY_N_TIMES)

- **显示文本**：`本月：<本月完成天数>/<本月需完成天数> 今天：<今天已完成>/<今日完成量>`
- **例子**：`本月：2/3 今天：20/100`
- **实现逻辑**：
  - 本月完成天数：统计本月内已完成目标量的天数
  - 本月需完成天数：从规则的 count 字段获取
  - 今天已完成：从当天的进度记录中获取完成量总和
  - 今日完成量：从任务的 dailyTarget 字段获取

### 每几天 (N_DAYS)

- **显示文本**：`第 <当前天数> 天 <周期天数> 天内：<本周期已完成>/<本周期总量>`
- **例子**：`第 2 天 3 天内：40/100`
- **实现逻辑**：
  - 当前天数：计算当前日期是周期内的第几天 (dayOfPeriod)
  - 周期天数：从规则的 interval 字段获取
  - 本周期已完成：统计当前周期内所有进度记录的总和 (completedCount)
  - 本周期总量：从任务的 totalRequired 字段获取 (totalCount)

### 每几个周 (N_WEEKS)

- **显示文本**：`第 <当前周数> 周 <周期周数> 周内：<本周期已完成>/<本周期总量>`
- **例子**：`第 2 周 3 周内：40/100`
- **实现逻辑**：
  - 当前周数：计算当前日期是周期内的第几周 (weekOfPeriod)
  - 周期周数：从规则的 interval 字段获取
  - 本周期已完成：统计当前周期内所有进度记录的总和 (completedCount)
  - 本周期总量：从任务的 totalRequired 字段获取 (totalCount)

### 每几个月 (N_MONTHS)

- **显示文本**：`第 <当前月数> 月 <周期月数> 月内：<本周期已完成>/<本周期总量>`
- **例子**：`第 2 月 3 月内：40/100`
- **实现逻辑**：
  - 当前月数：计算当前日期是周期内的第几月 (monthOfPeriod)
  - 周期月数：从规则的 interval 字段获取
  - 本周期已完成：统计当前周期内所有进度记录的总和 (completedCount)
  - 本周期总量：从任务的 totalRequired 字段获取 (totalCount)

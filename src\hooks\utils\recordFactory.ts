import useRecord from '../useRecord'
import { UseRecordOptions, UseRecordReturn } from '../types/record'
import useRecordCompat from './useRecordCompat'
import { ref } from 'vue'
import { RecordError } from './recordErrors'

/**
 * 创建录音实例配置选项
 */
export interface CreateRecordOptions extends UseRecordOptions {
  /** 自动请求权限 */
  autoRequestPermission?: boolean
  /** 自动检查兼容性 */
  checkCompat?: boolean
}

/**
 * 录音工厂函数，创建录音实例
 *
 * @param options 配置选项
 * @returns 录音实例创建结果
 */
export async function createRecord(options?: CreateRecordOptions) {
  const { autoRequestPermission = true, checkCompat = true, ...recordOptions } = options || {}

  // 状态
  const loading = ref(true)
  const error = ref<Error | null>(null)
  const instance = ref<any>(null)

  try {
    if (checkCompat) {
      // 检查兼容性
      const { isSupported, requestPermission } = useRecordCompat()

      if (!isSupported.value) {
        throw RecordError.notSupported()
      }

      // 请求权限
      if (autoRequestPermission) {
        const hasPermission = await requestPermission()
        if (!hasPermission) {
          throw RecordError.permissionDenied()
        }
      }
    }

    // 创建录音实例
    instance.value = useRecord(recordOptions)
  } catch (err) {
    error.value = err instanceof Error ? err : new Error(String(err))
  } finally {
    loading.value = false
  }

  return {
    loading,
    error,
    instance,
  }
}

# 录音功能兼容 App 端需求

## 背景

目前项目中的录音功能仅支持 H5 端，使用了基于 RecordRTC 库的实现方式。随着应用场景的扩展，需要使录音功能同时支持 App 端，以提供更加一致的用户体验。在 retell-page.vue 中的录音功能需要能够同时在 H5 和 App 环境下正常工作。

## 需求

### 功能需求

1. 将现有 H5 端的录音功能与 App 端的录音功能进行整合
2. 保持对外暴露的 API 接口一致，使得业务代码不需要关心底层实现差异
3. 自动根据运行环境选择合适的录音实现方式
4. 保留现有的录音功能，包括：
   - 开始录音
   - 暂停录音
   - 继续录音
   - 停止录音
   - 获取录音文件
   - 播放录音
   - 获取音频波形数据
   - 取消录音
5. 在 App 端使用 uni.getRecorderManager() 实现录音功能

### 非功能需求

1. 代码结构清晰，易于维护
2. 性能优化，确保录音过程流畅
3. 录音质量保持一致

## 技术方案

### 实现思路

将现有的录音功能拆分成三个文件：

1. **useRecordH5.ts**: 包含原有的 H5 录音实现
2. **useRecordApp.ts**: 新增的 App 端录音实现
3. **useRecord.ts**: 作为入口文件，使用 uniapp 条件编译自动选择合适的实现

### 架构设计

```mermaid
graph TD
    A[useRecord.ts] --> B{条件编译}
    B -->|#ifdef H5| C[useRecordH5.ts]
    B -->|#ifdef APP-PLUS| D[useRecordApp.ts]
    C --> E[RecordRTC实现]
    D --> F[uni.getRecorderManager实现]
    E --> G[统一返回结构]
    F --> G
    G --> H[业务代码]
```

### 详细设计

1. **useRecordH5.ts**

   - 直接迁移现有 useRecord.ts 的代码
   - 保持原有的接口和功能不变
   - 针对 H5 环境进行优化

2. **useRecordApp.ts**

   - 基于 uni.getRecorderManager() 实现
   - 提供与 H5 版本相同的接口
   - 处理 App 端特有的事件和状态

3. **useRecord.ts**
   - 使用 uniapp 条件编译判断当前运行环境
   - 根据环境导入并使用对应的实现
   - 统一对外接口，保持与原有 useRecord.ts 一致

### 条件编译实现方式

在 useRecord.ts 中使用 uniapp 的条件编译来导入和使用不同平台的实现：

```typescript
// useRecord.ts

import { ref } from 'vue'
import type { Ref } from 'vue'

// 导入类型定义
import type { UseRecordOptions, UseRecordReturn } from './types'

// #ifdef H5
import useRecordH5 from './useRecordH5'
// #endif

// #ifdef APP-PLUS
import useRecordApp from './useRecordApp'
// #endif

export default function useRecord(options?: UseRecordOptions): UseRecordReturn {
  // #ifdef H5
  return useRecordH5(options)
  // #endif

  // #ifdef APP-PLUS
  return useRecordApp(options)
  // #endif
}
```

### 接口定义

保持原有接口定义不变：

```typescript
interface UseRecordReturn {
  /** 录音状态 */
  isRecording: Readonly<Ref<boolean>>
  /** 是否已暂停 */
  isPaused: Readonly<Ref<boolean>>
  /** 录音时长 (ms) */
  duration: Readonly<Ref<number>>
  /** 音量大小 0-100 */
  volume: Readonly<Ref<number>>
  /** 录音文件 Blob */
  recordBlob: Readonly<Ref<Blob | null>>
  /** 录音文件 URL */
  recordURL: Readonly<Ref<string>>
  /** 开始录音 */
  startRecording: () => Promise<void>
  /** 暂停录音 */
  pauseRecording: () => void
  /** 继续录音 */
  resumeRecording: () => void
  /** 停止录音 */
  stopRecording: () => Promise<Blob | string>
  /** 播放录音 */
  playRecording: () => void
  /** 获取音频波形数据 */
  getAudioWaveform: () => Uint8Array | null
  /** 取消录音 */
  cancelRecording: () => void
  /** 清理资源 */
  destroy: () => void
}
```

### App 端实现关键点

1. 使用 uni.getRecorderManager() 获取录音管理器
2. 通过事件监听处理录音状态变化
3. 适配返回数据格式，使其与 H5 版本一致
4. 处理 App 端特有的权限和错误情况

### 代码改造步骤

1. 创建 useRecordH5.ts，将现有 useRecord.ts 的代码复制过去
2. 创建 useRecordApp.ts，实现 App 端的录音功能
3. 修改 useRecord.ts 为入口文件，使用条件编译根据环境选择实现
4. 更新 retell-page.vue 中的录音功能调用代码

## 风险评估

### 假设与未知因素

1. 假设 App 端的录音权限可以正常获取
2. 假设两种环境下的录音质量和格式可以兼容
3. 未知不同机型上 App 录音功能的兼容性差异

### 潜在风险

1. App 端和 H5 端的录音文件格式可能存在差异，导致后续处理逻辑需要调整
2. 部分低端设备可能存在录音性能问题
3. 不同环境下的音频波形数据获取方式不同，可能影响可视化效果
4. 录音权限获取失败的处理逻辑需要完善
5. 条件编译可能导致打包体积增大，同时引入两种实现方式

### 解决方案

1. 在 App 端实现中添加音频格式转换功能，确保输出格式一致
2. 增加录音质量参数配置，针对不同设备进行优化
3. 为音频波形数据添加模拟生成功能，保证在不支持的环境下仍有基本显示
4. 完善权限检查和错误处理流程
5. 使用 tree-shaking 和代码分割技术减小最终打包体积

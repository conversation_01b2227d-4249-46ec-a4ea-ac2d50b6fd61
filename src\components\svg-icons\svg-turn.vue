<template>
  <BaseSvgIcon :path="path" :viewBox="viewBox" v-bind="$attrs" />
</template>

<script setup>
import BaseSvgIcon from './BaseSvgIcon.vue'

defineProps({
  viewBox: {
    type: String,
    default: '0 0 1024 1024',
  },
  path: {
    type: String,
    default:
      'M498.8 50.8c25-25 65.6-25 90.6 0s25 65.6 0 90.6L538.6 192H832c106 0 192 86 192 192v64c0 35.4-28.6 64-64 64s-64-28.6-64-64v-64c0-35.4-28.6-64-64-64H538.6l50.8 50.8c25 25 25 65.6 0 90.6s-65.6 25-90.6 0l-160-160c-25-25-25-65.6 0-90.6l160-160z m26.6 512l160 160c25 25 25 65.6 0 90.6l-160 160c-25 25-65.6 25-90.6 0s-25-65.6 0-90.6l50.6-50.8H192c-35.4 0-64 28.6-64 64v64c0 35.4-28.6 64-64 64S0 995.4 0 960v-64c0-106 86-192 192-192h293.4l-50.8-50.8c-25-25-25-65.6 0-90.6s65.6-25 90.6 0zM768 768a128 128 0 1 1 256 0 128 128 0 1 1-256 0zM128 384a128 128 0 1 1 0-256 128 128 0 1 1 0 256z',
  },
})
</script>

# PC 端布局设计需求

## 背景

为了提升用户在桌面设备上的使用体验，我们计划对应用进行 PC 端兼容性优化。本次需求主要目标是设计和实现一个全新的 PC 端专属布局，以适应更宽的屏幕，并提供更高效的信息组织和导航方式。

## 需求

### 功能需求

1.  **三栏式布局**:

    - 在 `src/pc/index.vue` 文件中实现一个三栏式的主体布局容器。
    - 从左到右依次为：导航区（竖向 Tab）、主内容区（视图）、详情区。
    - 需要为这三个区域提供基础的 Demo 样式。

2.  **左侧导航区**:

    - 实现一个竖向的 Tab 导航栏。
    - Tab 切换时，只刷新中间的主内容区。
    - 导航区的宽度应固定。

3.  **中间主内容区**:

    - 该区域用于展示核心内容和列表视图。
    - 宽度应可自适应，占据剩余空间的大部分。
    - 点击此区域的内容项，应在右侧详情区展示相关详情。

4.  **右侧详情区**:
    - 用于展示选中项的详细信息。
    - 宽度固定，当没有选中项时可以隐藏或显示提示信息。

### 非功能需求

- **响应式设计**: 布局在不同尺寸的 PC 屏幕上都能良好地显示。
- **性能**: 页面加载和交互应保持流畅，避免卡顿。

## 技术方案

### 实现思路

- **布局容器**: 在 `src/pc/index.vue` 中使用 Flexbox 或 CSS Grid 来创建三栏布局的容器。
- **组件通信**:
  - `index.vue` 作为父组件，管理整体布局和核心状态。
  - 左侧导航、中间内容、右侧详情作为子组件。
  - 通过 `props` 将数据从父组件传递到子组件。
  - 通过 `emits` 将子组件的事件（如 Tab 切换、内容项点击）通知到父组件，由父组件统一处理状态变更。
- **状态持久化**: 使用本地缓存（如 `uni.setStorageSync` / `uni.getStorageSync`）来持久化当前激活的 Tab 或选中的项目 ID，以便在刷新后恢复状态。
- **组件化**:
  - 左侧竖向 Tab 封装成独立的组件 `l-pc-sidebar`。
  - 中间内容区根据不同模块创建不同的视图组件。
  - 右侧详情区封装成通用或特定模块的详情组件 `l-pc-detail`。

### 架构设计

```mermaid
graph TD
    subgraph PC端布局 (src/pc/index.vue)
        direction LR
        A[左侧导航<br/>l-pc-sidebar] -- "emit('tab-change')" --> B((父组件<br/>index.vue))
        B -- "props" --> A

        B -- "props" --> C{中间内容<br/>router-view}
        C -- "emit('item-select')" --> B

        B -- "props" --> D[右侧详情<br/>l-pc-detail]
    end

    subgraph 浏览器
        E[本地缓存<br/>uni.storage]
    end

    B -- "写入/读取状态" --> E
```

### 示例代码 (Demo 样式)

在 `src/pc/index.vue` 中可以实现如下基本结构和样式:

```vue
<template>
  <view class="pc-container">
    <!-- 左侧导航区 -->
    <view class="sidebar">
      <l-pc-sidebar @tab-change="handleTabChange" />
    </view>

    <!-- 中间主内容区 -->
    <view class="main-content">
      <router-view :key="activeTab" @item-select="handleItemSelect" />
    </view>

    <!-- 右侧详情区 -->
    <view class="detail-pane">
      <l-pc-detail :item-id="selectedItemId" />
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onMounted } from 'vue'

// ... 导入子组件

const activeTab = ref('')
const selectedItemId = ref(null)

const handleTabChange = (tab) => {
  activeTab.value = tab
  uni.setStorageSync('active-pc-tab', tab)
}

const handleItemSelect = (itemId) => {
  selectedItemId.value = itemId
  uni.setStorageSync('selected-pc-item', itemId)
}

onMounted(() => {
  activeTab.value = uni.getStorageSync('active-pc-tab') || 'default-tab'
  selectedItemId.value = uni.getStorageSync('selected-pc-item') || null
})
</script>

<style scoped>
.pc-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  background-color: #f0f2f5;
}

.sidebar {
  width: 200px;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.main-content {
  flex-grow: 1;
  padding: 20px;
  overflow-y: auto;
}

.detail-pane {
  width: 300px;
  background-color: #fff;
  border-left: 1px solid #e8e8e8;
  padding: 20px;
  flex-shrink: 0;
}
</style>
```

## 风险评估

### 假设与未知因素

- 假设当前所有页面模块都适合在 PC 端三栏布局中展示。
- 未知在将移动端组件复用到 PC 端时可能遇到的适配问题。

### 潜在风险

- **样式冲突**: 移动端和 PC 端的样式可能存在冲突。
  - **解决方案**: 使用 CSS 作用域（Scoped CSS）或 CSS Modules，并为 PC 端编写独立的样式文件。
- **组件复用**: 移动端设计的组件可能无法直接在 PC 端使用，需要重构或调整。
  - **解决方案**: 在开发前评估现有组件的复用性，并为 PC 端创建专用的布局和组件。

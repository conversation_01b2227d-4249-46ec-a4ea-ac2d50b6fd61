<template>
  <view class="container">
    <view class="title">选择训练模式</view>
    <view class="training-list">
      <view class="training-item" @click="goToStoryRetelling">
        <view class="item-content">
          <view class="item-title">故事复述</view>
          <view class="item-description">根据故事或笔记进行复述训练</view>
        </view>
        <view class="item-arrow">
          <i class="fas fa-chevron-right"></i>
        </view>
      </view>
      <!-- 更多训练模式可以加在这里 -->
    </view>
  </view>
</template>

<script setup>
import { router } from '@/utils/tools'

const goToStoryRetelling = () => {
  router.push('/pages/speak/retell-page')
}
</script>

<style lang="scss" scoped>
.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  color: #333;
}

.training-list {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.training-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.training-item:last-child {
  border-bottom: none;
}

.training-item:active {
  background-color: #fafafa;
}

.item-content {
  display: flex;
  flex-direction: column;
}

.item-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.item-description {
  font-size: 26rpx;
  color: #888;
}

.item-arrow i {
  font-size: 28rpx;
  color: #ccc;
}
</style> 
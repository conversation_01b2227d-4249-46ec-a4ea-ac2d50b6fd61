/**
 * 专注管理模块
 * 处理番茄专注相关功能
 */

const {
	DIDA_API_BASE,
	DIDA_POMODORO_APIS,
	REQUEST_CONFIG,
	buildDidaApiUrl,
} = require("../config");

const {
	logInfo,
	logError,
	createSuccessResponse,
	createErrorResponse,
	validateRequired,
	validateType,
	buildAuthHeaders,
	buildAuthCookies,
	safeJsonParse,
	formatDuration,
} = require("../utils");

/**
 * 获取专注概览（桌面版）
 * @param {string} authToken - 认证令牌
 * @param {string} csrfToken - CSRF令牌
 * @returns {object} 专注概览响应
 */
async function getPomodoroGeneral(authToken, csrfToken) {
	const methodName = "getPomodoroGeneral";
	logInfo(methodName, "开始获取专注概览");

	try {
		// 参数验证
		const authValidation = validateRequired(authToken, "authToken");
		if (authValidation) return authValidation;

		const csrfValidation = validateRequired(csrfToken, "csrfToken");
		if (csrfValidation) return csrfValidation;

		// 构建请求URL
		const apiUrl = buildDidaApiUrl(DIDA_POMODORO_APIS.general_for_desktop);

		// 构建请求头和cookies
		const headers = buildAuthHeaders(authToken, csrfToken);
		const cookies = buildAuthCookies(authToken, csrfToken);

		logInfo(methodName, "发起获取专注概览请求", { apiUrl });

		// 发起请求
		const response = await uniCloud.httpclient.request(apiUrl, {
			method: "GET",
			headers: headers,
			cookies: cookies,
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`获取专注概览失败，状态码: ${response.status}`);
		}

		// 解析响应数据
		const responseData = safeJsonParse(response.data, null);

		if (!responseData) {
			throw new Error("无法解析专注概览数据");
		}

		logInfo(methodName, "成功获取专注概览");

		// 处理专注概览数据
		const processedData = {
			// 今日专注统计
			today: {
				focus_time: responseData.todayFocusTime || 0,
				focus_time_formatted: formatDuration(responseData.todayFocusTime || 0),
				pomodoro_count: responseData.todayPomodoroCount || 0,
				task_count: responseData.todayTaskCount || 0,
			},
			// 本周专注统计
			week: {
				focus_time: responseData.weekFocusTime || 0,
				focus_time_formatted: formatDuration(responseData.weekFocusTime || 0),
				pomodoro_count: responseData.weekPomodoroCount || 0,
				task_count: responseData.weekTaskCount || 0,
			},
			// 总计专注统计
			total: {
				focus_time: responseData.totalFocusTime || 0,
				focus_time_formatted: formatDuration(responseData.totalFocusTime || 0),
				pomodoro_count: responseData.totalPomodoroCount || 0,
				task_count: responseData.totalTaskCount || 0,
			},
			// 专注趋势（最近7天）
			trend: responseData.trend || [],
			// 专注排名
			ranking: {
				current_rank: responseData.currentRank || 0,
				total_users: responseData.totalUsers || 0,
				beat_percentage: responseData.beatPercentage || 0,
			},
		};

		return createSuccessResponse("获取专注概览成功", processedData);
	} catch (error) {
		logError(methodName, "获取专注概览失败", error);
		throw error;
	}
}

/**
 * 获取专注分布数据
 * @param {string} authToken - 认证令牌
 * @param {string} csrfToken - CSRF令牌
 * @param {string} startDate - 开始日期，格式：YYYY-MM-DD
 * @param {string} endDate - 结束日期，格式：YYYY-MM-DD
 * @returns {object} 专注分布响应
 */
async function getFocusDistribution(authToken, csrfToken, startDate, endDate) {
	const methodName = "getFocusDistribution";
	logInfo(methodName, "开始获取专注分布", { startDate, endDate });

	try {
		// 参数验证
		const authValidation = validateRequired(authToken, "authToken");
		if (authValidation) return authValidation;

		const csrfValidation = validateRequired(csrfToken, "csrfToken");
		if (csrfValidation) return csrfValidation;

		const startDateValidation = validateRequired(startDate, "startDate");
		if (startDateValidation) return startDateValidation;

		const endDateValidation = validateRequired(endDate, "endDate");
		if (endDateValidation) return endDateValidation;

		// 构建请求URL（带日期参数）
		const apiUrl = `${buildDidaApiUrl(
			DIDA_POMODORO_APIS.focus_distribution
		)}/${startDate}/${endDate}`;

		// 构建请求头和cookies
		const headers = buildAuthHeaders(authToken, csrfToken);
		const cookies = buildAuthCookies(authToken, csrfToken);

		logInfo(methodName, "发起获取专注分布请求", { apiUrl });

		// 发起请求
		const response = await uniCloud.httpclient.request(apiUrl, {
			method: "GET",
			headers: headers,
			cookies: cookies,
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`获取专注分布失败，状态码: ${response.status}`);
		}

		// 解析响应数据
		const responseData = safeJsonParse(response.data, null);

		if (!responseData) {
			throw new Error("无法解析专注分布数据");
		}

		logInfo(methodName, "成功获取专注分布");

		// 处理专注分布数据
		const processedData = {
			date_range: {
				start_date: startDate,
				end_date: endDate,
			},
			// 按项目分布
			by_project: responseData.byProject || [],
			// 按标签分布
			by_tag: responseData.byTag || [],
			// 按日期分布
			by_date: responseData.byDate || [],
			// 总计信息
			summary: {
				total_focus_time: responseData.totalFocusTime || 0,
				total_focus_time_formatted: formatDuration(
					responseData.totalFocusTime || 0
				),
				total_pomodoro_count: responseData.totalPomodoroCount || 0,
				average_daily_focus: responseData.averageDailyFocus || 0,
				average_daily_focus_formatted: formatDuration(
					responseData.averageDailyFocus || 0
				),
			},
		};

		return createSuccessResponse("获取专注分布成功", processedData);
	} catch (error) {
		logError(methodName, "获取专注分布失败", error);
		throw error;
	}
}

/**
 * 获取专注时间线
 * @param {string} authToken - 认证令牌
 * @param {string} csrfToken - CSRF令牌
 * @param {number} limit - 结果数量限制，默认50
 * @returns {object} 专注时间线响应
 */
async function getFocusTimeline(authToken, csrfToken, limit = 50) {
	const methodName = "getFocusTimeline";
	logInfo(methodName, "开始获取专注时间线", { limit });

	try {
		// 参数验证
		const authValidation = validateRequired(authToken, "authToken");
		if (authValidation) return authValidation;

		const csrfValidation = validateRequired(csrfToken, "csrfToken");
		if (csrfValidation) return csrfValidation;

		const limitValidation = validateType(limit, "number", "limit");
		if (limitValidation) return limitValidation;

		// 构建请求URL（带限制参数）
		const apiUrl = `${buildDidaApiUrl(
			DIDA_POMODORO_APIS.focus_timeline
		)}?limit=${limit}`;

		// 构建请求头和cookies
		const headers = buildAuthHeaders(authToken, csrfToken);
		const cookies = buildAuthCookies(authToken, csrfToken);

		logInfo(methodName, "发起获取专注时间线请求", { apiUrl });

		// 发起请求
		const response = await uniCloud.httpclient.request(apiUrl, {
			method: "GET",
			headers: headers,
			cookies: cookies,
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`获取专注时间线失败，状态码: ${response.status}`);
		}

		// 解析响应数据
		const responseData = safeJsonParse(response.data, null);

		if (!responseData) {
			throw new Error("无法解析专注时间线数据");
		}

		logInfo(methodName, "成功获取专注时间线", {
			recordCount: responseData.length || 0,
		});

		// 处理专注时间线数据
		const timeline = Array.isArray(responseData) ? responseData : [];
		const processedTimeline = timeline.map((record) => ({
			id: record.id,
			task_id: record.taskId,
			task_title: record.taskTitle,
			project_id: record.projectId,
			project_name: record.projectName,
			start_time: record.startTime,
			end_time: record.endTime,
			duration: record.duration,
			duration_formatted: formatDuration(record.duration || 0),
			created_time: record.createdTime,
			is_completed: record.isCompleted || false,
		}));

		return createSuccessResponse("获取专注时间线成功", {
			timeline: processedTimeline,
			total: processedTimeline.length,
			limit: limit,
		});
	} catch (error) {
		logError(methodName, "获取专注时间线失败", error);
		throw error;
	}
}

/**
 * 获取专注热力图
 * @param {string} authToken - 认证令牌
 * @param {string} csrfToken - CSRF令牌
 * @param {string} startDate - 开始日期，格式：YYYY-MM-DD
 * @param {string} endDate - 结束日期，格式：YYYY-MM-DD
 * @returns {object} 专注热力图响应
 */
async function getFocusHeatmap(authToken, csrfToken, startDate, endDate) {
	const methodName = "getFocusHeatmap";
	logInfo(methodName, "开始获取专注热力图", { startDate, endDate });

	try {
		// 参数验证
		const authValidation = validateRequired(authToken, "authToken");
		if (authValidation) return authValidation;

		const csrfValidation = validateRequired(csrfToken, "csrfToken");
		if (csrfValidation) return csrfValidation;

		const startDateValidation = validateRequired(startDate, "startDate");
		if (startDateValidation) return startDateValidation;

		const endDateValidation = validateRequired(endDate, "endDate");
		if (endDateValidation) return endDateValidation;

		// 构建请求URL（带日期参数）
		const apiUrl = `${buildDidaApiUrl(
			DIDA_POMODORO_APIS.focus_heatmap
		)}/${startDate}/${endDate}`;

		// 构建请求头和cookies
		const headers = buildAuthHeaders(authToken, csrfToken);
		const cookies = buildAuthCookies(authToken, csrfToken);

		logInfo(methodName, "发起获取专注热力图请求", { apiUrl });

		// 发起请求
		const response = await uniCloud.httpclient.request(apiUrl, {
			method: "GET",
			headers: headers,
			cookies: cookies,
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`获取专注热力图失败，状态码: ${response.status}`);
		}

		// 解析响应数据
		const responseData = safeJsonParse(response.data, null);

		if (!responseData) {
			throw new Error("无法解析专注热力图数据");
		}

		logInfo(methodName, "成功获取专注热力图");

		// 处理专注热力图数据
		const processedData = {
			date_range: {
				start_date: startDate,
				end_date: endDate,
			},
			// 热力图数据（按日期）
			heatmap_data: responseData.heatmapData || [],
			// 按小时分布
			hourly_distribution: responseData.hourlyDistribution || [],
			// 按星期分布
			weekly_distribution: responseData.weeklyDistribution || [],
			// 统计信息
			statistics: {
				total_days: responseData.totalDays || 0,
				active_days: responseData.activeDays || 0,
				max_daily_focus: responseData.maxDailyFocus || 0,
				max_daily_focus_formatted: formatDuration(
					responseData.maxDailyFocus || 0
				),
				average_daily_focus: responseData.averageDailyFocus || 0,
				average_daily_focus_formatted: formatDuration(
					responseData.averageDailyFocus || 0
				),
			},
		};

		return createSuccessResponse("获取专注热力图成功", processedData);
	} catch (error) {
		logError(methodName, "获取专注热力图失败", error);
		throw error;
	}
}

// 导出专注管理模块函数
module.exports = {
	getPomodoroGeneral,
	getFocusDistribution,
	getFocusTimeline,
	getFocusHeatmap,
};

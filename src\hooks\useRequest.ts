import { ref, onMounted, onUnmounted, Ref } from 'vue'

interface UseRequestOptions {
  defaultParams?: any
  formatter?: (data: any) => any
  manual?: boolean
  ready?: Ref<boolean>
  // isWacth?: boolean
  initData?: any
}

/**
 *
 * @param requestFn 请求函数
 * @param formatter 格式化数据
 * @param manual 是否手动执行
 */
export function useRequest(requestFn, options: UseRequestOptions = {}) {
  const {
    defaultParams,
    ready,
    formatter = (data: any) => data,
    manual = true,
    // isWacth = true,
    initData = [],
  } = options
  const data = ref(initData)
  const loading = ref(false)
  const error = ref()

  let curParams = JSON.parse(JSON.stringify(defaultParams || {}))
  const currentRun = () => run(curParams)

  // TODO 入参应该可选
  const run = async (params) => {
    loading.value = true
    error.value = null
    // data.value = initData
    curParams = params

    try {
      const response = await requestFn(params)
      // 订阅数据变化
      // if (isWacth) {
      //   console.log('订阅数据变化了', response)

      //   if (Array.isArray(response)) {
      //     response.forEach((item) => {
      //       const { _id } = item
      //       if (!_id) return

      //       db.subscribe(_id, currentRun) // TODO 这里是否会导致内存泄漏
      //     })
      //   } else if (isObject(response)) {
      //     const { _id } = response
      //     if (!_id) return
      //     db.subscribe(_id, currentRun) // TODO 这里是否会导致内存泄漏
      //   } else {
      //     console.error('无法订阅，数据格式不正确')
      //   }
      // }

      data.value = await formatter(response)
    } catch (e) {
      error.value = e
    } finally {
      loading.value = false
    }
  }

  if (ready !== undefined) {
    watch(ready, (newVal) => {
      if (newVal) {
        run(defaultParams)
      }
    })
  }

  // 携带默认参数，重新请求
  const refresh = () => {
    run(defaultParams)
  }

  if (!manual) run(defaultParams)

  onUnmounted(() => {
    // console.log('卸载订阅数据变化')
    // console.log(data.value)
    // // 取消订阅数据变化
    // if (!isWacth) return
    // if (Array.isArray(data.value)) {
    //   console.log('isArray')
    //   data.value.forEach((item) => {
    //     if (!item._id) return
    //     db.unsubscribe(item._id, currentRun)
    //   })
    // } else if (isObject(data.value)) {
    //   if (!data?.value?._id) return
    //   db.unsubscribe(data.value._id, currentRun)
    // }
  })

  return { data, loading, error, run, refresh }
}

<template>
	<view class="pages">
		<!-- #ifndef H5 -->
		<statusBar></statusBar>
		<!-- #endif -->

		<!-- 搜索功能 -->
		<view class="uni-search-box">
			<uni-search-bar v-model="keyword" ref="searchBar" radius="100" cancelButton="none" disabled
				:placeholder="inputPlaceholder" />
			<view class="cover-search-bar" @click="searchClick"></view>
		</view>
		<unicloud-db v-slot:default="{data, loading, error, options}" collection="memos">
			<view v-if="error">{{error.message}}</view>
			<view v-else>
				<view class="" v-for="(item,index) in data">
					{{item.content}}
				</view>
			</view>
		</unicloud-db>
		<unicloud-db ref='udb' v-slot:default="{data,pagination,hasMore, loading, error, options}" @error="onqueryerror"
			:collection="colList" :page-size="10">
			<!-- 基于 uni-list 的页面布局 field="user_id.nickname"-->
			<uni-list class="uni-list" :border="false" :style="{height:listHight}">

				<!-- 作用于app端nvue页面的下拉加载 -->
				<!-- #ifdef APP-NVUE -->
				<refreshBox @refresh="refresh" :loading="loading"></refreshBox>
				<!-- #endif -->

				<!-- 列表渲染 -->
				<uni-list-item :to="'/pages/list/detail?id='+item._id+'&title='+item.title" v-for="(item,index) in data"
					:key="index">
					<!-- 通过header插槽定义列表左侧图片 -->
					<template v-slot:header>
						<image class="avatar" :src="item.avatar" mode="aspectFill"></image>
					</template>
					<!-- 通过body插槽定义布局 -->
					<template v-slot:body>
						<view class="main">
							<text class="title">{{item.title}}</text>
							<view class="info">
								<text class="author">{{item.user_id[0]?item.user_id[0].nickname:''}}</text>
								<uni-dateformat class="last_modify_date" :date="item.last_modify_date"
									format="yyyy-MM-dd" :threshold="[60000, 2592000000]" />
							</view>
						</view>
					</template>
				</uni-list-item>
				<!-- 加载状态：上拉加载更多，加载中，没有更多数据了，加载错误 -->
				<!-- #ifdef APP-PLUS -->
				<uni-list-item>
					<template v-slot:body>
						<!-- #endif -->
						<uni-load-state @networkResume="refresh" :state="{data,pagination,hasMore, loading, error}"
							@loadMore="loadMore">
						</uni-load-state>
						<!-- #ifdef APP-PLUS -->
					</template>
				</uni-list-item>
				<!-- #endif -->
			</uni-list>
		</unicloud-db>
		<button @click="addMemo">添加 memo</button>
	</view>
</template>

<script>
	let cdbRef;
	import statusBar from "@/uni_modules/uni-nav-bar/components/uni-nav-bar/uni-status-bar";

	import Gps from '@/uni_modules/json-gps/js_sdk/gps.js';
	const gps = new Gps(),
		db = uniCloud.database();

	export default {
		components: {
			statusBar
		},
		computed: {
			inputPlaceholder(e) {
				if (uni.getStorageSync('CURRENT_LANG') == "en") {
					return 'Please enter the search content'
				} else {
					return '请输入搜索内容'
				}
			},
			colList() {
				return [
					// db.collection('opendb-news-articles').where(this.where).field(
					// 	'avatar,title,last_modify_date,user_id').getTemp(),
					// db.collection('uni-id-users').field('_id,nickname').getTemp()
					db.collection('memos').field('_id, content').getTemp()
				]
			}
		},
		data() {
			return {
				where: '"article_status" == 1',
				keyword: "",
				showRefresh: false,
				listHight: 0
			}
		},
		watch: {
			keyword(keyword, oldValue) {
				let where = '"article_status" == 1 '
				if (keyword) {
					this.where = where + `&& /${keyword}/.test(title)`;
				} else {
					this.where = where;
				}
			}
		},
		async onReady() {
			// #ifdef APP-NVUE
			/* 可用窗口高度 - 搜索框高 - 状态栏高 */
			this.listHight = uni.getSystemInfoSync().windowHeight - uni.getSystemInfoSync().statusBarHeight - 50 +
				'px';
			// #endif
			// #ifndef APP-NVUE
			this.listHight = 'auto'
			// #endif
			cdbRef = this.$refs.udb
		},
		async onShow() {
			this.keyword = getApp().globalData.searchText
			getApp().globalData.searchText = ''
			//这里仅演示如何，在onShow生命周期获取设备位置，并在设备或者应用没有权限时自动引导。设置完毕自动重新获取。
			//你可以基于他做自己的业务，比如：根据距离由近到远排序列表数据等
			// uni.showLoading({
			// 	title:"获取定位中"
			// });
			//默认h5端不获取定位
			// #ifndef H5
			let location = await gps.getLocation({
				geocode: true
			})
			// console.log(location);
			// #endif
			// if(location){
			// 	uni.showToast({
			// 		title: JSON.stringify(location),
			// 		icon: 'none'
			// 	});
			// }
			// uni.hideLoading()
		},
		methods: {
			addMemo() {
				console.log('add memo');
				db.collection("memos").add({
					"content": "efssffsdf",
					"type": 1,
					slug: 'dgfgg'
				}).then((res) => {
					// res 为数据库查询结果
					console.log(res)
				}).catch((err) => {
					console.log(err.message)
				});
			},
			searchClick(e) { //点击搜索框
				uni.hideKeyboard();
				uni.navigateTo({
					url: '/pages/list/search/search',
					animationType: 'fade-in'
				});
			},
			retry() {
				this.refresh()
			},
			refresh() {
				cdbRef.loadData({
					clear: true
				}, () => {
					uni.stopPullDownRefresh()
					// #ifdef APP-NVUE
					this.showRefresh = false
					// #endif
					console.log('end');
				})
				console.log('refresh');
			},
			loadMore() {
				cdbRef.loadMore()
			},
			onqueryerror(e) {
				console.error(e);
			},
			onpullingdown(e) {
				console.log(e);
				this.showRefresh = true
				if (e.pullingDistance > 100) {
					this.refresh()
				}
			}
		},
		// #ifndef APP-NVUE
		onPullDownRefresh() {
			this.refresh()
		},
		onReachBottom() {
			this.loadMore()
		}
		// #endif
	}
</script>

<style scoped>
	/* #ifndef APP-NVUE */
	view {
		display: flex;
		box-sizing: border-box;
		flex-direction: column;
	}

	/* #endif */
	.pages {
		background-color: #FFFFFF;
	}

	.avatar {
		width: 200rpx;
		height: 200rpx;
		margin-right: 10rpx;
	}

	.main {
		justify-content: space-between;
		flex: 1;
	}

	.title {
		font-size: 16px;
	}

	.info {
		flex-direction: row;
		justify-content: space-between;
	}

	.author,
	.last_modify_date {
		font-size: 14px;
		color: #999999;
	}

	.uni-search-box {
		background-color: #FFFFFF;
		position: sticky;
		height: 50px;
		top: 0;
		left: 0;
		/* #ifndef APP-PLUS */
		z-index: 9;
		/* #endif */
		/* #ifdef MP-WEIXIN */
		width: 580rpx;
		/* #endif */
	}

	.cover-search-bar {
		height: 50px;
		position: relative;
		top: -50px;
		margin-bottom: -50px;
		/* #ifndef APP-NVUE */
		z-index: 999;
		/* #endif */
	}
</style>
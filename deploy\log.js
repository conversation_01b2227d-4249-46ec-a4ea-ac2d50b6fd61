const ora = require('ora')
const chalk = require('chalk')

module.exports = {
  // 加载中
  loading: (message) => {
    const spinner = ora(`${message}\n`)
    spinner.start()
    return spinner
  },
  // 日志信息
  log: (message) => {
    // eslint-disable-next-line no-console
    console.log(message)
  },
  // 成功信息
  succeed: (...message) => {
    ora().succeed(chalk.greenBright.bold(message))
  },
  // 提示信息
  info: (...message) => {
    ora().info(chalk.blueBright.bold(message))
  },
  // 错误信息
  error: (...message) => {
    ora().fail(chalk.redBright.bold(message))
  },
  // 下划线重点信息
  underline: (message) => {
    console.log(chalk.underline.blueBright.bold(message))
  },
  gray: (message) => {
    console.log(chalk.gray(message))
  },
}

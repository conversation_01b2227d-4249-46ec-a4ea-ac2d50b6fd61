<template>
  <view class="content">
    <view class="news-container">
      <view class="news-header">
        <text class="news-title">新闻资讯</text>
      </view>
      
      <view class="news-list">
        <view v-for="(item, index) in newsList" :key="index" class="news-item">
          <view class="news-item-title">{{item.title}}</view>
          <view class="news-item-content">{{item.content}}</view>
          <view class="news-item-time">{{formatDate(item.createTime)}}</view>
        </view>
      </view>
      
      <z-empty v-if="newsList.length === 0" text="暂无新闻"></z-empty>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import dayjs from 'dayjs'

// 定义组件名称
const __NAME__ = 'speak-news'

// 数据
const newsList = ref([])

// 获取新闻列表
const getNewsList = () => {
  // 模拟数据，实际项目中应替换为真实接口调用
  newsList.value = [
    {
      title: '系统更新公告',
      content: '系统将于近期更新，带来更多功能与体验优化',
      createTime: new Date()
    },
    {
      title: '功能介绍',
      content: '新增说话功能，可以快速记录您的想法',
      createTime: new Date(Date.now() - 86400000)
    }
  ]
}

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

onMounted(() => {
  getNewsList()
})
</script>

<style lang="scss">
.content {
  padding: 30rpx;
}

.news-container {
  display: flex;
  flex-direction: column;
}

.news-header {
  margin-bottom: 30rpx;
  
  .news-title {
    font-size: 36rpx;
    font-weight: bold;
  }
}

.news-list {
  .news-item {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .news-item-title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 16rpx;
    }
    
    .news-item-content {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 16rpx;
    }
    
    .news-item-time {
      font-size: 24rpx;
      color: #999;
      text-align: right;
    }
  }
}
</style>
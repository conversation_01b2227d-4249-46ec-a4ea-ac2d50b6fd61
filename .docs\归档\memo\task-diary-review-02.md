# 任务：日记分析服务开发

- **所属功能模块**: `diary-review`
- **任务ID**: `task-diary-review-02`
- **优先级**: 低

## 任务描述
实现日记内容的分析服务，该服务负责从用户的日记数据中提取关键信息，如情绪分数、主要主题和标签等。这将通过一个API端点（或客户端等效逻辑）`/api/diary/analyze` 来实现。

## 技术实现详情
1.  **分析服务逻辑**:
    - 实现 `/api/diary/analyze` 的逻辑。
    - **输入**:
        - 时间范围（如：最近7天、某个月份）。
        - 对应时间范围内的所有日记数据（`DiaryContent` 对象数组）。
    - **处理**:
        - **情绪分析**:
            - 遍历所有日记条目（`entries`），将其文本内容提交给AI模型进行情绪分析。
            - AI模型返回每段文本的情绪（如：积极、消极、中性）及置信度。
            - 将情绪量化为分数（如：积极=1, 中性=0, 消极=-1），并按天计算平均分。
        - **主题提取**:
            - 将指定周期内的所有文本内容合并，提交给AI模型。
            - 指导AI模型提取出3-5个核心主题或关键词。
    - **输出**:
        - 一个包含分析结果的对象，例如：
          ```json
          {
            "emotion_trend": [{ "date": "2023-10-27", "score": 0.8 }, ...],
            "key_topics": ["项目管理", "团队协作", "个人成长"]
          }
          ```

## 验收标准
- 调用分析服务并传入模拟的日记数据后，能返回结构正确、内容合理的分析结果。
- 情绪分析结果能基本反映文本的感情色彩。
- 主题提取能抓取文本中的主要内容。
- 服务处理大量文本时性能可接受，并有适当的缓存机制。

## 依赖关系
- `task-diary-data-01`: 依赖其定义的数据结构作为输入。

## 状态追踪
- 未开始 
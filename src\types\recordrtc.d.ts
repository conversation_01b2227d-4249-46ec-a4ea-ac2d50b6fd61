declare module 'recordrtc' {
  export interface RecordRTCConfiguration {
    type?: string
    mimeType?: string
    recorderType?: any
    timeSlice?: number
    disableLogs?: boolean
    numberOfAudioChannels?: number
    sampleRate?: number
    desiredSampleRate?: number
    bufferSize?: number
    checkForInactiveTracks?: boolean
    ondataavailable?: (blob: Blob) => void
    bitsPerSecond?: number
  }

  export class StereoAudioRecorder {}

  export default class RecordRTC {
    constructor(stream: MediaStream, config?: RecordRTCConfiguration)
    startRecording(): void
    stopRecording(callback?: (audioVideoURL?: string) => void): void
    pauseRecording(): void
    resumeRecording(): void
    getBlob(): Blob
    getDataURL(callback: (dataURL: string) => void): void
    destroy(): void
  }
}

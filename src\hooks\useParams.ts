import { ref, reactive } from 'vue'

export default <T extends { [key: string]: any }>(initialParams: T): [T, (params?: T) => void] => {
  // 保存初始参数的副本
  // TODO 优化深拷贝方式
  const originalParams = JSON.parse(JSON.stringify(initialParams))

  // 表单参数
  const formParams = reactive<T>({
    ...initialParams,
  })

  // 重置表单
  const resetForm = (data) => {
    data = data || originalParams
    Object.keys(formParams).forEach((key) => {
      formParams[key] = data[key]
    })
  }

  return [formParams, resetForm]
}

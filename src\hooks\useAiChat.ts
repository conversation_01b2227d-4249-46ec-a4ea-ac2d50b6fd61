import { ref, nextTick, onBeforeUnmount } from 'vue'
import dayjs from 'dayjs'
import {
  addChatRecordApi,
  updateChatRecordApi,
} from '@/api/chatRecord'
import request from '@/utils/request'
import { useRecord, useRecordCompat } from '@/hooks'
import { router } from '@/utils/tools'

export function useAiChat(options) {
  const { systemPrompt = 'You are a helpful assistant.', onTranscriptionComplete } = options || {}

  // 响应式状态
  const messages = ref([])
  const userInput = ref('')
  const messageListRef = ref(null)
  const drawerRef = ref(null)
  const recordListRef = ref(null)
  const currentChatId = ref('')
  const uploadProgress = ref({
    show: false,
    percent: 0,
  })

  // 录音 Hooks
  const { isRecording, duration, startRecording, stopRecording, cancelRecording } = useRecord({
    maxDuration: 60000,
    appOptions: { format: 'mp3' },
  })
  const { isSupported, requestPermission, openSettings } = useRecordCompat()

  // 检查录音权限
  const checkRecordPermission = async () => {
    if (!isSupported.value) {
      uni.showModal({ title: '不支持录音', content: '当前环境不支持录音功能', showCancel: false })
      return false
    }
    const hasPermission = await requestPermission()
    if (!hasPermission) {
      uni.showModal({
        title: '需要权限',
        content: '需要录音权限才能使用该功能',
        confirmText: '去设置',
        success: (res) => {
          if (res.confirm) {
            // #ifdef APP-PLUS
            openSettings()
            // #endif
          }
        },
      })
      return false
    }
    return true
  }

  // 保存聊天记录
  const saveChatRecord = async () => {
    try {
      if (!messages.value.length) return
      const hasUserMessage = messages.value.some((msg) => msg.isUser)
      if (!hasUserMessage) return

      const firstUserMessage = messages.value.find((msg) => msg.isUser)?.content || ''
      const title = firstUserMessage.substring(0, 20) || dayjs().format('YYYY-MM-DD HH:mm:ss')

      if (currentChatId.value) {
        await updateChatRecordApi(currentChatId.value, {
          content: JSON.stringify(messages.value),
          updateTime: dayjs().toISOString(),
        })
      } else {
        const result = await addChatRecordApi({
          content: JSON.stringify(messages.value),
          title: title,
        })
        currentChatId.value = result._id
        recordListRef.value?.refresh()
      }
    } catch (error) {
      console.error('保存聊天记录失败：', error)
    }
  }

  // 获取 AI 回复
  const getAiReply = async () => {
    messages.value.push({ loading: true, isUser: false, time: dayjs().valueOf() })
    await nextTick()
    messageListRef.value?.scrollToBottom()

    try {
      const messagesForAI = messages.value
        .slice(0, -1)
        .filter((msg) => (msg.type === 'text' || msg.transcribeResult) && !msg.loading)

      if (messagesForAI.length === 0) {
        messages.value.pop()
        return
      }

      const lastMessage = messagesForAI[messagesForAI.length - 1]
      const userQuestion = lastMessage.aiContent || lastMessage.transcribeResult || lastMessage.content
      const history = messagesForAI.slice(0, -1).map((msg) => ({
        role: msg.isUser ? 'user' : 'assistant',
        content: msg.aiContent || msg.transcribeResult || msg.content,
      }))

      const { content: resData } = await request.post('/ai/chat', {
        message: userQuestion,
        history_records: history,
        model: 'deepseek-chat',
        system: systemPrompt,
      })

      let aiResponse = typeof resData === 'string'
        ? resData
        : (resData?.content || resData?.output || JSON.stringify(resData))

      messages.value.pop()
      messages.value.push({ type: 'text', content: aiResponse, isUser: false, time: dayjs().valueOf() })
    } catch (error) {
      console.error('AI response error:', error)
      messages.value.pop()
      messages.value.push({
        type: 'text',
        content: '抱歉，我暂时无法回答，请稍后再试。',
        isUser: false,
        time: dayjs().valueOf(),
      })
    }
    await nextTick()
    messageListRef.value?.scrollToBottom()
    await saveChatRecord()
  }

  // 发送文本消息
  const sendMessage = async (content, aiContent) => {
    if (!content.trim()) return
    const userMessage = {
      type: 'text',
      content,
      isUser: true,
      time: dayjs().valueOf(),
    }
    if (aiContent) {
      userMessage.aiContent = aiContent
    }
    messages.value.push(userMessage)
    userInput.value = ''
    await nextTick()
    messageListRef.value?.scrollToBottom()
    await getAiReply()
  }

  // 处理语音转写轮询
  const pollAsrResult = async (asrId) => {
    let retryCount = 0
    const maxRetries = 30
    const pollInterval = 1000

    const poll = async () => {
      try {
        const { data: result } = await request.post('/speak/getAsr', { taskId: asrId })
        const status = result.Data.StatusStr
        const lastAudioMessage = [...messages.value].reverse().find((msg) => msg.type === 'audio' && msg.isUser)

        if (status === 'success') {
          const transcript = result.Data.ResultDetail.map((item) => item.FinalSentence).join('\n')
          if (lastAudioMessage) {
            lastAudioMessage.isTranscribing = false
            lastAudioMessage.transcribeResult = transcript
          }
          await messageListRef.value?.scrollToBottom()
          await saveChatRecord()
          
          if (onTranscriptionComplete) {
            onTranscriptionComplete(transcript)
          } else {
            await getAiReply()
          }
          return
        } else if (result.status === 'failed') {
          if (lastAudioMessage) lastAudioMessage.isTranscribing = false
          throw new Error('语音转写失败')
        }

        retryCount++
        if (retryCount < maxRetries) {
          setTimeout(poll, pollInterval)
        } else {
           if (lastAudioMessage) lastAudioMessage.isTranscribing = false
          throw new Error('语音转写超时')
        }
      } catch (error) {
        const lastAudioMessage = [...messages.value].reverse().find((msg) => msg.type === 'audio' && msg.isUser)
        if (lastAudioMessage) lastAudioMessage.isTranscribing = false
        messages.value.push({ type: 'text', content: `${error.message}，请重试`, isUser: false, time: dayjs().valueOf() })
        await nextTick()
        messageListRef.value?.scrollToBottom()
      }
    }
    poll()
  }

  // 发送语音消息
  const sendAudioMessage = async (audioData) => {
    if (!(await checkRecordPermission())) return

    try {
      uploadProgress.value = { show: true, percent: 0 }

      const cloudPath = `speak/${dayjs().format('YYYY-MM-DD')}_${Date.now()}.mp3`
      let filePath, estimatedDuration
      
      // #ifdef H5
      if (audioData?.blob) {
        filePath = URL.createObjectURL(audioData.blob)
        estimatedDuration = audioData.duration || 0
      }
      // #endif
      // #ifndef H5
      if (audioData?.blob) {
        filePath = audioData.blob
        estimatedDuration = audioData.duration || 0
      } else if (audioData?.tempFilePath) {
        filePath = audioData.tempFilePath
        estimatedDuration = duration.value
      }
      // #endif

      if (!filePath) {
        throw new Error('录音数据格式不正确')
      }
      
      const uploadResult = await uniCloud.uploadFile({
        filePath,
        cloudPath,
        cloudPathAsRealPath: true,
        onUploadProgress: (e) => { uploadProgress.value.percent = Math.round((e.loaded / e.total) * 100) },
      })
      
      uploadProgress.value.percent = 100
      setTimeout(() => { uploadProgress.value.show = false }, 500)

      const { fileList } = await uniCloud.getTempFileURL({ fileList: [uploadResult.fileID] })
      const audioUrl = fileList[0].tempFileURL

      messages.value.push({
        type: 'audio',
        audioUrl: uploadResult.fileID,
        audioDuration: estimatedDuration,
        isUser: true,
        time: dayjs().valueOf(),
        isTranscribing: true,
      })
      await messageListRef.value?.scrollToBottom()

      const { data } = await request.post('/speak/asr', { url: audioUrl })
      await pollAsrResult(data.asrId)
    } catch (error) {
      uploadProgress.value.show = false
      console.error('录音或上传失败：', error)
      uni.showToast({ title: '录音或上传失败', icon: 'none' })
    }
  }

  // 历史记录相关
  const openDrawer = () => drawerRef.value.open()

  const selectChatRecord = async (record) => {
    try {
      currentChatId.value = record._id
      if (record.content) {
        messages.value = JSON.parse(record.content)
        await nextTick()
        messageListRef.value?.scrollToBottom()
      }
      drawerRef.value.close()
      uni.showToast({ title: '已切换到历史对话', icon: 'none' })
    } catch (error) {
      console.error('加载聊天记录失败', error)
      uni.showToast({ title: '加载聊天记录失败', icon: 'error' })
    }
  }

  const clearChatRecords = () => {
    if (currentChatId.value) {
      messages.value = []
      currentChatId.value = ''
    }
  }
  
  const handleBack = () => router.back()
  
  const handleReset = () => {
    uni.showModal({
      title: '确认重置',
      content: '确定要开始新的对话吗？',
      success: (res) => {
        if (res.confirm) {
          currentChatId.value = ''
          messages.value = []
        }
      },
    })
  }

  onBeforeUnmount(() => {
    if (isRecording.value) {
      cancelRecording()
    }
  })

  return {
    // State
    messages,
    userInput,
    messageListRef,
    drawerRef,
    recordListRef,
    currentChatId,
    uploadProgress,
    isRecording,
    duration,
    // Methods
    sendMessage,
    sendAudioMessage,
    startRecording,
    stopRecording,
    cancelRecording,
    openDrawer,
    selectChatRecord,
    clearChatRecords,
    handleBack,
    handleReset
  }
}
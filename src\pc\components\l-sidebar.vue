<template>
  <view class="sidebar-container">
    <view class="logo-container">
      <image src="/static/logo.png" class="logo" />
    </view>
    <view class="menu">
      <view
        v-for="item in menuItems"
        :key="item.id"
        class="menu-item"
        :class="{ active: activeTab === item.id }"
        @click="handleTabChange(item.id)"
      >
        <i :class="item.icon"></i>
        <view class="tooltip">{{ item.name }}</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

const emit = defineEmits(['tab-change'])

const props = defineProps({
  activeTab: String,
})

const menuItems = ref([
  { id: 'goals', name: '目标', icon: 'fas fa-bullseye' },
  { id: 'diary', name: '日记', icon: 'fas fa-book' },
  { id: 'speak', name: '表达', icon: 'fas fa-comment-dots' },
  { id: 'setting', name: '设置', icon: 'fas fa-cog' },
])

const handleTabChange = (tabId) => {
  emit('tab-change', tabId)
}
</script>

<style scoped>
.sidebar-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
  align-items: center;
}
.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 14px 0;
  width: 100%;
  border-bottom: 1px solid #e8e8e8;
}
.logo {
  width: 32px;
  height: 32px;
}
.menu {
  flex-grow: 1;
  padding: 12px 8px;
  width: 100%;
  box-sizing: border-box;
}
.menu-item {
  position: relative; /* 为tooltip定位 */
  display: flex;
  justify-content: center;
  align-items: center;
  height: 44px;
  width: 44px;
  margin: 0 auto 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  color: #606266;
}
.menu-item:hover {
  background-color: #f2f6fc;
  color: var(--color-primary);
}
.menu-item.active {
  background-color: var(--color-primary);
  color: #fff;
  box-shadow: 0 4px 10px -2px rgba(var(--color-primary-rgb), 0.4);
  border-right: none;
}
.menu-item i {
  font-size: 20px;
  width: 24px;
  text-align: center;
  transition: color 0.2s ease-in-out;
}

.tooltip {
  position: absolute;
  left: 56px; /* 将提示框定位在侧边栏右侧 */
  top: 50%;
  transform: translateY(-50%);
  background-color: #303133;
  color: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  white-space: nowrap;
  pointer-events: none; /* 确保tooltip不干扰鼠标事件 */
  visibility: hidden;
  opacity: 0;
  /* HIDE transition: 鼠标移开时，快速消失 */
  transition: opacity 0.2s ease, visibility 0s linear 0.2s;
  z-index: 10;
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 100%; /* 将箭头定位到tooltip的左边 */
  margin-top: -6px; /* 垂直居中箭头 */
  border-width: 6px;
  border-style: solid;
  border-color: transparent #303133 transparent transparent;
}

.menu-item:hover .tooltip {
  visibility: visible;
  opacity: 1;
  /* SHOW transition: 鼠标悬浮时，延迟500毫秒显示 */
  transition: opacity 0.2s ease 500ms, visibility 0s linear 500ms;
}

/* 点击后（即active状态），悬浮时不再显示tooltip */
.menu-item.active:hover .tooltip {
  visibility: hidden;
  opacity: 0;
  transition: none;
}
</style>

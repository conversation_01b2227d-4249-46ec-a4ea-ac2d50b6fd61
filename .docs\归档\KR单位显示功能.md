# KR 单位显示功能需求

status: draft

## 背景

目前系统中的 KR（关键结果）显示只有纯数字，缺乏单位信息，无法直观表达 KR 所度量的内容类型（如次数、个数、章节等）。为了提高用户体验和数据可读性，需要为 KR 添加单位显示功能，使数据更有意义和直观。

## 需求

### 功能需求

1. 在 KR 编辑页面（krEdit.vue）添加单位输入字段，允许用户为每个 KR 指定单位
2. 单位输入支持自定义文本，如"个"、"次"、"章"、"公里"等
3. 在所有展示 KR 数值的地方，在数字后面添加对应的单位显示
4. 具体需要修改的页面包括但不限于：
   - KR 编辑页（krEdit.vue）- 添加单位输入
   - 今日页面（today.vue）- 在展示 KR 进度时加上单位
   - KR 详情页（krDetail.vue）- 在展示 KR 数值和进度时加上单位
   - OKR 详情页（okrDetail.vue）- 在展示 KR 列表时加上单位

### 非功能需求

1. 界面设计保持一致性，单位显示样式与现有 UI 风格统一
2. 确保在所有设备尺寸上的良好适配性
3. 对现有数据进行兼容处理，对于没有设置单位的历史数据，显示时不带单位

## 技术方案

### 实现思路

1. 数据库字段修改

   - 在 KR 相关表结构中添加`unit`字段，用于存储用户设定的单位
   - 在`src/api/dataSchema/typings.d.ts`中更新接口类型定义
   - 在`src/api/dataSchema/index.ts`中更新数据库表结构配置

2. 编辑页面修改

   - 在`krEdit.vue`中添加单位输入框，设置在数值输入框旁边
   - 在保存 KR 时，将单位信息一并保存到数据库

3. 显示页面修改
   - 修改`today.vue`中的任务展示逻辑，在数值后添加单位显示
   - 修改`krDetail.vue`中的进度历史和统计显示，添加单位信息
   - 修改`okrDetail.vue`中的 KR 列表显示，添加单位信息

### UI 设计

KR 编辑页面单位输入设计：

```
+------------------+-------------------+
|  数值输入        |  单位输入         |
|  [          ]    |  [          ]     |
+------------------+-------------------+
```

数值与单位的展示效果（以今日任务为例）：

```
今天: 3/5个
本周: 12/20次
```

### 架构设计

```mermaid
graph TD
    A[数据库结构修改] --> B[添加unit字段]
    B --> C{页面修改}
    C --> D[krEdit.vue<br>添加单位输入]
    C --> E[today.vue<br>显示带单位的进度]
    C --> F[krDetail.vue<br>显示带单位的进度]
    C --> G[okrDetail.vue<br>显示带单位的KR]
    D --> H[数据保存逻辑修改]
    E --> I[循环任务进度文本格式化]
    F --> J[进度历史显示格式化]
```

### 代码修改要点

1. 数据结构修改

```typescript
// src/api/dataSchema/typings.d.ts
interface TaskItem {
  // 现有字段...
  unit?: string // KR 单位，如"个"、"次"等
}
```

2. KR 编辑页面修改

```html
<!-- krEdit.vue -->
<view class="input-group">
  <view class="input-label">目标值</view>
  <view class="input-row">
    <input class="input-field value-field" type="number" v-model="formData.target" placeholder="请输入" />
    <input class="input-field unit-field" type="text" v-model="formData.unit" placeholder="单位" />
  </view>
</view>
```

3. 今日页面进度文本修改

```javascript
// today.vue - getCustomRepeatProgressText 函数修改
const getCustomRepeatProgressText = (task) => {
  // 初始化进度百分比为 0
  task.progressPercent = 0

  // 获取单位或使用空字符串
  const unit = task.unit || ''

  // 其他逻辑...

  // 返回带单位的进度文本
  return `今天:${progressData.completedToday}/${dailyTarget}${unit}`
}
```

## 风险评估

### 假设与未知因素

- 假设单位文本不会过长（如超过 10 个字符），否则可能影响 UI 展示
- 假设历史数据中没有单位信息，需要兼容处理

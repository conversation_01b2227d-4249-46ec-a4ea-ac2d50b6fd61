<template>
  <view>
    <!-- 概览 -->
    <view class="fixed top-0 left-0 right-0 bg-[#3eb575] color-white p-4 z-10">
      <view class="inline-flex items-center bg-[#52bd83] p-2 ml-2 rounded-1 mb-3" @click="openCategory = true">
        {{ selectedCategory?.name || '全部类型' }}
        <view class="mx-2 bg-white h-30 w-1"></view>
        <u-icon name="grid"></u-icon>
      </view>
      <view class="flex">
        <text class="mr-4 px-4 flex items-center " @click="openMonth = true">
          <text>{{ dayjs(currentMonth).format('YYYY 年 MM 月') }}</text>
          <u-icon ml-1 name="arrow-down-fill" size="14"></u-icon>
        </text>
        <text>支出￥{{ currentExpense }}</text>
      </view>
    </view>
    <!-- 列表 -->
    <view class="h-200"></view>
    <view v-for="(b, i) in bills" :key="i" :id="`month-${i}`">
      <view class="flex items-center color-gray px-4 my-2">
        {{ b.month }} <view class="mx-2 bg-gray w-1 h-20"></view> 总支出 ￥{{
        getTotalAmount(b.days, 'expense')
        }}
        总收入 ￥{{ getTotalAmount(b.days, 'income') }}
      </view>
      <view v-for="(d, j) in b.days" :key="j">
        <view class="bg-white rounded-2 mx-4 mb-4 overflow-hidden">
          <view class="bg-[#fbfbfb] p-4 flex justify-between">
            <text>{{ dayjs(d.date).format('MM 月 DD 日') }} {{ getChineseDay(d.date) }}</text>
            <text><text class="bg-[#f3f3f3] color-gray p-4rpx t-26">出</text>￥{{ getDayTotalAmount(d.bills, 'expense') }}
              <text class="ml-2 bg-[#f3f3f3] color-gray p-4rpx t-26">入</text>￥{{
              getDayTotalAmount(d.bills, 'income') }}</text>
          </view>
          <uni-swipe-action>
            <uni-swipe-action-item v-for="(item, k) in d.bills" :key="k" :right-options="options"
              @click="onClick($event, item)">
              <view @click="showBillPop(item._id)" class="flex items-center px-4 bg-white">
                <view
                  class="bg-[#3eb575] mr-4 w-70 h-70 rounded-[50%] mb-2 color-white flex justify-center items-center">
                  {{ classObj[item.category][0] }}</view>

                <view class="flex-1 py-4" style="border-bottom: 1px solid #eee">
                  <view class="flex justify-between font-medium">
                    <text>{{ classObj[item.category] }}</text>
                    <text :class="item.type === 'expense' ? 'text-red-500' : 'text-green-500'">
                      {{ item.type === 'expense' ? '-' : '+' }}{{ item.amount }}
                    </text>
                  </view>
                  <view class="mt-1 flex items-center text-gray text-25">
                    <view>{{ dayjs(item.date).format('HH:mm') }}</view>
                    <template v-if="item.remark || item.merchants">
                      <view class="mx-2 bg-gray w-1 h-20"></view>
                      <view>{{ item.remark || item.merchants }}</view>
                    </template>
                  </view>
                </view>
              </view>
            </uni-swipe-action-item>
          </uni-swipe-action>
        </view>
      </view>
    </view>

    <!-- 悬浮球 -->
    <view>
      <uni-fab horizontal="right" vertical="bottom" direction="vertical" @fabClick="showBillPop" :popMenu="false"
        icon="paperplane-filled" :pattern="{
          color: '#7A7E83',
          backgroundColor: '#3eb575',
          selectedColor: '#fff',
          buttonColor: '#3eb575',
          iconColor: '#fff',
        }"></uni-fab>
    </view>
    <!-- 选择月份 -->
    <l-month-pop v-model:open="openMonth" :date="currentMonth" @update:date="scrollToMonth"></l-month-pop>
    <!-- 选择分类 -->
    <l-category-pop v-model:open="openCategory" v-model="selectedCategory"></l-category-pop>
    <!-- 账单记录弹窗 -->
    <l-new-bill :editId="editId" v-model:open="isNewBillOpen" @submit="newBill"></l-new-bill>
  </view>
</template>

<script setup>
import currency from 'currency.js'
import dayjs from 'dayjs'
import LNewBill from './components/l-new-bill.vue' // 引入封装后的组件
const openMonth = ref(false)
const isNewBillOpen = ref(false) // 控制 l-new-bill 弹窗
const bills = ref([])
const classObj = {}
const editId = ref('')
const options = [
  {
    text: '删除',
    key: 'delete',
    style: {
      backgroundColor: '#dd524d',
    },
  },
]
const currentMonth = ref()
const currentExpense = ref()
const openCategory = ref(false)
const selectedCategory = ref(null)

// 添加原始数据存储
const originalBills = ref([])

// 编辑
const showBillPop = (id) => {
  isNewBillOpen.value = true
  // 可以通过事件或 props 传递 id 给 l-new-bill 组件
  editId.value = id
}
const init = async () => {
  const categoryList = await getCategoryApi()
  const b = await getBillListApi()

  // 保存原始数据
  originalBills.value = b

  // 根据选中分类过滤数据
  const filteredBills = selectedCategory.value
    ? b.filter(item => item.category === selectedCategory.value._id)
    : b

  const monthList = Array.from(new Set(filteredBills.map((item) => dayjs(item.date).format('YYYY-MM')))).sort(
    (a, b) => dayjs(b).valueOf() - dayjs(a).valueOf()
  )

  const monthBills = monthList.map((month) => {
    const monthData = filteredBills.filter((item) => dayjs(item.date).format('YYYY-MM') === month)
    const dateList = Array.from(new Set(monthData.map((item) => dayjs(item.date).format('YYYY-MM-DD')))).sort(
      (a, b) => dayjs(b).valueOf() - dayjs(a).valueOf()
    )
    const dayBills = dateList.map((date) => {
      return {
        date,
        bills: monthData
          .filter((item) => dayjs(item.date).format('YYYY-MM-DD') === date)
          .sort((a, b) => dayjs(b.date).valueOf() - dayjs(a.date).valueOf()),
      }
    })
    return {
      month,
      days: dayBills,
    }
  })

  bills.value = monthBills
  // 保存月份 ref
  // monthBills.forEach((item, index) => {
  //   monthRefs.value[index] = `month-${index}`
  // })
  categoryList.forEach((item) => {
    classObj[item._id] = item.name
  })
  handlePageScroll()
}

// 监听分类变化
watch(
  () => selectedCategory.value,
  () => {
    // 使用原始数据重新过滤
    const filteredBills = selectedCategory.value
      ? originalBills.value.filter(item => item.category === selectedCategory.value._id)
      : originalBills.value

    const monthList = Array.from(new Set(filteredBills.map((item) => dayjs(item.date).format('YYYY-MM')))).sort(
      (a, b) => dayjs(b).valueOf() - dayjs(a).valueOf()
    )

    const monthBills = monthList.map((month) => {
      const monthData = filteredBills.filter((item) => dayjs(item.date).format('YYYY-MM') === month)
      const dateList = Array.from(new Set(monthData.map((item) => dayjs(item.date).format('YYYY-MM-DD')))).sort(
        (a, b) => dayjs(b).valueOf() - dayjs(a).valueOf()
      )
      const dayBills = dateList.map((date) => {
        return {
          date,
          bills: monthData
            .filter((item) => dayjs(item.date).format('YYYY-MM-DD') === date)
            .sort((a, b) => dayjs(b.date).valueOf() - dayjs(a.date).valueOf()),
        }
      })
      return {
        month,
        days: dayBills,
      }
    })

    bills.value = monthBills
    handlePageScroll()
  }
)

const newBill = () => {
  isNewBillOpen.value = false
  init()
}
onShow(init)

// 删除当前这条账单
const onClick = async (e, item) => {
  if (e.position === 'right' && e.content.key === 'delete') {
    // 删除账单
    try {
      await delBillApi(item._id)
      init() // 重新初始化账单数据
      uni.showToast({
        title: '删除成功',
        icon: 'success',
      })
    } catch (error) {
      uni.showToast({
        title: '删除失败',
        icon: 'none',
      })
    }
  }
}

const getChineseDay = (date) => {
  const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  return days[new Date(date).getDay()]
}

const getTotalAmount = (days, type) => {
  return days.reduce((total, day) => {
    return currency(total).add(
      day.bills.reduce((dayTotal, bill) => {
        return bill.type === type ? currency(dayTotal).add(bill.amount) : dayTotal
      }, 0)
    ).value
  }, 0)
}

const getDayTotalAmount = (bills, type) => {
  return bills.reduce((total, bill) => {
    return bill.type === type ? currency(total).add(bill.amount) : total
  }, 0)
}

const SCROLL_OFFSET = 200 // 设置偏移量，根据顶部固定区域的实际高度调整
const handlePageScroll = (res) => {
  const scrollTop = res?.scrollTop || 0
  const query = uni.createSelectorQuery()

  bills.value.forEach((_, index) => {
    query
      .select(`#month-${index}`)
      .boundingClientRect(data => {
        if (data && data.top <= SCROLL_OFFSET && data.bottom >= SCROLL_OFFSET) {
          currentMonth.value = dayjs(bills.value[index].month).format('YYYY-MM')
          currentExpense.value = getTotalAmount(bills.value[index].days, 'expense')
        }
      })
      .exec()
  })
}

const scrollToMonth = (date) => {
  const monthIndex = bills.value.findIndex(bill => dayjs(bill.month).isSame(dayjs(date), 'month'))
  if (monthIndex !== -1) {
    const query = uni.createSelectorQuery()
    query
      .select(`#month-${monthIndex}`)
      .boundingClientRect(data => {
        if (data) {
          uni.pageScrollTo({
            scrollTop: data.top - SCROLL_OFFSET,
            duration: 300
          })
        }
      })
      .exec()
  }
}

onPageScroll(handlePageScroll)
</script>

<style lang="scss"></style>

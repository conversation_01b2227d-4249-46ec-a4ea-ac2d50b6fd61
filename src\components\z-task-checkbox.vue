<template>
  <div class="task-checkbox" :class="{ checked: localStatus === 1 }" @click.stop="toggleTaskStatus">
    <i v-if="localStatus === 1" class="fas fa-check"></i>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, watch, onMounted } from 'vue'
import { updateTaskApi, getTaskApi } from '@/api/task'

const props = defineProps({
  // 任务 ID
  taskId: {
    type: String,
    required: true,
  },
  // 状态 0: 未完成 1: 已完成
  status: {
    type: Number,
    default: 0,
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 父任务 ID
  parentId: {
    type: String,
    default: '',
  },
  // 进度值
  progressValue: {
    type: Number,
    default: 0,
  },
  // 状态变更后的延迟时间（毫秒）
  delay: {
    type: Number,
    default: 300,
  },
})

const emit = defineEmits(['status-change', 'update:status'])

// 本地状态，用于控制 UI 显示
const localStatus = ref(props.status)

// 确保组件挂载时状态同步
onMounted(() => {
  localStatus.value = props.status
})

// 强化监听外部状态变化，使用 immediate 确保立即执行一次
watch(
  () => props.status,
  (newStatus) => {
    localStatus.value = newStatus
  },
  { immediate: true }
)

// 监听 taskId 变化，当组件被重用于不同任务时重置状态
watch(
  () => props.taskId,
  () => {
    localStatus.value = props.status
  }
)

// 更新父任务进度记录
const updateParentProgress = async (parentId: string, progressValue: number) => {
  if (!parentId || progressValue <= 0) return

  try {
    // 获取父任务信息
    const parentTask = await getTaskApi(parentId)
    if (!parentTask) return

    // 构造新的进度记录
    const newRecord = {
      _id: Date.now().toString(),
      val: progressValue,
      recTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      notes: '子任务完成自动记录',
      taskId: props.taskId, // 添加关联的子任务ID
    }

    // 准备更新数据
    const updateData: API.EditTask = {}

    // 如果父任务已有记录，添加新记录
    if (parentTask.recList && Array.isArray(parentTask.recList)) {
      updateData.recList = [...parentTask.recList, newRecord]
    } else {
      // 否则创建新的记录列表
      updateData.recList = [newRecord]
    }

    // 更新父任务
    await updateTaskApi(parentId, updateData)
    console.log('父任务进度已更新')
  } catch (error) {
    console.error('更新父任务进度失败：', error)
  }
}

// 移除父任务中由当前任务添加的进度记录
const removeParentProgress = async (parentId: string) => {
  if (!parentId) return

  try {
    // 获取父任务信息
    const parentTask = await getTaskApi(parentId)
    if (!parentTask || !parentTask.recList || !Array.isArray(parentTask.recList)) return

    // 过滤掉由当前任务添加的进度记录
    const filteredRecList = parentTask.recList.filter((record: any) => record.taskId !== props.taskId)

    // 如果过滤后的记录数量与原记录数量相同，说明没有找到对应记录，无需更新
    if (filteredRecList.length === parentTask.recList.length) return

    // 准备更新数据
    const updateData: API.EditTask = {
      recList: filteredRecList,
    }

    // 更新父任务
    await updateTaskApi(parentId, updateData)
    console.log('已移除父任务中的相关进度记录')
  } catch (error) {
    console.error('移除父任务进度记录失败：', error)
  }
}

// 切换任务状态
const toggleTaskStatus = async () => {
  if (props.disabled) return

  try {
    // 计算新状态
    const newStatus = props.status === 1 ? 0 : 1

    // 立即更新本地状态，实现立即可见的勾选动画
    localStatus.value = newStatus

    // 构造更新参数
    const updateData: API.EditTask = {
      status: newStatus as 0 | 1,
    }

    // 如果标记为完成，添加完成时间
    if (newStatus === 1) {
      updateData.completeTime = new Date().toISOString()

      // 如果任务有父任务 ID 和进度值，则更新父任务进度
      if (props.parentId && props.progressValue > 0) {
        await updateParentProgress(props.parentId, props.progressValue)
      }
    } else {
      // 如果取消勾选，且任务有父任务 ID，则移除父任务中由当前任务添加的进度记录
      if (props.parentId) {
        await removeParentProgress(props.parentId)
      }
    }

    // 调用 API 更新状态
    await updateTaskApi(props.taskId, updateData)

    // 显示提示
    uni.showToast({
      title: newStatus === 1 ? '任务已完成' : '任务已重置',
      icon: 'none',
      duration: 1500,
    })

    // 延迟触发状态变更事件
    setTimeout(() => {
      // 触发状态变更事件
      emit('status-change', {
        taskId: props.taskId,
        status: newStatus,
        statusText: newStatus === 1 ? '已完成' : '待开始',
      })

      // 同时支持 v-model:status 绑定
      emit('update:status', newStatus)
    }, props.delay)
  } catch (error) {
    console.error('更新任务状态失败：', error)
    uni.showToast({
      title: '更新任务状态失败',
      icon: 'none',
    })
    // 如果发生错误，回滚本地状态
    localStatus.value = props.status
  }
}
</script>

<style scoped lang="scss">
.task-checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-gray-300);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  flex-shrink: 0;

  &.checked {
    background: var(--color-primary);
    border-color: var(--color-primary);
    color: white;
  }

  i {
    font-size: 12px;
    animation: checkmark 0.2s ease-out;
  }
}

@keyframes checkmark {
  0% {
    transform: translateY(5px);
  }
  50% {
    transform: translateY(-2px);
  }
  100% {
    transform: translateY(0px);
  }
}
</style>

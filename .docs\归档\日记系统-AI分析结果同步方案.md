# 背景

当前日记系统中，用户编写日记后会使用 AI 进行分析，生成标题、标签分析和内容润色。目前的实现中存在以下问题：

1. 在日记编辑页面 (`diary_edit.vue`) 保存日记时，会调用 AI 工作流进行分析，并在原页面轮询等待结果（最多等待约 60 秒）。这导致用户体验不佳，需要在保存页面等待较长时间。

2. 在日记列表页面 (`diary.vue`) 查看日记时，只从本地数据库加载数据，没有检查云端是否已有最新的 AI 分析结果。如果用户提前离开编辑页面，AI 分析结果可能永远不会被同步到本地。

3. 当前没有有效的轮询中止机制，可能导致不必要的网络请求和数据混乱问题。

# 需求

## 功能需求

1. **编辑页面优化**：
   - 用户在日记编辑页面点击保存并调用 AI 工作流后，直接返回列表页面
   - 不再在编辑页面轮询等待 AI 分析结果
   
2. **列表页面数据同步**：
   - 用户在列表页面点击日期查看日记时，先查询云端数据
   - 将云端的 AI 分析结果 (`aiAnalysis`字段) 更新到本地数据库
   - 如果云端 AI 分析结果为空，以 1 秒间隔轮询查询，直到有结果或用户切换到其他日期

3. **轮询控制优化**：
   - 切换日期时自动中止当前轮询
   - 离开页面或应用进入后台时中止轮询
   - 轮询超时后自动中止（避免无限轮询）
   - 确保轮询结果与当前查看的日期匹配，避免数据显示混乱

# 技术方案

## 1. 修改日记编辑页面 (`diary_edit.vue`)

### 当前逻辑
目前编辑页面在保存日记后，会在原页面轮询等待 AI 分析结果，代码位于`onSubmit`函数中，大约 300-330 行：

```js
// 轮询获取 AI 分析结果
let checkCount = 0
const maxChecks = 30 // 最多检查 30 次，约 60 秒
const checkInterval = 3000 // 每 3 秒检查一次

const checkResult = async () => {
  // 检查是否有 AI 分析结果
  if (res.data && res.data.aiAnalysis) {
    // 有结果，同步数据并返回上一页
  } else {
    // 继续轮询
    checkCount++
    if (checkCount < maxChecks) {
      setTimeout(checkResult, checkInterval)
    }
  }
}
```

### 修改方案
1. 移除轮询逻辑，直接保存日记并调用工作流后返回列表页
2. 日记内容保存成功后直接显示提示并返回上一页

```js
// 保存日记后直接返回
try {
  // 保存日记数据
  if (diaryId) {
    await db.table('memo').update(diaryId, diaryData)
  } else {
    savedDiaryId.value = await db.table('memo').add(diaryData)
  }
  
  // 调用工作流 API 进行 AI 分析（不等待结果）
  workflowApi('7484172363748130835', {
    content: content.value,
    question: selectedTags.value,
    id: savedDiaryId.value,
    tags: tags.value,
  }).catch(error => console.error('AI 分析失败', error)) // 错误处理，但不阻止返回
  
  // 显示保存成功提示
  uni.showToast({
    title: '内容已保存，AI 分析中...',
    icon: 'success',
    duration: 1500,
  })
  
  // 延迟 1.5 秒后返回，让用户看到提示
  setTimeout(() => {
    uni.navigateBack()
  }, 1500)
} catch (error) {
  console.error('保存日记失败', error)
  uni.showToast({ title: '保存失败', icon: 'error' })
}
```

## 2. 修改日记列表页面 (`diary.vue`)

### 当前逻辑
当前在`getDay`函数中获取日记数据并处理 AI 分析结果，但没有查询云端数据或进行轮询：

```js
const getDay = async (date = '') => {
  // 设置当前日期
  curDay.value = date
  
  const data = await getDiaryApi(curDay.value)
  
  if (data?.content) {
    memoForm.value = data
    // 处理 AI 分析数据
  }
}
```

### 修改方案
1. 增加云端数据查询和本地数据更新逻辑
2. 添加轮询机制，当云端 AI 分析结果为空时进行轮询
3. 实现完善的轮询中止机制，避免无效轮询和数据混乱

```js
// 记录当前轮询任务的标识符，用于在需要时取消
let currentPollTimer = null
// 记录当前轮询的日记 ID，用于验证轮询结果是否匹配当前查看的日记
let currentPollingDiaryId = null
// 记录当前查看的日期，用于验证轮询结果是否匹配当前查看的日期
let currentViewDate = null

const getDay = async (date = '') => {
  // 取消之前的轮询（如果存在）
  if (currentPollTimer) {
    clearTimeout(currentPollTimer)
    currentPollTimer = null
    currentPollingDiaryId = null
  }
  
  // 设置当前日期
  if (!date) {
    date = dayjs().format('YYYY-MM-DD')
  }
  curDay.value = date
  currentViewDate = date // 记录当前查看的日期
  showDiaryContent.value = false
  isOriginalContent.value = true
  
  // 先从本地获取数据
  const localData = await getDiaryApi(curDay.value)
  
  // 判断是否需要查询云端（有本地数据的情况）
  if (localData?.content) {
    // 先显示本地数据
    memoForm.value = localData
    processAndDisplayData(localData)
    
    // 查询云端数据
    try {
      const cloudResponse = await request.post('/okr/getDiaryById', {
        _id: localData._id
      })
      
      // 验证是否仍在查看相同日期的日记
      if (currentViewDate !== curDay.value) {
        console.log('日期已切换，停止处理旧日期的数据')
        return
      }
      
      // 检查是否需要更新本地数据
      if (cloudResponse?.data) {
        const cloudData = cloudResponse.data
        
        // 如果云端有 AI 分析结果且与本地不同，更新本地数据
        if (cloudData.aiAnalysis && (!localData.aiAnalysis || cloudData.aiAnalysis !== localData.aiAnalysis)) {
          // 更新本地数据库
          await db.table('memo').update(localData._id, {
            aiAnalysis: cloudData.aiAnalysis
          })
          
          // 更新显示
          memoForm.value = {...localData, aiAnalysis: cloudData.aiAnalysis}
          processAndDisplayData(memoForm.value)
          
          console.log('从云端更新了 AI 分析结果')
        }
        // 如果云端没有 AI 分析结果，开始轮询
        else if (!cloudData.aiAnalysis) {
          startPolling(localData._id)
        }
      }
    } catch (error) {
      console.error('查询云端数据失败', error)
    }
  } else {
    // 没有本地数据
    memoForm.value = { content: '' }
    aiList.value = []
    polishContent.value = ''
  }
}

// 轮询云端数据
const startPolling = (diaryId) => {
  const pollInterval = 1000 // 1 秒间隔
  const maxPolls = 60 // 最多轮询 60 次（约 1 分钟）
  let pollCount = 0
  
  // 设置当前轮询的日记 ID
  currentPollingDiaryId = diaryId
  
  const pollCloudData = async () => {
    // 验证是否应该继续轮询
    if (currentViewDate !== curDay.value || !currentPollingDiaryId) {
      console.log('日期已切换或轮询已手动取消，停止轮询')
      currentPollTimer = null
      currentPollingDiaryId = null
      return
    }
    
    try {
      const cloudResponse = await request.post('/okr/getDiaryById', {
        _id: diaryId
      })
      
      pollCount++
      
      // 如果有 AI 分析结果或达到最大轮询次数，停止轮询
      if (cloudResponse?.data?.aiAnalysis || pollCount >= maxPolls) {
        // 有结果，更新本地数据
        if (cloudResponse?.data?.aiAnalysis) {
          // 再次验证是否仍在查看相同日记
          if (diaryId === currentPollingDiaryId && currentViewDate === curDay.value) {
            await db.table('memo').update(diaryId, {
              aiAnalysis: cloudResponse.data.aiAnalysis
            })
            
            // 更新显示
            if (diaryId === memoForm.value._id) { // 确保仍在查看相同日记
              memoForm.value = {...memoForm.value, aiAnalysis: cloudResponse.data.aiAnalysis}
              processAndDisplayData(memoForm.value)
              
              uni.showToast({
                title: 'AI 分析完成',
                icon: 'success',
                duration: 1500
              })
            }
          } else {
            console.log('日记 ID 不匹配或日期已切换，不更新显示')
            // 仍然更新数据库，但不更新 UI 显示
            await db.table('memo').update(diaryId, {
              aiAnalysis: cloudResponse.data.aiAnalysis
            })
          }
        } else if (pollCount >= maxPolls) {
          console.log('轮询达到最大次数，停止轮询')
          // 可选：显示轮询超时提示
          if (currentViewDate === curDay.value) {
            uni.showToast({
              title: '获取 AI 分析结果超时',
              icon: 'none',
              duration: 1500
            })
          }
        }
        
        currentPollTimer = null
        currentPollingDiaryId = null
        return
      }
      
      // 继续轮询
      currentPollTimer = setTimeout(pollCloudData, pollInterval)
    } catch (error) {
      console.error('轮询云端数据失败', error)
      currentPollTimer = null
      currentPollingDiaryId = null
    }
  }
  
  // 开始轮询
  currentPollTimer = setTimeout(pollCloudData, pollInterval)
}

// 提取处理数据的逻辑为单独函数
const processAndDisplayData = (data) => {
  title.value = ''
  polishContent.value = ''
  
  // 处理 AI 分析数据
  if (data.aiAnalysis) {
    processAiAnalysis(data).then(processedData => {
      // 设置标题
      const titleItem = processedData.find(item => item.parentId === 'title')
      if (titleItem) {
        title.value = titleItem.content
      }
      
      // 设置润色内容
      const polishItem = processedData.find(item => item.parentId === 'polish')
      if (polishItem) {
        polishContent.value = polishItem.content
      }
      
      // 过滤标题和润色项，只保留标签分析内容
      aiList.value = processedData.filter(
        item => item.parentId !== 'title' && item.parentId !== 'polish'
      )
      
      // 初始化所有 AI 分析部分为收起状态
      aiList.value.forEach((_, index) => {
        aiSectionVisible[index] = false
      })
    })
  } else {
    aiList.value = []
  }
}

// 页面生命周期钩子 - 离开页面时中止轮询
onUnmounted(() => {
  if (currentPollTimer) {
    clearTimeout(currentPollTimer)
    currentPollTimer = null
    currentPollingDiaryId = null
  }
})

// 监听应用切换到后台事件，中止轮询以节省资源
onHide(() => {
  if (currentPollTimer) {
    clearTimeout(currentPollTimer)
    currentPollTimer = null
    currentPollingDiaryId = null
    console.log('应用进入后台，停止轮询')
  }
})
```

## 3. 切换日期处理

为了确保切换日期时能正确中止当前轮询并开始新的查询，需要在`onChangeWeek`函数中添加验证和处理：

```js
const onChangeWeek = (date) => {
  // 清除之前的轮询
  if (currentPollTimer) {
    clearTimeout(currentPollTimer)
    currentPollTimer = null
    currentPollingDiaryId = null
  }
  
  // 设置新的当前日期
  currentViewDate = date
  
  // 获取新日期的数据
  getDay(date)
  weekSwitch()
}
```

# 任务

1. **修改日记编辑页面 (`diary_edit.vue`)**
   - 移除现有轮询逻辑
   - 修改保存逻辑，直接返回列表页
   - 确保工作流 API 调用不影响页面返回

2. **修改日记列表页面 (`diary.vue`)**
   - 增加查询云端数据的逻辑
   - 添加本地数据库更新机制
   - 实现轮询机制检查 AI 分析结果
   - 增加多种轮询中止机制：
     - 切换日期时自动中止
     - 轮询超时自动中止
     - 离开页面时中止
     - 应用进入后台时中止
   - 添加日期/ID 匹配验证，防止数据显示混乱
   - 提取数据处理逻辑为独立函数，方便复用

3. **测试验证**
   - 测试日记保存后直接返回功能
   - 测试列表页面云端数据同步功能
   - 测试轮询获取 AI 分析结果功能
   - 测试切换日期时轮询正确中止功能
   - 测试离开页面时轮询正确中止功能
   - 测试轮询超时后自动中止功能
   - 测试确保不会出现日期切换导致的数据显示混乱问题 
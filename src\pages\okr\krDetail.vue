<template>
  <div class="task-detail-page-container">
    <!-- 导航栏 -->
    <z-page-navbar title="关键结果详情" rightButtonType="more" @back="handleNavBack" @menuItemClick="handleMenuAction">
      <template #menu-items>
        <div class="action-menu-item" @click="handleMenuAction('edit')">
          <i class="fas fa-edit"></i>
          <span>编辑</span>
        </div>
        <div class="action-menu-item delete" @click="handleMenuAction('delete')">
          <i class="fas fa-trash"></i>
          <span>删除</span>
        </div>
      </template>
    </z-page-navbar>

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 关键结果标题和描述 -->
      <div class="kr-header" :class="{ completed: progressPercentage >= 100 }">
        <div class="kr-title">
          {{ taskDetail.title }}
        </div>
        <div class="kr-description">{{ taskDetail.content || '暂无描述' }}</div>
        <div class="kr-meta">
          <!-- <div class="kr-meta-item" v-if="taskDetail.weight && taskDetail.weight > 0">
            <i class="fas fa-balance-scale"></i>
            <span>权重：{{ taskDetail.weight || 1 }}</span>
          </div> -->
          <div class="kr-meta-item">
            <i class="fas fa-calculator"></i>
            <span>计算方式：{{ getCalcMethodName(taskDetail.valType) }}</span>
          </div>
          <div class="kr-meta-item" v-if="hasDateInfo">
            <i class="fas fa-calendar"></i>
            <span>{{ getDateDisplay() }}</span>
          </div>
          <div class="kr-meta-item" v-if="taskDetail.repeatFlag">
            <i class="fas fa-redo"></i>
            <span>循环：{{ getCycleTypeName(taskDetail.repeatFlag) }}</span>
          </div>
          <div class="kr-meta-item" v-if="taskDetail.dailyTarget && hasDateInfo">
            <i class="fas fa-bullseye"></i>
            <span>日目标：{{ taskDetail.dailyTarget }}</span>
          </div>
        </div>
      </div>

      <!-- 进度条 -->
      <div class="progress-container">
        <div class="progress-header">
          <div class="progress-title">当前进度</div>
          <div class="progress-value">
            {{ Math.round(Number(taskDetail.curVal || 0)) }}{{ taskDetail.unit || '' }}/{{ taskDetail.tgtVal || 0
            }}{{ taskDetail.unit || '' }} ({{ progressPercentage }}%)
          </div>
        </div>
        <div class="progress-bar">
          <div
            class="progress-fill"
            :class="{ 'progress-fill-completed': progressPercentage >= 100 }"
            :style="{ width: progressPercentage + '%' }"
          ></div>
        </div>
        <div class="progress-labels">
          <div>起始值：{{ taskDetail.initVal || 0 }}{{ taskDetail.unit || '' }}</div>
          <div>目标值：{{ taskDetail.tgtVal || 0 }}{{ taskDetail.unit || '' }}</div>
        </div>
        <div class="progress-info">
          <div class="progress-weight">
            权重系数：<span class="weight-value">{{ weightValueDisplay }}</span>
          </div>
          <div class="progress-calc-method">
            计算方式：<span class="calc-method-value">{{ getCalcMethodName(taskDetail.valType) }}</span>
          </div>
        </div>
      </div>

      <!-- 进度记录卡片 -->
      <div class="card">
        <div class="card-header">
          <div class="card-title-container">
            <div class="card-tabs">
              <div class="card-tab" :class="{ active: activeTab === 'history' }" @click="activeTab = 'history'">
                进度历史
              </div>
              <div class="card-tab" :class="{ active: activeTab === 'statistics' }" @click="activeTab = 'statistics'">
                统计情况
              </div>
              <div class="card-tab" :class="{ active: activeTab === 'trend' }" @click="activeTab = 'trend'">
                进度走势图
              </div>
              <!-- <div class="card-tab" :class="{ active: activeTab === 'monthView' }" @click="activeTab = 'monthView'">
                执行月视图
              </div> -->
            </div>
          </div>
        </div>
        <div class="card-body">
          <l-progress-history
            v-if="activeTab === 'history'"
            :kr-id="taskDetail?._id || ''"
            :initial-records="taskDetail?.recList || []"
            :unit="taskDetail?.unit || ''"
            @update-record="openEditProgressModal"
            @delete-record="deleteProgressRecord"
            ref="progressHistoryRef"
          />
          <l-statistics v-if="activeTab === 'statistics'" :task-detail="taskDetail" />
          <l-trend-chart v-if="activeTab === 'trend'" :kr-data="taskDetail" :unit="taskDetail?.unit || ''" />
          <l-month-calendar-view v-if="activeTab === 'monthView'" :task-detail="taskDetail" />
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="footer">
      <div class="action-btn primary-btn" @click="openUpdateProgressModal">
        <i class="fas fa-chart-line mr-2"></i>
        {{ progressPercentage >= 100 ? '修改进度' : '更新进度' }}
      </div>
    </div>

    <UpdateProgressModal
      v-model:visible="isUpdateProgressModalVisible"
      :kr-title="currentTaskTitle"
      :calc-method="currentTaskCalcMethod"
      :task-id="taskDetail?._id || ''"
      :initial-progress="currentEditProgressValue"
      :initial-remark="currentEditProgressRemark"
      :edit-record-id="currentEditProgressId"
      @save-success="handleSaveProgressSuccess"
    />

    <!-- Tab bar placeholder -->
    <div id="tab-bar-container-placeholder"></div>

    <!-- 添加确认删除关键结果弹窗 -->
    <z-confirm-modal
      v-model:visible="showDeleteConfirmModal"
      title="确认删除"
      message="确定要删除此关键结果吗？该操作不可恢复，相关联的任务也将被删除。"
      confirm-text="删除"
      cancel-text="取消"
      type="danger"
      @confirm="deleteKeyResult"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import ZPageNavbar from '@/components/z-page-navbar.vue'
import { router } from '@/utils/tools'
import UpdateProgressModal from '@/components/z-update-progress-modal.vue'
import { getTaskApi, delTaskApi, updateTaskApi } from '@/api/task'
import ZConfirmModal from '@/components/z-confirm-modal.vue'
import dayjs from 'dayjs'
import * as rrule from '@/utils/rrrrule'
import LProgressHistory from './components/l-progress-history.vue'
import LStatistics from './components/l-statistics.vue'
import LTrendChart from './components/l-trend-chart/l-trend-chart.vue'
import LMonthCalendarView from './components/l-month-calendar-view/l-month-calendar-view.vue'

const taskDetail = ref<any>(null)
const progressHistoryRef = ref<InstanceType<typeof LProgressHistory> | null>(null)
const activeTab = ref('history')

const isUpdateProgressModalVisible = ref(false)
const currentTaskTitle = ref('')
const currentTaskCalcMethod = ref('')
const currentEditProgressValue = ref(0)
const currentEditProgressRemark = ref('')
const currentEditProgressId = ref('')

const progressPercentage = ref(0)
const weightPercentage = ref(0)
const weightValueDisplay = computed(() => {
  return `${taskDetail.value?.weight || 1} (相对重要程度)`
})

// 判断是否有日期信息
const hasDateInfo = computed(() => {
  return !!(taskDetail.value?.startDate || taskDetail.value?.endDate)
})

const openUpdateProgressModal = () => {
  currentTaskTitle.value = taskDetail.value?.title || ''
  currentTaskCalcMethod.value = taskDetail.value?.valType || 'sum'
  currentEditProgressValue.value = 0 // 新增时不填充当前总进度值，从 0 开始
  currentEditProgressRemark.value = ''
  currentEditProgressId.value = '' // 设置为空字符串，表示新增进度
  isUpdateProgressModalVisible.value = true
}

// 获取关键结果详情
const fetchTaskDetail = async (id: string) => {
  try {
    const result = await getTaskApi(id, { progress: true })
    console.log('result--->', result)
    if (result) {
      taskDetail.value = result

      // 更新页面数据
      // 计算进度百分比，确保不超过 100%
      const calculatedPercentage = Math.round((result.curVal / result.tgtVal) * 100) || 0
      progressPercentage.value = Math.min(calculatedPercentage, 100)
      weightPercentage.value = result.weight || 1

      // 处理进度记录数据
      processProgressData(result)
    } else {
      uni.showToast({
        title: '获取关键结果详情失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('获取关键结果详情失败：', error)
    uni.showToast({
      title: '获取关键结果详情失败',
      icon: 'none',
    })
  }
}

// 处理进度记录数据，用于图表和时间线
const processProgressData = (data: any) => {
  if (!data || !data.recList || !Array.isArray(data.recList) || data.recList.length === 0) {
    return
  }

  // 按日期排序
  const sorted = [...data.recList].sort((a, b) => {
    return new Date(b.recTime).getTime() - new Date(a.recTime).getTime()
  })
  console.log('sorted--->', sorted)
  // 更新排序后的记录
  sortedRecords.value = sorted

  // 计算图表最大值 (稍微大于最大进度或目标值，便于显示)
  const maxProgressValue = Math.max(...sorted.map((r) => r.val), data.tgtVal || 0)
  maxValue.value = Math.ceil(maxProgressValue * 1.1) // 加 10% 的空间

  // 准备图表点数据
  chartPoints.value = sorted.map((record) => ({
    value: record.val,
    date: record.date,
  }))

  // 生成 SVG 折线图的点坐标
  chartLinePoints.value = chartPoints.value
    .map((point, index) => {
      const x = (index / (chartPoints.value.length - 1 || 1)) * 100
      const y = 100 - (point.value / maxValue.value) * 100
      return `${x},${y}`
    })
    .join(' ')

  // 格式化图表日期标签 (如果过多，则只显示部分)
  const labels = sorted.map((r) => formatDate(r.date))
  if (labels.length > 6) {
    // 如果记录过多，只显示开始、结束和中间的一些点
    const step = Math.ceil(labels.length / 5) // 大约显示5个点
    chartDateLabels.value = labels.filter((_, i) => i === 0 || i === labels.length - 1 || i % step === 0)
  } else {
    chartDateLabels.value = labels
  }
}

const onInit = () => {
  // 获取路由参数
  console.log('获取路由参数')
  const { okrId, id } = getRoute.params()
  console.log('onInit1111', okrId, id)
  // 保存关键参数
  taskDetail.value = {
    _id: id,
    okrId: okrId,
  }

  // 如果缺少必要参数，显示提示
  if (!okrId || !id) {
    uni.showToast({
      title: '缺少必要参数',
      icon: 'none',
    })
    setTimeout(() => {
      router.back()
    }, 500)
    return
  }

  if (taskDetail.value?._id) {
    fetchTaskDetail(taskDetail.value?._id)
  }
  if (taskDetail.value && taskDetail.value.recList) {
    maxValue.value = Math.max(...taskDetail.value.recList.map((r: any) => r.val))
    chartPoints.value = taskDetail.value.recList.map((r: any) => ({
      value: r.val,
      date: r.date,
    }))
    chartLinePoints.value = chartPoints.value.map((p) => `${p.value * 100}%`).join(',')
    chartDateLabels.value = taskDetail.value.recList.map((r: any) => r.date)
    sortedRecords.value = [...taskDetail.value.recList]
    sortedRecords.value.sort((a: any, b: any) => new Date(a.date).getTime() - new Date(b.date).getTime())
  }
}
useReload(onInit)
onShow(() => {
  onInit()
})

const handleNavBack = () => {
  // As per development specifications, use router.back()
  router.back()
}

const handleNavEdit = () => {
  // 跳转到编辑页面，传递 okrId 和 id 参数
  const okrId = taskDetail.value?.okrId || ''
  const id = taskDetail.value?._id || ''

  if (okrId && id) {
    router.push('/pages/okr/krEdit', { okrId, id })
  } else {
    uni.showToast({
      title: '缺少必要参数',
      icon: 'none',
    })
  }
}

// 处理菜单项操作
const handleMenuAction = (action: string) => {
  switch (action) {
    case 'edit':
      handleNavEdit()
      break
    case 'delete':
      showDeleteConfirmModal.value = true
      break
    default:
      break
  }
}

// 删除确认弹窗状态
const showDeleteConfirmModal = ref(false)

// 删除关键结果
const deleteKeyResult = async () => {
  try {
    if (!taskDetail.value || !taskDetail.value._id) return

    // 显示加载状态
    uni.showLoading({
      title: '正在删除...',
    })

    // 1. 先删除所有关联任务
    await delTaskApi(`parentId == "${taskDetail.value._id}"`)

    // 2. 删除关键结果本身
    await delTaskApi(taskDetail.value._id)

    // 隐藏加载状态
    uni.hideLoading()

    // 显示成功提示
    uni.showToast({
      title: '删除成功',
      icon: 'success',
    })

    // 返回上一页
    setTimeout(() => {
      router.back()
    }, 500)
  } catch (error) {
    console.error('删除关键结果失败：', error)
    uni.hideLoading()
    uni.showToast({
      title: '删除失败',
      icon: 'none',
    })
  }
}

const maxValue = ref(0)
const chartPoints = ref<{ value: number; date: string }[]>([])
const chartLinePoints = ref('')
const chartDateLabels = ref<string[]>([])
const sortedRecords = ref<any[]>([])

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD')
}

const isProgressIncrease = (index: number) => {
  if (index < sortedRecords.value.length - 1) {
    return sortedRecords.value[index].val < sortedRecords.value[index + 1].val
  }
  return false
}

const getProgressChange = (index: number) => {
  if (index < sortedRecords.value.length - 1) {
    // 获取计算方式
    const calcMethod = taskDetail.value?.valType || 'sum'

    // 获取当前记录及之前的所有记录
    const currentRecords = sortedRecords.value.slice(index)
    // 获取不包含当前记录的之前所有记录
    const previousRecords = sortedRecords.value.slice(index + 1)

    console.log(`[进度变化计算] 索引: ${index}, 计算方式: ${calcMethod}`)
    console.log(`[进度变化计算] 当前记录及之前: `, currentRecords)
    console.log(`[进度变化计算] 之前记录: `, previousRecords)

    // 根据不同计算方式计算值
    let currentValue = 0
    let previousValue = 0

    switch (calcMethod) {
      case 'sum':
        // 求和：所有记录值的总和
        currentValue = currentRecords.reduce((sum, record) => sum + record.val, 0)
        previousValue = previousRecords.length > 0 ? previousRecords.reduce((sum, record) => sum + record.val, 0) : 0
        console.log(`[进度变化计算 - 求和] 当前值：${currentValue}, 之前值：${previousValue}`)
        break
      case 'avg':
        // 平均值：所有记录值的平均
        currentValue =
          currentRecords.length > 0
            ? currentRecords.reduce((sum, record) => sum + record.val, 0) / currentRecords.length
            : 0
        previousValue =
          previousRecords.length > 0
            ? previousRecords.reduce((sum, record) => sum + record.val, 0) / previousRecords.length
            : 0
        console.log(`[进度变化计算 - 平均值] 当前值：${currentValue}, 之前值：${previousValue}`)
        break
      case 'max':
        // 最大值：所有记录中的最大值
        currentValue = currentRecords.length > 0 ? Math.max(...currentRecords.map((record) => record.val)) : 0
        previousValue = previousRecords.length > 0 ? Math.max(...previousRecords.map((record) => record.val)) : 0
        console.log(`[进度变化计算 - 最大值] 当前值：${currentValue}, 之前值：${previousValue}`)
        break
      case 'latest':
      default:
        // 最新值：直接使用最新记录的值
        currentValue = sortedRecords.value[index].val
        previousValue = index + 1 < sortedRecords.value.length ? sortedRecords.value[index + 1].val : 0
        console.log(`[进度变化计算-最新值] 当前值: ${currentValue}, 之前值: ${previousValue}`)
        break
    }

    // 计算变化值
    const change = currentValue - previousValue
    console.log(`[进度变化计算] 最终变化值: ${change}`)

    // 如果变化值为0，不显示
    if (change === 0) return ''

    // 格式化显示：正值加上+号，使用整数
    return change > 0 ? `+${Math.round(change)}` : Math.round(change).toString()
  }
  return ''
}

const getProgressChangeClass = (index: number) => {
  if (index < sortedRecords.value.length - 1) {
    // 获取计算方式
    const calcMethod = taskDetail.value?.valType || 'sum'

    // 获取当前记录及之前的所有记录
    const currentRecords = sortedRecords.value.slice(index)
    // 获取不包含当前记录的之前所有记录
    const previousRecords = sortedRecords.value.slice(index + 1)

    // 根据不同计算方式计算值
    let currentValue = 0
    let previousValue = 0

    switch (calcMethod) {
      case 'sum':
        // 求和：所有记录值的总和
        currentValue = currentRecords.reduce((sum, record) => sum + record.val, 0)
        previousValue = previousRecords.length > 0 ? previousRecords.reduce((sum, record) => sum + record.val, 0) : 0
        break
      case 'avg':
        // 平均值：所有记录值的平均
        currentValue =
          currentRecords.length > 0
            ? currentRecords.reduce((sum, record) => sum + record.val, 0) / currentRecords.length
            : 0
        previousValue =
          previousRecords.length > 0
            ? previousRecords.reduce((sum, record) => sum + record.val, 0) / previousRecords.length
            : 0
        break
      case 'max':
        // 最大值：所有记录中的最大值
        currentValue = currentRecords.length > 0 ? Math.max(...currentRecords.map((record) => record.val)) : 0
        previousValue = previousRecords.length > 0 ? Math.max(...previousRecords.map((record) => record.val)) : 0
        break
      case 'latest':
      default:
        // 最新值：直接使用最新记录的值
        currentValue = sortedRecords.value[index].val
        previousValue = index + 1 < sortedRecords.value.length ? sortedRecords.value[index + 1].val : 0
        break
    }

    // 计算变化值
    const change = currentValue - previousValue

    // 如果变化值为0，不返回样式类
    if (change === 0) return ''

    if (change > 0) {
      return 'text-green-500'
    } else if (change < 0) {
      return 'text-red-500'
    }
  }
  return ''
}

// 获取计算方式名称
const getCalcMethodName = (valType = 'sum') => {
  switch (valType) {
    case 'sum':
      return '求和'
    case 'latest':
      return '最新'
    case 'max':
      return '最大值'
    case 'avg':
      return '平均值'
    default:
      return '求和'
  }
}

const deleteProgressRecord = async (recordIdToDelete: string) => {
  if (!taskDetail.value || !taskDetail.value._id || !taskDetail.value.recList) {
    uni.showToast({
      title: '数据异常，无法删除',
      icon: 'none',
    })
    return
  }

  try {
    uni.showLoading({ title: '正在删除...' })

    const originalRecList = taskDetail.value.recList
    const updatedRecList = originalRecList.filter((rec: any) => rec._id !== recordIdToDelete)

    if (updatedRecList.length === originalRecList.length) {
      uni.hideLoading()
      uni.showToast({ title: '未找到要删除的记录', icon: 'none' })
      return
    }

    const updateData: API.EditTask = {
      recList: updatedRecList,
      // 我们只更新 recList，其他字段保持不变或让后端处理
    }

    // 如果更新后的 recList 为空，需要显式传递空数组，否则后端可能不会清空
    if (updatedRecList.length === 0) {
      updateData.recList = []
    }

    await updateTaskApi(taskDetail.value._id, updateData)

    uni.hideLoading()
    uni.showToast({
      title: '删除成功',
      icon: 'success',
    })

    // 重新获取任务详情以刷新数据和视图
    fetchTaskDetail(taskDetail.value._id)

    // 刷新子组件数据
    if (progressHistoryRef.value) {
      setTimeout(() => {
        progressHistoryRef.value?.loadRecords()
      }, 300)
    }
  } catch (error) {
    uni.hideLoading()
    console.error('删除进度记录失败：', error)
    uni.showToast({
      title: '删除进度记录失败',
      icon: 'none',
    })
  }
}

const openEditProgressModal = (record: any) => {
  currentTaskTitle.value = taskDetail.value?.title || ''
  currentTaskCalcMethod.value = taskDetail.value?.valType || 'sum'
  currentEditProgressValue.value = record.val || 0
  currentEditProgressRemark.value = record.remark || ''
  currentEditProgressId.value = record._id || ''
  isUpdateProgressModalVisible.value = true
}

const getDateDisplay = () => {
  if (!taskDetail.value || (!taskDetail.value.startDate && !taskDetail.value.endDate)) return ''

  // 如果有开始日期和结束日期，显示日期区间
  if (taskDetail.value.startDate && taskDetail.value.endDate) {
    const startDateFormatted = formatDateShort(taskDetail.value.startDate)
    const endDateFormatted = formatDateShort(taskDetail.value.endDate)
    return `${startDateFormatted} ~ ${endDateFormatted}`
  }
  // 如果只有开始日期，显示单日
  else if (taskDetail.value.startDate) {
    return formatDateShort(taskDetail.value.startDate)
  }
  // 没有日期
  else {
    return ''
  }
}

// 格式化日期为简短格式：YYYY/MM/DD
const formatDateShort = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${year}/${month}/${day}`
}

// 获取循环类型名称
const getCycleTypeName = (cycleType: string) => {
  if (!cycleType) return '不重复'
  return rrule.toText(cycleType)
}

const handleSaveProgressSuccess = () => {
  // 重新获取任务详情以刷新父组件数据
  fetchTaskDetail(taskDetail.value?._id || '')

  // 刷新子组件数据
  if (progressHistoryRef.value) {
    setTimeout(() => {
      progressHistoryRef.value?.loadRecords()
    }, 300)
  }
}
</script>

<style scoped lang="scss">
.task-checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-gray-300);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  flex-shrink: 0;

  &.checked {
    background: var(--color-primary);
    border-color: var(--color-primary);
    color: white;
  }

  i {
    font-size: 12px;
    animation: checkmark 0.2s ease-out;
  }
}

@keyframes checkmark {
  0% {
    transform: translateY(5px);
  }
  50% {
    transform: translateY(-2px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Global page container styles (originally body) */
.task-detail-page-container {
  background: var(--color-gray-50, #f9fafb);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  color: var(--color-gray-800);
  overflow-x: hidden;
}

.content-area {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px 90px;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.navbar {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);
  position: sticky;
  top: 0;
  z-index: 40;

  &-title {
    font-weight: 600;
    font-size: 18px;
    margin-left: 15px;
    color: var(--color-primary);
  }

  &-back {
    color: var(--color-primary);
    font-size: 16px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: var(--color-gray-100);
    cursor: pointer;
  }

  &-action {
    margin-left: auto;
    color: var(--color-primary);
    font-weight: 500;
    font-size: 15px;
    padding: 8px 16px;
    border-radius: 6px;
    background: var(--color-primary-light);
    cursor: pointer;
    display: flex;
    align-items: center;
  }
}

.section {
  margin-bottom: 24px;
}

.card {
  background: var(--color-white);
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
  overflow: hidden;
  border: 1px solid var(--color-gray-100);

  &-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--color-gray-100);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--color-gray-800);
  }

  &-subtitle {
    font-size: 14px;
    color: var(--color-gray-500);
    margin-top: 4px;
  }

  &-body {
    padding: 20px;
  }

  &-actions {
    display: flex;
    gap: 8px;
  }

  &-action-btn {
    color: var(--color-gray-600);
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    background: var(--color-gray-100);
    cursor: pointer;

    &:hover {
      background: var(--color-gray-200);
      color: var(--color-primary);
    }
  }
}

.kr-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 20px;
  background: linear-gradient(to right, var(--color-primary-light), rgba(255, 255, 255, 0.8));
  border-radius: 16px;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.kr-header.completed::after {
  content: '已完成';
  position: absolute;
  top: 10%;
  right: 5%;
  transform: rotate(-30deg);
  font-size: 28px;
  font-weight: bold;
  color: rgba(var(--color-primary-rgb, 99, 102, 241), 0.15);
  pointer-events: none;
  z-index: 1;
}

.kr-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-gray-800);
  line-height: 1.4;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.kr-description {
  font-size: 15px;
  color: var(--color-gray-600);
  line-height: 1.5;
}

.kr-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 12px;

  &-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: var(--color-gray-600);
    background: rgba(255, 255, 255, 0.7);
    padding: 6px 12px;
    border-radius: 6px;

    i {
      font-size: 14px;
      color: var(--color-primary);
    }
  }
}

.progress-container {
  margin: 20px 0;

  .progress-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .progress-title {
    font-size: 15px;
    font-weight: 500;
    color: var(--color-gray-700);
  }

  .progress-value {
    font-size: 15px;
    font-weight: 600;
    color: var(--color-primary);
  }

  .progress-bar {
    height: 8px;
    background: var(--color-gray-200);
    border-radius: 4px;
    margin-bottom: 8px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(to right, var(--color-primary), var(--color-primary-light));
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .progress-labels {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--color-gray-500);
  }

  .progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--color-gray-600);
  }

  .progress-weight {
    font-weight: 500;
  }

  .progress-calc-method {
    font-weight: 500;
    margin-top: 4px;
  }

  .calc-method-value {
    font-weight: 600;
    color: var(--color-primary);
  }

  .weight-value {
    font-weight: 600;
    color: var(--color-primary);
  }

  .progress-daily-target {
    font-weight: 500;
    margin-top: 4px;
  }

  .daily-target-value {
    font-weight: 600;
    color: var(--color-primary);
  }
}

.chart-container {
  position: relative;
  height: 200px;
  margin: 20px 0;
}

.tabs {
  display: flex;
  border-bottom: 1px solid var(--color-gray-200);
  margin-bottom: 16px;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  .tab {
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 500;
    color: var(--color-gray-600);
    cursor: pointer;
    white-space: nowrap;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;

    &.active {
      color: var(--color-primary);
      border-bottom-color: var(--color-primary);
    }
  }
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 4px 2px;
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--color-gray-400);
  background: var(--color-white);
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--color-gray-100);
  text-align: center;

  .empty-icon {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.5;
  }

  .empty-hint {
    margin-top: 8px;
    font-size: 13px;
    color: var(--color-gray-500);
  }
}

.task-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 16px;
  background: var(--color-white);
  border-radius: 12px;
  transition: all 0.2s;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--color-gray-100);

  &:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
    border-color: var(--color-gray-200);
  }

  .task-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
    min-width: 0;
  }

  .task-name {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    font-weight: 500;
    color: var(--color-gray-700);
  }

  .task-meta {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 12px;
    color: var(--color-gray-500);
    margin-left: 30px; /* 与复选框对齐 */

    .task-deadline,
    .task-progress-value {
      display: flex;
      align-items: center;
      gap: 6px;

      i {
        font-size: 12px;
      }
    }

    .task-progress-value {
      color: var(--color-primary);
      font-weight: 500;
    }
  }

  .task-status {
    font-size: 12px;
    padding: 5px 10px;
    border-radius: 6px;
    font-weight: 500;
    white-space: nowrap;
    margin-left: 12px;
  }
}

.text-line-through {
  text-decoration: line-through;
}

.text-gray-400 {
  color: var(--color-gray-400, #9ca3af);
}

.status-pending {
  background: var(--color-gray-50);
  color: var(--color-gray-600);
  border: 1px solid var(--color-gray-200);
}

.status-in-progress {
  background: rgba(33, 150, 243, 0.1);
  color: #2196f3;
  border: 1px solid rgba(33, 150, 243, 0.2);
}

.status-completed {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.2);
}

.status-abandoned {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.2);
}

.milestone-list {
  position: relative;
  padding-left: 20px;
  margin-top: 10px;

  &::before {
    content: '';
    position: absolute;
    left: 4px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--color-gray-200);
  }

  .milestone-item {
    position: relative;
    margin-bottom: 16px;
    padding-left: 18px;

    &::before {
      content: '';
      position: absolute;
      left: -4px;
      top: 6px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: var(--color-gray-300);
      z-index: 1;
    }

    &.active::before {
      background: var(--color-primary);
    }

    &.completed::before {
      background: var(--color-primary);
    }

    .milestone-date {
      font-size: 12px;
      color: var(--color-gray-500);
    }

    .milestone-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--color-gray-700);
      margin: 4px 0;
    }

    .milestone-desc {
      font-size: 13px;
      color: var(--color-gray-600);
    }
  }
}

.note-list {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .note-item {
    background: var(--color-white);
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--color-gray-100);

    .note-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      .note-author {
        font-size: 14px;
        font-weight: 500;
        color: var(--color-gray-800);
      }

      .note-date {
        font-size: 12px;
        color: var(--color-gray-500);
      }
    }

    .note-content {
      font-size: 14px;
      color: var(--color-gray-700);
      line-height: 1.5;
    }
  }
}

/* Footer styles */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: var(--color-white);
  padding: 16px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 50;
  display: flex;
  gap: 12px;

  .action-btn {
    flex: 1;
    border: none;
    border-radius: 12px;
    padding: 14px;
    font-size: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
  }

  .primary-btn {
    background: var(--color-primary);
    color: var(--color-white);

    &:hover {
      background: var(--color-primary-dark);
    }
  }

  .secondary-btn {
    background: var(--color-gray-100);
    color: var(--color-gray-700);

    &:hover {
      background: var(--color-gray-200);
    }
  }
}

/* Modal styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100;
  justify-content: center;
  align-items: center;

  &.show {
    display: flex;
  }

  &-content {
    background: var(--color-white);
    border-radius: 16px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  &-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--color-gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--color-gray-800);
  }

  &-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--color-gray-500);
    cursor: pointer;
  }

  &-body {
    padding: 20px;
  }

  &-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--color-gray-200);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

.form {
  &-group {
    margin-bottom: 16px;
  }

  &-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--color-gray-700);
    margin-bottom: 8px;
  }

  &-input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--color-gray-300);
    border-radius: 8px;
    font-size: 14px;
    color: var(--color-gray-800);
    background: var(--color-white);
    box-sizing: border-box;

    &:focus {
      border-color: var(--color-primary);
      outline: none;
      box-shadow: 0 0 0 2px var(--color-primary-light);
    }
  }
}

.related-kr {
  padding: 12px;
  background: var(--color-gray-50);
  border-radius: 8px;
  border: 1px solid var(--color-gray-200);
}

.kr-info {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .kr-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--color-gray-800);
  }

  .kr-progress {
    font-size: 12px;
    color: var(--color-gray-600);
  }
}

.cycle-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;

  .cycle-option {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border-radius: 6px;
    background: var(--color-gray-50);
    border: 1px solid var(--color-gray-200);
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background: var(--color-gray-100);
    }

    input[type='radio'] {
      margin: 0;
      cursor: pointer;
    }

    label {
      cursor: pointer;
      margin-left: 4px;
      display: inline-flex;
      align-items: center;
    }
  }
}

.progress-input-container {
  display: flex;
  align-items: center;

  .progress-unit {
    margin-left: 8px;
    font-size: 14px;
    color: var(--color-gray-600);
  }
}

.btn {
  &-cancel {
    padding: 10px 16px;
    border: 1px solid var(--color-gray-300);
    background: var(--color-white);
    color: var(--color-gray-700);
    font-size: 14px;
    font-weight: 500;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background: var(--color-gray-50);
    }
  }

  &-save {
    padding: 10px 16px;
    border: none;
    background: var(--color-primary);
    color: var(--color-white);
    font-size: 14px;
    font-weight: 500;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background: var(--color-primary-dark);
    }
  }
}

/* Ensure Font Awesome icons are displayed if classes are used directly */
.fas,
.far {
  /* Adjust display if necessary, though often handled by FA CSS */
  /* display: inline-block; */
}

/* Basic Tailwind utility classes */
.mr-1 {
  margin-right: 0.25rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mt-4 {
  margin-top: 1rem;
}
.flex {
  display: flex;
}
.justify-center {
  justify-content: center;
}
.justify-end {
  justify-content: flex-end;
}
.items-center {
  align-items: center;
}
.text-green-500 {
  color: #22c55e;
}
.text-blue-500 {
  color: #3b82f6;
}
.text-gray-400 {
  color: var(--color-gray-400, #9ca3af);
}
.text-gray-600 {
  color: var(--color-gray-600, #4b5563);
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.w-full {
  width: 100%;
}
.border {
  border-width: 1px;
}
.border-gray-300 {
  border-color: var(--color-gray-300, #d1d5db);
}
.bg-gray-100 {
  background-color: var(--color-gray-100, #f3f4f6);
}
.cursor-pointer {
  cursor: pointer;
}

/* 删除按钮样式 */
:deep(.action-menu-item.delete) {
  color: var(--color-danger, #dc3545);

  i {
    color: var(--color-danger, #dc3545);
  }
}

/* 进度历史图表和时间线样式 */
.progress-chart {
  display: flex;
  flex-direction: column;
  height: 300px;
  margin-bottom: 16px;

  .chart-header {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;

    .chart-legend {
      display: flex;
      gap: 16px;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;

        .legend-color {
          width: 14px;
          height: 4px;
          border-radius: 2px;
        }
      }
    }
  }

  .chart-container {
    position: relative;
    height: 200px;
    border-left: 1px solid var(--color-gray-200);
    border-bottom: 1px solid var(--color-gray-200);
    margin-bottom: 24px;

    .chart-grid {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;

      .grid-line {
        position: absolute;
        left: 0;
        right: 0;
        border-top: 1px dashed var(--color-gray-200);
        height: 0;

        &:nth-child(1) {
          top: 0;
        }
        &:nth-child(2) {
          top: 25%;
        }
        &:nth-child(3) {
          top: 50%;
        }
        &:nth-child(4) {
          top: 75%;
        }
        &:nth-child(5) {
          top: 100%;
        }
      }
    }

    .chart-target-line {
      position: absolute;
      left: 0;
      right: 0;
      height: 1px;
      background: #ddd;
      z-index: 2;
      border-top: 1px dashed var(--color-gray-400);
    }

    .chart-point {
      position: absolute;
      width: 8px;
      height: 8px;
      background: var(--color-primary);
      border-radius: 50%;
      transform: translate(-50%, 50%);
      z-index: 4;
      cursor: pointer;

      &:hover {
        background: var(--color-primary-dark);
        box-shadow: 0 0 0 4px rgba(var(--color-primary-rgb), 0.2);
      }
    }

    .chart-line {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 3;
    }
  }

  .chart-footer {
    .chart-date-labels {
      position: relative;
      height: 20px;

      .date-label {
        position: absolute;
        transform: translateX(-50%);
        font-size: 10px;
        color: var(--color-gray-500);
      }
    }
  }
}

.timeline {
  display: flex;
  flex-direction: column;
  gap: 0;

  .timeline-item {
    display: flex;
    position: relative;
    padding-bottom: 16px;

    &:not(:last-child):before {
      content: '';
      position: absolute;
      left: 14px;
      top: 24px;
      bottom: 0;
      width: 2px;
      background-color: var(--color-gray-200);
      z-index: 1;
    }

    .timeline-marker {
      flex: 0 0 30px;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background: var(--color-white);
      border: 2px solid var(--color-gray-300);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      z-index: 2;

      i {
        font-size: 12px;
        color: var(--color-gray-600);
      }

      &.is-increase {
        background: var(--color-primary-light);
        border-color: var(--color-primary);

        i {
          color: var(--color-primary);
        }
      }
    }

    .timeline-content {
      flex: 1;
      background: var(--color-white);
      border-radius: 12px;
      padding: 12px 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      border: 1px solid var(--color-gray-100);

      .timeline-header {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 8px;
        gap: 12px;

        .timeline-date {
          font-size: 13px;
          font-weight: 500;
          color: var(--color-gray-600);
        }

        .timeline-progress {
          display: flex;
          align-items: center;
          gap: 8px;

          .progress-value {
            font-size: 16px;
            font-weight: 600;
            color: var(--color-primary);
          }

          .progress-change {
            font-size: 12px;
            font-weight: 500;
            padding: 2px 6px;
            border-radius: 10px;
            background: var(--color-gray-50);
          }

          .text-green-500 {
            color: #22c55e;
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.2);
          }

          .text-red-500 {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
          }
        }
      }

      .timeline-body {
        font-size: 14px;
        color: var(--color-gray-700);
        line-height: 1.5;
        cursor: pointer;
        transition: background-color 0.2s ease;
        padding: 4px;
        border-radius: 4px;
        position: relative;

        &:hover {
          background-color: var(--color-gray-50);

          .timeline-edit-hint {
            opacity: 1;
          }
        }
      }
    }
  }
}

.timeline-delete-icon {
  color: var(--color-gray-400);
  cursor: pointer;
  margin-left: auto; /* 自动占据右侧空间 */
  font-size: 14px; /* 调整图标大小 */
  transition: color 0.2s;

  &:hover {
    color: var(--color-danger, #dc3545); /* 使用主题色或备用红色 */
  }
}

.timeline-edit-hint {
  position: absolute;
  right: 4px;
  bottom: 4px;
  font-size: 12px;
  color: var(--color-primary);
  opacity: 0;
  transition: opacity 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;

  i {
    font-size: 10px;
  }

  span {
    font-size: 10px;
  }
}

/* 进度完成时添加动画效果 */
.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: progressShine 1.5s infinite;
  display: none;
}

/* 只有当进度为 100% 时才显示动画效果 */
.progress-fill-completed::after {
  display: block;
}

@keyframes progressShine {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(100%);
  }
}

.add-task-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 16px;
  background: var(--color-white);
  color: var(--color-primary);
  border: 1px dashed var(--color-primary-light);
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: var(--color-primary-light);
    color: var(--color-primary-dark);
  }

  i {
    font-size: 12px;
  }
}

.add-note-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background: var(--color-white);
  color: var(--color-primary);
  border: 1px solid var(--color-primary-light);
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: var(--color-primary-light);
    color: var(--color-primary-dark);
  }
}

/* 新增 Tab 样式 */
.card-title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.card-tabs {
  display: flex;
  align-items: center;
  margin-left: 0;
}

.card-tab {
  padding: 6px 16px;
  font-size: 14px;
  color: var(--color-gray-600, #666);
  cursor: pointer;
  border-radius: 16px;
  transition: all 0.2s ease;
}

.card-tab.active {
  color: var(--color-primary, #3370ff);
  background-color: var(--color-primary-transparent-20, rgba(51, 112, 255, 0.1));
  font-weight: 500;
}

.card-tab:hover:not(.active) {
  background-color: var(--color-gray-100, #f5f5f5);
}
</style>

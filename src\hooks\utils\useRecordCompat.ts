import { ref, computed } from 'vue'
import { getPlatformInfo, checkRecordSupport } from './platform'
import { RecordErrorType } from '../utils/recordErrors'

/**
 * 录音功能兼容性检查Hook
 *
 * @returns 录音功能兼容性信息
 */
export default function useRecordCompat() {
  const platformInfo = getPlatformInfo()
  const isSupported = ref(checkRecordSupport())
  const errorReason = ref<RecordErrorType | null>(null)

  // 检查录音权限
  const checkPermission = async () => {
    try {
      if (platformInfo.isH5) {
        // H5环境下检查权限
        if (navigator && navigator.permissions) {
          const result = await navigator.permissions.query({ name: 'microphone' as PermissionName })
          return result.state === 'granted' || result.state === 'prompt'
        }
        // 对于不支持permissions API的浏览器，只能在使用时检查
        return true
      } else if (platformInfo.isApp) {
        // App环境下检查权限
        return new Promise<boolean>((resolve) => {
          // #ifdef APP-PLUS
          if (uni.getSystemInfoSync().platform === 'android') {
            plus.android.requestPermissions(
              ['android.permission.RECORD_AUDIO'],
              (result) => {
                resolve(result.granted.length > 0)
              },
              () => {
                resolve(false)
              }
            )
          } else {
            // iOS 平台默认有权限，或在 info.plist 中配置
            resolve(true)
          }
          // #endif
          // #ifndef APP-PLUS
          resolve(false)
          // #endif
        })
      }
      return false
    } catch (error) {
      console.error('检查录音权限失败:', error)
      return false
    }
  }

  // 请求录音权限
  const requestPermission = async () => {
    try {
      if (platformInfo.isH5) {
        // H5环境下请求权限
        if (navigator && navigator.mediaDevices) {
          try {
            await navigator.mediaDevices.getUserMedia({ audio: true })
            return true
          } catch (err: any) {
            if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
              errorReason.value = RecordErrorType.PERMISSION_DENIED
            } else {
              errorReason.value = RecordErrorType.INITIALIZATION_FAILED
            }
            return false
          }
        }
        errorReason.value = RecordErrorType.NOT_SUPPORTED
        return false
      } else if (platformInfo.isApp) {
        // App环境下请求权限
        return new Promise<boolean>((resolve) => {
          // #ifdef APP-PLUS
          if (uni.getSystemInfoSync().platform === 'android') {
            plus.android.requestPermissions(
              ['android.permission.RECORD_AUDIO'],
              (result) => {
                if (result.granted.length > 0) {
                  resolve(true)
                } else {
                  errorReason.value = RecordErrorType.PERMISSION_DENIED
                  resolve(false)
                }
              },
              (err) => {
                errorReason.value = RecordErrorType.PERMISSION_DENIED
                resolve(false)
              }
            )
          } else {
            // iOS 平台
            resolve(true) // 假设已配置
          }
          // #endif
          // #ifndef APP-PLUS
          errorReason.value = RecordErrorType.NOT_SUPPORTED
          resolve(false)
          // #endif
        })
      }
      errorReason.value = RecordErrorType.NOT_SUPPORTED
      return false
    } catch (error) {
      console.error('请求录音权限失败:', error)
      errorReason.value = RecordErrorType.UNKNOWN_ERROR
      return false
    }
  }

  // 打开设置页面
  const openSettings = () => {
    if (platformInfo.isApp) {
      uni.openSetting({
        success: (res) => {
          console.log('打开设置页面成功', res)
        },
        fail: (err) => {
          console.error('打开设置页面失败', err)
        },
      })
    } else {
      console.warn('当前平台不支持打开设置页面')
    }
  }

  // 获取兼容性信息
  const compatInfo = computed(() => ({
    isH5: platformInfo.isH5,
    isApp: platformInfo.isApp,
    isSupported: isSupported.value,
    platform: platformInfo.platform,
    errorReason: errorReason.value,
  }))

  return {
    isSupported,
    errorReason,
    compatInfo,
    checkPermission,
    requestPermission,
    openSettings,
  }
}

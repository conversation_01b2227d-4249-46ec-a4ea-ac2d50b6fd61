<template>
  <view class="pages">
    <unicloud-db
      ref="udb"
      v-slot="{ data, loading, error, options }"
      loadtime="manual"
      :page-size="100"
      where="user_id == $cloudEnv_uid"
      orderby="create_date desc"
      collection="habit,habit-clocking"
    >
      <view v-if="error">{{ error.message }}</view>
      <view v-else>
        <view v-for="(item, index) in data" class="habit-box" @click="onDetail(item)">
          {{ item.title }}
          <view class="habit-info">
            <view class="record-day"> {{ item._id['habit-clocking'].length }} 次 </view>
            <view class="record-text"> 共计打卡 </view>
          </view>
        </view>
      </view>
    </unicloud-db>
    <navigator url="/pages/habit/edit" hover-class="navigator-hover">
      <view class="new-habit"> + 添加习惯 </view>
    </navigator>
  </view>
</template>

<script>
let cdbRef
import statusBar from '@/uni_modules/uni-nav-bar/components/uni-nav-bar/uni-status-bar'
import Gps from '@/uni_modules/json-gps/js_sdk/gps.js'
const gps = new Gps(),
  db = uniCloud.database()

export default {
  components: {
    statusBar,
  },
  data() {
    return {
      where: '"article_status" == 1',
      keyword: '',
      showRefresh: false,
      listHight: 0,
    }
  },
  computed: {
    inputPlaceholder(e) {
      if (uni.getStorageSync('CURRENT_LANG') == 'en') {
        return 'Please enter the search content'
      } else {
        return '请输入搜索内容'
      }
    },
    colList() {
      return [
        // db.collection('opendb-news-articles').where(this.where).field(
        // 	'avatar,title,last_modify_date,user_id').getTemp(),
        // db.collection('uni-id-users').field('_id,nickname').getTemp()
        db.collection('memos').field('_id, content').getTemp(),
      ]
    },
  },
  watch: {
    keyword(keyword, oldValue) {
      let where = '"article_status" == 1 '
      if (keyword) {
        this.where = where + `&& /${keyword}/.test(title)`
      } else {
        this.where = where
      }
    },
  },
  async onReady() {
    cdbRef = this.$refs.udb
    uni.startPullDownRefresh()
  },
  async onShow() {},
  methods: {
    onDetail(item) {
      uni.navigateTo({
        url: `/pages/habit/stats?id=${item._id._value}`,
      })
    },
    loadMore() {
      cdbRef.loadMore()
    },
  },
  onPullDownRefresh() {
    cdbRef.loadData(
      {
        clear: true,
      },
      () => {
        // 停止下拉刷新
        uni.stopPullDownRefresh()
      }
    )
  },
  // onReachBottom() {
  // 	this.loadMore()
  // }
}
</script>

<style scoped lang="scss">
.pages {
  padding: 10px;
}

.habit-box {
  display: flex;
  flex-direction: initial;
  align-items: center;
  margin-bottom: 10px;
  padding: 20px;
  border: 1px solid;
  border-radius: 5px;
  background-color: #7cca8d;

  .habit-info {
    margin-left: auto;

    .record-day {
      text-align: center;
      font-weight: bold;
    }

    .record-text {
      font-size: 12px;
    }
  }
}

.new-habit {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f4f4f0;
  border-radius: 4px;
  height: 50px;
  color: #8c8c88;
  font-size: 12px;
}
</style>

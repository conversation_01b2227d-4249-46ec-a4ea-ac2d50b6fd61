<template>
  <uni-popup ref="popup" @maskClick="closePop">
    <view v-if="isEdit" class="habit-content">
      <textarea
        v-model="curHabit.content"
        placeholder="有啥收获..."
        :show-confirm-bar="false"
        auto-focus
        class="textarea"
        @input="onSaveDraft"
      />
      <button class="add-btn" size="mini" type="primary" @click="addContent">提交</button>
    </view>
    <view v-else class="habit-card">
      <view class="habit-status">
        {{ curHabit.isClocking ? '已打卡' : '未打卡' }}
      </view>
      <view class="whitespace-pre-wrap" v-html="curHabit.content" />
      <view v-if="curHabit.isClocking" class="habit-sign" @click="onLog">写日志</view>
      <view v-else class="habit-sign" @click="onClocking">打卡</view>
    </view>
  </uni-popup>
</template>
<script setup>
import { defineProps, defineEmits, watch, ref } from 'vue'

const db = uniCloud.database()
const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  habit: {
    type: Object,
    default: {
      content: '',
      isClocking: '',
      clocking_id: '',
      habit_id: '',
      clocking_date: '',
    },
  },
})
const emits = defineEmits('update:visible', 'onUpdate')
const popup = ref()
const isEdit = ref(false)
const curHabit = ref({})
watch(
  () => props.visible,
  (newValue, oldValue) => {
    if (newValue) {
      // 打开
      popup.value.open()
      curHabit.value = { ...props.habit }
    } else {
      // 关闭
      popup.value.close()
      emits('onUpdate')
      isEdit.value = false
    }
  }
)
const closePop = () => {
  emits('update:visible', false)
  // popup.value.close()
}

const onSaveDraft = (e) => {
  console.log(e.detail.value)
  const { habit_id } = curHabit.value
  uni.setStorageSync(`draft:habit_${habit_id}`, e.detail.value)
}

const onLog = () => {
  isEdit.value = true
  const { habit_id } = curHabit.value
  const draft = uni.getStorageSync(`draft:habit_${habit_id}`)
  if (draft) {
    uni.showModal({
      title: '草稿',
      content: '你有之前尚未保存的草稿，是否恢复？',
      success(val) {
        if (val.confirm) {
          curHabit.value.content = draft
        } else if (val.cancel) {
          uni.removeStorageSync(`draft:habit_${habit_id}`)
        }
      },
    })
  }
}
// 添加日志
const addContent = () => {
  uni.showLoading({
    title: '提交中',
  })
  const { content, clocking_id, habit_id } = curHabit.value
  console.log(curHabit.value)
  db.collection('habit-clocking')
    .where(`_id == '${clocking_id}'`)
    .update({
      content,
    })
    .then(() => {
      uni.showToast({
        title: '提交成功',
      })
      emits('update:visible', false)
      uni.removeStorageSync(`draft:habit_${habit_id}`)
    })
    .catch(() => {
      uni.showToast({
        title: '提交失败',
      })
    })
    .finally(() => {
      uni.hideLoading()
    })
}

// 打卡
let isLoading = false
const onClocking = () => {
  const { habit_id, clocking_date } = curHabit.value
  const params = {
    habit_id,
  }
  // 补卡
  if (clocking_date) {
    params.clocking_date = clocking_date
  }
  if (isLoading) return
  uni.showLoading({
    title: '打卡中',
  })
  db.collection('habit-clocking')
    .add(params)
    .then(({ result }) => {
      isLoading = false
      uni.hideLoading()
      isEdit.value = true
      curHabit.value.clocking_id = result.id
    })
    .catch((err) => {})
}
</script>

<style lang="scss">
.habit-card {
  background-color: #fff;
  width: 250px;
  height: 250px;
  border-radius: 5px;
  padding: 10px;
  .habit-status {
    text-align: center;
    font-weight: bold;
    margin-bottom: 10px;
  }
  .habit-log {
    text-indent: 16px;
    font-size: 12px;
  }
  .habit-sign {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    background-color: $uni-color-success;
    color: #fff;
    width: 80px;
    font-size: 16px;
    border-radius: 15px;
    padding: 6px 4px;
  }
}
.habit-content {
  background-color: #fff;
  width: 250px;
  height: 250px;
  border-radius: 5px;
  padding: 10px;
  position: relative;
  .textarea {
    width: 100%;
    height: 100px;
  }
  .add-btn {
    position: absolute;
    bottom: 0;
    left: 50%;
    bottom: 10px;
    transform: translateX(-50%);
  }
}
</style>

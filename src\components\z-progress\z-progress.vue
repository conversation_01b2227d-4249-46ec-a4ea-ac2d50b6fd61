<template>
  <view class="z-progress">
    <view class="z-progress-bar" :style="{ width: `${percent}%`, backgroundColor: color }"></view>
    <text v-if="showInfo" class="z-progress-info">{{ percent }}%</text>
  </view>
</template>

<script>
export default {
  name: 'z-progress',
  props: {
    percent: {
      type: Number,
      default: 0,
    },
    strokeWidth: {
      type: Number,
      default: 6,
    },
    color: {
      type: String,
      default: '#2979ff',
    },
    showInfo: {
      type: Boolean,
      default: false,
    },
  },
}
</script>

<style>
.z-progress {
  position: relative;
  width: 100%;
  height: 6px;
  background-color: #ebeef5;
  border-radius: 100px;
  overflow: hidden;
}

.z-progress-bar {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  border-radius: 100px;
  transition: width 0.3s ease;
}

.z-progress-info {
  position: absolute;
  right: 0;
  top: -20px;
  font-size: 12px;
  color: #606266;
}
</style>

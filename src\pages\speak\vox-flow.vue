<template>
  <component :is="currentComponent" />
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import RetellStoryPage from './retell-story-page.vue'

const pageType = ref('')

const pageComponents = {
  'retell-story': RetellStoryPage,
}

const currentComponent = computed(() => {
  return pageComponents[pageType.value] || null
})

onLoad((options) => {
  if (options && options.type) {
    pageType.value = options.type
  }
})
</script>

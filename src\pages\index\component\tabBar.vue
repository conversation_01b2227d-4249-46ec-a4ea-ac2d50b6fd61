<template>
  <div class="ios-tab-bar">
    <div
      v-for="(tab, index) in tabs"
      :key="index"
      class="tab-item"
      :class="{ active: currentTab === index }"
      @click="handleTabClick(index, tab.path)"
      @touchstart="handleTouchStart"
      @touchend="handleTouchEnd"
    >
      <i :class="getIconClass(index)"></i>
      <span>{{ tab.title }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, computed, onMounted, onUnmounted } from 'vue'
import { router } from '@/utils/tools'

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
  tabMapping: {
    type: Array,
    required: true,
  },
  isSyncing: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

// 当前选中的 tab 索引
const currentTab = ref(props.modelValue)

// 监听 props 变化，避免无限循环
watch(
  () => props.modelValue,
  (newValue) => {
    // 只有当内部值与外部值不同时才更新，防止循环
    if (currentTab.value !== newValue) {
      currentTab.value = newValue
    }
  },
  { flush: 'post' }
)

// 计算处理后的标签数据
const tabs = computed(() => props.tabMapping || [])

// 根据索引获取对应的图标类名
const getIconClass = (index) => {
  const tab = tabs.value[index]
  if (!tab) {
    return 'fas fa-circle' // 提供一个默认图标
  }

  const iconClass = tab.icon || 'fas fa-circle'

  // 如果是设置 tab 且同步状态为 true，添加旋转类名
  if (tab.key === 'setting' && props.isSyncing) {
    return `${iconClass} icon-spinning`
  }

  return iconClass
}

// 用于触摸反馈的变量
let touchTimeout = null

// 触摸开始时的处理
const handleTouchStart = (event) => {
  const target = event.currentTarget
  target.classList.add('touching')
}

// 触摸结束时的处理
const handleTouchEnd = (event) => {
  const target = event.currentTarget

  // 延迟移除 touching 类，提供更好的视觉反馈
  clearTimeout(touchTimeout)
  touchTimeout = setTimeout(() => {
    target.classList.remove('touching')
  }, 100)
}

// 处理 tab 点击事件
const handleTabClick = (index, path) => {
  if (index !== currentTab.value) {
    // 添加点击音效反馈（如果平台支持）
    try {
      if (uni.vibrateShort) {
        uni.vibrateShort({ type: 'light' }) // 轻微震动反馈
      }
    } catch (e) {
      // 忽略不支持的平台错误
    }

    // 重要：先触发 change 事件，让父组件决定方向，然后再更新本地值
    emit('change', index, path)

    // 更新本地状态 (等父组件更新后，watch 会自动同步)
    // currentTab.value = index
    // emit('update:modelValue', index)
  }
}

// 主题监听处理
const applyTheme = () => {
  // 获取当前主题
  // const currentTheme = uni.getStorageSync('theme') || 'default'
  // const tabBar = document.querySelector('.ios-tab-bar')
  // if (tabBar) {
  //   tabBar.setAttribute('data-theme', currentTheme)
  // }
}

// 主题变更监听
const themeChangeHandler = () => {
  applyTheme()
}

onMounted(() => {
  // 应用初始主题
  applyTheme()

  // 监听主题变更
  uni.$on('themeChange', themeChangeHandler)
})

onUnmounted(() => {
  // 移除监听器
  uni.$off('themeChange', themeChangeHandler)
})
</script>

<style scoped>
/* 底部导航栏样式 - 现代感风格 */
.ios-tab-bar {
  display: flex;
  width: 100%;
  height: 80px;
  background: var(--color-bg-card, #ffffff);
  justify-content: space-evenly;
  align-items: center;
  padding-bottom: 20px;
  border-top: none;
  box-shadow: 0 -2px 10px var(--color-shadow, rgba(94, 106, 210, 0.08));
  border-radius: 24px 24px 0 0;
  will-change: transform; /* 启用硬件加速 */
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--color-text-secondary, #868aaf);
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  padding: 10px 12px;
  border-radius: 12px;
  width: 22%;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent; /* 移除移动设备上的点击高亮 */
}

.tab-item i {
  font-size: 22px;
  margin-bottom: 6px;
  transition: all 0.3s ease;
}

.tab-item span {
  line-height: 1.2;
}

.tab-item.active {
  color: var(--color-primary, #5e6ad2);
  background: var(--color-bg-active, #f5f7ff);
  transform: translateY(-4px);
  box-shadow: 0 -4px 10px var(--color-shadow-active, rgba(94, 106, 210, 0.2));
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  width: 30px;
  height: 4px;
  background: var(--color-primary, #5e6ad2);
  border-radius: 10px;
  opacity: 0.8;
}

.tab-item.active i {
  color: var(--color-primary, #5e6ad2);
  transform: scale(1.15);
}

/* 触摸效果 */
.tab-item.touching {
  transform: scale(0.95);
}

/* 针对不同设备进行触摸优化 */
@media (pointer: coarse) {
  .tab-item {
    padding: 8px 0; /* 在触摸设备上增加点击区域 */
  }
}

/* 优化小屏幕设备显示 */
@media (max-width: 320px) {
  .tab-item span {
    font-size: 10px;
  }

  .tab-item i {
    font-size: 20px;
  }
}

/* 图标旋转动画 */
@keyframes spinning {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.icon-spinning {
  animation: spinning 1.5s linear infinite;
}
</style>

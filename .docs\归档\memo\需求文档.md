# 日记功能增强需求

## 背景
当前应用已有基础日记功能，但用户体验和功能深度有待提升。用户希望日记功能更灵活、更智能，能够适应随时记录想法和回顾的场景。通过 AI 辅助，可以提供更丰富的日记体验，帮助用户更好地记录和整理日常思考。

### 市场分析
- 竞品调研显示，市场上大多数日记应用仍停留在传统单一文本输入模式
- 用户调研数据表明，80% 的用户希望有更便捷的记录方式，65% 期待智能化辅助功能
- AI 辅助写作已成为新趋势，但在日记领域应用尚未普及

### 用户痛点
- 缺乏时间一次性完成完整日记记录
- 日记内容单一，缺乏多角度思考引导
- 日常记录碎片化，难以形成系统性思考
- 回顾价值低，缺乏内容关联和深度分析

## 需求

### 功能需求
1. **多次输入日记**
   - 在同一天内可以多次输入日记内容，而不限于一次性填写
   - 每次新输入时，AI 提供简短回应或鼓励
   - 支持输入类型标记（如"思考"、"记事"、"感悟"等）
   - 提供快捷输入入口，支持语音、文字、图片多种方式
   - 允许用户设置提醒，定时提示记录日记
   
2. **问答式日记与总结**
   - 提供一个"总结"按钮，用户可主动触发当日日记的总结流程。
   - 点击总结按钮后，将启动问答式日记模式，由 AI 根据用户当日记录或常见反思点生成引导性问题。
   - 用户回答 AI 提出的问题后，AI 会整合当天的所有输入内容和问答记录，生成一篇结构化的日记总结。
   - 问题类型可包括：今日收获、情绪变化、未完成事项等
   - 支持自定义问题模板，用户可创建个人专属的日记引导
   - 提供跳过问题选项，确保灵活性
   - 智能识别用户当日任务和习惯记录，生成相关问题

3. **日记回顾与分析**
   - 提供周期性日记摘要（周报、月报形式）
   - AI 分析情绪变化趋势，生成情绪曲线
   - 识别用户关注的关键主题，提供主题聚合视图
   - 支持时间线回顾模式，方便查看历史记录
   - 提供标签系统，自动和手动标记日记内容

### 非功能需求
1. **用户体验**
   - 界面简洁直观，便于随时记录
   - 多次输入的过渡自然流畅
   - 问答互动感强，增加用户参与感
   - 操作步骤不超过 3 步，确保快速记录
   - 提供细致的动效反馈，增强互动感

## 技术方案

### 实现思路
1. **数据结构调整**
   - 所有日记相关的数据，包括多次输入的条目、问答记录以及最终的 AI 总结，都将以 JSON 格式序列化后，统一存储在现有 `Memo` 表的 `content` 字段中。
   - 这种方法避免了修改数据库 schema，同时保留了完整的交互记录。
   - `content` 字段内的 JSON 对象结构设计如下：
     ```typescript
     // Memo.content 字段存储的 JSON 对象结构
     interface DiaryContent {
       entries: Array<{
         content: string;        // 用户输入内容
         type: string;           // 内容类型（思考/记事/感悟）
         timestamp: number;      // 时间戳
         ai_response?: string;    // AI 的即时回应
         media_attachments?: string[]; // 媒体附件路径
       }>;
       questions_answers?: Array<{
         question: string;       // AI 提问
         answer: string;         // 用户回答
         timestamp: number;      // 回答时间
       }>;
       summary?: string;         // AI 生成的日记摘要
     }
     ```
   - 这要求在应用层处理好 JSON 的序列化和反序列化，并管理好 `content` 字段的向后兼容问题。

2. **AI 交互设计**
   - 实现两种 AI 交互模式：即时回应和问答引导
   - 即时回应：用户输入后，调用 AI 接口生成简短反馈
   - 问答引导：基于固定问题库 + 动态生成，提供个性化问题
   - AI 提示词设计策略：
     - 即时回应：保持简短（20-30 字），积极鼓励，提供延展思考
     - 问答引导：基于用户历史输入和当日活动，生成 3-5 个有针对性问题
     - 摘要生成：保留用户原始表达，整合为连贯叙事，添加简要点评
   - 本地问题库设计：预设 100+ 引导性问题，分类存储，智能筛选

3. **界面设计**
   - 日记页面改为时间轴形式，显示当天所有输入
   - 新增快捷输入按钮，方便随时记录
   - 问答模式采用对话气泡形式，增强交互感
   - 页面布局：
     - 主页面：时间轴 + 快捷输入按钮 + 情绪标签
     - 输入页面：富文本编辑器 + 语音输入 + 类型选择
     - 问答页面：对话式界面，上下文显示问答历史
     - 回顾页面：日历视图 + 标签筛选 + 情绪曲线图表

4. **交互流程设计**
   - 快速记录流程：
     1. 点击主页面快捷按钮
     2. 选择输入类型（文字/语音/图片）
     3. 完成输入后自动保存，显示 AI 反馈
     4. 可选择继续输入或返回主页
   - 问答式日记流程：
     1. 用户点击"总结"按钮后启动流程
     2. 一次展示一个问题，用户回答后显示下一问题
     3. 回答完所有问题或用户选择结束后，AI 整合当天所有日记条目与问答内容，生成总结
     4. 用户可编辑最终生成的摘要内容

### 架构设计
```mermaid
graph TD
    A[用户输入] --> B[日记组件]
    B --> C1[分条记录存储]
    C1 --> D[本地数据库]
    B --> C2[AI即时回应]
    E[用户触发总结] --> F[AI问题生成]
    F --> G[用户回答]
    G --> H[AI整合日记]
    H --> I[完整日记存储]
    I --> D
    D --> J[云端同步服务]
    J --> K[远程数据库]
    M[用户] --> E
    M --> N[回顾分析页面]
    N --> O[数据分析服务]
    O --> P[可视化组件]
    P --> M
```

### 技术栈与约束
- 前端框架：uni-app
- 日记编辑器：现有 z-editor 组件进行扩展
- AI 接口：接入现有的 AI 服务
- 存储：使用本地 SQLite + 云端同步机制
- 组件规划：
  - 全局组件：
    - `z-diary-editor`：增强版日记编辑器，支持多媒体输入
    - `z-question-answer`：问答式交互组件
    - `z-emotion-chart`：情绪变化图表组件
  - 局部组件：
    - `l-diary-timeline`：日记时间轴组件
    - `l-quick-input`：快速输入浮动按钮组件
    - `l-diary-card`：日记条目卡片组件
- API 接口设计：
  - `/api/diary/addEntry`：添加单条日记记录
  - `/api/diary/generateQuestions`：生成问答式引导问题
  - `/api/diary/generateSummary`：生成日记摘要
  - `/api/diary/analyze`：分析日记内容，提取情绪和标签

## 实施计划

### 阶段规划
1. **第一阶段（4 周）**
   - 数据结构调整和基础架构搭建
   - 多次输入功能实现
   - 简单 AI 回应集成

2. **第二阶段（3 周）**
   - 问答式日记功能开发
   - AI 问题生成与摘要功能实现
   - 用户界面优化

3. **第三阶段（2 周）**
   - 回顾分析功能开发
   - 数据可视化实现
   - 性能优化和测试

4. **第四阶段（3 周）**
   - 安全和隐私保护措施
   - 用户反馈收集和功能迭代

### 验收标准
1. **功能验收**
   - 所有列出的功能需求均已实现并通过测试
   - UI/UX符合设计规范和原型
   - 各功能模块间无冲突

2. **性能验收**
   - 页面加载时间不超过 1.5 秒
   - AI 响应时间不超过 2 秒
   - 本地存储与云端同步无明显延迟
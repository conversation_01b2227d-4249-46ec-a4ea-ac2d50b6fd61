# 任务：情绪图表UI开发

- **所属功能模块**: `diary-review`
- **任务ID**: `task-diary-review-01`
- **优先级**: 低

## 任务描述
开发一个全局的 `z-emotion-chart` 组件，用于将一段时间内的情绪变化趋势进行可视化展示。

## 技术实现详情
1.  **`z-emotion-chart` 组件**:
    - **技术选型**: 基于项目现有的图表库（如 `qiun-data-charts`）或引入轻量级的图表库。
    - **UI**:
        - 采用折线图的形式。
        - X轴为日期（如最近7天或30天）。
        - Y轴为情绪分数（需要一个量化标准，如 1-5分，或-2到+2）。
    - **Props**:
        - 接收一个数据数组，格式为 `[{ date: 'YYYY-MM-DD', score: 3 }, ...]`。
    - **开发方式**:
        - 初始阶段使用模拟数据进行图表开发和样式调试。

## 验收标准
- `z-emotion-chart` 组件能根据传入的模拟数据正确渲染出情绪变化的折线图。
- 图表的坐标轴、数据点、提示信息（tooltip）等都显示正常。
- 组件样式与应用整体风格保持一致。

## 依赖关系
- 无。可独立开发。

## 状态追踪
- 未开始 
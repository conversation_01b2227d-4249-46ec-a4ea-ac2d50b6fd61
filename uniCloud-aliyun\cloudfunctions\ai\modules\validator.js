/**
 * 参数验证模块
 *
 * 主要功能：
 * 1. 负责验证工具调用参数的类型和必需性
 * 2. 提供参数类型转换和格式化功能
 * 3. 确保工具调用参数的正确性和完整性
 * 4. 支持多种数据类型的验证和转换
 *
 * 核心特性：
 * - 类型验证：支持字符串、数字、布尔值、对象、数组等类型验证
 * - 必需检查：检查必需参数是否存在且不为空
 * - 类型转换：自动进行合理的类型转换（如字符串转数字）
 * - 错误处理：提供清晰的错误信息和异常处理
 * - 扩展性：支持新增数据类型和验证规则
 *
 * 验证策略：
 * - 基于工具注册表中的参数定义进行验证
 * - 采用抛出异常的方式处理验证失败
 * - 支持参数的默认值和可选参数处理
 * - 允许额外参数但会记录警告信息
 *
 * 技术架构：
 * - 静态方法设计：便于全局调用，无需实例化
 * - 异常驱动：使用异常机制处理验证失败情况
 * - 类型转换：智能的类型转换和格式化
 * - 配置驱动：基于工具注册表的参数配置
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

const { TOOL_REGISTRY } = require('./config')

/**
 * 参数验证器
 * 负责验证和转换工具调用参数
 *
 * 核心职责：
 * - 验证参数的存在性和类型正确性
 * - 进行必要的类型转换和格式化
 * - 处理必需参数和可选参数
 * - 提供详细的错误信息和异常处理
 *
 * 验证流程：
 * 1. 检查工具是否存在于注册表中
 * 2. 验证必需参数的存在性
 * 3. 对每个参数进行类型验证和转换
 * 4. 返回验证和转换后的参数对象
 *
 * 设计原则：
 * - 严格验证：确保参数的完全正确性
 * - 智能转换：进行合理的类型转换
 * - 快速失败：遇到验证错误立即抛出异常
 * - 友好提示：提供清晰的错误信息
 */
class ParameterValidator {
  static validate(toolName, parameters) {
    const toolConfig = TOOL_REGISTRY[toolName]
    if (!toolConfig) {
      throw new Error(`未知的工具：${toolName}`)
    }

    const validated = {}
    const toolParams = toolConfig.parameters || {}

    // 检查必需参数
    for (const [paramName, paramConfig] of Object.entries(toolParams)) {
      if (paramConfig.required && (parameters[paramName] === undefined || parameters[paramName] === null)) {
        throw new Error(`缺少必需参数：${paramName}`)
      }
    }

    // 验证参数类型
    for (const [paramName, value] of Object.entries(parameters)) {
      if (value !== undefined && value !== null) {
        const paramConfig = toolParams[paramName]
        if (paramConfig) {
          validated[paramName] = this.validateParameterType(paramName, value, paramConfig.type)
        } else {
          // 允许额外参数，但记录警告
          console.warn(`工具 ${toolName} 收到未定义的参数：${paramName}`)
          validated[paramName] = value
        }
      }
    }

    return validated
  }

  static validateParameterType(paramName, value, expectedType) {
    switch (expectedType) {
      case 'string':
        return String(value)
      case 'number':
        const num = Number(value)
        if (isNaN(num)) {
          throw new Error(`参数 ${paramName} 必须是数字`)
        }
        return num
      case 'boolean':
        if (typeof value === 'boolean') return value
        if (typeof value === 'string') {
          return value.toLowerCase() === 'true'
        }
        return Boolean(value)
      case 'object':
        if (typeof value !== 'object' || value === null) {
          throw new Error(`参数 ${paramName} 必须是对象`)
        }
        return value
      default:
        return value
    }
  }
}

module.exports = {
  ParameterValidator,
}

# leven-js-tools
## 介绍

插件为uniapp前端js工具包，工具包包含了众多模块，数组、颜色、日期、加解密、函数、数字、对象、请求、字符串、验证等模块，可以让您的开发得心应手，不需要为了一个功能需要花费大量的时间从网上再去搜索，本插件已集成了大部分常用的功能，本插件会持续更新。

## 使用文档

您也可以参考以下链接的使用文档 

请参考：[使用文档](https://z7poo9xpe4.k.topthink.com/@5q2g35mreg)

## 全局引入


在main.js文件中添加以下代码


```
// 加载js工具插件
import jsTools from "uni_modules/leven-js-tools/index.js"
Vue.use(jsTools);
```


然后就可以直接使用了，不需要在页面中单独引入插件

## 使用方式


每一种插件都是以uni.$lv开头，具体的功能会在单独的章节中讲解
1.数组


```
uni.$lv.array
```


2.颜色


```
uni.$lv.color
```


3.日期


```
uni.$lv.date
```


4.加解密


```
uni.$lv.encrypt
```


5.函数


```
uni.$lv.func
```


6.数字


```
uni.$lv.number
```


7.对象


```
uni.$lv.object
```


8.请求


```
uni.$lv.request
```


9.字符串


```
uni.$lv.string
```


10.定时器


```
uni.$lv.timer
```


11.验证器


```
uni.$lv.validate
```


> 如有任何疑问或建议可在下方评论区提出，谢谢！
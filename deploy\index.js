// pnpm -D add node-ssh dayjs minimist ora@5.1.0 chalk@4.1.0 cross-env axios archiver inquirer@7.3.3 minimist
// "deploy": "cross-env node ./deploy/index.js",
const { exec } = require('child_process')
// const open = require('open')
const fs = require('fs')
const path = require('path')
const axios = require('axios')
// const archiver = require('archiver')
// const { NodeSSH } = require('node-ssh')
const rawArgv = process.argv.slice(2)
const { platform, model, env, quick } = require('minimist')(rawArgv)
const inquirer = require('inquirer')
const { uploadFile, removeJsComments, addZero } = require('./tools')
const dayjs = require('dayjs')
const conf = require('./conf')
const { log, succeed, info, error, gray, loading } = require('./log')
const iconv = require('iconv-lite')
let currentTime = ''
let newVersionCode = ''
let uploadFileName = ''
let uploadFilePath = ''
// 任务列表
const taskList = []
const isDev = env === 'dev' // 开发环境

const deleteDir = (path) => {
  var files = []
  if (fs.existsSync(path)) {
    files = fs.readdirSync(path)
    files.forEach(function (file, index) {
      var curPath = path + '/' + file
      if (fs.statSync(curPath).isDirectory()) {
        // recurse
        deleteDir(curPath)
      } else {
        // delete file
        fs.unlinkSync(curPath)
      }
    })
    fs.rmdirSync(path)
  }
}

// 封装 exec
const execPromise = (cmd, timeout = 10000) => {
  cmd = cmd.replace(/[\r\n]+/g, ' ')
  const execPromise = new Promise((resolve, reject) => {
    exec(cmd, (error, stdout, stderr) => {
      if (error) reject(error)
      else if (stdout.indexOf('-1:cloud') !== -1) reject(-1)
      else resolve(stdout)
    })
  })

  const timeoutPromise = new Promise((resolve, reject) => {
    setTimeout(() => {
      reject(new Error('error:timeout'))
    }, timeout)
  })

  return Promise.race([execPromise, timeoutPromise])
}
// 封装统计耗时的函数
const calcElapsedTime = (taskName) => {
  const spinner = loading(`${taskName}\n`)
  const startTime = new Date().getTime()
  let time = '00:00'
  const intervalId = setInterval(() => {
    const currentTime = new Date().getTime()
    time = dayjs(currentTime - startTime).format('mm:ss')
    spinner.text = `[耗时 ${time}] ${taskName}\n`
  }, 1000)
  return () => {
    clearInterval(intervalId)
    spinner.stop()
    return time !== '00:00' ? time : ''
  }
}

// 打开 HbuilderX
const openHbuilderX = async (config, index) => {
  try {
    log(`(${index}) 打开 HbuilderX`)
    // 统计耗时
    const stopSpinner = calcElapsedTime('正在打开 HbuilderX')
    await execPromise(config.script_openHbuilder)
    const time = stopSpinner()
    succeed(`${time} 打开 HbuilderX 成功`)

    const stopSpinner2 = calcElapsedTime('HbuilderX 初始化中...')
    await checkSign(config)
    await new Promise((resolve) => {
      setTimeout(() => {
        const s = stopSpinner2()
        succeed(`${s} HbuilderX 初始化成功`)
        resolve()
      }, 3000)
    })
  } catch (e) {
    error('打开 HbuilderX 失败')
    error(e)
    process.exit(1)
  }
}
// 关闭 HbuilderX
const closeHbuilderX = async (config, index) => {
  try {
    log(`(${index}) 关闭 HbuilderX`)
    // 统计耗时
    const stopSpinner = calcElapsedTime('正在关闭 HbuilderX')
    await execPromise(config.script_closeHbuilder)
    const time = stopSpinner()
    succeed(`${time} 关闭 HbuilderX 成功`)
    await new Promise((resolve) => {
      setTimeout(() => {
        resolve()
      }, 1000)
    })
  } catch (e) {
    error('关闭 HbuilderX 失败')
    error(e)
    process.exit(1)
  }
}
// 运行开发环境
const runDev = async (config, index) => {
  try {
    log(`(${index}) 运行开发环境`)
    // 统计耗时
    const stopSpinner = calcElapsedTime('正在运行开发环境')
    await execPromise(config.script_runDev, 3 * 60000)
    const time = stopSpinner()
    succeed(`${time} 运行开发环境：${env}`)
  } catch (e) {
    error('运行开发环境失败')
    error(e)
    process.exit(1)
  }
}
const checkSign = async (config) => {
  const userInfo = await execPromise('cli user info')
  if(!userInfo){
    await new Promise((resolve) => setTimeout(resolve, 1000))
    await checkSign(config)
  }
}
const executeCommandWithRetry = async (params, config) => {
  try {
    await execPromise(params.cmd)
  } catch (error) {
    if (error === -1) {
      params.count++
      params.spinner.text = `[第${params.count}次尝试] 服务端环境切换中...`
      await new Promise((resolve) => setTimeout(resolve, 1000))
      await executeCommandWithRetry(params, config)
    }
  }
}
// 检查当前服务端环境
const checkServer = async (script_listDb) => {
  try {
    const stdout = await execPromise(script_listDb)
    if (stdout.indexOf('zcj-prod') !== -1) return 'prod'
    if (stdout.indexOf('zcj-dev') !== -1) return 'dev'
  } catch (e) {
    error(e)
    process.exit(1)
  }
}
// 切换服务端环境
const cutServer = async (config, index) => {
  try {
    log(`(${index}) 切换服务端环境`)
    // 统计耗时
    const params = {
      cmd: config.cutServer,
      spinner: loading(`服务端环境切换中...\n`),
      count: 0,
    }
    await executeCommandWithRetry(params, config)
    await new Promise((resolve) => setTimeout(resolve, 1000))
    await execPromise(params.cmd)
    params.spinner.stop()
    const e = await checkServer(config.script_listDb)
    if (e !== env) {
      error(`环境检查失败，当前服务端环境：${e}，期望环境：${env}`)
      process.exit(1)
    } else {
      succeed(`环境检查通过，本地环境：${env}，服务端环境：${e}`)
    }

    // 修改 env 文件
    const sEnvPath = path.join(config.envPath, `env.${env}.js`)
    const envPath = path.join(config.envPath, 'env.js')
    const envmPath = path.join(config.envPath, 'env.mjs')
    // fs.unlinkSync(envPath)
    // fs.unlinkSync(envmPath)
    fs.copyFileSync(sEnvPath, envPath)
    fs.copyFileSync(sEnvPath, envmPath)
    succeed(`命令执行 ${params.count} 次，环境切换成功：${env}`)
  } catch (e) {
    error('切换服务端环境失败')
    error(e)
    process.exit(1)
  }
}

// 修改 appid
const cutAppid = async (config, index) => {
  try {
    log(`(${index}) 修改 manifestFile appid`)
    // 统计耗时
    const stopSpinner = calcElapsedTime('正在修改 manifestFile appid')
    // 获取 manifest.json 路径
    const manifestStr = fs.readFileSync(config.manifestFile, 'UTF-8').toString()
    const manifestData = JSON.parse(manifestStr)
    manifestData.appid = config.APPID
    fs.writeFileSync(config.manifestFile, JSON.stringify(manifestData, null, '\t'))
    newVersionCode = manifestData.versionCode
    const time = stopSpinner()
    succeed(`已修改 manifestFile appid: ${config.APPID}`)
  } catch (e) {
    error('修改失败')
    error(e)
    process.exit(1)
  }
}
// 修改版本号和应用名
const updateVersionCode = async (config) => {
  const { manifestFile, appName } = config
  try {
    // 获取 manifest.json 路径
    const manifestStr = fs.readFileSync(manifestFile, 'UTF-8').toString()
    const manifestData = JSON.parse(manifestStr)
    // 修改版本号
    manifestData.versionCode++
    manifestData.versionName = manifestData.versionCode.toString().split('').join('.')
    // 修改应用名
    manifestData.name = appName

    fs.writeFileSync(manifestFile, JSON.stringify(manifestData, null, '\t'))
    newVersionCode = manifestData.versionCode
  } catch (e) {
    console.error(e)
  }
}
// 构建 Wgt
const buildWgt = async (config, index) => {
  const { wgtPath, wgtName, script_buildWgt } = config
  // 清除 wgt 目录
  deleteDir(wgtPath)
  try {
    // 修改版本号
    updateVersionCode(config)
    log(`(${index}) 构建 wgt 包`)
    // 统计耗时
    const stopSpinner = calcElapsedTime('正在构建 wgt 包')
    await execPromise(script_buildWgt, 5 * 60 * 1000)
    // 判断 wgt 包是否构建成功
    if (!fs.existsSync(path.join(wgtPath, wgtName))) {
      throw new Error('wgt 包构建失败')
    }
    const time = stopSpinner()
    succeed(`${time} wgt 包构建成功`)
    const [name, suffix] = wgtName.split('.')
    const nowTime = new Date()
    const year = nowTime.getFullYear()
    const month = nowTime.getMonth() + 1
    const day = nowTime.getDate()
    const hours = nowTime.getHours()
    const minutes = nowTime.getMinutes()
    const seconds = nowTime.getSeconds()
    uploadFileName = `${name}__${year}-${month}-${day}-${hours}-${minutes}-${seconds}__v${newVersionCode}.${suffix}`
    uploadFilePath = path.join(wgtPath, wgtName)
  } catch (e) {
    error('构建 Wgt 失败')
    error(e)
    process.exit(1)
  }
}
// 构建 apk
const buildApk = async (config, index) => {
  try {
    // 添加安卓打包证书参数
    for (let key in config.androidConfig) {
      config.script_buildApk += ` ${key} ${config.androidConfig[key]}`
    }
    // console.log(config.script_buildApk)
    // 清除 apk 目录
    deleteDir(config.apkPath)
    // 修改版本号
    updateVersionCode(config)
    log(`(${index}) 构建 apk 包`)
    // 统计耗时
    const stopSpinner = calcElapsedTime('正在构建 apk 包')
    await execPromise(config.script_buildApk, 20 * 60 * 1000)
    const time = stopSpinner()
    succeed(`${time} apk 包构建成功`)
    const apkName = fs.readdirSync(config.apkPath)[0]
    uploadFilePath = path.join(config.apkPath, apkName)
    const [name, suffix] = apkName.split('.')
    uploadFileName = `${name}__v${newVersionCode}.${suffix}`
  } catch (e) {
    error('构建 apk 失败')
    error(e)
    process.exit(1)
  }
}
// 发布 H5
const updateH5 = async (config, index) => {
  try {
    log(`(${index}) 发布 H5`)
    let stopSpinner = '',
      time = ''
    // 编译
    stopSpinner = calcElapsedTime('正在编译 H5')
    await execPromise(config.script_buildH5[0], 3 * 60 * 1000)
    time = stopSpinner()
    succeed(`${time} 编译完毕`)
    // 部署
    stopSpinner = calcElapsedTime('正在部署 H5')
    await execPromise(config.script_buildH5[1], 3 * 60 * 1000)
    time = stopSpinner()
    succeed(`${time} 部署完毕`)
    import('open').then((open) => {
      // 打开网站
      ;(async () => {
        await open.default(`https://unicloud.dcloud.net.cn/pages/web-host/web-host?pageid=${config.spaceId}`)
      })()
    })
  } catch (e) {
    error('发布 H5 失败')
    error(e)
    process.exit(1)
  }
}

// 上传新版本
const updateVersion = async (config, index) => {
  try {
    log(`(${index}) 上传新版本`)
    const env = await import(`../src/config/env.mjs`)
    const deployTask = platform

    const location = await uploadFile(uploadFilePath, uploadFileName)
    const versionName = newVersionCode.toString().split('').join('.')
    const params = {
      appid: config.APPID,
      title: '版本' + versionName,
      contents: '发布时间：' + dayjs().format('MM-DD HH:mm:ss'),
      type: deployTask === 'app:apk' ? 'native_app' : 'wgt',
      version: versionName,
      url: `https://${location}`,
      is_mandatory: true, // 是否强制更新
    }
    const { data } = await axios.post(`${env.default.baseHost}/app/update`, params)
    if (data) {
      info(`版本号：${versionName}`)
    }
  } catch (e) {
    error('上传新版本失败')
    error(e)
    process.exit(1)
  }
}

// 输出打包信息
const outputBuildInfo = () => {
  log('\n')
  succeed('项目部署成功！')
  info(`部署环境 ${isDev ? '开发环境' : '生产环境'}`)
  info(`部署耗时 ${dayjs(new Date().getTime() - currentTime).format('mm:ss')}`)
  info(`部署时间 ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`)
}

// 命令行交互式选择部署任务
const selectDeployTask = async () => {
  const answers = await inquirer.prompt([
    {
      type: 'list',
      name: 'deployTask',
      message: '请选择部署任务',
      choices: [
        {
          name: '发布 wgt',
          value: 'updateWgt',
        },
        {
          name: '发布 apk',
          value: 'updateApk',
        },
      ],
    },
  ])
  return answers.deployTask
}

// 创建任务列表
const createTaskList = async (config) => {
  const deployTask = platform
  const manifestStr = fs.readFileSync(config.manifestFile, 'UTF-8').toString()
  const manifestData = JSON.parse(manifestStr)

  // if (isDev && model !== 'dev' && manifestData.appid === '__UNI__41B9DE2') {
  //   // 开发模式下，打包 wgt 不需要重启 hbuilderx
  //   const userInfo = await execPromise('cli user info')
  //   if (!userInfo) {
  //     error('请手动启动 hbuilderX')
  //     return
  //   }
  // } else {
  //   if (quick !== 'direct') {
  //     taskList.push(closeHbuilderX)
  //     taskList.push(openHbuilderX)
  //     // 切换服务端环境
  //     taskList.push(cutAppid)
  //     taskList.push(cutServer)
  //   }else{

  //   }
  // }
  const userInfo = await execPromise('cli user info')
  if (!userInfo) taskList.push(openHbuilderX)

  // 本地运行
  if (model === 'dev') {
    taskList.push(runDev)
    return
  }

  // 发布全平台
  if (deployTask === 'all') {
    taskList.push(updateH5)
    taskList.push(buildWgt)
    taskList.push(updateVersion)
    taskList.push(outputBuildInfo)
  }
  // 发布 wgt
  if (deployTask === 'app:wgt') {
    taskList.push(buildWgt)
    taskList.push(updateVersion)
    taskList.push(outputBuildInfo)
  }
  // 发布 apk
  if (deployTask === 'app:apk') {
    taskList.push(buildApk)
    taskList.push(updateVersion)
    taskList.push(outputBuildInfo)
  }
  // 发布 H5
  if (deployTask === 'H5') {
    taskList.push(updateH5)
    taskList.push(outputBuildInfo)
  }
}
// 执行任务列表
const executeTaskList = async (config) => {
  currentTime = new Date().getTime()
  for (const [index, execute] of new Map(taskList.map((execute, index) => [index, execute])))
    await execute(config, index + 1)

  process.exit(0)
}

;(async () => {
  try {
    let c = {}
    const { dev, prod, ...config } = conf
    if (isDev) {
      // 切换开发环境参数
      c = { ...config, ...dev }
    } else {
      // 切换生产环境参数
      c = { ...config, ...prod }
    }
    await createTaskList(c)
    await executeTaskList(c)
  } catch (e) {
    error(e)
    process.exit(1)
  }
})()

import { dataConfig, tableSchema } from '../dataSchema'
import { syncToServer } from '../syncServer'

const dbName = 'database'

// #ifdef APP-PLUS
import { AppDB } from './app'
const appSql = new AppDB(dataConfig, tableSchema)
// #endif

// #ifdef H5
import webDB from './h5'
const dexie = new webDB(dataConfig, tableSchema)
// #endif
const operatorList: { [key: string]: (value: string | number, item: string) => boolean } = {
  '==': (value: string | number, item: string) => item === value,
  '!=': (value: string | number, item: string) => item !== value,
  '<=': (value: string | number, item: string) => item <= value, // TODO 如果不放在<前面，会被<拦截，导致<=无法使用
  '>=': (value: string | number, item: string) => item >= value,
  '>': (value: string | number, item: string) => item > value,
  '<': (value: string | number, item: string) => item < value,
}

class DB {
  listenrs
  constructor() {
    this.listenrs = {}
  }

  table(tableName: string) {
    return new TableOperation(tableName)
  }

  // 查询数据
  async query(table, params) {
    // #ifdef APP-PLUS
    return await this.queryByApp(table, params)
    // #endif

    // #ifdef H5
    return await this.queryByH5(table, params)
    // #endif
  }

  // #ifdef APP-PLUS
  async queryByApp(table: string, params = '') {
    try {
      const {
        id,
        orderBy,
        query,
        filterDeleted = true,
      } = typeof params === 'string' ? { id: params, query: params } : params
      // // 根据 _id 查询
      if (id && isUUID(id)) {
        const list = await appSql.selectSQL(`select * from ${table} where _id = '${id}' order by createTime desc`)

        if (list.length > 0) {
          return list[0]
        } else {
          throw new Error('没有找到该条数据')
        }
      }

      // 过滤已删除数据
      let queryStr = query
      if (filterDeleted) {
        if (query) {
          queryStr += " && deleteTime == ''"
        } else {
          queryStr = "deleteTime == ''"
        }
      }

      queryStr = queryStr
        .split('||')
        .map((item) => `(${item})`)
        .join(' or ')
      queryStr = queryStr.replace(/==/g, '=')
      queryStr = queryStr.replace(/&&/g, ' and ')
      if (!queryStr) queryStr = '1=1' // 如果没有查询条件则默认查询全部
      const res = await appSql.selectSQL(
        `select * from ${table} where ${queryStr} order by ${orderBy || 'createTime'} desc`
      )
      return res
    } catch (error) {
      uni.showToast({
        title: '查询出错',
        icon: 'error',
      })
      // uni.showModal({
      //   content: JSON.stringify(error),
      // })
      console.error(error)
      throw new Error('查询失败')
    }
  }
  // #endif

  // #ifdef H5
  async queryByH5(table: string, params = '') {
    try {
      const {
        orderBy,
        query,
        id,
        filterDeleted = true,
      } = typeof params === 'string' ? { id: params, query: params } : params

      // 根据 _id 查询
      if (id && isUUID(id)) {
        return await dexie.table(table).get(id)
      }

      // 过滤已删除数据
      let queryStr = query
      if (filterDeleted) {
        if (query) {
          queryStr += ' && deleteTime == ""'
        } else {
          queryStr = 'deleteTime == ""'
        }
      }

      const _table: any = dexie.table(table).orderBy(orderBy || 'createTime')
      const queryArr = queryStr.split('||').map((item) => item.split('&&'))

      return await _table
        .filter((item) => {
          // 每个条件都是 || 关系
          return queryArr.some((query) => {
            // 每个条件都是 && 关系
            return query.every((queryItem) => {
              for (const operator in operatorList) {
                if (queryItem.includes(operator)) {
                  const [key, value] = queryItem.split(operator)
                  const method = operatorList[operator]
                  let newValue: string | number = value.trim()

                  // 判断是否是字符串
                  if (/^['"].*['"]$/.test(newValue)) {
                    // 如果是字符串则去掉首尾的引号
                    newValue = newValue.replace(/(^['"]|['"]$)/g, '')
                  } else {
                    // 如果不是字符串则转换为数字
                    newValue = Number(newValue)
                  }

                  return method(newValue, item[key.trim()])
                }
              }
            })
          })
        })
        .toArray()
    } catch (error) {
      uni.showToast({
        title: '查询出错',
        icon: 'error',
      })
      console.error(error)
      throw new Error('查询失败')
    }
  }
  // #endif

  // 新增数据
  async create(table: string, params: any) {
    try {
      const defaultParams = Utils.getDefaultFields(params, table)
      // 补全默认字段
      const nowTime = new Date().toISOString()
      const _id = generateUUID()
      params = {
        ...defaultParams,
        ...params,
        _id,
        isDirty: 1,
        createTime: nowTime,
        updateTime: nowTime,
      }
      params = Utils.prepareData(params, table)
      // #ifdef APP-PLUS
      const keyStr = Object.keys(params).join(',')
      const valsStr = Object.values(params).join(',')
      const sqlStr = `INSERT INTO ${table}(${keyStr}) VALUES(${valsStr})`
      await appSql.executeSQL(sqlStr)
      // #endif
      // #ifdef H5
      await dexie.table(table).add({
        ...params,
        isDirty: 1,
      })
      // #endif

      // 开始同步
      syncToServer({
        [_id]: {
          data: params,
          tableName: table,
        },
      })

      return _id
    } catch (error) {
      uni.showToast({
        title: '添加出错',
        icon: 'error',
      })
      console.error('create err', error)
      throw new Error(String(error))
    }
  }

  // 更新数据
  async update(table: string, queryStr: string, params: any) {
    try {
      // TODO 是否需要删除 params 里面的 _id
      params = JSON.parse(
        JSON.stringify({
          ...params,
          updateTime: new Date().toISOString(),
          isDirty: 1,
        })
      )

      // 过滤没有定义的字段
      Object.keys(params).forEach((field) => {
        if (!tableSchema[table][field]) delete params[field]
      })

      // 获取需要更新的数据
      let updateList = []
      if (isUUID(queryStr)) {
        // 根据 _id 查询
        const queryResult = await this.query(table, { id: queryStr, filterDeleted: false })
        updateList.push({
          ...queryResult, // 数据库查询出来的数据
          ...params, // 需要更新的数据
          _id: queryStr, // _id
        })
      } else {
        const queryResult = await this.query(table, { query: queryStr, filterDeleted: false })

        updateList = queryResult.map((item) => {
          return {
            ...item, // 数据库查询出来的数据
            ...params, // 需要更新的数据
          }
        })
      }

      // 批量更新数据 TODO: 批量更新功能，判断是否全部更新成功
      // #ifdef APP-PLUS
      for (let i = 0; i < updateList.length; i++) {
        const { _id, ...item } = updateList[i]
        const updateStr = Object.keys(item)
          .map((e) => {
            let val = item[e]
            console.log(val)
            if (typeof val === 'number') return `${e} = ${val}`
            // TODO 应该添加默认值
            if (val === null) return `${e} = ${val}`
            val = val.replace(/'/g, "''")
            return `${e} = '${val}'`
          })
          .join(',')

        const sql = `update ${table} set ${updateStr} where _id = '${_id}'`
        await appSql.executeSQL(sql)
      }
      // #endif
      // #ifdef H5
      await dexie.table(table).bulkPut(updateList)
      // #endif

      // 添加到同步列表
      const sData: any = {}
      updateList.forEach((item: any) => {
        delete item.isDirty
        const key = item._id
        sData[key] = {
          data: item,
          tableName: table,
        }
      })
      // 开始同步
      syncToServer(sData)
      // TODO 这里是否需要返回更新状态
    } catch (error) {
      uni.showToast({
        title: '更新出错',
        icon: 'error',
      })
      console.error(error)
      throw new Error('更新失败')
    }
  }
  /**
   * 删除整个数据库
   */
  async delete() {
    try {
      // #ifdef APP-PLUS
      for (const tName in tableSchema) {
        await appSql.dropTable(tName)
      }
      // #endif
      // #ifdef H5
      await dexie.delete()
      // #endif
    } catch (error) {
      uni.showToast({
        title: '删除数据库出错',
        icon: 'error',
      })
      console.error('delete database err', error)
      throw new Error(String(error))
    }
  }
}

class TableOperation {
  constructor(tableName: string) {
    Utils.options = {
      tableName,
    }
  }
  /**
   * 添加一条新的数据
   * @param params 新数据
   * @returns 新数据的 _id
   */
  async add(params: Object) {
    try {
      const { tableName } = Utils.options

      const defaultParams = Utils.getDefaultFields(params, tableName)
      // 补全默认字段
      const nowTime = new Date().toISOString()
      const _id = generateUUID()
      params = {
        ...defaultParams,
        ...params,
        _id,
        isDirty: 1,
        createTime: nowTime,
        updateTime: nowTime,
      }
      params = Utils.prepareData(params, tableName)
      // #ifdef APP-PLUS
      const keyStr = Object.keys(params).join(',')
      const valsStr = Object.values(params).join(',')
      const sqlStr = `INSERT INTO ${tableName}(${keyStr}) VALUES(${valsStr})`
      await appSql.executeSQL(sqlStr)
      // #endif
      // #ifdef H5
      await dexie.table(tableName).add({
        ...params,
      })
      // #endif

      const sData: any = {
        [_id]: {
          data: params,
          tableName,
        },
      }
      // 开始同步
      syncToServer(sData)
      return _id
    } catch (error) {
      uni.showToast({
        title: '添加出错',
        icon: 'error',
      })
      console.error('add err', error)
      throw new Error(String(error))
    }
  }
  /**
   * 批量添加数据
   * @param items 新数据数组
   */
  async bulkAdd(items: Array<any>) {
    try {
      const { tableName } = Utils.options
      items = items.map((params) => {
        const defaultParams = Utils.getDefaultFields(params, tableName)
        // 补全默认字段
        const nowTime = new Date().toISOString()
        const _id = generateUUID()
        params = {
          ...defaultParams,
          ...params,
          _id,
          isDirty: 1,
          createTime: nowTime,
          updateTime: nowTime,
        }
        params = Utils.prepareData(params, tableName)
        return params
      })
      // #ifdef APP-PLUS
      const keyList = Object.keys(items[0])
      const valList = items.map((item) => `(${keyList.map((key) => item[key]).join(',')})`).join(',')
      const sqlStr = `INSERT INTO ${tableName}(${keyList.join(',')}) VALUES${valList}`
      await appSql.executeSQL(sqlStr)
      // #endif
      // #ifdef H5
      await dexie.table(tableName).bulkAdd(items)
      // #endif

      const sData: any = {}
      items.forEach((item) => {
        sData[item._id] = {
          data: item,
          tableName,
        }
      })
      // 开始同步
      syncToServer(sData)
      return items
    } catch (error) {
      uni.showToast({
        title: '添加出错',
        icon: 'error',
      })
      console.error('bulkadd err', error)
      throw new Error(String(error))
    }
  }

  /**
   * 删除数据
   * @param primaryKey 主键
   * @returns void
   */
  // async delete(primaryKey: string) {
  //   const { tableName } = Utils.options
  //   try {
  //     // #ifdef APP-PLUS
  //     await appSql.executeSQL(`delete from ${tableName} where _id = '${primaryKey}'`)
  //     // #endif
  //     // #ifdef H5
  //     await dexie.table(tableName).delete(primaryKey)
  //     // #endif
  //     // 开始同步
  //     console.error('删除数据的上传功能还没兼容')
  //     const sData = await dexie.table(tableName).get(primaryKey)
  //     syncToServer([
  //       {
  //         [sData._id]: {
  //           data: sData,
  //           tableName,
  //         },
  //       },
  //     ])
  //     return primaryKey
  //   } catch (error) {
  //     uni.showToast({
  //       title: '删除出错',
  //       icon: 'error',
  //     })
  //     console.error(error)
  //     throw new Error('删除失败')
  //   }
  // }
  /**
   * 批量删除数据
   * @param keys 主键数组
   */
  // async bulkDelete(keys: Array<string>) {
  //   const { tableName } = Utils.options
  //   // #ifdef APP-PLUS
  //   const sqlStr = `DELETE FROM ${tableName} WHERE _id IN ('${keys.join("', '")}')`
  //   await appSql.executeSQL(sqlStr)
  //   // #endif
  //   // #ifdef H5
  //   await dexie.table(tableName).bulkDelete(keys)
  //   // #endif
  //   // 开始同步
  //   console.error('删除数据的上传功能还没兼容')
  //   const dbList = await dexie.table(tableName).bulkGet(keys)
  //   const sData = dbList.reduce((acc, item) => {
  //     acc[item._id] = {
  //       data: item,
  //       tableName,
  //     }
  //     return acc
  //   }, {})
  //   syncToServer(sData)
  // }
  /**
   * 更新/替换数据
   * 会直接替换整条数据
   * @param params 数据
   * @returns 查询结果
   */
  async put(params: any) {
    const { tableName } = Utils.options
    try {
      const isNew = !!params._id

      const defaultParams = Utils.getDefaultFields(params, tableName)
      // 补全默认字段
      const nowTime = new Date().toISOString()
      const _id = params._id || generateUUID()
      params = {
        ...defaultParams,
        ...params,
        _id,
        isDirty: 1,
        updateTime: nowTime,
      }
      if (isNew) params.createTime = nowTime

      params = Utils.prepareData(params, tableName)
      // #ifdef APP-PLUS
      const keyStr = Object.keys(params).join(',')
      const valsStr = Object.values(params).join(',')
      const sqlStr = `REPLACE INTO ${tableName}(${keyStr}) VALUES(${valsStr})`
      await appSql.executeSQL(sqlStr)
      // #endif
      // #ifdef H5
      await dexie.table(tableName).put(params)
      // #endif

      // 同步云端
      const sData = {
        [_id]: {
          data: params,
          tableName,
        },
      }
      syncToServer(sData)
      return _id
    } catch (error) {
      uni.showToast({
        title: '更新出错 put',
        icon: 'error',
      })
      console.error('put err', error)
      throw new Error(String(error))
    }
  }
  /**
   * 批量 更新/新增 数据
   * 会直接覆盖整条数据
   * @param params 数据
   * @param options 配置项
   * @param options.isCloudData 是否是服务器同步下来的数据，默认为 false
   */
  async bulkPut(items: Array<any>, { isCloudData } = { isCloudData: false }) {
    const { tableName } = Utils.options
    try {
      items = items.map((params) => {
        const isNew = !!params._id
        const defaultParams = Utils.getDefaultFields(params, tableName)

        if (!isCloudData) {
          // 补全默认字段
          const _id = params._id || generateUUID()
          const nowTime = new Date().toISOString()
          if (isNew) params.createTime = nowTime
          params = {
            ...defaultParams,
            ...params,
            _id,
            isDirty: 1,
            updateTime: nowTime,
          }
        } else {
          params = {
            ...defaultParams,
            ...params,
          }
        }
        return Utils.prepareData(params, tableName)
      })
      // #ifdef APP-PLUS
      const keysStr = Object.keys(tableSchema[tableName]).join(',')
      const valuesStr = items
        .map((params) => {
          const vals = Object.keys(tableSchema[tableName])
            .map((key) => params[key])
            .join(',')
          return `(${vals})`
        })
        .join(',')
      const sqlStr = `REPLACE INTO ${tableName} (${keysStr}) VALUES ${valuesStr}`
      await appSql.executeSQL(sqlStr)
      // #endif

      // #ifdef H5
      await dexie.table(tableName).bulkPut(items)
      // #endif

      // 同步云端
      if (!isCloudData) {
        const sData = items.reduce((acc, item) => {
          acc[item._id] = {
            data: item,
            tableName,
          }
          return acc
        }, {})
        syncToServer(sData)
      }
      return items.map((item) => item._id)
    } catch (error) {
      uni.showToast({
        title: '更新出错 bulkPut',
        icon: 'error',
      })
      console.error('bulkPut err', error)
      throw new Error(String(error))
    }
  }
  /**
   * 更新数据，只更新指定字段
   * @param id 主键
   * @param params 数据
   * @returns void
   */
  async update(id: string, params: any) {
    try {
      const { tableName } = Utils.options
      // 补全默认字段
      const nowTime = new Date().toISOString()
      const { _id, ...item } = params
      params = {
        ...item,
        isDirty: 1,
        updateTime: nowTime,
      }
      params = Utils.prepareData(params, tableName)

      // #ifdef APP-PLUS
      const updateStr = Object.keys(params)
        .map((key) => {
          const val = params[key]
          return `${key} = ${val}`
        })
        .join(',')
      const sql = `update ${tableName} set ${updateStr} where _id = '${id}'`
      console.log('updateStr', sql)
      await appSql.executeSQL(sql)
      // #endif
      // #ifdef H5
      await dexie.table(tableName).update(id, params)
      // #endif
      // 同步云端
      let dbData: any = {}
      // #ifdef APP-PLUS
      dbData = await appSql.selectSQL(`select * from ${tableName} where _id = '${id}'`)
      // #endif
      // #ifdef H5
      dbData = await dexie.table(tableName).get(id)
      // #endif
      const sData = {
        [id]: {
          data: dbData,
          tableName,
        },
      }
      syncToServer(sData)
    } catch (error) {
      uni.showToast({
        title: '更新出错 update',
        icon: 'error',
      })
      console.error('update err', error)
      throw new Error(String(error))
    }
  }
  /**
   * 批量更新数据，只更新指定字段
   * @param items 数据
   * @param options 配置项
   * @returns void
   */
  async bulkUpdate(items: API.BulkUpdate[]) {
    const { tableName } = Utils.options
    try {
      // 不是服务器同步下来的数据，需要处理数据，添加更新时间
      items = items.map((item) => {
        const { _id, ...changes } = item.changes
        item.changes = {
          ...changes,
          updateTime: new Date().toISOString(),
          isDirty: 1,
        }
        item.changes = Utils.prepareData(item.changes, tableName)
        return item
      })
      // #ifdef APP-PLUS
      const sqlList = items.map((item) => {
        const { _id, changes } = item
        const updateStr = Object.keys(changes)
          .map((key) => {
            const val = changes[key]
            return `${key} = ${val}`
          })
          .join(',')
        return `update ${tableName} set ${updateStr} where _id = '${_id}'` // TODO：因为将多条更新语句合并成一条比较麻烦，而且目前这个需求不是很大，所以暂时不做，先这样
      })
      await appSql.executeSQL(sqlList, true)
      // #endif
      // #ifdef H5
      await dexie.table(tableName).bulkUpdate(items)
      // #endif

      // 同步云端
      if (!isCloudData) {
        let dbData = []
        // #ifdef APP-PLUS
        dbData = await dexie.table(tableName).bulkGet(items.map((item) => item.key))
        // #endif
        // #ifdef H5
        dbData = await dexie.table(tableName).bulkGet(items.map((item) => item.key))
        // #endif

        const sData = dbData.reduce((acc, item) => {
          acc[item._id] = {
            data: item,
            tableName,
          }
          return acc
        }, {})
        syncToServer(sData)
      }
      return items.map((item) => item.key)
    } catch (error) {
      uni.showToast({
        title: '更新出错 bulkUpdate',
        icon: 'error',
      })
      console.error('bulkUpdate err', error)
      throw new Error(String(error))
    }
  }
  /**
   * 查询单条数据
   * @param primaryKey 主键
   * @returns 对象，如果没有找到则返回 undefined
   */
  async get(primaryKey: string) {
    try {
      const { tableName } = Utils.options
      let res: any = {}
      // #ifdef APP-PLUS
      res = await appSql.selectSQL(`select * from ${tableName} where _id = '${primaryKey}' and deleteTime == ''`)
      return res.length === 0 ? undefined : res[0]
      // #endif
      // #ifdef H5
      res = await dexie.table(tableName).get(primaryKey)
      if (res && res.deleteTime === '') return res
      return undefined
      // #endif
    } catch (error) {
      console.error('get err', error)
      throw new Error(String(error))
    }
  }
  /**
   * 查询多条数据
   * @param keys 主键数组
   */
  async bulkGet(keys: Array<string>) {
    try {
      const { tableName } = Utils.options
      // #ifdef APP-PLUS
      const sqlStr = `SELECT * FROM ${tableName} WHERE _id IN ('${keys.join("', '")}') and deleteTime == ''`
      return await appSql.executeSQL(sqlStr)
      // #endif
      // #ifdef H5
      const res = await dexie.table(tableName).bulkGet(keys)
      return res.filter((item) => item.deleteTime === '')
      // #endif
    } catch (error) {
      console.error('bulkGet err', error)
      throw new Error(String(error))
    }
  }

  /**
   * 根据条件查询数据，只能在在外层使用 || 语句
   * @param query
   * @param 查询方式 str： (name == 'zcj' && b >= 2) || (c != 3)
   * @param equals age, equals(20) 查询 age 等于 20 的记录
   * @param above age, above(20) 查询 age 大于 20 的记录
   * @param aboveOrEqual 类上，区别：大于等于
   * @param anyOf age, anyOf(18,29,30) 查询 age 等于 18、29、30 的记录
   * @param below age, below(20) 查询 age 小于 20 的记录
   * @param belowOrEqual 同上，区别：大于等于
   * @param between age, between(18, 30) 查询 age 在 18(包含) 和 30(包含) 之间的记录
   * @param inAnyRange age, inAnyRange([[18, 30], [33, 36], [40, 66]]) 查询 age 在 18-30、33-36、40-66 之间的记录，包含边界
   * @param noneOf age, noneOf(18, 29, 30) 查询 age 不等于 18、29、30 的记录
   * @param notEqual age, notEqual(20) 查询 age 不等于 20 的记录
   * @param filterDelete 是否过滤已删除数据，默认为 true
   */
  where(query = '', { filterDelete } = { filterDelete: true }) {
    try {
      // 开始构建查询语句
      // #ifdef H5
      Utils.dexieQuery = dexie.table(Utils.options.tableName)
      // #endif

      const regex = /(\w+),\s*(\w+)\((.*?)\)/ // 匹配 name.above(2) 这种形式
      const queryType = this.getQueryType(query, regex)
      if (queryType === 'fn') {
        this.fnQuery(query, regex)
      } else if (queryType === 'str') {
        this.strQuery(query)
      }
      // 过滤已删除数据
      if (filterDelete) {
        // #ifdef H5
        Utils.dexieQuery = Utils.dexieQuery.filter((item) => item.deleteTime === '')
        // #endif
        // #ifdef APP-PLUS
        Utils.options.filterDelete = true
        // #endif
      }
      return new WhereOperation()
    } catch (error) {
      console.error('where err', error)
      throw new Error(String(error))
    }
  }
  // 判断查询类型
  private getQueryType(query: string, regex: RegExp) {
    if (query === '') return 'all' // 如果没有查询条件则默认查询全部
    else if (regex.test(query)) return 'fn' // 如果是 name.above(2) 这种形式
    return 'str' // 如果是字符串形式
  }
  // 处理 name.above(xxx) 类型的查询
  private fnQuery(query: string, regex: RegExp) {
    const match = regex.exec(query)
    const [_, field, operator, queryStr] = match

    // #ifdef H5
    Utils.dexieQuery = Utils.operatorMap[operator](field, queryStr, Utils.dexieQuery)
    // #endif
    // #ifdef APP-PLUS
    Utils.options.where = Utils.operatorMap[operator](field, queryStr) // 例子：'from taskName where name > 2'
    // #endif
  }
  // 处理 字符串 类型的查询
  private strQuery(query: string) {
    // 将入参转化为数组
    const queryArr = query.split('||').map((item) => {
      // 去掉空格
      item = item.replace(/\s+/g, '')
      // 判断两端是否有括号，如果有则去掉
      if (item.startsWith('(') && item.endsWith(')')) {
        item = item.slice(1, -1)
      }
      return item.split('&&')
    })
    // #ifdef H5
    // 每个 orItem 的第一个 andItem，使用 where 或者 or 初步过滤，减少数据量
    queryArr.forEach((orItem, orIndex) => {
      const [firstItem] = orItem
      const { left, operator, right } = Utils.extractFirstOperatorWithSides(firstItem)

      if (orIndex === 0) {
        Utils.dexieQuery = Utils.dexieQuery.where(left)
      } else {
        Utils.dexieQuery = Utils.dexieQuery.or(left)
      }
      Utils.dexieQuery = Utils.operatorMap[operator](Utils.dexieQuery, eval(right)) // TODO!!! 这里注意使用了 eval
    })
    // 再使用 filter 将所有条件过滤一遍
    Utils.dexieQuery.filter((item) => {
      return queryArr.some((andItem) => {
        return andItem.every((str) => {
          const { left, operator, right } = Utils.extractFirstOperatorWithSides(str)
          const dbVal = item[left]
          if (Utils.operatorsStr[operator]) {
            return Utils.operatorsStr[operator](dbVal, eval(right))
          }
        })
      })
    })
    // #endif
    // #ifdef APP-PLUS
    queryArr.forEach((orItem, orIndex) => {
      if (orIndex === 0) {
        Utils.options.where = `where`
      } else {
        Utils.options.where = `or`
      }
      Utils.options.where += `(${orItem.join(' and ')})`
    })
    // #endif
  }
}
class WhereOperation {
  constructor() {}
  // 返回查询结果的数组
  toArray() {
    try {
      // #ifdef APP-PLUS
      Utils.options.type = 'select'
      return Utils.exec()
      // #endif
      // #ifdef H5
      return Utils.dexieQuery.toArray()
      // #endif
    } catch (error) {
      console.error('toArray err', error)
      throw new Error(String(error))
    }
  }
  /**
   *
   * @param index 排序字段
   * @param order 排序方式
   */
  orderBy(index: string, order: 'asc' | 'desc' = 'asc') {
    // #ifdef APP-PLUS
    Utils.options.orderBy = `order by ${index}` // 例子：'from taskName where name > 2 order by createTime'
    if (order === 'desc') Utils.options.decs += ' desc'
    // #endif
    // #ifdef H5
    Utils.dexieQuery = Utils.dexieQuery.sortBy(index)
    if (order === 'desc') Utils.dexieQuery = Utils.dexieQuery.reverse()
    // #endif
    return this
  }
  // 返回查询结果的第一个对象
  async first() {
    // #ifdef APP-PLUS
    Utils.options.type = 'select'
    const res = await Utils.exec() // 例子：'select * from taskName where name > 2 order by createTime desc'
    return res.length > 0 ? res[0] : undefined
    // #endif
    // #ifdef H5
    return Utils.dexieQuery.first()
    // #endif
  }
  // 返回查询结果的最后一个对象
  async last() {
    // #ifdef APP-PLUS
    Utils.options.type = 'select'
    const res = await Utils.exec()
    return res.length > 0 ? res[res.length - 1] : undefined
    // #endif
    // #ifdef H5
    return Utils.dexieQuery.last()
    // #endif
  }
  // 返回限制为 N 个项目的集合
  limit(N: number) {
    // #ifdef APP-PLUS
    Utils.options.limit = `limit ${N}`
    // #endif
    // #ifdef H5
    Utils.dexieQuery = Utils.dexieQuery.limit(N)
    // #endif
    return this
  }
  // 返回集合中第 N 条以后的数据（N 是该方法参数）
  offset(N: number) {
    // #ifdef APP-PLUS
    Utils.options.offset = `offset ${N}`

    // #endif
    // #ifdef H5
    Utils.dexieQuery = Utils.dexieQuery.offset(N)
    // #endif
    return this
  }

  // 删除查询出来的数据
  delete() {
    try {
      // #ifdef APP-PLUS
      Utils.options.type = 'delete'
      return Utils.exec()
      // #endif
      // #ifdef H5
      return Utils.dexieQuery.delete()
      // #endif
    } catch (error) {
      console.error('add err', error)
      throw new Error(String(error))
    }
  }
  // 统计数量
  count() {
    // #ifdef APP-PLUS
    Utils.options.type = 'count'
    return Utils.exec()
    // #endif
    // #ifdef H5
    return Utils.dexieQuery.count()
    // #endif
  }

  /**
   * 修改查询出来的数据
   * @param changes 要修改的字段，只传入要修改的字段即可
   * @returns 修改成功的数量
   */
  modify(changes: any) {
    try {
      // #ifdef APP-PLUS
      Utils.options.type = 'modify'
      changes = Utils.prepareData(changes, Utils.options.tableName)
      Utils.options.modify = Object.keys(changes)
        .map((key) => `${key} = ${changes[key]}`)
        .join(',')
      return Utils.exec()
      // #endif
      // #ifdef H5
      return Utils.dexieQuery.modify(changes)
      // #endif
    } catch (error) {
      console.error('add err', error)
      throw new Error(String(error))
    }
  }
}

class Utils {
  static options = {
    type: '', // 操作类型
    tableName: '', // 表名
    where: '', // 查询条件
    orderBy: '', // 排序字段
    decs: '', // 排序方式
    limit: '', // 限制数量
    offset: '', // 偏移量
    modify: '', // 批量更新字段
    filterDelete: false, // 是否过滤已删除数据
  }
  static dexieQuery: any
  // appSql 执行语句
  static async exec() {
    const { type, tableName, modify, where, orderBy, decs, limit, offset, filterDelete } = Utils.options
    let sql = ''
    if (type === 'select') {
      sql = `select * from ${tableName}`
    } else if (type === 'delete') {
      sql = `delete from ${tableName}`
    } else if (type === 'update') {
      sql = `update ${tableName}`
    } else if (type === 'count') {
      sql = `select count(*) from ${tableName}`
    } else if (type === 'modify') {
      // 批量修改
      sql = `update ${tableName} set ${modify}`
    }
    if (where) sql += ` ${where}`
    if (orderBy) sql += ` ${orderBy}`
    if (decs) sql += ` ${decs}`
    if (limit) sql += ` ${limit}`
    if (offset) sql += ` ${offset}`
    try {
      let res = await appSql.selectSQL(sql)
      if (res && filterDelete) res = res.filter((item) => item.deleteTime === '')
      return res?.length > 0 ? res : []
    } catch (e) {
      console.log(e)
      new Error('exec error')
    }
  }
  // 转换数据类型
  static prepareData(params: any, table: string) {
    if (!params || typeof params !== 'object' || Array.isArray(params)) {
      throw new Error('Invalid data. Expected an object.')
    }
    // 转换数据
    params = Object.keys(params).reduce((acc: any, key) => {
      // 过滤没有定义的字段，并且转换数据类型
      if (tableSchema[table][key]) {
        const value = params[key]
        if (value === null || value === undefined) acc[key] = `''`
        else if (typeof value === 'number') acc[key] = value
        else if (typeof value === 'boolean') acc[key] = value ? 1 : 0
        // #ifdef APP-PLUS
        else if (typeof value === 'string') acc[key] = `'${value.replace(/'/g, "''")}'`
        else if (isObject(value) || Array.isArray(value)) acc[key] = `'${JSON.stringify(value).replace(/'/g, "''")}'`
        // #endif
        // #ifdef H5
        else if (typeof value === 'string') acc[key] = value
        else if (isObject(value) || Array.isArray(value))
          acc[key] = JSON.stringify(value) // 对象或者数组，使用 JSON.stringify 格式化
        // #endif
        else {
          acc[key] = `'${value}'`
          console.error(`Invalid data type for field ${key}.`)
        }
      }
      return acc
    }, {})
    return params
  }
  // 过滤没有定义的字段
  static filterFields(params: any, table: string) {
    return Object.keys(params).reduce((acc: any, key) => {
      if (tableSchema[table][key]) acc[key] = params[key]
      return acc
    }, {})
  }
  // 获取默认字段
  static getDefaultFields(params: any, table: string) {
    const defaultParams: any = {}
    const curTableSchema = tableSchema[table]

    Object.keys(curTableSchema).forEach((field) => {
      // eslint-disable-next-line no-prototype-builtins
      if (curTableSchema[field].hasOwnProperty('defaultVal')) {
        defaultParams[field] = curTableSchema[field].defaultVal
      }
    })

    return defaultParams
  }
  // ==、!=、>=、<=、>、<、= 运算符提取
  static extractFirstOperatorWithSides(str: string) {
    // 定义包含所有需要识别的运算符的正则表达式
    const operatorPattern = /(\S+)\s*(==|!=|>=|<=|>|<)\s*(\S+)/
    const match = operatorPattern.exec(str)
    if (match) {
      return {
        left: match[1],
        operator: match[2],
        right: match[3],
      }
    } else {
      return new Error('查询条件格式错误')
    }
  }
  static operatorsStr = {
    '==': (a, b) => a == b,
    '!=': (a, b) => a != b,
    '<': (a, b) => a < b,
    '>': (a, b) => a > b,
    '<=': (a, b) => a <= b,
    '>=': (a, b) => a >= b,
  }
  static operatorMap = {
    '==': (query, value) => query.equals(value),
    '!=': (query, value) => query.notEqual(value), // TODO 还支持数组
    '>=': (query, value) => query.aboveOrEqual(value),
    '<=': (query, value) => query.below(value),
    '>': (query, value) => query.above(value),
    '<': (query, value) => query.belowOrEqual(value),
    equals: (field: string, value: string, query: any) => {
      // #ifdef APP-PLUS
      return `where ${field} = ${value}`
      // #endif
      // #ifdef H5
      const queryValue = Utils.isQuoted(value) ? value.slice(1, -1) : Number(value)
      return query.where(field).equals(queryValue)
      // #endif
    },
    above: (field: string, value: string, query: any) => {
      // #ifdef APP-PLUS
      return `where ${field} > ${value}`
      // #endif
      // #ifdef H5
      const queryValue = Utils.isQuoted(value) ? value.slice(1, -1) : Number(value)
      return query.where(field).above(queryValue)
      // #endif
    },
    aboveOrEqual: (field: string, value: string, query: any) => {
      // #ifdef APP-PLUS
      return `where ${field} >= ${value}`
      // #endif
      // #ifdef H5
      const queryValue = Utils.isQuoted(value) ? value.slice(1, -1) : Number(value)
      return query.where(field).aboveOrEqual(queryValue)
      // #endif
    },
    anyOf: (field: string, value: string, query: any) => {
      // #ifdef APP-PLUS
      return `where ${field} in (${value})`
      // #endif
      // #ifdef H5
      const queryValue = value.split(',').map((item) => (Utils.isQuoted(item) ? item.slice(1, -1) : Number(item)))
      return query.where(field).anyOf(...queryValue)
      // #endif
    },
    below: (field: string, value: string, query: any) => {
      // #ifdef APP-PLUS
      return `where ${field} < ${value}`
      // #endif
      // #ifdef H5
      const queryValue = Utils.isQuoted(value) ? value.slice(1, -1) : Number(value)
      return query.where(field).below(queryValue)
      // #endif
    },
    belowOrEqual: (field: string, value: string, query: any) => {
      // #ifdef APP-PLUS
      return `where ${field} <= ${value}`
      // #endif
      // #ifdef H5
      const queryValue = Utils.isQuoted(value) ? value.slice(1, -1) : Number(value)
      return query.where(field).belowOrEqual(queryValue)
      // #endif
    },
    between: (field: string, value: string, query: any) => {
      const [lowerBound, upperBound] = value.split(',').map((item) => {
        if (this.isQuoted(item)) {
          return item
        } else {
          return Number(item)
        }
      })
      // #ifdef APP-PLUS
      return `where ${field} between ${lowerBound} and ${upperBound}`
      // #endif
      // #ifdef H5
      return query.where(field).between(lowerBound, upperBound, true, true)
      // #endif
    },
    inAnyRange: (field: string, value: string, query: any) => {
      const ranges = JSON.parse(value)
      // #ifdef APP-PLUS
      const qStr = ranges.map((range) => `(${field} between ${range.join(' and ')})`).join(' or ')
      return `where ${qStr}`
      // #endif
      // #ifdef H5
      return query.where(field).inAnyRange(ranges, { includeLowers: true, includeUppers: true })
      // #endif
    },
    noneOf: (field: string, value: string, query: any) => {
      // #ifdef APP-PLUS
      return `where ${field} not in (${value})`
      // #endif
      // #ifdef H5
      const arr = value.split(',').map((item) => {
        if (this.isQuoted(item)) {
          return item
        } else {
          return Number(item)
        }
      })
      return query.where(field).noneOf(arr)
      // #endif
    },
    notEqual: (field: string, value: string, query: any) => {
      // #ifdef APP-PLUS
      return `where ${field} != ${value}`
      // #endif
      // #ifdef H5
      const queryValue = Utils.isQuoted(value) ? value.slice(1, -1) : Number(value)
      return query.where(field).notEqual(queryValue)
      // #endif
    },
  }
  // 判断是否是字符串 (带引号)
  static isQuoted(str: string): boolean {
    return /^["'].*["']$/.test(str)
  }
}

export default new DB()

# 日记功能优化方案对比

## 当前实现分析

目前日记功能的实现使用多条数据方式存储 AI 分析结果，核心逻辑包括：

1. 用户提交日记内容后，调用 AI 分析服务
2. 将 AI 分析结果按标签拆分为多条数据，分别存储在数据库中
3. 展示时，根据日期获取原始日记内容和对应的 AI 分析内容
4. 提供标签筛选和内容折叠等交互功能

```javascript
// 当前提交逻辑
const handleDiarySubmit = async ({ content, selectedTags, diaryId }) => {
  // 1. 保存日记基本内容
  const params = { content, date: dayjs(curDay.value).format('YYYY-MM-DD') }
  let newDiaryData = diaryId 
    ? await updateDiaryApi(diaryId, params) 
    : await addDiaryApi(params)
  
  // 2. AI 分析
  let { data } = await workflowApi('7484172363748130835', { content, question: selectedTags })
  const aiContent = JSON.parse(data.input)
  
  // 3. 删除旧的 AI 分析结果
  await delMemoApi(`type == "aiDiary" && date == "${curDay.value}"`)
  
  // 4. 保存新的 AI 分析结果（多条数据）
  const tagList = await getTagListApi()
  for (const key in aiContent) {
    // 按标签分别保存 AI 分析结果
    if (key === '标题') {
      await addDiaryApi({ 
        content: aiContent[key], 
        parentId: 'title', 
        type: 'aiDiary', 
        date: curDay.value 
      })
    } else if (tagList.find(t => t.content === key)) {
      await addDiaryApi({ 
        content: aiContent[key], 
        parentId: tagList.find(t => t.content === key)._id, 
        type: 'aiDiary', 
        date: curDay.value 
      })
    }
  }
}
```

## 两种方案对比

### 方案一：多条数据方式（当前方案）

#### 优点

1. **灵活性高**：每个分析项单独存储，可以独立查询、更新或删除
2. **UI 渲染便捷**：直接从数据库获取列表，循环渲染到 UI 界面
3. **关联查询友好**：基于`parentId`可以方便地查询特定标签的分析内容
4. **数据结构简单**：每条记录结构清晰，职责单一
5. **按需加载**：可以只加载需要的分析项，减少数据传输量
6. **现有功能兼容性**：与当前实现完全兼容，无需修改现有功能

#### 缺点

1. **数据库操作频繁**：保存一篇日记需要多次数据库写入操作
2. **数据一致性风险**：多次写入可能导致部分失败，造成数据不一致
3. **存储冗余**：每条记录都包含完整元数据（日期、类型等），存在冗余
4. **查询复杂度**：查看完整日记需要多次查询（日记内容 + 多个 AI 分析）
5. **代码复杂度**：实现代码相对复杂，需要处理多个相关记录

### 方案二：JSON 存储方式（建议方案）

#### 优点

1. **原子性好**：日记和分析结果一起存储，确保数据一致性
2. **查询效率高**：一次查询即可获取日记和所有分析结果
3. **减少数据库操作**：保存时只需一次写入操作
4. **减少存储冗余**：不需要为每个分析项重复存储元数据
5. **代码简洁**：实现逻辑更简单，减少错误可能性
6. **扩展性好**：可以方便地添加新的分析维度，不需要修改数据结构

#### 缺点

1. **查询特定标签内容复杂**：无法直接查询特定标签的分析内容，需要先获取整个日记，再提取标签内容
2. **灵活性降低**：无法单独更新某个标签的分析内容
3. **结构可能复杂**：JSON 结构设计需要考虑未来扩展性
4. **索引能力有限**：JSON 内部字段无法直接建立索引，可能影响查询性能
5. **需调整现有代码**：需要修改现有查询和展示逻辑

## 解决方案与建议

针对方案二（JSON 存储）的缺点，有以下解决方案：

### 1. 特定标签内容查询

为支持按标签查询功能，可以采用以下方案：

1. **应用层过滤**：获取所有日记后在应用层进行过滤
```javascript
export const getDiaryByTagApi = async (tagName) => {
  const allDiaries = await db.table('memo').where('type == "diary"').toArray();
  return allDiaries.filter(diary => {
    if (!diary.aiAnalysis) return false;
    try {
      const analysis = JSON.parse(diary.aiAnalysis);
      return tagName in analysis;
    } catch (e) {
      return false;
    }
  });
}
```

2. **辅助索引字段**：添加`tagsList`字段存储日记包含的标签
```javascript
// 保存时构建标签列表
const tagsList = Object.keys(aiContent).filter(key => key !== '标题');
await db.table('memo').update(diaryId, {
  content,
  date,
  aiAnalysis: JSON.stringify(aiContent),
  tagsList: JSON.stringify(tagsList)
});

// 查询包含特定标签的日记
const diaries = await db.table('memo').where('type == "diary"').toArray();
return diaries.filter(diary => {
  const tags = JSON.parse(diary.tagsList || '[]');
  return tags.includes(tagName);
});
```

3. **索引表方案**：创建专门的索引表记录日记与标签的关系
```javascript
// 创建索引表记录关系
for (const tagName in aiContent) {
  if (tagName === '标题') continue;
  await db.table('diaryTagIndex').add({
    diaryId: savedDiaryId,
    tagName,
    date
  });
}

// 按标签查询
const indexEntries = await db.table('diaryTagIndex')
  .where(`tagName == "${tagName}"`)
  .toArray();
const diaryIds = indexEntries.map(entry => entry.diaryId);
return await db.table('memo').bulkGet(diaryIds);
```

### 2. 数据迁移策略

从当前方案迁移到 JSON 存储方案需要精心规划，以确保数据一致性和平滑过渡。以下是详细的迁移策略：

#### 准备阶段
1. **更新数据库表结构**
   ```javascript
   // 修改 src/api/dataSchema/index.ts 添加新字段
   memo: {
     // 现有字段...
     aiAnalysis: {
       aType: 'TEXT',
       defaultVal: '{}',
     },
     tagsList: {
       aType: 'TEXT',
       defaultVal: '[]',
     }
   }
   ```

2. **更新类型定义**
   ```typescript
   // 修改 src/api/dataSchema/typings.d.ts
   interface Memo {
     // 现有字段...
     aiAnalysis?: string; // JSON 字符串存储所有 AI 分析结果
     tagsList?: string; // JSON 字符串存储标签列表
   }
   ```

#### 迁移脚本开发
3. **编写数据迁移函数**
   ```javascript
   // 迁移脚本 src/scripts/migrateDiaryData.js
   const migrateDiaryData = async () => {
     try {
       console.log('开始数据迁移...');
       
       // 1. 获取所有日记记录
       const allDiaries = await db.table('memo')
         .where('type == "diary"')
         .toArray();
       
       console.log(`找到 ${allDiaries.length} 条日记记录`);
       
       // 2. 为每条日记处理 AI 分析内容
       for (const diary of allDiaries) {
         // 获取该日记的所有 AI 分析记录
         const aiRecords = await db.table('memo')
           .where(`type == "aiDiary" && date == "${diary.date}"`)
           .toArray();
         
         if (aiRecords.length === 0) {
           console.log(`日记 ${diary._id} (${diary.date}) 没有AI分析记录，跳过`);
           continue;
         }
         
         // 构建 AI 分析 JSON
         const aiAnalysis = {};
         const tagsList = [];
         
         for (const record of aiRecords) {
           if (record.parentId === 'title') {
             aiAnalysis['标题'] = record.content;
           } else {
             // 查找标签名称
             const tag = await db.table('memo').get(record.parentId);
             if (tag && tag.content) {
               aiAnalysis[tag.content] = record.content;
               tagsList.push(tag.content);
             }
           }
         }
         
         // 更新日记记录
         await db.table('memo').update(diary._id, {
           aiAnalysis: JSON.stringify(aiAnalysis),
           tagsList: JSON.stringify(tagsList)
         });
         
         console.log(`日记 ${diary._id} (${diary.date}) 迁移完成，包含 ${tagsList.length} 个标签`);
       }
       
       console.log('数据迁移完成！');
       return { success: true, message: '迁移成功' };
     } catch (error) {
       console.error('数据迁移失败：', error);
       return { success: false, error };
     }
   };
   ```

#### API 更新
4. **更新 API 函数**
   ```javascript
   // 修改 src/api/memo.ts 中的函数
   
   // 获取一条日记（包含 AI 分析）
   export const getDiaryApi = async (date) => {
     date = dayjs(date).format('YYYY-MM-DD');
     const res = await db.table('memo').where(`type == "diary" && date == "${date}"`).toArray();
     return res[0];
   };
   
   // 获取 AI 分析内容（兼容旧版和新版）
   export const getDiaryAiListApi = async (date) => {
     const diary = await getDiaryApi(date);
     
     // 新版：从 JSON 字段中提取
     if (diary?.aiAnalysis) {
       try {
         const analysis = JSON.parse(diary.aiAnalysis);
         const tagList = await getTagListApi();
         
         // 转换为原来的格式返回
         const result = [];
         
         // 处理标题
         if (analysis['标题']) {
           result.push({
             content: analysis['标题'],
             parentId: 'title',
             type: 'aiDiary',
             date
           });
         }
         
         // 处理标签内容
         for (const tagName in analysis) {
           if (tagName === '标题') continue;
           
           const tag = tagList.find(t => t.content === tagName);
           if (tag) {
             result.push({
               content: analysis[tagName],
               parentId: tag._id,
               tag: tagName,
               type: 'aiDiary',
               date
             });
           }
         }
         
         return result;
       } catch (e) {
         console.error('解析 AI 分析数据失败', e);
       }
     }
     
     // 旧版：从单独记录中获取
     return await db.table('memo').where(`type == "aiDiary" && date == "${date}"`).toArray();
   };
   
   // 保存日记内容（新版方式）
   export const saveDiaryWithAnalysisApi = async (params) => {
     const { content, date, aiAnalysis, diaryId, tagsList } = params;
     
     const diaryData = {
       content,
       date,
       type: 'diary',
       aiAnalysis: typeof aiAnalysis === 'string' ? aiAnalysis : JSON.stringify(aiAnalysis),
       tagsList: typeof tagsList === 'string' ? tagsList : JSON.stringify(tagsList)
     };
     
     if (diaryId) {
       return await db.table('memo').update(diaryId, diaryData);
     } else {
       return await db.table('memo').add(diaryData);
     }
   };
   ```

#### UI 更新
5. **更新提交处理函数**
   ```javascript
   // 修改日记提交函数
   const handleDiarySubmit = async ({ content, selectedTags, diaryId }) => {
     uni.vibrateShort();
     
     try {
       // AI 分析
       uni.showLoading({ title: 'AI 复盘中', mask: true });
       let { data } = await workflowApi('7484172363748130835', { 
         content, 
         question: selectedTags 
       });
       uni.hideLoading();
       
       const aiContent = JSON.parse(data.input);
       const tagsList = Object.keys(aiContent).filter(key => key !== '标题');
       
       // 使用新 API 保存日记和分析
       await saveDiaryWithAnalysisApi({
         content,
         date: dayjs(curDay.value).format('YYYY-MM-DD'),
         diaryId,
         aiAnalysis: aiContent,
         tagsList
       });
       
       // 兼容性处理：同时保存旧版格式（可选，待所有功能完全迁移后可移除）
       if (needBackwardCompatibility) {
         await saveOldFormatAiAnalysis(aiContent, curDay.value);
       }
       
       uni.showToast({ title: '保存成功', icon: 'success' });
       showInputPopup.value = false;
       
       // 重新获取完整数据
       getDay(curDay.value);
     } catch (error) {
       console.error(error);
       uni.showToast({ title: '保存失败', icon: 'error' });
     }
   };
   ```

#### 执行迁移
6. **执行迁移的策略**
   - 开发环境：直接执行迁移脚本进行测试
   - 生产环境：添加版本检查，首次启动新版时执行迁移
   ```javascript
   // 应用启动时检查并执行迁移
   const checkAndMigrate = async () => {
     const currentVersion = '2.0.0'; // 新版本号
     const lastVersion = uni.getStorageSync('app_version');
     
     // 版本升级且需要迁移
     if (lastVersion !== currentVersion) {
       // 显示升级提示
       uni.showLoading({ title: '正在升级数据，请稍候...', mask: true });
       
       try {
         // 执行迁移
         const result = await migrateDiaryData();
         
         if (result.success) {
           // 更新版本记录
           uni.setStorageSync('app_version', currentVersion);
           uni.hideLoading();
           uni.showToast({ title: '数据升级完成', icon: 'success' });
         } else {
           uni.hideLoading();
           uni.showModal({
             title: '数据升级失败',
             content: '请联系客服处理',
             showCancel: false
           });
         }
       } catch (e) {
         uni.hideLoading();
         uni.showModal({
           title: '数据升级异常',
           content: '请重启应用或联系客服',
           showCancel: false
         });
       }
     }
   };
   ```

#### 监控与回滚
7. **部署监控与应急预案**
   - 添加数据迁移日志上报
   - 准备回滚脚本，出现严重问题时可以恢复旧版数据
   - 实施灰度发布，先对小部分用户进行升级测试
   - 保留旧版 API 兼容代码一段时间，确保稳定后再完全移除

## 最终建议

基于需求和分析，推荐采用**JSON 存储 + 辅助索引字段**的方案：

1. 在`memo`表中添加`aiAnalysis`和`tagsList`字段
2. 将 AI 分析结果以 JSON 格式存储在`aiAnalysis`字段
3. 将标签列表存储在`tagsList`字段便于快速筛选
4. 开发数据迁移脚本确保平滑过渡
5. 更新相关 API 和 UI 代码，适配新的数据结构

这种方案既保留了 JSON 存储的优势（原子性、查询效率），又解决了标签查询的问题，同时对现有功能的影响也相对可控。

# 周计划与复盘功能 - 任务拆分总结

## 需求概述
为应用增加周目标和周复盘功能，允许用户进行周级别的计划与回顾。功能核心包括扩展现有周概览弹窗、提供专门的编辑页面，并集成模拟 AI 能力以自动生成和更新周概览。

## 任务拆分列表
1.  **task-weekly-planning-01**: 扩展周概览弹窗 UI
    - **内容**: 修改 `today.vue` 弹窗，增加"周目标"、"周概览"、"周复盘"三个标签页的 UI 结构。

2.  **task-weekly-planning-02**: 创建共享编辑页面 `weekEdit.vue`
    - **内容**: 创建用于编辑周目标和周复盘的通用页面，通过 URL 参数区分模式。

3.  **task-weekly-planning-03**: 实现数据处理和导航
    - **内容**: 实现页面跳转、数据库读写周目标和周复盘数据的核心逻辑。

4.  **task-weekly-planning-04**: 实现模拟 AI 函数
    - **内容**: 创建 `generateWeekOverview` 和 `updateWeekOverview` 两个模拟函数，用于处理周概览数据。

5.  **task-weekly-planning-05**: 集成 AI 函数
    - **内容**: 将模拟 AI 函数与业务流程结合，在保存周目标和日回顾时自动调用。

## 任务依赖关系
```mermaid
graph TD
    subgraph "阶段一: UI准备"
        A[task-01: 扩展弹窗UI]
        B[task-02: 创建编辑页]
    end

    subgraph "阶段二: 核心逻辑"
        C[task-03: 数据与导航]
    end
    
    subgraph "阶段三: AI功能"
        D[task-04: 实现模拟AI函数]
        E[task-05: 集成AI函数]
    end

    A --> C
    B --> C
    C --> E
    D --> E
```

## 任务优先级
- **高优先级**: `task-weekly-planning-01`, `task-weekly-planning-02`, `task-weekly-planning-03`
- **中优先级**: `task-weekly-planning-04`, `task-weekly-planning-05`

## 开发建议
1.  **并行开发**: 任务 01 和 02 可以并行开始，它们是后续功能的基础。
2.  **分步实施**: 优先完成核心的数据流（任务 03），确保基本的数据增删改查功能通畅。
3.  **独立模块**: AI 相关功能（任务 04、05）可以作为一个独立模块开发，在核心功能稳定后再进行集成，降低耦合。
4.  **数据先行**: 在开始任务 04 之前，先在 `typings.d.ts` 中明确周概览的数据结构，这将作为前后端交互和函数实现的依据。 
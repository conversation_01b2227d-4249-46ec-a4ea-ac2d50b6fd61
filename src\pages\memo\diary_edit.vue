<template>
  <view class="diary-edit-page">
    <z-page-navbar title="记录日记" back-button></z-page-navbar>

    <view class="content-area">
      <!-- 标签选择区域 -->
      <view class="tag-section">
        <text class="section-title">选择标签</text>
        <view class="tag-list">
          <view
            v-for="(tag, index) in tags"
            :key="index"
            class="tag-item"
            :class="{ 'tag-selected': selectedTags.includes(tag.content) }"
            @click="toggleTag(tag.content)"
            @longpress="handleLongPress(tag)"
          >
            {{ tag.content }}
          </view>
          <view class="tag-item tag-add" @click="openAddTagDialog">
            <text class="add-icon">+</text>
          </view>
        </view>
      </view>

      <!-- 日记内容输入区域 -->
      <view class="editor-section">
        <text class="section-title">日记内容</text>
        <view class="editor-wrapper">
          <textarea
            v-model="content"
            class="diary-editor"
            placeholder="今天发生了什么有趣的事情......"
            maxlength="5000"
          ></textarea>
        </view>
      </view>

      <!-- 保存按钮 -->
      <view class="button-area">
        <view class="save-btn" :class="{ processing: isProcessing }" @click="onSubmit" :disabled="isProcessing">
          <span v-if="!isProcessing">保存日记</span>
          <span v-else class="processing-text">
            AI 处理中
            <view class="dots-container">
              <text class="dot dot1">.</text>
              <text class="dot dot2">.</text>
              <text class="dot dot3">.</text>
            </view>
          </span>
        </view>
      </view>
    </view>

    <!-- 标题选择弹窗 -->
    <view v-if="showTitleSelector" class="title-selector-overlay">
      <view class="title-selector-container">
        <view class="title-selector-header">
          <text class="title-selector-title">选择一个标题</text>
          <text class="title-selector-subtitle">AI 为您的日记生成了以下标题，请选择一个您喜欢的</text>
        </view>
        <view class="title-options-list">
          <view
            v-for="(title, index) in titleOptions"
            :key="index"
            class="title-option-item"
            :class="{ 'title-selected': title === selectedTitle }"
            @click="selectTitle(title)"
          >
            {{ title }}
          </view>
        </view>
        <view class="title-selector-actions">
          <view class="title-confirm-btn" @click="confirmTitle">确认选择</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import dayjs from 'dayjs'
import { getTagListApi, addDiaryApi, getDiaryApi } from '@/api/memo'
import { renderMarkdown, toolGlobalData, getUrlParams, toolCloudSync } from '@/utils/tools'
import db from '@/api/database/index'
import request from '@/utils/request'
import { syncToServer } from '@/api/syncServer'

// 获取路由参数
const params = getUrlParams()
const diaryId = params.id
const date = params.date
const currentDate = ref(date || dayjs().format('YYYY-MM-DD'))

// 页面数据
const tags = ref([])
const selectedTags = ref([])
const content = ref('')
const tagIds = ref([]) // 存储标签 ID，用于保存
const isProcessing = ref(false) // 处理状态标记
// 标题相关变量
const titleOptions = ref([]) // 存储 AI 生成的标题选项
const selectedTitle = ref('') // 存储用户选中的标题
const showTitleSelector = ref(false) // 控制标题选择弹窗显示
const savedDiaryId = ref('') // 存储已保存的日记 ID，用于后续更新标题

// 获取标签列表
const getTagList = async () => {
  try {
    const res = await getTagListApi()
    tags.value = res
    return res
  } catch (error) {
    console.error('获取标签列表失败', error)
    uni.showToast({
      title: '获取标签列表失败',
      icon: 'none',
    })
    return []
  }
}

// 切换标签选择状态
const toggleTag = (tagContent) => {
  const tagIndex = selectedTags.value.indexOf(tagContent)
  if (tagIndex === -1) {
    // 添加标签
    selectedTags.value.push(tagContent)
    // 找到对应的标签 ID
    const tag = tags.value.find((t) => t.content === tagContent)
    if (tag && !tagIds.value.includes(tag._id)) {
      tagIds.value.push(tag._id)
    }
  } else {
    // 移除标签
    selectedTags.value.splice(tagIndex, 1)
    // 移除对应的标签 ID
    const tag = tags.value.find((t) => t.content === tagContent)
    if (tag) {
      const idIndex = tagIds.value.indexOf(tag._id)
      if (idIndex !== -1) {
        tagIds.value.splice(idIndex, 1)
      }
    }
  }
}

// 长按标签处理
const handleLongPress = (tag) => {
  uni.showModal({
    title: '标签操作',
    editable: true,
    content: tag.content,
    placeholderText: '请输入新标签内容',
    showCancel: true,
    cancelText: '删除',
    confirmText: '保存',
    success: async ({ confirm, cancel, content }) => {
      if (cancel) {
        // 删除标签
        await db.table('memo').delete(tag._id)
        await getTagList()
        // 清除已选中的已删除标签
        const index = selectedTags.value.indexOf(tag.content)
        if (index !== -1) {
          selectedTags.value.splice(index, 1)
          const idIndex = tagIds.value.indexOf(tag._id)
          if (idIndex !== -1) {
            tagIds.value.splice(idIndex, 1)
          }
        }
      }
      if (confirm && content && content !== tag.content) {
        // 更新标签
        await db.table('memo').update(tag._id, { content })
        await getTagList()
        // 更新已选中的标签
        const index = selectedTags.value.indexOf(tag.content)
        if (index !== -1) {
          selectedTags.value[index] = content
        }
      }
    },
  })
}

// 新增标签对话框
const openAddTagDialog = () => {
  console.log('openAddTagDialog')
  uni.showModal({
    title: '新建标签',
    editable: true,
    placeholderText: '请输入标签内容',
    success(res) {
      const { confirm, content } = res
      if (confirm && content) {
        handleAddTag(content)
      }
    },
  })
}

// 处理添加标签逻辑
const handleAddTag = async (content) => {
  try {
    const existingTag = tags.value.find((tag) => tag.content === content)
    if (!existingTag) {
      // 添加新标签到数据库
      const tagData = { content, type: 'tag' }
      const tagId = await db.table('memo').add(tagData)

      // 重新获取标签列表
      await getTagList()

      // 自动选中新创建的标签
      selectedTags.value.push(content)
      tagIds.value.push(tagId)

      uni.showToast({ title: '添加成功', icon: 'success' })
    } else {
      uni.showToast({ title: '标签已存在', icon: 'none' })
    }
  } catch (error) {
    console.error('添加标签失败', error)
    uni.showToast({ title: '添加失败', icon: 'error' })
  }
}

// 选择标题
const selectTitle = (title) => {
  selectedTitle.value = title
}

// 确认选择标题
const confirmTitle = async () => {
  try {
    // 更新日记标题
    await db.table('memo').update(savedDiaryId.value, {
      title: selectedTitle.value,
    })

    uni.showToast({
      title: '标题已更新',
      icon: 'success',
    })

    // 关闭标题选择器
    showTitleSelector.value = false

    // 返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1000)
  } catch (error) {
    console.error('更新标题失败', error)
    uni.showToast({
      title: '更新标题失败',
      icon: 'none',
    })
  }
}

// 提交日记
const onSubmit = async () => {
  if (!content.value.trim()) {
    uni.showToast({
      title: '请输入日记内容',
      icon: 'none',
    })
    return
  }

  if (isProcessing.value) {
    return // 如果正在处理中，不执行任何操作
  }

  try {
    isProcessing.value = true // 开始处理，更新按钮状态

    // 构建基础日记数据（不包含 AI 分析）
    const diaryData = {
      content: content.value,
      date: currentDate.value,
      type: 'diary',
      tagsList: JSON.stringify(tagIds.value),
    }

    // 先保存用户输入内容到数据库
    let tempDiaryId = diaryId
    if (diaryId) {
      await db.table('memo').update(diaryId, diaryData)
    } else {
      tempDiaryId = await db.table('memo').add(diaryData)
    }
    savedDiaryId.value = tempDiaryId // 保存日记 ID 用于后续更新

    // 调用工作流 API 进行 AI 分析（不等待结果）
    workflowApi('7484172363748130835', {
      content: content.value,
      question: selectedTags.value,
      id: savedDiaryId.value,
      tags: tags.value,
    }).catch(error => console.error('AI 分析失败', error)) // 错误处理，但不阻止返回
    
    // 显示保存成功提示
    uni.showToast({
      title: '内容已保存，AI 分析中...',
      icon: 'success',
      duration: 1500,
    })
    
    // 延迟 1.5 秒后返回，让用户看到提示
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    console.error('保存日记失败', error)
    uni.showToast({ title: '保存失败', icon: 'error' })
    isProcessing.value = false // 恢复按钮状态
  }
}

// 工作流 API
const workflowApi = async (workflow_id, params) => {
  try {
    // 获取应用程序实例和 token
    const app = getApp()
    const token = app.globalData?.kzToken || toolGlobalData.get('kzToken')

    if (!token) {
      throw new Error('未找到 kzToken')
    }

    const res = await request.post(
      'https://api.coze.cn/v1/workflow/run',
      {
        workflow_id,
        app_id: '7484161211752431679',
        parameters: params,
      },
      {
        timeout: 300000,
        header: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    )
    return {
      ...res,
      data: JSON.parse(res.data),
    }
  } catch (error) {
    console.error('工作流 API 调用失败', error)
    throw error
  }
}

// 加载日记数据
const loadDiaryData = async () => {
  if (!diaryId) return

  try {
    const diary = await getDiaryApi(currentDate.value, diaryId)
    if (diary) {
      content.value = diary.content || ''
      currentDate.value = diary.date || dayjs().format('YYYY-MM-DD')

      // 处理标签
      if (diary.tagsList) {
        try {
          const tagsList = JSON.parse(diary.tagsList)
          tagIds.value = tagsList || []

          // 根据标签 ID 找到对应的标签内容
          for (const id of tagIds.value) {
            const tag = tags.value.find((t) => t._id === id)
            if (tag && !selectedTags.value.includes(tag.content)) {
              selectedTags.value.push(tag.content)
            }
          }
        } catch (e) {
          console.error('解析标签列表失败', e)
        }
      }
    }
  } catch (error) {
    console.error('加载日记数据失败', error)
    uni.showToast({
      title: '加载日记数据失败',
      icon: 'none',
    })
  }
}

// 生命周期钩子
onMounted(async () => {
  await getTagList()
  await loadDiaryData()
})
</script>

<style lang="scss" scoped>
.diary-edit-page {
  min-height: 100vh;
  background-color: var(--color-gray-50);
  padding-bottom: calc(var(--window-bottom) + 40rpx);
}

.content-area {
  padding: 30rpx;
}

// 分区标题样式
.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--color-gray-600);
  margin-bottom: 20rpx;
  display: block;
  cursor: pointer;
}

// 标签区域样式
.tag-section {
  margin-bottom: 40rpx;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-item {
  padding: 16rpx 24rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  background-color: var(--color-gray-100);
  color: var(--color-gray-600);
  transition: all 0.3s ease;
  cursor: pointer;

  &.tag-selected {
    background-color: var(--color-primary);
    color: var(--color-white);
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.tag-add {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 70rpx;
    min-height: 70rpx;
    cursor: pointer;
    background-color: var(--color-gray-200);

    &:active {
      background-color: var(--color-gray-300);
      transform: scale(0.98);
    }
  }
}

.add-icon {
  font-weight: bold;
  font-size: 36rpx;
  /* 增大加号大小 */
  line-height: 1;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 编辑器区域样式
.editor-section {
  margin-bottom: 40rpx;
}

.editor-wrapper {
  background-color: var(--color-white);
  border-radius: 16rpx;
  padding: 4rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.diary-editor {
  width: 100%;
  min-height: 400rpx;
  padding: 30rpx;
  font-size: 28rpx;
  line-height: 1.8;
  border: none;
  border-radius: 16rpx;
  background-color: transparent;
  box-sizing: border-box;
}

// 按钮区域
.button-area {
  padding: 20rpx 0;
  display: flex;
  justify-content: center;
}

.save-btn {
  background: linear-gradient(to right, #4f46e5, #6366f1);
  color: #ffffff;
  padding: 24rpx 0;
  border-radius: 50rpx;
  width: 80%;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
  }

  &.processing {
    background: linear-gradient(to right, #4f46e5, #6366f1);
    cursor: not-allowed;
    transform: none;
    animation: pulse 1.5s infinite ease-in-out;

    &:active {
      transform: none;
    }
  }
}

.processing-text {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.dots-container {
  display: inline-flex;
  margin-left: 4rpx;
}

.dot {
  font-size: 32rpx;
  line-height: 1;
  opacity: 0;
}

.dot1 {
  animation: dotFade 1.5s infinite;
  animation-delay: 0s;
}

.dot2 {
  animation: dotFade 1.5s infinite;
  animation-delay: 0.3s;
}

.dot3 {
  animation: dotFade 1.5s infinite;
  animation-delay: 0.6s;
}

@keyframes dotFade {
  0%,
  80%,
  100% {
    opacity: 0;
  }
  30%,
  50% {
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

// 标题选择弹窗样式
.title-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.title-selector-container {
  background-color: var(--color-white);
  padding: 40rpx;
  border-radius: 16rpx;
  width: 80%;
  max-height: 80%;
  overflow-y: auto;
}

.title-selector-header {
  margin-bottom: 20rpx;
}

.title-selector-title {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--color-gray-600);
}

.title-selector-subtitle {
  font-size: 24rpx;
  color: var(--color-gray-500);
}

.title-options-list {
  margin-bottom: 20rpx;
}

.title-option-item {
  padding: 16rpx 24rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  background-color: var(--color-gray-100);
  color: var(--color-gray-600);
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 10rpx;

  &.title-selected {
    background-color: var(--color-primary);
    color: var(--color-white);
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.title-selector-actions {
  display: flex;
  justify-content: flex-end;
}

.title-confirm-btn {
  background: linear-gradient(to right, #4f46e5, #6366f1);
  color: #ffffff;
  padding: 24rpx 40rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  cursor: pointer;
}
</style>

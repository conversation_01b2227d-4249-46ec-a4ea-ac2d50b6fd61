// 云对象教程：https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc 语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129

// 导入功能模块
const createAuthManager = require('./modules/authManager.js')
const createTaskManager = require('./modules/taskManager.js')
const createProjectManager = require('./modules/projectManager.js')
const { API_CONFIG } = require('./config.js')

/**
 * 滴答清单 API 云对象
 * 提供滴答清单的完整 API 功能，包括认证、任务管理、项目管理等
 * 采用模块化架构，符合 uniCloud 云对象开发规范
 */
module.exports = {
	_before: function () {
		// ... (保留 _before 逻辑不变)
		const clientInfo = this.getClientInfo()
		const methodName = this.getMethodName()

		if (!methodName) {
			console.error('错误：方法名为空。请确保使用 uniCloud.importObject() 而不是 uniCloud.callFunction() 来调用云对象')
			throw new Error(
				'Method name is required. Please use uniCloud.importObject() instead of uniCloud.callFunction() to call cloud objects.'
			)
		}

		console.log(`[Dida API] [${new Date().toISOString()}] 调用方法：${methodName}, 客户端 IP：${clientInfo.clientIP}`)

		this.BASE_URL = API_CONFIG.BASE_URL
		this.token = null
		this.headers = {}
		this.debug = true

		console.log('[Dida API] 初始化功能模块...');
		this.authManager = createAuthManager(this)
		this.taskManager = createTaskManager(this, this.authManager)
		this.projectManager = createProjectManager(this, this.authManager)
		console.log('[Dida API] 功能模块初始化完成。');

		if (this.debug) {
			const debugToken =
        '73AE2E6CC13DD9673F421A1F3E02AED0E1BFB595FD663AFA63ED00682C85E0350ECBA76C0D9169C1842C895EC3C7FD43FA4BB3D094DAFA93E6FC18AA49B4F5302701265667560665A0D14835FCC55972EB9036F52182EC2D6CFEC251B6B3AD83385AA04082B6E13207380EE6E17F65D7D02746F0B1CB9D088DFB1EDE0D3D45D112B6963F72E74B8898CEFB2AD56ED90B75338A509771CA53C093C355F178EA86151002FFD8A51141ED48EB889B07BD4E' // 请替换为你的有效 token
			console.warn(`[Dida API] [DEBUG] 调试模式已开启，将使用固定 token 初始化...`);
			this.authManager.initWithToken(debugToken)
		}
	},

	_after: function(error, result) {
		// ... (保留 _after 逻辑不变)
		if (error) {
			console.error(`[Dida API] [${new Date().toISOString()}] 方法 ${this.getMethodName()} 调用异常:`, error);
		} else {
			if (this.debug) {
				console.log(`[Dida API] [${new Date().toISOString()}] 方法 ${this.getMethodName()} 调用成功。`);
			}
		}
	},

	// ==================== 认证相关方法 ====================

	/**
	 * 用户登录认证
	 * @param {object} options - 登录参数对象
	 * @param {string} options.username - 用户名（手机号或邮箱）
	 * @param {string} options.password - 用户密码
	 * @param {boolean} [options.isPhone=true] - 是否使用手机号登录
	 * @returns {object} 登录结果
	 */
	async login(options = {}) {
		const { username, password, isPhone = true } = options;
		console.log(`[Dida API] [login] - 开始登录，用户名：${username}, 登录方式：${isPhone ? '手机' : '邮箱'}`);
		return await this.authManager.login(username, password, isPhone)
	},

	/**
	 * 初始化 API（使用已有 token）
	 * @param {string} token - 访问令牌
	 * @returns {object} 初始化结果
	 */
	async initWithToken(token) {
		console.log(`[Dida API] [initWithToken] - 使用已有 token 初始化 API...`);
		return await this.authManager.initWithToken(token)
	},

	/**
	 * 获取所有基础数据（任务、项目、标签）
	 * @returns {object} 基础数据
	 */
	async getBatchData() {
		console.log(`[Dida API] [getBatchData] - 开始获取所有基础数据...`);
		return await this.authManager.getBatchData()
	},

	// ==================== 任务管理方法 ====================

	/**
	 * 获取任务列表
	 * @param {object} [options={}] - 查询参数对象
	 * @returns {object} 任务列表
	 */
	async getTasks(options = {}) {
		console.log(`[Dida API] [getTasks] - 获取任务列表，参数:`, options);
		return await this.taskManager.getTasks(options)
	},

	/**
	 * 创建任务
	 * @param {object} options - 任务数据对象
	 * @returns {object} 创建结果
	 */
	async createTask(options = {}) {
		console.log(`[Dida API] [createTask] - 创建新任务，标题：${options.title}`);
		return await this.taskManager.createTask(options)
	},

	/**
	 * 更新任务
	 * @param {string} taskId - 任务 ID
	 * @param {object} updateData - 更新数据
	 * @returns {object} 更新结果
	 */
	async updateTask(taskId, updateData) {
		console.log(`[Dida API] [updateTask] - 更新任务，ID: ${taskId}`, { updateData });
		return await this.taskManager.updateTask(taskId, updateData)
	},

	/**
	 * 删除任务
	 * @param {string} taskId - 任务 ID
	 * @returns {object} 删除结果
	 */
	async deleteTask(taskId) {
		console.log(`[Dida API] [deleteTask] - 删除任务，ID: ${taskId}`);
		return await this.taskManager.deleteTask(taskId)
	},

	/**
	 * 完成任务
	 * @param {string} taskId - 任务 ID
	 * @returns {object} 完成结果
	 */
	async completeTask(taskId) {
		console.log(`[Dida API] [completeTask] - 完成任务，ID: ${taskId}`);
		return await this.taskManager.completeTask(taskId)
	},

	/**
	 * 取消完成任务
	 * @param {string} taskId - 任务 ID
	 * @returns {object} 取消完成结果
	 */
	async uncompleteTask(taskId) {
		console.log(`[Dida API] [uncompleteTask] - 取消完成任务，ID: ${taskId}`);
		return await this.taskManager.uncompleteTask(taskId)
	},

	/**
	 * 获取单个任务详情
	 * @param {string} taskId - 任务 ID
	 * @returns {object} 任务详情
	 */
	async getTask(taskId) {
		console.log(`[Dida API] [getTask] - 获取单个任务详情，ID: ${taskId}`);
		return await this.taskManager.getTask(taskId)
	},

	/**
	 * 批量操作任务
	 * @param {object} options - 操作参数对象
	 * @returns {object} 批量操作结果
	 */
	async batchOperateTasks(options = {}) {
		console.log(`[Dida API] [batchOperateTasks] - 批量操作任务，操作：${options.action}，任务 ID:`, options.taskIds);
		return await this.taskManager.batchOperateTasks(options)
	},

	// ==================== 项目管理方法 ====================

	/**
	 * 获取项目列表
	 * @param {object} [options={}] - 查询参数对象
	 * @returns {object} 项目列表
	 */
	async getProjects(options = {}) {
		console.log(`[Dida API] [getProjects] - 获取项目列表，参数:`, options);
		return await this.projectManager.getProjects(options)
	},

	/**
	 * 创建项目
	 * @param {object} options - 项目数据对象
	 * @returns {object} 创建结果
	 */
	async createProject(options = {}) {
		console.log(`[Dida API] [createProject] - 创建新项目，名称：${options.name}`);
		return await this.projectManager.createProject(options)
	},

	/**
	 * 更新项目
	 * @param {string} projectId - 项目 ID
	 * @param {object} updateData - 更新数据
	 * @returns {object} 更新结果
	 */
	async updateProject(projectId, updateData) {
		console.log(`[Dida API] [updateProject] - 更新项目，ID: ${projectId}`, { updateData });
		return await this.projectManager.updateProject(projectId, updateData)
	},

	/**
	 * 删除项目
	 * @param {string} projectId - 项目 ID
	 * @returns {object} 删除结果
	 */
	async deleteProject(projectId) {
		console.log(`[Dida API] [deleteProject] - 删除项目，ID: ${projectId}`);
		return await this.projectManager.deleteProject(projectId)
	},

	/**
	 * 关闭项目
	 * @param {string} projectId - 项目 ID
	 * @returns {object} 关闭结果
	 */
	async closeProject(projectId) {
		console.log(`[Dida API] [closeProject] - 关闭项目，ID: ${projectId}`);
		return await this.projectManager.closeProject(projectId)
	},

	/**
	 * 重新打开项目
	 * @param {string} projectId - 项目 ID
	 * @returns {object} 重新打开结果
	 */
	async reopenProject(projectId) {
		console.log(`[Dida API] [reopenProject] - 重新打开项目，ID: ${projectId}`);
		return await this.projectManager.reopenProject(projectId)
	},

	/**
	 * 获取单个项目详情
	 * @param {string} projectId - 项目 ID
	 * @returns {object} 项目详情
	 */
	async getProject(projectId) {
		console.log(`[Dida API] [getProject] - 获取单个项目详情，ID: ${projectId}`);
		return await this.projectManager.getProject(projectId)
	},
}

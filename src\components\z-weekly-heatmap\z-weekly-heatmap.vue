<template>
  <view class="heatmap-container p-4">
    <view class="flex justify-between items-center mb-4">
      <text class="text-lg font-bold">{{ currentYear }}年周历</text>
      <view class="color-legend flex gap-1">
        <view 
          v-for="(color, index) in colorScale"
          :key="index"
          class="h-4 w-6 rounded-sm"
          :style="{ backgroundColor: color }">
        </view>
      </view>
    </view>
    
    <view class="grid grid-cols-13 gap-1">
      <view
        v-for="(week, index) in weeksData"
        :key="index"
        class="aspect-square rounded-sm transition-colors relative"
        :style="{
          backgroundColor: colorScale[week.value],
          cursor: 'pointer'
        }"
        @click="handleWeekClick(week)">
        <text class="absolute top-1 left-1 text-xs font-medium" 
              :class="[ 
                { 'current-week': week.weekNumber === currentWeek.value },
                { 'text-white': week.value > 1, 'text-gray-900': week.value <= 1 }
              ]">
          {{ week.weekNumber }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import dayjs from 'dayjs'
import weekOfYear from 'dayjs/plugin/weekOfYear'

dayjs.extend(weekOfYear)

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['week-selected'])

const currentYear = ref(dayjs().year())

// 生成 52 周数据（演示用随机数据）
const weeksData = computed(() => {
  return Array.from({ length: 52 }, (_, weekIndex) => ({
    weekNumber: weekIndex + 1,
    value: props.data[weekIndex] || Math.floor(Math.random() * 5)
  }))
})

const currentWeek = computed(() => dayjs().week())

// 颜色梯度
const colorScale = ['#ebedf0', '#9be9a8', '#40c463', '#30a14e', '#216e39']

const handleWeekClick = (week) => {
  emit('week-selected', {
    year: currentYear.value,
    week: week.weekNumber
  })
}
</script>

<style lang="scss" scoped>
.heatmap-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.color-legend {
  background: rgba(0,0,0,0.05);
  padding: 4px;
  border-radius: 4px;
}

.current-week {
  box-shadow: 0 0 0 2px #3b82f6;
  z-index: 1;
}
</style>
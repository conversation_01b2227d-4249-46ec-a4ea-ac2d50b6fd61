# 用户登录时本地数据处理需求

## 背景
系统采用本地数据库与云端数据库双重存储架构，支持以下功能：
- 离线状态下可在本地数据库操作
- 在线状态下可将数据同步至云端
- 支持多设备数据同步

## 问题描述
用户在未登录状态下可能已在本地产生数据，登录后需要解决本地数据与账号云端数据的处理问题。

## 当前实现（v1 版本）
1. 登录检测：系统检测用户登录前是否有本地数据
2. 数据处理方式：
   - 保留云端：登录后仅使用账号下的云端数据，放弃本地数据
3. 用户提示：登录后通过弹窗提示用户本地数据将被云端数据覆盖
4. 操作流程：用户登录成功后，系统自动使用云端数据覆盖本地数据

## 预期效果
用户登录后收到明确提示，了解本地数据将被云端数据覆盖的情况，确保用户对数据变更有清晰认知。

## 后续规划
未来版本将考虑增加更多数据处理选项：
- 合并数据：将本地数据与云端数据进行智能合并
- 保留本地：将本地数据上传并覆盖云端数据

# 技术方案

## 登录流程改造

### 1. 登录成功时数据处理

在用户登录成功后，通过以下步骤处理本地与云端数据：

```javascript
// 登录成功后的处理函数
async function handleLoginSuccess(userInfo) {
  try {
    // 检查本地是否有数据
    const hasLocalData = await checkLocalData();
    
    if (hasLocalData) {
      // 提示用户本地数据将被覆盖
      uni.showModal({
        title: '数据同步提示',
        content: '检测到您本地已有数据，登录后将使用云端数据覆盖本地数据，是否继续？',
        confirmText: '继续登录',
        cancelText: '取消登录',
        success: async (res) => {
          if (res.confirm) {
            // 用户确认继续，执行数据覆盖操作
            await resetLocalDBAndSyncFromCloud(userInfo);
          } else {
            // 用户取消，退出登录状态
            await logout();
          }
        }
      });
    } else {
      // 本地无数据，直接同步云端数据
      await syncFromCloud(userInfo);
    }
  } catch (error) {
    console.error('登录数据处理出错：', error);
    uni.showToast({
      title: '数据同步失败',
      icon: 'error'
    });
  }
}
```

### 2. 本地数据检测方法

```javascript
/**
 * 检查本地是否有数据
 * @returns {Promise<boolean>} 是否有本地数据
 */
async function checkLocalData() {
  // 遍历所有表，检查是否有数据
  for (const tableName in tableSchema) {
    const count = await db.table(tableName).count();
    if (count > 0) {
      return true;
    }
  }
  return false;
}
```

### 3. 数据覆盖实现

```javascript
/**
 * 重置本地数据库并从云端同步数据
 * @param {Object} userInfo 用户信息
 */
async function resetLocalDBAndSyncFromCloud(userInfo) {
  uni.showLoading({
    title: '同步数据中...'
  });
  
  try {
    // 1. 清除同步时间戳，确保获取全量云端数据
    uni.removeStorageSync('lastSyncTime');
    
    // 2. 重置本地数据库（删除所有表中的数据）
    await resetLocalDatabase();
    
    // 3. 从云端拉取数据
    const cloudData = await fetchCloudData(userInfo.uid);
    
    // 4. 将云端数据写入本地
    await saveCloudDataToLocal(cloudData);
    
    // 5. 更新最后同步时间
    uni.setStorageSync('lastSyncTime', new Date().toISOString());
    
    uni.hideLoading();
    uni.showToast({
      title: '数据同步完成',
      icon: 'success'
    });
    
    // 6. 通知应用内其他页面数据已更新
    uni.$emit('updateCloud', { isRefresh: true });
  } catch (error) {
    uni.hideLoading();
    console.error('数据重置同步失败：', error);
    uni.showToast({
      title: '同步失败，请重试',
      icon: 'error'
    });
  }
}

/**
 * 重置本地数据库
 */
async function resetLocalDatabase() {
  // 方式 1：彻底删除并重建数据库
  await db.delete();
  
  // 方式 2：保留数据库结构，仅清空数据
  // for (const tableName in tableSchema) {
  //   await clearTable(tableName);
  // }
}

/**
 * 从云端获取数据
 * @param {string} uid 用户 ID
 * @returns {Promise<Object>} 云端数据
 */
async function fetchCloudData(uid) {
  const data = {};
  
  // 遍历所有表，从云端获取数据
  for (const tableName in tableSchema) {
    // 调用云函数获取该表的所有用户数据
    const result = await request({
      url: `/api/sync/${tableName}`,
      method: 'GET',
      data: { uid }
    });
    
    if (result && result.data) {
      data[tableName] = result.data;
    }
  }
  
  return data;
}

/**
 * 将云端数据保存至本地数据库
 * @param {Object} cloudData 云端数据
 */
async function saveCloudDataToLocal(cloudData) {
  const promises = [];
  
  for (const tableName in cloudData) {
    if (cloudData[tableName] && cloudData[tableName].length > 0) {
      // 使用 bulkPut 批量写入数据，标记为云端数据
      promises.push(
        db.table(tableName).bulkPut(cloudData[tableName], { isCloudData: true })
      );
    }
  }
  
  await Promise.all(promises);
}
```

## 登录页面集成

在现有登录页面中集成数据处理逻辑：

```javascript
// 在 login-withpwd.vue 或其他登录页面的登录成功回调中
loginSuccess(e) {
  // 先处理数据同步问题
  handleLoginSuccess(e.userInfo).then(() => {
    // 数据处理完成后，再执行标准的登录成功流程
    this.mutations.loginSuccess({
      ...e,
      autoBack: false // 暂不自动返回，等数据处理完成
    });
    
    // 数据处理完成后再跳转页面
    this.mutations.loginBack({
      uniIdRedirectUrl: e.uniIdRedirectUrl
    });
  }).catch(error => {
    console.error('登录数据处理出错：', error);
  });
}
```

## 技术实现要点

1. **性能优化**：
   - 使用批量操作接口减少数据库操作次数
   - 仅在有本地数据时才弹窗提示用户

2. **安全保障**：
   - 数据操作过程中使用 try-catch 确保异常处理
   - 操作失败时提供清晰错误反馈
   - 保留数据库结构确保应用稳定性

3. **用户体验**：
   - 数据同步过程中显示加载提示
   - 操作结果以 Toast 方式提示用户
   - 确保用户能理解并确认数据覆盖操作

4. **兼容性处理**：
   - 同时兼容 H5 和 APP 环境
   - 使用条件编译确保不同平台逻辑正确

5. **错误恢复**：
   - 同步失败时支持重试机制
   - 严重错误时提供回退选项

此方案确保用户在登录时能清晰了解数据处理方式，并在未来版本中为更灵活的数据处理方式做好准备。
<template>
  <view class="task-confirm-card">
    <view class="card-header">
      <view class="header-title-wrapper">
        <i class="fas fa-tasks"></i>
        <text class="header-title">创建任务确认</text>
      </view>
      <text class="header-subtitle">我理解您想创建以下任务：</text>
    </view>
    <view class="task-content">
      <text class="task-text">{{ recognizedContent }}</text>
    </view>
    <view class="card-actions">
      <view class="action-btn confirm-btn" @click="handleConfirm">
        <i class="fas fa-check"></i>
        <text>创建任务</text>
      </view>
      <view class="action-btn cancel-btn" @click="handleCancel">
        <i class="fas fa-times"></i>
        <text>取消</text>
      </view>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  recognizedContent: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['confirm', 'cancel'])

const handleConfirm = () => {
  emit('confirm', props.recognizedContent)
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.task-confirm-card {
  background-color: transparent;
  padding: 0;
  box-shadow: none;
  border: none;

  .card-header {
    margin-bottom: 12px;

    .header-title-wrapper {
      display: flex;
      align-items: center;
      margin-bottom: 4px;

      i {
        color: var(--color-primary);
        font-size: 16px;
        margin-right: 6px;
      }
    }

    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--color-primary);
    }

    .header-subtitle {
      display: block;
      font-size: 14px;
      color: #666;
      margin-left: 22px;
    }
  }

  .task-content {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
    border: 1px solid #eaeaea;
    position: relative;

    &::before {
      content: '"';
      position: absolute;
      top: 2px;
      left: 8px;
      font-size: 20px;
      color: #ccc;
      font-family: sans-serif;
    }

    &::after {
      content: '"';
      position: absolute;
      bottom: 2px;
      right: 8px;
      font-size: 20px;
      color: #ccc;
      font-family: sans-serif;
    }

    .task-text {
      font-size: 14px;
      color: #333;
      line-height: 1.5;
      word-break: break-word;
      white-space: pre-wrap;
      padding: 0 6px;
    }
  }

  .card-actions {
    display: flex;
    justify-content: space-between;
    gap: 12px;

    .action-btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      padding: 10px 0;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;

      i {
        font-size: 14px;
      }
    }

    .confirm-btn {
      background-color: var(--color-primary-light);
      color: white;

      &:active {
        opacity: 0.9;
        transform: scale(0.98);
      }
    }

    .cancel-btn {
      background-color: #f5f5f5;
      color: #666;

      &:active {
        background-color: #eaeaea;
        transform: scale(0.98);
      }
    }
  }
}
</style>

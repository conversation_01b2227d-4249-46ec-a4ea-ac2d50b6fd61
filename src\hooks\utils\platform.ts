/**
 * 获取当前运行平台信息
 * 注: 此文件不使用条件编译，因为需要在运行时判断平台
 */
export const getPlatformInfo = () => {
  let isH5 = false
  // #ifdef H5
  isH5 = true
  // #endif

  let isApp = false
  // #ifdef APP-PLUS
  isApp = true
  // #endif

  return {
    isH5,
    isApp,
    platform: isH5 ? 'h5' : isApp ? 'app' : 'unknown',
  }
}

/**
 * 检查当前环境是否支持录音功能
 */
export const checkRecordSupport = () => {
  const { isH5, isApp } = getPlatformInfo()

  if (isH5) {
    // 检查 H5 环境的 mediaDevices API 支持
    return Boolean(navigator && navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
  }

  if (isApp) {
    // App环境下假设总是支持
    return true
  }

  // 其他平台暂不支持
  return false
}

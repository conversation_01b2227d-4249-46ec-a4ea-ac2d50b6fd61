<template>
  <BaseSvgIcon :path="path" :viewBox="viewBox" v-bind="$attrs" />
</template>

<script setup>
import BaseSvgIcon from './BaseSvgIcon.vue'

defineProps({
  viewBox: {
    type: String,
    default: '0 0 1024 1024',
  },
  path: {
    type: String,
    default:
      'M640 192H384l-94.8-142.2c-14.2-21.4 1-49.8 26.6-49.8h392.4c25.6 0 40.8 28.4 26.6 49.8L640 192z m-256 64h256c7.6 5 16.2 10.6 26 16.8 113.4 72.6 358 229 358 559.2 0 106-86 192-192 192H192c-106 0-192-86-192-192 0-330.2 244.6-486.6 358-559.2 9.6-6.2 18.4-11.8 26-16.8z m168 176c0-22-18-40-40-40s-40 18-40 40v28c-15.2 3.4-30.4 8.8-44.4 17-27.8 16.6-51.8 45.6-51.6 87.8 0.2 40.6 24 66.2 49.4 81.4 22 13.2 49.4 21.6 71.2 28l3.4 1c25.2 7.6 43.6 13.6 56 21.4 10.2 6.4 11.6 10.8 11.8 16.4 0.2 10-3.6 16-11.8 21-10 6.2-25.8 10-42.8 9.4-22.2-0.8-43-7.8-70.2-17-4.6-1.6-9.4-3.2-14.4-4.8-21-7-43.6 4.4-50.6 25.2s4.4 43.6 25.2 50.6c3.8 1.2 8 2.6 12.2 4.2 16.6 5.8 35.8 12.4 56.4 16.8v29.2c0 22 18 40 40 40s40-18 40-40V820c16-3.4 32-9 46.4-18 28.6-17.8 50.2-48.2 49.6-90-0.6-40.6-23.4-66.8-49.2-83.2-23-14.4-51.8-23.2-74.2-30l-1.4-0.4c-25.6-7.8-43.8-13.4-56.6-21-10.4-6.2-10.6-9.8-10.6-13.4 0-7.4 2.8-13 12.4-18.6 10.8-6.4 27.2-10.2 43-10 19.2 0.2 40.4 4.4 62.4 10.4 21.4 5.6 43.2-7 49-28.4s-7-43.2-28.4-49c-13-3.4-27.4-6.8-42.2-9.4v-27.8z',
  },
})
</script>

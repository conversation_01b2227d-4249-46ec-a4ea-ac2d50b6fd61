# V1.1 - 执行上下文管理引入

## 1. 改动概述

### 1.1 核心目标
- 引入执行上下文管理器，参考MCP的上下文管理模式
- 实现基础的步骤间数据传递能力
- 支持简单的工具函数调用和执行计划
- 为V1.2版本的动态参数解析做技术铺垫

### 1.2 解决的问题
- **当前问题**：意图识别后仍无法执行实际的任务操作
- **解决方案**：实现工具调用机制和执行上下文管理
- **技术提升**：从纯意图识别升级为具备执行能力的智能助手

### 1.3 基于V1.0的改进
- 利用V1.0建立的工具注册表
- 在现有参数验证基础上增加工具调用
- 保持向后兼容，不影响现有功能

## 2. 技术方案

### 2.1 执行上下文管理器（参考MCP设计）

```javascript
// 新增：执行上下文管理器
class ExecutionContextManager {
  constructor(sessionId, userInput) {
    this.sessionId = sessionId
    this.userInput = userInput
    this.stepResults = new Map() // 存储每步的执行结果
    this.contextData = new Map()  // 存储上下文数据
    this.metadata = {
      startTime: Date.now(),
      currentStep: 0,
      totalSteps: 0
    }
  }

  // 设置步骤结果
  setStepResult(stepId, result, metadata = {}) {
    this.stepResults.set(stepId, {
      result,
      metadata: {
        ...metadata,
        timestamp: Date.now(),
        stepIndex: this.metadata.currentStep
      }
    })
    
    // 自动提取关键数据到上下文
    this.extractContextData(stepId, result)
  }

  // 获取步骤结果
  getStepResult(stepId) {
    return this.stepResults.get(stepId)?.result
  }

  // 设置上下文数据
  setContextData(key, value) {
    this.contextData.set(key, value)
  }

  // 获取上下文数据
  getContextData(key) {
    return this.contextData.get(key)
  }

  // 自动提取上下文数据（借鉴MCP的智能数据提取）
  extractContextData(stepId, result) {
    // 如果是项目查询结果，自动提取目标项目
    if (result.projects && Array.isArray(result.projects)) {
      const targetProject = this.findTargetProject(result.projects)
      if (targetProject) {
        this.setContextData('targetProject', targetProject)
      }
    }
    
    // 如果是任务查询结果，提取任务统计信息
    if (result.tasks && Array.isArray(result.tasks)) {
      this.setContextData('taskCount', result.tasks.length)
      this.setContextData('uncompletedTasks', result.tasks.filter(t => !t.completed))
    }
  }

  // 智能项目筛选
  findTargetProject(projects) {
    const userInput = this.userInput.toLowerCase()
    
    // 提取关键词
    const keywords = this.extractKeywords(userInput)
    
    // 按匹配度排序
    const scored = projects.map(project => ({
      project,
      score: this.calculateMatchScore(project, keywords)
    }))
    
    scored.sort((a, b) => b.score - a.score)
    
    return scored.length > 0 && scored[0].score > 0 ? scored[0].project : null
  }

  // 提取关键词
  extractKeywords(input) {
    const keywords = []
    
    // 提取项目名称关键词
    const projectMatches = input.match(/(\w+)项目|(\w+)project/gi)
    if (projectMatches) {
      projectMatches.forEach(match => {
        const keyword = match.replace(/项目|project/gi, '')
        if (keyword) keywords.push(keyword.toLowerCase())
      })
    }
    
    return keywords
  }

  // 计算匹配分数
  calculateMatchScore(project, keywords) {
    let score = 0
    const projectName = project.name.toLowerCase()
    
    keywords.forEach(keyword => {
      if (projectName.includes(keyword)) {
        score += 1
      }
    })
    
    return score
  }
}
```

### 2.2 简单执行计划生成器

```javascript
// 新增：简单执行计划生成器
class SimpleExecutionPlanner {
  static async generatePlan(userInput, intentType) {
    const executionPlan = {
      planId: this.generateUUID(),
      userInput: userInput,
      intentType: intentType,
      steps: [],
      totalSteps: 0,
      status: 'pending',
      startTime: Date.now()
    }

    // 根据意图类型生成简单的执行计划
    switch (intentType) {
      case 'find_task':
        // 查找任务的执行计划
        if (userInput.includes('项目') || userInput.includes('project')) {
          // 需要先查项目再查任务
          executionPlan.steps = [
            {
              stepId: this.generateUUID(),
              toolName: 'getProjects',
              description: '获取项目列表',
              parameters: { filter: this.extractProjectKeyword(userInput) },
              dependencies: [],
              status: 'pending'
            },
            {
              stepId: this.generateUUID(),
              toolName: 'getTasks',
              description: '获取项目下的任务',
              parameters: { 
                projectId: '$context.targetProject.id', // 动态参数，V1.2版本实现
                completed: false 
              },
              dependencies: ['step1'],
              status: 'pending'
            }
          ]
        } else {
          // 直接查任务
          executionPlan.steps = [
            {
              stepId: this.generateUUID(),
              toolName: 'getTasks',
              description: '获取任务列表',
              parameters: { completed: false },
              dependencies: [],
              status: 'pending'
            }
          ]
        }
        break
        
      case 'create_task':
        // V1.1版本暂不实现创建功能，返回空计划
        break
        
      default:
        // chat类型不需要执行计划
        break
    }

    executionPlan.totalSteps = executionPlan.steps.length
    return executionPlan
  }

  static extractProjectKeyword(input) {
    const matches = input.match(/(\w+)项目|(\w+)project/gi)
    if (matches && matches.length > 0) {
      return matches[0].replace(/项目|project/gi, '')
    }
    return ''
  }

  static generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c == 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }
}
```

### 2.3 基础工具调用引擎

```javascript
// 新增：基础工具调用引擎
async function executeSimplePlan(executionPlan, context, sseChannel) {
  try {
    // 推送执行计划开始
    await sseChannel.write({
      type: 'execution_plan_start',
      plan: {
        planId: executionPlan.planId,
        totalSteps: executionPlan.totalSteps
      },
      timestamp: Date.now()
    })

    executionPlan.status = 'executing'

    for (let i = 0; i < executionPlan.steps.length; i++) {
      const step = executionPlan.steps[i]
      context.metadata.currentStep = i

      // 推送当前执行步骤
      await sseChannel.write({
        type: 'execution_step',
        step: {
          stepId: step.stepId,
          description: step.description,
          toolName: step.toolName
        },
        timestamp: Date.now()
      })

      step.status = 'executing'

      try {
        // V1.1版本：简单的工具调用（不支持动态参数）
        const validatedParams = ParameterValidator.validate(step.toolName, step.parameters)
        
        // 模拟工具调用（V1.1版本先返回模拟数据）
        const result = await simulateToolCall(step.toolName, validatedParams)
        
        // 存储结果到上下文
        context.setStepResult(step.stepId, result)
        
        step.status = 'completed'

        // 推送步骤执行结果
        await sseChannel.write({
          type: 'step_result',
          stepId: step.stepId,
          result: result,
          timestamp: Date.now()
        })

      } catch (error) {
        step.status = 'failed'
        step.error = error.message

        await sseChannel.write({
          type: 'step_error',
          stepId: step.stepId,
          error: error.message,
          timestamp: Date.now()
        })

        throw error
      }
    }

    executionPlan.status = 'completed'

    // 推送执行完成
    await sseChannel.write({
      type: 'execution_complete',
      plan: executionPlan,
      contextData: Array.from(context.contextData.keys()),
      timestamp: Date.now()
    })

    return executionPlan

  } catch (error) {
    executionPlan.status = 'failed'
    executionPlan.error = error.message

    await sseChannel.write({
      type: 'execution_failed',
      plan: executionPlan,
      error: error.message,
      timestamp: Date.now()
    })
    
    throw error
  }
}

// 模拟工具调用（V1.1版本使用，V1.2版本替换为真实调用）
async function simulateToolCall(toolName, parameters) {
  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  switch (toolName) {
    case 'getProjects':
      return {
        projects: [
          { id: 'proj-1', name: 'OKR项目', description: '目标管理项目' },
          { id: 'proj-2', name: '日常任务', description: '日常工作任务' }
        ],
        metadata: { total: 2, filtered: parameters.filter ? 1 : 2 }
      }
      
    case 'getTasks':
      return {
        tasks: [
          { id: 'task-1', title: '制定Q1目标', completed: false, projectId: parameters.projectId || 'proj-1' },
          { id: 'task-2', title: '更新KR进度', completed: false, projectId: parameters.projectId || 'proj-1' }
        ],
        metadata: { total: 2, projectId: parameters.projectId }
      }
      
    default:
      throw new Error(`未知的工具：${toolName}`)
  }
}
```

## 3. chatStreamSSE函数修改

### 3.1 主要修改点

```javascript
// 在意图识别完成后，新增执行计划生成和执行逻辑
async chatStreamSSE(params) {
  // ... 现有的参数处理和意图识别逻辑保持不变 ...

  // 新增：在意图识别完成后
  if (intentType && intentType !== 'chat') {
    // 创建执行上下文
    const context = new ExecutionContextManager(generateUUID(), message)
    
    // 生成执行计划
    const executionPlan = await SimpleExecutionPlanner.generatePlan(message, intentType)
    
    if (executionPlan.totalSteps > 0) {
      // 执行计划
      await executeSimplePlan(executionPlan, context, sseChannel)
      
      // 修改最终返回结果
      return {
        errCode: 0,
        errMsg: 'success',
        data: {
          type: 'task_executed',
          intentType: intentType,
          executionPlan: executionPlan,
          contextData: Array.from(context.contextData.keys())
        }
      }
    }
  }

  // ... 现有的结束逻辑保持不变 ...
}
```

## 4. 新增SSE消息类型

```javascript
// V1.1版本新增的SSE消息类型
const SSE_MESSAGE_TYPES = {
  // 现有类型保持不变
  'start': '开始生成回复',
  'intent_type': '意图类型识别',
  'intent_content_start': '意图内容开始',
  'intent_content_chunk': '意图内容块',
  'end': '结束',
  'error': '错误',
  
  // V1.1新增类型
  'execution_plan_start': '执行计划开始',
  'execution_step': '执行步骤',
  'step_result': '步骤结果',
  'step_error': '步骤错误',
  'execution_complete': '执行完成',
  'execution_failed': '执行失败'
}
```

## 5. 测试用例

### 5.1 执行上下文管理器测试
```javascript
test('执行上下文管理器', () => {
  const context = new ExecutionContextManager('session-1', '查看okr项目的任务')
  
  // 测试项目结果提取
  const projectResult = {
    projects: [{ id: 'proj-1', name: 'OKR项目' }]
  }
  context.setStepResult('step-1', projectResult)
  
  expect(context.getContextData('targetProject')).toEqual({ id: 'proj-1', name: 'OKR项目' })
})
```

### 5.2 执行计划生成测试
```javascript
test('执行计划生成', async () => {
  const plan = await SimpleExecutionPlanner.generatePlan('查看okr项目的任务', 'find_task')
  
  expect(plan.steps).toHaveLength(2)
  expect(plan.steps[0].toolName).toBe('getProjects')
  expect(plan.steps[1].toolName).toBe('getTasks')
})
```

### 5.3 工具调用测试
```javascript
test('模拟工具调用', async () => {
  const result = await simulateToolCall('getProjects', { filter: 'okr' })
  
  expect(result.projects).toBeDefined()
  expect(result.metadata).toBeDefined()
})
```

## 6. 部署指南

### 6.1 部署前准备
- 确保V1.0版本已稳定运行
- 准备V1.1版本的完整测试用例
- 设置监控指标基线

### 6.2 部署步骤
1. **代码集成**：将新增代码集成到现有index.obj.js
2. **功能测试**：验证新增的执行能力
3. **兼容性测试**：确保现有功能不受影响
4. **性能测试**：验证响应时间在可接受范围内
5. **灰度发布**：20%用户先行体验
6. **全量发布**：确认无问题后全量发布

## 7. 风险评估

### 7.1 功能风险
- **风险**：新增的执行逻辑可能影响现有意图识别
- **应对**：保持意图识别逻辑不变，只在识别后增加执行

### 7.2 性能风险
- **风险**：工具调用可能增加响应时间
- **应对**：V1.1使用模拟调用，响应时间可控

## 8. 下一版本预告

V1.2版本将在V1.1的基础上：
- 实现真实的工具函数调用（替换模拟调用）
- 支持动态参数解析（$context.key、$step.id.path）
- 实现复杂的数据筛选和处理逻辑
- 支持更复杂的执行计划和依赖关系

V1.1版本建立的执行上下文管理器将为V1.2版本的动态参数解析提供完整的数据基础。

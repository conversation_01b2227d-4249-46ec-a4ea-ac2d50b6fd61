/**
 * 格式化时间
 * 其他更多是格式化有如下:
 * yyyy:mm:dd|yyyy:mm|yyyy 年 mm 月 dd 日|yyyy 年 mm 月 dd 日 hh 时 MM 分等，可自定义组合
 *
 * 来源：uview 2.0
 */
export const formatDate = (dateTime = null, formatStr = 'yyyy-mm-dd') => {
  let date
  // 若传入时间为假值，则取当前时间
  if (!dateTime) {
    date = new Date()
  }
  // 若为 unix 秒时间戳，则转为毫秒时间戳（逻辑有点奇怪，但不敢改，以保证历史兼容）
  else if (/^\d{10}$/.test(dateTime?.toString().trim())) {
    date = new Date(dateTime * 1000)
  }
  // 若用户传入字符串格式时间戳，new Date 无法解析，需做兼容
  else if (typeof dateTime === 'string' && /^\d+$/.test(dateTime.trim())) {
    date = new Date(Number(dateTime))
  }
  // 处理平台性差异，在 Safari/Webkit 中，new Date仅支持/作为分割符的字符串时间
  // 处理 '2022-07-10 01:02:03'，跳过 '2022-07-10T01:02:03'
  else if (typeof dateTime === 'string' && dateTime.includes('-') && !dateTime.includes('T')) {
    date = new Date(dateTime.replace(/-/g, '/'))
  }
  // 其他都认为符合 RFC 2822 规范
  else {
    date = new Date(dateTime)
  }

  const timeSource = {
    y: date.getFullYear().toString(), // 年
    m: (date.getMonth() + 1).toString().padStart(2, '0'), // 月
    d: date.getDate().toString().padStart(2, '0'), // 日
    h: date.getHours().toString().padStart(2, '0'), // 时
    M: date.getMinutes().toString().padStart(2, '0'), // 分
    s: date.getSeconds().toString().padStart(2, '0'), // 秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  }

  for (const key in timeSource) {
    const [ret] = new RegExp(`${key}+`).exec(formatStr) || []
    if (ret) {
      // 年可能只需展示两位
      const beginIndex = key === 'y' && ret.length === 2 ? 2 : 0
      formatStr = formatStr.replace(ret, timeSource[key].slice(beginIndex))
    }
  }

  return formatStr
}

/**
 * @description 日期的月或日补零操作
 * @param {String} value 需要补零的值
 *
 * 来源：uview 2.0
 */
const padZero = (value) => {
  return `00${value}`.slice(-2)
}

/**
 * 获取昨天
 */
export const yesterday = (day = new Date()) => {
  if (!(day instanceof Date)) {
    day = new Date(day)
  }

  day.setTime(day.getTime() - 24 * 60 * 60 * 1000)
  let yesterday = day.getFullYear() + '-' + padZero(day.getMonth() + 1) + '-' + padZero(day.getDate())
  return yesterday
}

//获取当前路由、参数
export const getRoute = {
  params() {
    const routes = getCurrentPages() // 获取当前打开过的页面路由数组
    let curPage = routes[routes.length - 1] //获取当前页面路由
    console.log('curPage')
    console.log(JSON.stringify(curPage.$route.query))
    let queryObj = {}
    // #ifdef H5
    queryObj = curPage.$route.query
    // #endif
    // #ifndef H5
    queryObj = curPage.options
    // #endif
    for (let key in queryObj) {
      queryObj[key] = decodeURIComponent(queryObj[key])
    }
    return queryObj
  },
  path() {
    const routes = getCurrentPages() // 获取当前打开过的页面路由数组
    let curRoute = routes[routes.length - 1].route //获取当前页面路由
    return '/' + curRoute
  },
}

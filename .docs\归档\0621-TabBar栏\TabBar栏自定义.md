# TabBar 栏自定义需求文档

status: draft

## 背景

当前项目使用 uniapp 自带的 tabBar 栏，样式固定，无法进行深度自定义，与项目整体设计风格不匹配。为提升用户体验和界面一致性，需要替换为完全自定义的 tabBar 栏。

## 需求

### 功能需求

1. 移除 pages.json 中自带的 tabBar 配置
2. 创建统一的主页作为应用入口页面
3. 主页结构分为两部分：
   - 上方为可切换的内容区域
   - 下方为自定义 tabBar 栏
4. 点击 tabBar 项目时:
   - 切换显示对应页面内容
   - 修改 URL 参数反映当前选中的 tab
5. 页面刷新时，根据 URL 参数判断并显示对应 tab 页内容

### 非功能需求

1. 自定义 tabBar 设计风格需与整体 UI 保持一致
2. 页面切换过渡效果流畅
3. 保持良好性能，避免不必要的页面重新加载

## 技术方案

### 实现思路

1. **移除原生 tabBar**

   - 从 pages.json 中删除 tabBar 配置
   - 将原 tabBar 中的页面路径收集，用于后续自定义实现

2. **创建统一主页**

   - 创建新的`pages/index/index.vue`页面作为应用入口
   - 配置为 pages.json 中的首页（列表第一个）

3. **主页结构设计**

   ```vue
   <template>
     <div class="main-container">
       <!-- 内容区域 -->
       <div class="content-area">
         <component :is="currentTabComponent" v-if="currentTabComponent"></component>
       </div>

       <!-- 自定义tabBar区域 -->
       <div class="custom-tabbar">
         <div
           v-for="(tab, index) in tabs"
           :key="index"
           class="tab-item"
           :class="{ active: currentTab === tab.id }"
           @click="switchTab(tab.id)"
         >
           <i :class="tab.icon"></i>
           <span>{{ tab.text }}</span>
         </div>
       </div>
     </div>
   </template>
   ```

4. **URL 参数处理**
   - 使用查询参数`?tab=tabName`存储当前选中的 tab
   - 页面加载时检查 URL 参数确定显示内容
   - tab 切换时使用`router.replace`更新 URL

### 架构设计

```mermaid
graph TD
    A[主页 index.vue] --> B[TabBar组件]
    A --> C[内容区域]
    B --> D[Tab1]
    B --> E[Tab2]
    B --> F[Tab3]
    B --> G[Tab4]
    C --> H{URL参数解析}
    H --> I[加载对应Tab页面]
    I --> J[Tab1页面]
    I --> K[Tab2页面]
    I --> L[Tab3页面]
    I --> M[Tab4页面]
```

### 具体实现细节

1. **创建 TabBar 组件**

   - 将 TabBar 抽取为独立组件`pages/index/component/tabBar.vue`
   - 定义 tabBar 数据结构包含：tab ID、图标、文本、页面路径等

2. **页面切换逻辑**

   ```javascript
   // 使用动态组件或条件渲染实现tab页面切换
   const currentTab = ref('tab1') // 默认tab
   const tabs = [
     { id: 'okr', text: '目标', icon: 'icon-goal', component: OkrList },
     { id: 'today', text: '今天', icon: 'icon-task', component: Today },
     { id: 'analysis', text: '分析', icon: 'icon-analysis', component: DataAnalysis },
     { id: 'diary', text: '日记', icon: 'icon-diary', component: Diary },
     { id: 'setting', text: '设置', icon: 'icon-setting', component: Setting },
   ]

   // 切换tab方法
   const switchTab = (tabId) => {
     currentTab.value = tabId
     // 更新URL参数
     router.replace({
       query: { tab: tabId },
     })
   }

   // 页面加载时处理URL参数
   onMounted(() => {
     const { tab } = route.query
     if (tab && tabs.some((t) => t.id === tab)) {
       currentTab.value = tab
     }
   })
   ```

3. **组件按需加载**
   - 使用异步组件提高性能
   ```javascript
   const OkrList = () => import('@/pages/okr/okrList.vue')
   const Today = () => import('@/pages/okr/today.vue')
   // ...其他组件
   ```

## 风险评估

### 假设与未知因素

- 假设所有 tab 页面都可以作为组件被加载，无特殊初始化需求
- 未知不同页面间的状态维护需求

### 潜在风险

1. 页面切换性能问题
   - 解决方案：使用 keep-alive 缓存已加载的 tab 页面，避免重复渲染
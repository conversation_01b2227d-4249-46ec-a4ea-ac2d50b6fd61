<style lang="scss" scope>
.plus-line {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 50rpx;
  height: 6rpx;
  border-radius: 3rpx;
  background-color: #fff;
  &.heng {
    transform: translate(-50%, -50%);
  }
  &.shu {
    transform: translate(-50%, -50%) rotate(90deg);
  }
}
</style>
<template>
  <view>
    <view
      v-if="type === 'plus'"
      class="fixed right-50 color-white bg-[#64b6f7] wh-100 r-50 z-2"
      style="bottom: calc(var(--window-bottom) + 100rpx)"
    >
      <view class="plus-line heng"></view>
      <view class="plus-line shu"></view>
    </view>

    <view
      v-else
      text="white center"
      p="30rpx 20rpx"
      :style="{
        borderRadius: shapeVal,
        background: props.color || '#64b6f7',
        display: 'block',
      }"
      ><slot
    /></view>
  </view>
</template>
<script lang="ts" setup>
import { defineProps, defineEmits, watch, ref } from 'vue'
const props = withDefaults(
  defineProps<{
    type: 'primary' | 'plus'
    size: 'small' | 'default' | 'large'
    shape: 'square' | 'round' | 'circle'
    color: string
  }>(),
  {
    type: 'primary',
    size: 'default',
    shape: 'square',
    color: '',
  }
)
const emits = defineEmits('update:visible', 'onChange')

const shapeVal = computed(() => {
  if (props.shape === 'circle') {
    return '50%'
  } else if (props.shape === 'round') {
    return '80rpx'
  } else {
    return '16rpx'
  }
})
</script>

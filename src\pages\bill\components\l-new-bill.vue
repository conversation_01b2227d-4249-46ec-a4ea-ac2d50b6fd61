<template>
  <uni-popup ref="popupRef" type="bottom" background-color="rgba(0,0,0,0)" @change="onChange">
    <view class="bg-white rounded-t-2 p-4">
      <view class="flex">
        <view class="flex items-center ml-auto bg-[#eee] rounded-1 px-2 py-1" @click="calendarRef.open()">
          {{ dayjs(params.date).format('M 月 D 日') || '请选择日期' }}
          <u-icon class="ml-1" size="15" name="arrow-down-fill"></u-icon>
        </view>
      </view>
      <!-- 金额 -->
      <view class="text-60 font-medium pb-2" style="border-bottom: 1px solid #eee">
        <view>￥ {{ money }}</view>
      </view>
      <!-- 分类 -->
      <scroll-view scroll-y="true" class="h-300 mt-4">
        <view class="grid grid-cols-6">
          <view
            @click="params.category = item._id"
            :class="params.category === item._id ? 'op-100' : 'op-50'"
            class="mb-4 flex justify-center items-center flex-col"
            v-for="(item, index) in classList"
            :key="index"
          >
            <view
              :class="params.category === item._id ? 'bg-[#3eb575]' : 'bg-gray'"
              class="bg-[#3eb575] w-70 h-70 rounded-[50%] mb-2 color-white flex justify-center items-center"
            >
              {{ item.name[0] }}
            </view>
            <view class="text-20">{{ item.name }}</view>
          </view>
        </view>
      </scroll-view>
      <view class="mb-5 flex justify-between items-center">
        <!-- <view v-if="!showMarket" @click="showMarket = true" class="color-blue">添加备注</view> -->
        <u-input v-model="params.remark" type="text" placeholder="请输入备注" />
        <view @click="goExcel">批量添加</view>
      </view>
      <Keyboard
        :value="money"
        :show="true"
        :btnColor="keyboardColor"
        :isDecimal="true"
        confirmText="确定"
        @change="getNumber"
        @confirm="handleSave"
      ></Keyboard>
      <uni-calendar
        ref="calendarRef"
        :insert="false"
        :start-date="'2019-3-2'"
        :end-date="'2030-1-1'"
        @confirm="changeCalendar"
      />
      <view class="z-bottom"></view>
    </view>
  </uni-popup>
</template>

<script setup>
import Keyboard from './l-keyboard.vue'
const popupRef = ref(null)
const calendarRef = ref(null)
const showCalendar = ref(false)
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  editId: {
    type: String,
    default: '',
  },
})
const emits = defineEmits(['update:open', 'submit'])
const showMarket = ref(false)
const moneyArr = ref([])
const [params, resetParams] = useParams({
  amount: '0.00',
  category: '1',
  date: dayjs().format('YYYY-MM-DD'),
  remark: '',
})

const classList = ref([])

// 新增：监听 `open` 属性，以在弹窗打开时加载数据
watch(
  () => props.open,
  (val) => {
    resetParams()
    if (val) {
      popupRef.value.open('bottom')
      if (props.editId) {
        loadEditData(props.editId)
      }
    } else {
      popupRef.value.close()
    }
  }
)

// 新增：封装加载编辑数据的函数
const loadEditData = async (id) => {
  try {
    const res = await getBillApi(id)
    Object.assign(params, {
      amount: res.amount,
      category: res.category,
      date: res.date,
      remark: res.remark,
      type: res.type,
    })
    moneyArr.value = res.amount.toString().split('')
  } catch (error) {
    uni.showToast({
      title: '获取账单失败',
      icon: 'none',
    })
  }
}

const changeCalendar = (e) => {
  console.log(e)
  params.date = e.fulldate
}

const handleSave = async () => {
  params.amount = Number(money.value)

  if (props.editId) {
    await updateBillApi(props.editId, {
      ...params,
    }).then((res) => {
      uni.showToast({
        title: '修改成功',
        icon: 'success',
      })
    })
  } else {
    if (!params.date) {
      params.date = dayjs().format('YYYY-MM-DD HH:mm')
    }
    await addBillApi({
      ...params,
      type: 'expense',
    }).then((res) => {
      uni.showToast({
        title: '添加成功',
        icon: 'success',
      })
    })
  }
  resetParams()
  moneyArr.value = []
  emits('submit')
  popupRef.value.close()
}

const getNumber = (e) => {
  console.log(e)
  moneyArr.value = [...e]
}
const money = computed(() => {
  const str = moneyArr.value.join('')
  if (str) {
    return str
  }
  return ''
})
const goExcel = () => {
  uni.navigateTo({
    url: '/pages/bill/excel',
  })
}
onMounted(async () => {
  const categoryList = await getCategoryApi()
  classList.value = categoryList
})

const onChange = (e) => {
  emits('update:open', e.show)
}
</script>

<style lang="scss"></style>

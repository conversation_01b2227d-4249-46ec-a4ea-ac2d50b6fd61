<template>
  <view class="keyboard " :class="{ 'show': show, 'dark': dark }">
    <view class="keyboard-content">
      <view class="keyboard-content-left">
        <view class="keyboard-content-left-item" v-for="item in KeyboardNumber" :index="item" @click="_setValue(item)">
          <view class="keyboard-content-left-item-cell hover">
            {{ item }}
          </view>
        </view>
        <view class="keyboard-content-left-item" v-if="isDecimal" @click="_setValue('.')">
          <view class="keyboard-content-left-item-cell hover">.</view>
        </view>
      </view>
      <view class="keyboard-content-right">
        <view class="keyboard-content-right-item" @click="_setValue('delete')">
          <view class="keyboard-content-right-item-cell hover">
            <image class="icon" v-if="!dark" src="@/static/backspace.png" mode="aspectFill"></image>
            <image class="icon" v-else src="@/static/backspace_dark.png" mode="aspectFill"></image>
          </view>
        </view>
        <view class="keyboard-content-right-item" @click="confirm">
          <view class="keyboard-content-right-item-confirm"
            :class="{ 'disable': confirmDisable(money), 'hover': money }" :style="{ 'background-color': btnColor }">{{
              confirmText }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    show: {
      default: false,
      type: Boolean
    },
    dark: {
      default: false,
      type: Boolean
    },
    btnColor: {
      default: '#07C160',
      type: String
    },
    confirmText: {
      default: '充值',
      type: String
    },
    isDecimal: {
      default: true,
      type: Boolean
    },
    value: {
      default: '',
      type: [Number, String]
    },
    maxIntNum: {
      default: 8,
      type: Number
    },
    maxFloatNum: {
      default: 2,
      type: Number
    },
    minLength: {
      default: 1,
      type: Number
    }
  },
  data() {
    return {
      KeyboardNumber: [1, 2, 3, 4, 5, 6, 7, 8, 9, 0],
      money: ''
    }
  },
  watch: {
    value: {
      handler(newValue, oldValue) {
        if (newValue !== null) {
          this.money = '' + newValue
        }
      },
      // 这里增加了一个 immediate 属性，说明监听到 props 传参后立即先去执行 handler 方法
      immediate: true,
    }
  },
  methods: {
    confirmDisable(money) {
      return !(money != null && money.length >= this.minLength)
    },
    _setValue: function (e) {
      switch (e) {
        case '.':
          if (this.money.indexOf('.') > -1) break
          if (this.money == '') {
            this.money = '0.'
          } else {
            this.money += e.toString()
          }
          break
        case 'delete':
          if (this.money.length > 0) {
            this.money = this.money.substr(0, this.money.length - 1)
          }
          break
        default:
          let tmp = this.money + e.toString()
          if (this.checkNum(tmp)) {
            this.money += e.toString()
          }
      }
      this.$emit("change", this.money)
    },
    confirm: function () {
      if (this.money > 0 && !this.confirmDisable(this.money)) {
        this.$emit("confirm", this.money)
      }

    },
    checkNum: function (e) {
      let arr = e.split('.')
      if (arr.length >= 1 && arr[0].length > this.maxIntNum) {
        return false;
      }
      if (arr.length == 2 && arr[1].length > this.maxFloatNum) {
        return false;
      }
      return true;
    }
  }
}
</script>
<style lang="less" scoped>
.dark {
  background-color: #1E1E1E !important;

  .keyboard-content-left-item-cell {
    background-color: #2c2c2c !important;
    color: rgba(255, 255, 255, 0.6);
  }

  .keyboard-content-right-item-cell {
    background-color: #2c2c2c !important;
  }

  .keyboard-content-left-item-cell:active {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }

  .keyboard-content-right-item-cell:active {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }
}

.keyboard {
  // position: fixed;
  left: 0;
  transform: translateY(100%);
  transition: all 0.3s ease;
  bottom: 0;
  background-color: #F7F7F7;
  width: 100%;
  padding: 20rpx 0 5rpx 0;
  box-sizing: border-box;

  .keyboard-content {
    display: flex;

    .keyboard-content-left {
      width: 75%;
      display: flex;
      flex-wrap: wrap;

      .keyboard-content-left-item {
        flex-basis: 33%;
        flex-grow: 1;
        padding: 0 0 15rpx 15rpx;
        box-sizing: border-box;

        .keyboard-content-left-item-cell {
          background-color: #ffffff;
          border-radius: 8rpx;
          text-align: center;
          font-size: 46rpx;
          line-height: 92rpx;
          font-weight: 500;
        }

        .keyboard-content-left-item-cell:active {
          background-color: rgba(0, 0, 0, 0.1);
        }
      }

      .keyboard-content-left-item:nth-child(10) {
        flex-basis: 66%;
        flex-grow: 1;
      }
    }

    .keyboard-content-right {
      width: 25%;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;

      .keyboard-content-right-item {
        width: 100%;
        padding: 0 15rpx 15rpx 15rpx;
        box-sizing: border-box;

        .keyboard-content-right-item-cell {
          border-radius: 8rpx;
          background-color: #ffffff;
          height: 92rpx;
          line-height: 92rpx;
          padding: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          box-sizing: border-box;

          image {
            height: 60%;
            width: 40%;
          }
        }

        .keyboard-content-right-item-cell:active {
          background-color: rgba(0, 0, 0, 0.1);
        }
      }

      .keyboard-content-right-item:nth-child(2) {
        flex: 1;

        .keyboard-content-right-item-confirm {
          background-color: #07C160;
          opacity: 1;
          color: rgba(255, 255, 255, 0.8);
          height: 100%;
          border-radius: 8rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .disable {
          opacity: 0.4;
        }

        .hover:active {
          //background-color: rgba(8,165,82,1);
          opacity: 0.8;
        }
      }
    }
  }
}

.show {
  transition: all 0.3s ease;
  transform: translateY(0%) !important;
}
</style>
:root {
  /* 使用海洋主题的颜色作为默认值 */
  --color-primary: #3498db;
  --color-primary-light: #5dade2;
  --color-primary-dark: #2980b9;
  --color-primary-transparent-20: rgba(52, 152, 219, 0.2);
  --color-secondary: #2ecc71;
  --color-secondary-light: #70c1e8;
  --color-secondary-dark: #2889c9;
  --color-accent: #1abc9c;
  --color-accent-light: #ff9e9e;
  --color-accent-dark: #ff5252;
  --color-success: #56c993;
  --color-success-transparent: rgba(86, 201, 147, 0.2);
  --color-warning: #ffae57;
  --color-warning-transparent: rgba(255, 174, 87, 0.2);
  --color-danger: #ff6b6b;
  --color-danger-transparent: rgba(255, 107, 107, 0.2);
  --color-bg: #eaf6ff;
  --color-bg-card: #ffffff;
  --color-bg-active: #e4f1f8;
  --color-white: #ffffff;
  --color-gray-100: #e4f1f8;
  --color-gray-200: #ebeefe;
  --color-gray-300: #d8dcef;
  --color-gray-400: #b0b5d1;
  --color-gray-500: #7f8c8d;
  --color-gray-600: #585d80;
  --color-gray-700: #4a5c6a;
  --color-gray-800: #2b2c44;
  --color-gray-900: #34495e;
  --color-text-primary: #34495e;
  --color-text-secondary: #7f8c8d;
  --color-shadow: rgba(52, 152, 219, 0.08);
  --color-shadow-active: rgba(52, 152, 219, 0.2);
  --rounded-full: 9999px;
  --rounded-2xl: 1.5rem; /* 24px */
  --rounded-xl: 1.25rem; /* 20px */
  --rounded-lg: 16px;
  --rounded-md: 12px;
  --rounded-sm: 8px;
  --shadow-sm: 0 2px 8px rgba(52, 152, 219, 0.08);
  --shadow-md: 0 4px 16px rgba(52, 152, 219, 0.12);
  --shadow-lg: 0 8px 24px rgba(52, 152, 219, 0.16);
  --color-primary-rgb: 52, 152, 219;
  --transition-fast: all 0.2s ease;
  --transition-normal: all 0.3s ease;
  --font-sans: 'ZhuZi', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

[data-theme='purple'] {
  --color-primary: #5e6ad2;
  --color-primary-light: #9fa8da;
  --color-primary-dark: #4549a9;
  --color-primary-transparent-20: rgba(94, 106, 210, 0.2);
  --color-secondary: #9fa8da;
  --color-accent: #9fa8da;
  --color-bg: #f8f9ff;
  --color-bg-card: #ffffff;
  --color-bg-active: #f5f7ff;
  --color-gray-100: #f5f7ff;
  --color-gray-500: #868aaf;
  --color-gray-700: #424366;
  --color-gray-900: #1a1b2e;
  --color-text-primary: #1a1b2e;
  --color-text-secondary: #868aaf;
  --color-shadow: rgba(94, 106, 210, 0.08);
  --color-shadow-active: rgba(94, 106, 210, 0.2);
}

[data-theme='forest'] {
  --color-primary: #4caf50;
  --color-primary-light: #81c784;
  --color-primary-dark: #3d9140;
  --color-secondary: #81c784;
  --color-accent: #81c784;
  --color-bg: #f5f9f5;
  --color-bg-card: #ffffff;
  --color-bg-active: #edf7ed;
  --color-gray-100: #edf7ed;
  --color-gray-500: #7d9175;
  --color-gray-700: #556b55;
  --color-gray-900: #2e3b2e;
  --color-text-primary: #2e3b2e;
  --color-text-secondary: #7d9175;
  --color-shadow: rgba(76, 175, 80, 0.08);
  --color-shadow-active: rgba(76, 175, 80, 0.2);
}

[data-theme='sunset'] {
  --color-primary: #e77e23;
  --color-primary-light: #f39c12;
  --color-primary-dark: #d35400;
  --color-secondary: #e67e22;
  --color-accent: #d35400;
  --color-bg: #fdf3e6;
  --color-bg-card: #ffffff;
  --color-bg-active: #fbf5ee;
  --color-gray-100: #fbf5ee;
  --color-gray-500: #8c7269;
  --color-gray-700: #5c4b4f;
  --color-gray-900: #42383c;
  --color-text-primary: #42383c;
  --color-text-secondary: #8c7269;
  --color-shadow: rgba(231, 126, 35, 0.08);
  --color-shadow-active: rgba(231, 126, 35, 0.2);
}

[data-theme='ocean'] {
  --color-primary: #3498db;
  --color-primary-light: #5dade2;
  --color-primary-dark: #2980b9;
  --color-secondary: #2ecc71;
  --color-accent: #1abc9c;
  --color-bg: #eaf6ff;
  --color-bg-card: #ffffff;
  --color-bg-active: #e4f1f8;
  --color-gray-100: #e4f1f8;
  --color-gray-500: #7f8c8d;
  --color-gray-700: #4a5c6a;
  --color-gray-900: #34495e;
  --color-text-primary: #34495e;
  --color-text-secondary: #7f8c8d;
  --color-shadow: rgba(52, 152, 219, 0.08);
  --color-shadow-active: rgba(52, 152, 219, 0.2);
}

[data-theme='berry'] {
  --color-primary: #c35b80;
  --color-primary-light: #d78da9;
  --color-primary-dark: #a94d6a;
  --color-secondary: #d78da9;
  --color-accent: #d78da9;
  --color-bg: #fdf6f8;
  --color-bg-card: #ffffff;
  --color-bg-active: #fbeff3;
  --color-gray-100: #fbeff3;
  --color-gray-500: #c48b9f;
  --color-gray-700: #865c68;
  --color-gray-900: #4a3740;
  --color-text-primary: #4a3740;
  --color-text-secondary: #c48b9f;
  --color-shadow: rgba(195, 91, 128, 0.08);
  --color-shadow-active: rgba(195, 91, 128, 0.2);
}

[data-theme='mint'] {
  --color-primary: #009688;
  --color-primary-light: #26a69a;
  --color-primary-dark: #00796b;
  --color-secondary: #26a69a;
  --color-accent: #00bfa5;
  --color-bg: #e7f4f1;
  --color-bg-card: #ffffff;
  --color-bg-active: #e0f2f1;
  --color-gray-100: #e0f2f1;
  --color-gray-500: #88afa7;
  --color-gray-700: #5b7a75;
  --color-gray-900: #2d3f3c;
  --color-text-primary: #2d3f3c;
  --color-text-secondary: #88afa7;
  --color-shadow: rgba(0, 150, 136, 0.08);
  --color-shadow-active: rgba(0, 150, 136, 0.2);
}

[data-theme='slate'] {
  --color-primary: #607d8b;
  --color-primary-light: #78909c;
  --color-primary-dark: #455a64;
  --color-secondary: #78909c;
  --color-accent: #546e7a;
  --color-bg: #eef2f4;
  --color-bg-card: #ffffff;
  --color-bg-active: #eceff1;
  --color-gray-100: #eceff1;
  --color-gray-500: #90a4ae;
  --color-gray-700: #62727b;
  --color-gray-900: #37474f;
  --color-text-primary: #37474f;
  --color-text-secondary: #90a4ae;
  --color-shadow: rgba(96, 125, 139, 0.08);
  --color-shadow-active: rgba(96, 125, 139, 0.2);
}

[data-theme='sunshine'] {
  --color-primary: #d4ac6e;
  --color-primary-light: #e5c495;
  --color-primary-dark: #b9925a;
  --color-secondary: #e5c495;
  --color-accent: #e5c495;
  --color-bg: #fffdf0;
  --color-bg-card: #ffffff;
  --color-bg-active: #fff9c4;
  --color-gray-100: #fff9c4;
  --color-gray-500: #c9b472;
  --color-gray-700: #8c7b4f;
  --color-gray-900: #4d422c;
  --color-text-primary: #4d422c;
  --color-text-secondary: #c9b472;
  --color-shadow: rgba(212, 172, 110, 0.08);
  --color-shadow-active: rgba(212, 172, 110, 0.2);
}

[data-theme='coffee'] {
  --color-primary: #795548;
  --color-primary-light: #a1887f;
  --color-primary-dark: #5d4037;
  --color-secondary: #a1887f;
  --color-accent: #8d6e63;
  --color-bg: #f5f0ee;
  --color-bg-card: #ffffff;
  --color-bg-active: #efebe9;
  --color-gray-100: #efebe9;
  --color-gray-500: #a89891;
  --color-gray-700: #756a64;
  --color-gray-900: #3e342f;
  --color-text-primary: #3e342f;
  --color-text-secondary: #a89891;
  --color-shadow: rgba(121, 85, 72, 0.08);
  --color-shadow-active: rgba(121, 85, 72, 0.2);
}

[data-theme='ruby'] {
  --color-primary: #d76c6c;
  --color-primary-light: #e58e8e;
  --color-primary-dark: #c95353;
  --color-secondary: #e58e8e;
  --color-accent: #e58e8e;
  --color-bg: #fef0f0;
  --color-bg-card: #ffffff;
  --color-bg-active: #ffebee;
  --color-gray-100: #ffebee;
  --color-gray-500: #d48c87;
  --color-gray-700: #a16059;
  --color-gray-900: #542e2b;
  --color-text-primary: #542e2b;
  --color-text-secondary: #d48c87;
  --color-shadow: rgba(215, 108, 108, 0.08);
  --color-shadow-active: rgba(215, 108, 108, 0.2);
}

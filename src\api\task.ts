import db from './database'
// import { getNextOccurrence, getAllOccurrences, parseRRULE } from '@/utils/rruleUtils'
import dayjs from 'dayjs'
import { StatusUtils } from '@/constants/status'

// 添加临时函数
const getNextOccurrence = (rruleString: string, targetDate: Date): Date | null => {
  // 临时函数，简单返回一个日期
  // 为了确保任务能正常显示，这里总是返回一个日期
  return new Date()
}

interface OccurrenceOptions {
  after?: Date
  before?: Date
  inclusive?: boolean
}

const getAllOccurrences = (rruleString: string, options: OccurrenceOptions = {}): Date[] => {
  if (
    options.after &&
    options.before &&
    options.after.getFullYear() === options.before.getFullYear() &&
    options.after.getMonth() === options.before.getMonth() &&
    options.after.getDate() === options.before.getDate()
  ) {
    return [new Date(options.after)]
  }

  // 否则返回包含几天的数组
  const result: Date[] = []
  if (options.after) {
    result.push(new Date(options.after))

    // 如果有 before，添加一些日期直到 before
    if (options.before) {
      const start = new Date(options.after)
      const end = new Date(options.before)

      // 最多添加 7 天的日期
      for (let i = 1; i <= 7; i++) {
        const date = new Date(start)
        date.setDate(start.getDate() + i)
        if (date <= end) {
          result.push(date)
        } else {
          break
        }
      }
    }
  }

  return result
}

const parseRRULE = (rruleString: string): { freq: number; interval: number; byweekday: null } => {
  // 临时函数，返回一个简单的对象
  return {
    freq: 3, // DAILY
    interval: 1,
    byweekday: null,
  }
}

// 遍历添加进度值，仅限关键结果
// 最多两层，第一层是关键结果，第二层是任务
const addProgVal = async (task: any) => {
  // 判断进度计算方式
  const valType = task.valType || 'sum' // 默认使用求和方式

  // 根据不同的计算方式，处理进度值
  let curVal = 0
  if (task.recList && task.recList.length > 0) {
    if (valType === 'sum') {
      // 求和方式：累计所有进度记录
      curVal = task.recList.reduce((acc: number, cur: any) => acc + Number(cur.val), 0)
    } else if (valType === 'latest') {
      // 最新方式：使用最新的记录值
      // 按日期降序排序，取最新的一条记录
      const sortedRecords = [...task.recList].sort(
        (a, b) => new Date(b.recTime || b.updateTime).getTime() - new Date(a.recTime || a.updateTime).getTime()
      )
      curVal = sortedRecords.length > 0 ? Number(sortedRecords[0].val) : 0
    } else if (valType === 'max') {
      // 最大值方式：使用最大的记录值
      curVal = Math.max(...task.recList.map((record: any) => Number(record.val)))
    } else if (valType === 'avg') {
      // 平均值方式：计算所有记录的平均值
      const sum = task.recList.reduce((acc: number, cur: any) => acc + Number(cur.val), 0)
      curVal = task.recList.length > 0 ? sum / task.recList.length : 0
    }
  }

  // 处理子任务的进度值（这部分逻辑保持不变）
  const subTasks = await db.table('task').where(`parentId == "${task._id}"`).toArray()
  if (subTasks.length) {
    subTasks.forEach((subTask: any) => {
      // Ensure subTask.compInfos is an array
      if (typeof subTask.compInfos === 'string') {
        try {
          // 尝试处理可能包含单引号的情况
          let compInfosStr = subTask.compInfos || '[]'
          // 如果字符串以单引号开始和结束，尝试替换为双引号
          if (compInfosStr.startsWith("'") && compInfosStr.endsWith("'")) {
            compInfosStr = compInfosStr.substring(1, compInfosStr.length - 1)
          }
          // 尝试解析 JSON
          subTask.compInfos = JSON.parse(compInfosStr)
        } catch (parseError: any) {
          console.error('解析 subTask.compInfos 失败', subTask.compInfos, parseError)
          subTask.compInfos = []
        }
      } else if (!Array.isArray(subTask.compInfos)) {
        subTask.compInfos = []
      }

      if (subTask.type === 'todo' && subTask.repeatFlag) {
        // 循环任务
        if (Array.isArray(subTask.compInfos)) {
          // Double check it's an array before reduce
          curVal += subTask.compInfos.reduce((acc: number, cur: any) => acc + Number(cur.val), 0)
        }
      } else if (subTask.type === 'todo' && subTask.status === 1) {
        // 普通任务
        curVal += task.progVal
      }
    })
  }

  return curVal
}
/**
 * 添加一条 task 记录
 * @param params
 * @returns
 */
export const addTaskApi = async (params: API.NewTask) => {
  try {
    if (params.recList) params.recList = JSON.stringify(params.recList)
    if (params.compInfos) params.compInfos = JSON.stringify(params.compInfos)
    const _id = await db.table('task').add(params)
    return _id
  } catch (error: any) {
    console.error(error)
    throw new Error(String(error))
  }
}
/**
 *
 * @param id 更新 id
 * @param params 更新参数，只传需要更新的字段
 */
export const updateTaskApi = async (id: string, params: API.EditTask) => {
  try {
    if (params.recList) params.recList = JSON.stringify(params.recList)
    if (params.compInfos) params.compInfos = JSON.stringify(params.compInfos)
    await db.table('task').update(id, params)
  } catch (error: any) {
    console.error(error)
    throw new Error(String(error))
  }
}

/**
 * 获取一条 task 记录
 * @param _id task 的唯一标识符
 * @param option 选项
 * - progress 是否需要计算进度值
 */
export const getTaskApi = async (
  _id: string,
  option: {
    progress?: boolean
  } = {}
) => {
  try {
    const { progress = true } = option
    const task = await db.table('task').get(_id)
    if (task) {
      try {
        // 安全解析 JSON 字符串
        if (typeof task.recList === 'string') {
          try {
            // 尝试处理可能包含单引号的情况
            let recListStr = task.recList || '[]'
            // 如果字符串以单引号开始和结束，尝试替换为双引号
            if (recListStr.startsWith("'") && recListStr.endsWith("'")) {
              recListStr = recListStr.substring(1, recListStr.length - 1)
            }
            // 尝试解析 JSON
            task.recList = JSON.parse(recListStr)
          } catch (parseError: any) {
            console.error('解析 recList 失败', task.recList, parseError)
            task.recList = []
          }
        } else if (!Array.isArray(task.recList)) {
          task.recList = []
        }

        if (typeof task.compInfos === 'string') {
          try {
            // 尝试处理可能包含单引号的情况
            let compInfosStr = task.compInfos || '[]'
            // 如果字符串以单引号开始和结束，尝试替换为双引号
            if (compInfosStr.startsWith("'") && compInfosStr.endsWith("'")) {
              compInfosStr = compInfosStr.substring(1, compInfosStr.length - 1)
            }
            // 尝试解析 JSON
            task.compInfos = JSON.parse(compInfosStr)
          } catch (parseError: any) {
            console.error('解析 compInfos 失败', task.compInfos, parseError)
            task.compInfos = []
          }
        } else if (!Array.isArray(task.compInfos)) {
          task.compInfos = []
        }

        if (task.type === 'kr' && progress) task.curVal = await addProgVal(task)
      } catch (parseError: any) {
        console.error('处理任务数据时出错', task, parseError)
        task.recList = []
        task.compInfos = []
      }
    }
    return task
  } catch (error: any) {
    console.error(error)
    throw new Error(String(error))
  }
}
/**
 * 获取 task 列表
 * @param params 传入筛选条件
 * @param option progress 是否需要计算进度值
 */
export const getTaskListApi = async (
  params?: string,
  option: {
    progress?: boolean
  } = {}
) => {
  const { progress = true } = option
  try {
    const list = await db.table('task').where(params).toArray()
    // 按照 createTime 排序
    list.sort((a: any, b: any) => dayjs(b.createTime).valueOf() - dayjs(a.createTime).valueOf())

    // 遍历添加进度值 TODO 是否可以一次性查询所有的子任务，然后再遍历添加进度值
    for (let i = 0; i < list.length; i++) {
      const item = list[i]
      try {
        // 安全解析 JSON 字符串
        if (typeof item.recList === 'string') {
          try {
            // 尝试处理可能包含单引号的情况
            let recListStr = item.recList || '[]'
            // 如果字符串以单引号开始和结束，尝试替换为双引号
            if (recListStr.startsWith("'") && recListStr.endsWith("'")) {
              recListStr = recListStr.substring(1, recListStr.length - 1)
            }
            // 尝试解析 JSON
            item.recList = JSON.parse(recListStr)
          } catch (parseError: any) {
            console.error('解析 recList 失败', item.recList, parseError)
            item.recList = []
          }
        } else if (!Array.isArray(item.recList)) {
          item.recList = []
        }

        if (typeof item.compInfos === 'string') {
          try {
            // 尝试处理可能包含单引号的情况
            let compInfosStr = item.compInfos || '[]'
            // 如果字符串以单引号开始和结束，尝试替换为双引号
            if (compInfosStr.startsWith("'") && compInfosStr.endsWith("'")) {
              compInfosStr = compInfosStr.substring(1, compInfosStr.length - 1)
            }
            // 尝试解析 JSON
            item.compInfos = JSON.parse(compInfosStr)
          } catch (parseError: any) {
            console.error('解析 compInfos 失败', item.compInfos, parseError)
            item.compInfos = []
          }
        } else if (!Array.isArray(item.compInfos)) {
          item.compInfos = []
        }

        // 关键结果添加进度值
        if (item.type === 'kr' && progress) item.curVal = await addProgVal(item)
      } catch (itemError: any) {
        console.error('处理任务项时出错', item, itemError)
        // 确保字段有默认值
        item.recList = []
        item.compInfos = []
      }
    }
    return list
  } catch (error: any) {
    console.error('getTaskListApi error', JSON.stringify(error))
    console.error(error)
    throw new Error(String(error))
  }
}
/**
 * 获取指定日期 task 列表
 * @param date 日期
 * @param complete 是否获取已完成任务
 * @param unComplete 是否获取未完成任务
 * @param expired 是否获取过期任务
 * @returns 返回三个列表，已完成、未完成、过期
 *  compTaskList: [],
    unCompTaskList: [],
    exTaskList: [],
 */
export const getTaskListByDateApi = async (date: string, { complete = true, unComplete = false, expired = false }) => {
  try {
    let taskList = await getTaskListApi(`type == 'todo' && startDate <= '${date}'`) // 这里的查询有性能问题，随着历史任务的增多，查询压力会越来越大
    taskList = taskList.filter((item: any) => {
      try {
        if (item.repeatFlag) {
          // 过滤出 循环任务 中当天需要执行的任务
          const startDate = dayjs(item.startDate).toDate()
          const endDate = item.endDate ? dayjs(item.endDate).toDate() : undefined
          const targetDate = dayjs(date).toDate()

          // 使用自定义库检查日期是否在循环规则内
          const isShowToday =
            getNextOccurrence(item.repeatFlag, targetDate) !== null ||
            getAllOccurrences(item.repeatFlag, {
              after: new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate()),
              before: new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate(), 23, 59, 59),
            }).length > 0

          // 查询完成情况
          item.status = item.compInfos.some((e: any) => e.date === date) ? 1 : 0
          return isShowToday
        } else {
          // 过滤单日任务
          if (item.startDate == date && item.endDate == date) return true
          else if (item.startDate == date && item.endDate == '') return true
          // 过滤多日任务
          else if (item.startDate <= date && item.endDate >= date) return true
          // 过滤过期任务
          else if (item.endDate < date && item.status === 0) return true
          else return false
        }
      } catch (filterError: any) {
        console.error('过滤任务时出错', item, filterError)
        return false
      }
    })
    // 查询父任务
    let parentIds = taskList.map((item: any) => item.parentId).filter((item: any) => item !== '')
    parentIds = [...new Set(parentIds)]

    let parentList: any[] = []
    if (parentIds.length > 0) {
      parentIds = parentIds.map((item: any) => `"${item}"`).join(',')
      try {
        parentList = await db.table('task').where(`_id, anyOf(${parentIds})`).toArray()
      } catch (parentError: any) {
        console.error('获取父任务失败', parentError)
        parentList = []
      }
    }

    // 查询目标
    let okrIds = taskList.map((item: any) => item.okrId).filter((okrId: any) => okrId !== '')
    okrIds = [...new Set(okrIds)]

    let targetList: any[] = []
    if (okrIds.length > 0) {
      okrIds = okrIds.map((item: any) => `"${item}"`).join(',')
      try {
        targetList = await db.table('task').where(`_id, anyOf(${okrIds})`).toArray()
      } catch (targetError: any) {
        console.error('获取目标失败', targetError)
        targetList = []
      }
    }

    // 添加父任务和目标信息
    taskList = taskList.map((item: any) => {
      try {
        item.parent = parentList.find((e: any) => e._id === item.parentId)
        item.target = targetList.find((e: any) => e._id === item.targetId)
        return item
      } catch (mapError: any) {
        console.error('添加父任务和目标信息时出错', item, mapError)
        return item
      }
    })
    // 只展示进行中目标的任务
    taskList = taskList.filter((item: any) => {
      try {
        const isHide = item.target && !StatusUtils.isOkrActive(item.target.status)
        return !isHide
      } catch (filterError: any) {
        console.error('过滤非活跃目标任务时出错', item, filterError)
        return false // 有错误的任务不显示
      }
    })
    const tasksResult = {
      compTaskList: [] as any[],
      unCompTaskList: [] as any[],
      exTaskList: [] as any[],
    }
    // 过滤任务状态
    if (complete) tasksResult.compTaskList = taskList.filter((item: any) => item.status === 1)
    if (unComplete)
      tasksResult.unCompTaskList = taskList.filter((item: any) => item.status === 0 && item.endDate >= date)
    if (expired) tasksResult.exTaskList = taskList.filter((item: any) => item.status === 0 && item.endDate < date)

    // 排序
    try {
      const orderBy = JSON.parse(uni.getStorageSync('OKReSort') || '{}')
      if (Object.keys(orderBy).length > 0 && tasksResult.compTaskList.length > 0) {
        let [newListByNoSort, newListBySort] = tasksResult.compTaskList.reduce(
          (acc: any[][], item: any) => {
            if (orderBy[item._id] !== undefined) {
              acc[1].push(item)
            } else {
              acc[0].push(item)
            }
            return acc
          },
          [[], []]
        )
        newListBySort.sort((a: any, b: any) => {
          const orderA = orderBy[a._id]
          const orderB = orderBy[b._id]
          if (orderA === undefined && orderB === undefined) return 0
          if (orderA === undefined) return 1
          if (orderB === undefined) return -1
          return orderA - orderB
        })
        tasksResult.compTaskList = [...newListByNoSort, ...newListBySort]
      }
    } catch (sortError: any) {
      console.error('排序任务时出错', sortError)
      // 排序出错时不影响整体功能
    }

    return tasksResult
  } catch (error: any) {
    console.error('getTaskListByDateApi error:', error)
    if (error.message && error.message.includes('JSON')) {
      console.error('JSON 解析错误，可能是数据格式问题')
    }
    throw new Error(String(error))
  }
}
/**
 * 删除 task
 * @params 传入 _id 或者筛选条件
 */
export const delTaskApi = async (params: string) => {
  try {
    if (isUUID(params)) {
      await db.table('task').update(params, {
        deleteTime: new Date().toISOString(),
      })
    } else {
      await db.table('task').where(params).modify({ deleteTime: new Date().toISOString() })
    }
  } catch (error: any) {
    console.error(error)
    throw new Error(String(error))
  }
}

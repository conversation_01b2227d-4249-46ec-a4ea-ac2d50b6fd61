<template>
  <view class="container">
    <div v-if="!showResult" class="w-[100%] flex justify-center items-center flex-col">
      <textarea v-model="inputValue" class="textarea"></textarea>
      <button @click="printInput" class="button">评价</button>
    </div>
    <view v-else class="result px-4">
      <view>{{ result.title }}</view>
      <view>{{ result.aiText }}</view>
      <view>{{ result.proposal }}</view>
      <view>
        <button @click="reset" class="button mt-2">重来</button>
        <button @click="onFlomo" type="primary" class="button mt-2">添加到 flomo</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { onMounted, ref } from 'vue'

const inputValue = ref('')
const showResult = ref(false)
const result = ref('')

const workflowApi = async (workflow_id, params) => {
  const res = await request.post(
    'https://api.coze.cn/v1/workflow/run',
    {
      workflow_id,
      app_id: '7470808377170837504',
      parameters: params,
    },
    {
      timeout: 300000,
      header: {
        Authorization: `Bearer ${getApp().globalData.kzToken}`,
        'Content-Type': 'application/json',
      },
    }
  )
  return {
    ...res,
    data: JSON.parse(res.data),
  }
}

onMounted(() => {
  console.log('mounted')
})

const printInput = async () => {
  console.log(inputValue.value)
  uni.showLoading({
    title: '加载中',
  })
  let { data } = await workflowApi('7470834234748321855', {
    content: inputValue.value,
  })
  const { input } = JSON.parse(data.data)
  result.value = JSON.parse(input)
  showResult.value = true
  uni.hideLoading()
}

const onFlomo = async () => {
  const res = await uni.request({
    url: 'https://flomoapp.com/iwh/MTE1MTQy/ad156344cdccbb9d22e39f424f502b83/',
    method: 'POST',
    header: {
      'Content-Type': 'application/json',
    },
    data: {
      content: `
      ${result.value.title}
      ${result.value.aiText}
      ----------------
      ${inputValue.value}
      ----------------
      ${result.value.proposal}
      `,
    },
  })
  uni.showToast({
    title: '添加成功',
    icon: 'success',
  })
  reset()
}

const reset = () => {
  inputValue.value = ''
  showResult.value = false
  result.value = ''
}
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.textarea,
.button,
.result {
  align-self: center;
}

.textarea {
  border: none;
  border-bottom: 1px solid #ccc;
  margin-bottom: 10px;
  width: 80%;
  height: 100px;
}

.result {
  margin-top: 10px;
  white-space: pre-wrap;
}
</style>

# 任务：OKR进度历史组件按天筛选模式实现

## 任务描述
实现进度历史组件的按天筛选模式，包括日历组件集成和日期选择功能。

## 所属功能模块
OKR进度历史组件

## 技术实现详情

### 日历组件设计
1. 基于现有的`z-calendar.vue`组件，创建简化版的周历组件：
   ```html
   <view class="calendar-area" v-if="filterMode === 'day'">
     <view class="calendar-wrapper">
       <swiper class="week-swiper" 
               :current="currentWeekIndex" 
               @change="onWeekChange">
         <swiper-item v-for="(week, weekIndex) in weeksInMonth" :key="weekIndex">
           <view class="week-row">
             <view class="day-cell" 
                   v-for="(day, dayIndex) in week" 
                   :key="dayIndex"
                   :class="{
                     'selected': isSelectedDay(day.date),
                     'has-record': hasRecord(day.date),
                     'current-month': isCurrentMonth(day.date),
                     'today': isToday(day.date)
                   }"
                   @click="selectDate(day.date)">
               <text class="day-number">{{ day.day }}</text>
               <view class="record-dot" v-if="hasRecord(day.date)"></view>
             </view>
           </view>
         </swiper-item>
       </swiper>
     </view>
   </view>
   ```

### 周历数据处理
1. 实现周历数据的计算和管理：
   ```javascript
   data() {
     return {
       // 已有数据结构...
       
       // 周历相关
       weeksInMonth: [], // 当前月份的周数据
       currentWeekIndex: 0, // 当前显示的周索引
     }
   },
   
   computed: {
     /**
      * 计算当前选中日期所在的周索引
      */
     selectedDateWeekIndex() {
       const selectedDate = dayjs(this.selectedDate);
       return this.weeksInMonth.findIndex(week => 
         week.some(day => day.date === selectedDate.format('YYYY-MM-DD'))
       );
     }
   },
   
   watch: {
     /**
      * 监听月份变化，重新计算周数据
      */
     filterMonth() {
       this.calculateWeeksInMonth();
       this.updateDateHasRecord();
     },
     
     /**
      * 监听选中日期变化，更新周索引
      */
     selectedDate() {
       this.currentWeekIndex = this.selectedDateWeekIndex;
       this.filterRecords();
     }
   }
   ```

### 周历方法实现
1. 计算月份的周数据：
   ```javascript
   /**
    * 计算当前月份的周数据
    */
   calculateWeeksInMonth() {
     const monthStart = dayjs(this.filterMonth).startOf('month');
     const monthEnd = dayjs(this.filterMonth).endOf('month');
     
     // 获取包含这个月的所有周
     const firstDay = monthStart.startOf('week');
     const lastDay = monthEnd.endOf('week');
     
     const totalDays = lastDay.diff(firstDay, 'day') + 1;
     const weeksCount = Math.ceil(totalDays / 7);
     
     const weeksData = [];
     let currentDate = firstDay;
     
     for (let week = 0; week < weeksCount; week++) {
       const weekDays = [];
       
       for (let i = 0; i < 7; i++) {
         weekDays.push({
           date: currentDate.format('YYYY-MM-DD'),
           day: currentDate.date(),
           month: currentDate.month(),
           isCurrentMonth: currentDate.month() === monthStart.month()
         });
         
         currentDate = currentDate.add(1, 'day');
       }
       
       weeksData.push(weekDays);
     }
     
     this.weeksInMonth = weeksData;
     
     // 设置当前周为包含选中日期的那一周
     this.currentWeekIndex = this.selectedDateWeekIndex >= 0 ? 
       this.selectedDateWeekIndex : this.findCurrentWeek();
   },
   
   /**
    * 找到当前日期所在的周索引
    */
   findCurrentWeek() {
     const today = dayjs().format('YYYY-MM-DD');
     for (let i = 0; i < this.weeksInMonth.length; i++) {
       if (this.weeksInMonth[i].some(day => day.date === today)) {
         return i;
       }
     }
     return 0; // 默认显示第一周
   },
   
   /**
    * 周切换事件处理
    */
   onWeekChange(e) {
     this.currentWeekIndex = e.detail.current;
   },
   
   /**
    * 选择日期
    */
   selectDate(date) {
     this.selectedDate = date;
   },
   
   /**
    * 检查是否是选中的日期
    */
   isSelectedDay(date) {
     return date === this.selectedDate;
   },
   
   /**
    * 检查日期是否有记录
    */
   hasRecord(date) {
     return this.dateHasRecord.includes(date);
   },
   
   /**
    * 检查是否是当前月份的日期
    */
   isCurrentMonth(date) {
     return dayjs(date).format('YYYY-MM') === this.filterMonth;
   },
   
   /**
    * 检查是否是今天
    */
   isToday(date) {
     return date === dayjs().format('YYYY-MM-DD');
   }
   ```

### 样式实现
1. 日历组件样式：
   ```css
   .calendar-area {
     padding: 10px 0;
     background-color: #fff;
     border-bottom: 1px solid #eee;
   }
   
   .week-swiper {
     height: 70px;
   }
   
   .week-row {
     display: flex;
     justify-content: space-around;
     padding: 10px 0;
   }
   
   .day-cell {
     width: 36px;
     height: 36px;
     display: flex;
     flex-direction: column;
     align-items: center;
     justify-content: center;
     position: relative;
     border-radius: 50%;
   }
   
   .day-number {
     font-size: 14px;
   }
   
   .day-cell.selected {
     background-color: var(--primary-color, #007aff);
     color: #fff;
   }
   
   .day-cell.has-record .record-dot {
     width: 4px;
     height: 4px;
     background-color: var(--primary-color, #007aff);
     border-radius: 50%;
     position: absolute;
     bottom: 2px;
   }
   
   .day-cell.selected.has-record .record-dot {
     background-color: #fff;
   }
   
   .day-cell.today {
     font-weight: bold;
   }
   
   .day-cell:not(.current-month) {
     opacity: 0.3;
   }
   ```

## 验收标准
1. 周历组件正确显示，能够以周为单位展示日期
2. 支持左右滑动切换周，交互流畅
3. 日期选中状态正确显示，当天有记录的日期能够正确标记
4. 选中日期后，下方记录列表正确显示对应日期的记录
5. 非当前月份的日期有适当的视觉区分
6. 当前日期（今天）有明显标识

## 依赖关系
- 上游依赖：task-progress-history-01.md, task-progress-history-02.md
- 下游依赖：task-progress-history-05.md

## 优先级
中

## 估计工作量
2人日 
<template>
  <view class="note-container">
    <!-- 顶部导航栏 -->
    <view class="note-header">
      <view class="back-button" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="note-title">{{ title }}</text>
      <view v-if="!loading && !error" class="speak-button" @click="useForSpeak">用于复述训练</view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <z-loading text="加载中" :fontSize="42" color="#3a7edc" />
    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" class="error-state">
      <text class="error-text">{{ error }}</text>
      <view class="retry-button" @click="loadContent">重试</view>
    </view>

    <!-- 内容显示 -->
    <view v-else class="note-content">
      <!-- 渲染 Markdown 内容 -->
      <rich-text v-if="isMarkdown" :nodes="renderedContent" selectable="true" user-select="true"></rich-text>
      <!-- 显示普通文本或 JSON 内容 -->
      <text v-else class="content-text" selectable="true">{{ content }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { getOkrMd } from '@/api/okrMd'
import { parseMarkdown } from '@/utils/tools'
import ZLoading from '@/components/z-loading/index.vue'

// 页面参数
const key = ref('')
const title = ref('笔记')
const content = ref('')
const loading = ref(true)
const error = ref('')
const isMarkdown = ref(false)
const htmlContent = ref('') // 存储解析后的 HTML 内容

// 处理后的 Markdown 内容
const renderedContent = computed(() => {
  return htmlContent.value || ''
})

// 获取页面参数
onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.$page?.options || {}

  if (options.key) {
    key.value = decodeURIComponent(options.key)
  }

  if (options.title) {
    title.value = decodeURIComponent(options.title)
  }

  // 加载内容
  loadContent()
})

// 加载笔记内容
const loadContent = async () => {
  if (!key.value) {
    error.value = '无效的文件标识'
    loading.value = false
    return
  }

  loading.value = true
  error.value = ''

  try {
    // 调用接口获取内容
    const data = await getOkrMd(key.value)
    console.log('笔记内容数据：', data)

    // 判断内容类型并设置显示模式
    if (data && typeof data === 'object') {
      // 如果返回的是对象结构，可能是 YAML 前置信息和正文内容
      if (data.content) {
        // 如果有 content 字段，尝试解析为 Markdown
        content.value = data.content
        htmlContent.value = parseMarkdown(data.content)
        isMarkdown.value = true
      } else {
        // 否则展示为 JSON
        content.value = JSON.stringify(data, null, 2)
        htmlContent.value = ''
        isMarkdown.value = false
      }
    } else if (typeof data === 'string') {
      // 如果是字符串，尝试以 Markdown 形式渲染
      content.value = data
      htmlContent.value = parseMarkdown(data)
      isMarkdown.value = true
    } else {
      content.value = '暂无内容'
      htmlContent.value = ''
      isMarkdown.value = false
    }
  } catch (err) {
    console.error('获取笔记内容失败：', err)
    error.value = '加载内容失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 用于复述训练
const useForSpeak = () => {
  // 检查是否有内容
  if (!content.value) {
    uni.showToast({
      title: '笔记内容为空',
      icon: 'none',
    })
    return
  }

  // 跳转到复述页面，传递笔记内容
  uni.navigateTo({
    url:
      '/pages/speak/speak?noteContent=' +
      encodeURIComponent(content.value) +
      '&noteTitle=' +
      encodeURIComponent(title.value),
  })
}
</script>

<style lang="scss" scoped>
.note-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #ffffff;

  .note-header {
    display: flex;
    align-items: center;
    padding: 20rpx 24rpx;
    background-color: #f8f8f8;
    border-bottom: 1rpx solid #eaeaea;
    height: 88rpx;

    .back-button {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;

      .back-icon {
        width: 20rpx;
        height: 20rpx;
        border-left: 3rpx solid #333;
        border-bottom: 3rpx solid #333;
        transform: rotate(45deg);
      }
    }

    .note-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .speak-button {
      padding: 16rpx 24rpx;
      background-color: #2468f2;
      color: #fff;
      font-size: 24rpx;
      border-radius: 8rpx;
      margin-left: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .loading-state,
  .error-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .error-text {
    font-size: 28rpx;
    color: #999;
    text-align: center;
    margin-bottom: 30rpx;
  }

  .retry-button {
    padding: 16rpx 40rpx;
    background-color: #2468f2;
    color: #fff;
    font-size: 28rpx;
    border-radius: 8rpx;
  }

  .note-content {
    flex: 1;
    padding: 30rpx;
    overflow-y: auto;
    user-select: text;
    -webkit-user-select: text;

    .content-text {
      font-size: 28rpx;
      line-height: 1.6;
      color: #333;
      white-space: pre-wrap;
    }

    :deep(rich-text) {
      font-size: 28rpx;
      line-height: 1.8;

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin: 20rpx 0;
        font-weight: bold;
        color: #333;
      }

      h1 {
        font-size: 40rpx;
      }
      h2 {
        font-size: 36rpx;
      }
      h3 {
        font-size: 32rpx;
      }

      p {
        margin: 16rpx 0;
      }

      a {
        color: #2468f2;
        text-decoration: none;
      }

      ul,
      ol {
        margin: 16rpx 0;
        padding-left: 40rpx;
      }

      li {
        margin: 8rpx 0;
      }

      code {
        background-color: #f5f5f5;
        padding: 4rpx 8rpx;
        border-radius: 4rpx;
        font-family: monospace;
      }

      pre {
        background-color: #f5f5f5;
        padding: 16rpx;
        border-radius: 8rpx;
        overflow-x: auto;
        margin: 16rpx 0;
        word-wrap: break-word;
        white-space: pre-wrap;

        code {
          background: none;
          padding: 0;
          word-wrap: break-word;
          white-space: pre-wrap;
        }
      }

      blockquote {
        margin: 16rpx 0;
        padding: 16rpx 24rpx;
        border-left: 4rpx solid #ddd;
        background-color: #f9f9f9;
        color: #666;
      }

      img {
        max-width: 100%;
        height: auto;
        margin: 16rpx 0;
        border-radius: 8rpx;
      }
    }
  }
}
</style>

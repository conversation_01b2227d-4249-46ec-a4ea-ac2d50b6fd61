<template>
  <view class="page-margin">
    <view class="setting-group">
      <view v-if="userInfo.mobile" class="setting">
        <view class="setting-title">手机号</view>
        <view class="setting-right">
          {{ userInfo.mobile }}
        </view>
      </view>
    </view>
    <view class="setting-group">
      <view v-if="userInfo.username" class="setting">
        <view class="setting-title">账号</view>
        <view class="setting-right">
          {{ userInfo.username }}
        </view>
      </view>
    </view>
    <view class="setting-group">
      <view class="setting" @click="changePassword">
        <view class="setting-title">修改密码</view>
        <view class="setting-right">
          <u-icon class="more-icon" name="arrow-right" size="24"></u-icon>
        </view>
      </view>
    </view>
    <view class="setting-group">
      <view class="setting" @click="deactivate">
        <view class="setting-title">注销账号</view>
        <view class="setting-right">
          <u-icon class="more-icon" name="arrow-right" size="24"></u-icon>
        </view>
      </view>
    </view>

    <view class="fixed bottom-100 left-0 right-0 flex justify-center">
      <z-button @click="showLogout = true" class="w-400" shape="round">退出登录</z-button>
    </view>
    <u-modal
      v-model="showLogout"
      show-cancel-button
      mask-close-able
      title="退出登录"
      content="确定要退出登录吗？"
      confirm-color="#fa3534"
      confirm-text="退出登录"
      @confirm="onLogout"
    ></u-modal>
  </view>
</template>
<script setup lang="ts">
import checkUpdate from '@/uni_modules/uni-upgrade-center-app/utils/check-update'
import callCheckVersion from '@/uni_modules/uni-upgrade-center-app/utils/call-check-version'
import { store, mutations } from '@/uni_modules/uni-id-pages/common/store.js'
import localDB from '@/api/database'
console.log(store.userInfo)

// 修改密码
const changePassword = () => {
  uni.navigateTo({
    url: '/uni_modules/uni-id-pages/pages/userinfo/change_pwd/change_pwd',
    complete: (e) => {
      // console.log(e);
    },
  })
}

// 注销账号
const deactivate = () => {
  uni.navigateTo({
    url: '/uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate',
  })
}

// 用户信息
const userInfo = computed(() => store.userInfo)

// 退出登录
const showLogout = ref(false)
const onLogout = async () => {
  // 先清空数据库
  uni.showLoading({
    title: '退出中...'
  })
  uni.removeStorageSync('lastSyncTime')
  await localDB.delete()
  
  // 然后退出登录
  await mutations.logout()
  
  uni.hideLoading()
  uni.showToast({
    title: '已退出登录',
    icon: 'success'
  })
}
</script>
<style scoped lang="scss">
.setting-title {
  font-size: 32rpx;
  color: #333;
}
.setting-group {
  margin: 30rpx 0rpx;
  $radius: 20rpx;
  border-radius: $radius;
  overflow: hidden;
  .setting {
    display: flex;
    align-items: center;
    background-color: #fff;
    $padding: 40rpx;
    padding: $padding;
    // &:not(:last-child) {
    //   border-bottom: 1rpx solid #f0f0f0;
    // }
    .setting-icon {
      margin-right: 20rpx;
      color: #333;
    }

    .setting-right {
      margin-left: auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #999;
      .setting-tip {
        font-size: 25rpx;
      }
      .more-icon {
        margin-left: 5rpx;
      }
    }
  }
}
</style>

<template>
  <view class="progress-tip-container" :class="{ visible: isVisible }">
    <view class="progress-tip-content">
      <view v-for="item in animatedProgresses" :key="item.id" class="objective-item">
        <view class="header">
          <text class="title">{{ item.title }}</text>
        </view>
        <view class="progress-bar-wrapper">
          <view class="progress-bar-background">
            <view class="progress-bar-current" :style="{ width: calculateCurrentWidth(item), backgroundColor: item.color }"></view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  progressInfo: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue']);

const isVisible = ref(false);
const animatedProgresses = ref([]);

function calculateCurrentWidth(item) {
  if (!item || item.total <= 0) return '0%';
  return `${(item.animatedValue / item.total) * 100}%`;
}

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    show();
  } else {
    hide();
  }
});

function show() {
  isVisible.value = true;
  animatedProgresses.value = props.progressInfo.map(item => ({
    ...item,
    animatedValue: item.initial
  }));

  setTimeout(() => {
    animatedProgresses.value = animatedProgresses.value.map(item => {
      if (item.added > 0) {
        return { ...item, animatedValue: item.initial + item.added };
      }
      return item;
    });
  }, 500); // Start animation after the component is visible

  setTimeout(() => {
    hide();
  }, 3000); // Auto-hide after 3 seconds
}

function hide() {
  isVisible.value = false;
  setTimeout(() => {
    emit('update:modelValue', false);
  }, 500); // Wait for fade-out animation to complete
}
</script>

<style scoped lang="scss">
.progress-tip-container {
  position: fixed;
  top: -200rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  max-width: 450rpx;
  background-color: var(--color-white);
  border-radius: var(--rounded-lg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  padding: 20rpx;
  z-index: 1000;
  transition: top 0.5s ease-in-out, opacity 0.5s ease-in-out;
  opacity: 0;

  &.visible {
    top: 200rpx; // Position closer to the middle of the screen
    opacity: 1;
  }
}

.progress-tip-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.objective-item {
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 8rpx;
}

.title {
  font-size: 26rpx;
  font-weight: 500;
  color: var(--color-gray-700);
}

.progress-bar-wrapper {
  width: 100%;
}

.progress-bar-background {
  width: 100%;
  height: 12rpx;
  background-color: var(--color-gray-200);
  border-radius: var(--rounded-full);
  position: relative;
  overflow: hidden;
}

.progress-bar-current {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  border-radius: var(--rounded-full);
  transition: width 1s ease-out;
}
</style> 
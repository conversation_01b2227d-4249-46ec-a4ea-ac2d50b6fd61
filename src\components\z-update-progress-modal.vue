<template>
  <!-- 自定义数字键盘作为主容器 -->
  <u-keyboard mode="number" :model-value="visible" :dot-enabled="true" :tooltip="false" tips="请输入进度数值"
    :cancel-btn="true" :confirm-btn="true" cancel-text="取消" confirm-text="确认" :mask="true" :mask-close-able="true"
    @change="onKeyboardChange" @backspace="onKeyboardBackspace" @confirm="onKeyboardConfirm" @cancel="onKeyboardCancel"
    @update:model-value="emit('update:visible', $event)">

    <!-- 键盘上方的内容区域 -->
    <view class="keyboard-content" :style="{ width: sys.isPC ? '600rpx' : '100%' }">
      <!-- 顶部标题区域 -->
      <view class="popup-header">
        <view class="kr-title">{{ krTitle || '进度更新' }}</view>
        <view class="remark-btn" @click="toggleRemarkInput">
          <text class="remark-btn-text">备注</text>
        </view>
      </view>

      <!-- 备注输入区 -->
      <view v-if="showRemarkInput" class="remark-area">
        <textarea class="remark-input" v-model="remarkValue" placeholder="请输入备注信息..." auto-height />
      </view>

      <!-- 分隔线 -->
      <view class="divider"></view>

      <!-- 底部操作区 -->
      <view class="popup-footer">
        <!-- 日期选择 -->
        <view class="date-picker" @click="showDatePicker">
          <view class="date-icon">
            <u-icon name="calendar" size="28" color="var(--color-primary)"></u-icon>
          </view>
          <text class="date-text">{{ formatDate(selectedDate) }}</text>
        </view>

        <!-- 进度显示和确认 -->
        <view class="progress-actions">
          <!-- 进度显示区域 -->
          <view class="progress-display" :class="{ 'has-value': displayValue }">
            <text class="progress-value">{{ displayValue || '输入完成量' }}</text>
          </view>
          <view class="confirm-button" :class="{ disabled: loading || currentProgressValue === null }"
            @click="handleConfirm">
            <text>{{ loading ? '...' : '确认' }}</text>
          </view>
        </view>
      </view>
    </view>
  </u-keyboard>

  <!-- 日期选择器 - 使用日历组件 -->
  <u-calendar v-model="showDatePickerPopup" mode="date" @change="onDatePickerChange" :max-date="maxDate"></u-calendar>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, computed } from 'vue'
import { getTaskApi, updateTaskApi } from '@/api/task'

interface Props {
  visible: boolean
  initialRemark?: string
  initialDate?: string
  krTitle?: string
  calcMethod?: string
  taskId: string
  initialProgress?: number
  editRecordId?: string
}

const props = withDefaults(defineProps<Props>(), {
  initialRemark: '',
  initialDate: '',
  krTitle: '',
  calcMethod: '',
  initialProgress: 0,
  editRecordId: '',
})

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'save-success', addedValue: number): void
}

const emit = defineEmits<Emits>()

const currentProgressValue = ref<number | null>(null)
const remarkValue = ref(props.initialRemark)
const selectedDate = ref(new Date())
const loading = ref(false)
const showRemarkInput = ref(false)
const showDatePickerPopup = ref(false)

// 键盘相关状态
const displayValue = ref('')

// 键盘高度
const keyboardHeight = ref(0)
// 添加 PC 和移动响应式判断
const sys = ref({
  isPC: false,
})

// 弹出位置类型
const positionType = computed(() => (sys.value.isPC ? 'center' : 'bottom'))

// 格式化日期为友好格式
const formatDate = (date: Date, format = 'yyyy-MM-dd') => {
  if (!date) return ''

  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()

  if (format === 'yyyy-MM-dd') {
    return `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`
  }

  return `${year}年${month}月${day}日`
}

// 设置日历可选的最大日期为明年的第一天
const maxDate = ref(formatDate(new Date(new Date().getFullYear() + 1, 0, 1)))

// 初始化系统环境
const initSysEnv = () => {
  // #ifdef H5
  // 简单判断是否为 PC 设备
  sys.value.isPC = window.screen.width > 768
  // #endif
}

// 键盘高度回调
const onKeyboardHeight = (res: { height: number }) => {
  if (props.visible && positionType.value === 'bottom') {
    keyboardHeight.value = res.height > 50 ? res.height - 50 : 0
  }
}

// 切换备注输入框显示状态
const toggleRemarkInput = () => {
  showRemarkInput.value = !showRemarkInput.value
}

// 组件加载时初始化
onMounted(() => {
  initSysEnv()

  // #ifdef APP-PLUS
  uni.onKeyboardHeightChange(onKeyboardHeight)
  // #endif
})

// 组件卸载时清理
onUnmounted(() => {
  // #ifdef APP-PLUS
  uni.offKeyboardHeightChange(onKeyboardHeight)
  // #endif
})

watch(
  () => props.initialRemark,
  (newValue) => {
    remarkValue.value = newValue
  }
)

// 键盘按键点击处理
const onKeyboardChange = (val: string | number) => {
  // 如果是小数点且已经有小数点，则不添加
  if (val === '.' && displayValue.value.includes('.')) {
    return
  }

  // 限制小数点后最多两位
  if (displayValue.value.includes('.')) {
    const parts = displayValue.value.split('.')
    if (parts[1] && parts[1].length >= 2 && val !== '.') {
      return
    }
  }

  // 限制最大值为 100
  const newValue = displayValue.value + val.toString()
  const numValue = parseFloat(newValue)
  if (numValue > 100) {
    return
  }

  displayValue.value += val.toString()
  currentProgressValue.value = Number(displayValue.value) || null
}

// 退格键处理
const onKeyboardBackspace = () => {
  if (displayValue.value.length > 0) {
    displayValue.value = displayValue.value.slice(0, -1)
    currentProgressValue.value = displayValue.value ? Number(displayValue.value) : null
  }
}

// 键盘确认处理
const onKeyboardConfirm = () => {
  // 键盘组件会自动关闭，直接触发确认操作
  if (currentProgressValue.value !== null) {
    handleConfirm()
  }
}

// 键盘取消处理
const onKeyboardCancel = () => {
  // 键盘组件会自动关闭，重置输入值
  displayValue.value = ''
  currentProgressValue.value = null
}

// 初始化表单数据
const initFormData = () => {
  currentProgressValue.value = props.initialProgress || null
  // 设置显示值
  displayValue.value = currentProgressValue.value !== null ? currentProgressValue.value.toString() : ''
  remarkValue.value = props.initialRemark
  showRemarkInput.value = !!props.initialRemark

  if (props.initialDate) {
    selectedDate.value = new Date(props.initialDate)
  } else {
    selectedDate.value = new Date()
  }
}

watch(
  () => props.visible,
  (newValue) => {
    if (newValue) {
      initFormData()
    }
  }
)

// 初始化编辑记录的日期
const initEditRecord = async () => {
  if (props.editRecordId && props.taskId) {
    try {
      const taskDetail = await getTaskApi(props.taskId)
      if (taskDetail?.recList && Array.isArray(taskDetail.recList)) {
        const record = taskDetail.recList.find((record: any) => record._id === props.editRecordId)
        if (record) {
          if (record.recTime) {
            selectedDate.value = new Date(record.recTime)
          }
        }
      }
    } catch (error) {
      console.error('获取编辑记录数据失败', error)
    }
  }
}

watch(
  () => [props.visible, props.editRecordId],
  ([visible, editRecordId]) => {
    if (visible && editRecordId) {
      initEditRecord()
    }
  },
  { immediate: true }
)

const closeModal = () => {
  emit('update:visible', false)
}

// 显示日期选择器
const showDatePicker = () => {
  // 显示日历选择器
  showDatePickerPopup.value = true
}

// 日期选择器确认事件
const onDatePickerChange = (e: {
  year: number
  month: number
  day: number
  result: string
  isToday: boolean
  week: string
}) => {
  // 更新选中日期，保留当前的时分秒
  const { year, month, day } = e
  const currentDateTime = new Date(selectedDate.value)

  // 只更新日期部分，保留时分秒
  const newDateTime = new Date(
    year,
    month - 1,
    day,
    currentDateTime.getHours(),
    currentDateTime.getMinutes(),
    currentDateTime.getSeconds(),
    currentDateTime.getMilliseconds()
  )

  selectedDate.value = newDateTime
  // 关闭日期选择器会自动关闭
}

const handleConfirm = async () => {
  if (currentProgressValue.value === null) {
    uni.showToast({ title: '请输入进度值', icon: 'none' })
    return
  }

  if (!props.taskId) {
    uni.showToast({ title: '任务 ID 不能为空', icon: 'none' })
    return
  }

  try {
    loading.value = true

    // 获取当前任务详情
    const taskDetail = await getTaskApi(props.taskId)

    if (!taskDetail) {
      uni.showToast({ title: '获取任务数据失败', icon: 'none' })
      loading.value = false
      return
    }

    // 构造更新数据并记录本次进度
    const updateData: API.EditTask = {}

    // 生成记录时间：编辑时保持原有时分秒，新增时使用当前时间
    let isoDate: string

    if (props.editRecordId) {
      // 编辑模式：查找原记录，保持原有时分秒，只更新日期部分
      const originalRecord = taskDetail.recList?.find((record: any) => record._id === props.editRecordId)
      if (originalRecord && originalRecord.recTime) {
        const originalDateTime = new Date(originalRecord.recTime)
        const selectedDateTime = new Date(selectedDate.value)

        // 如果选择的日期与原记录日期不同，则更新日期但保持时分秒
        if (selectedDateTime.toDateString() !== originalDateTime.toDateString()) {
          const updatedDateTime = new Date(
            selectedDateTime.getFullYear(),
            selectedDateTime.getMonth(),
            selectedDateTime.getDate(),
            originalDateTime.getHours(),
            originalDateTime.getMinutes(),
            originalDateTime.getSeconds(),
            originalDateTime.getMilliseconds()
          )
          isoDate = updatedDateTime.toISOString()
        } else {
          // 日期未变，保持原有时间
          isoDate = originalRecord.recTime
        }
      } else {
        // 如果原记录没有时间，使用当前时间
        isoDate = new Date().toISOString()
      }
    } else {
      // 新增模式：使用选择的日期时间
      const now = new Date()
      const selectedDateTime = new Date(selectedDate.value)

      // 如果选择的是今天，使用完整的当前时间
      if (selectedDateTime.toDateString() === now.toDateString()) {
        isoDate = now.toISOString()
      } else {
        // 选择的是其他日期，使用该日期 + 当前时分秒
        const recordDateTime = new Date(
          selectedDateTime.getFullYear(),
          selectedDateTime.getMonth(),
          selectedDateTime.getDate(),
          now.getHours(),
          now.getMinutes(),
          now.getSeconds(),
          now.getMilliseconds()
        )
        isoDate = recordDateTime.toISOString()
      }
    }

    // 判断是编辑现有记录还是添加新记录
    if (props.editRecordId && taskDetail.recList && Array.isArray(taskDetail.recList)) {
      // 编辑现有记录
      const updatedRecList = taskDetail.recList.map((record: any) => {
        if (record._id === props.editRecordId) {
          return {
            ...record,
            val: Number(currentProgressValue.value),
            remark: remarkValue.value,
            recTime: isoDate,
            updateTime: new Date().toISOString(),
          }
        }
        return record
      })
      updateData.recList = updatedRecList
    } else {
      // 创建新的进度记录
      const newRecord = {
        _id: Date.now().toString(),
        val: Number(currentProgressValue.value),
        recTime: isoDate,
        updateTime: new Date().toISOString(),
        remark: remarkValue.value,
      }

      // 如果是首次更新进度，创建 recList
      if (!taskDetail.recList || !Array.isArray(taskDetail.recList) || taskDetail.recList.length === 0) {
        updateData.recList = [newRecord]
      } else {
        // 已有记录，添加新记录
        const recList = [...taskDetail.recList]
        recList.push(newRecord)
        updateData.recList = recList
      }
    }

    // 执行更新
    await updateTaskApi(props.taskId, updateData)

    // 显示成功提示
    uni.showToast({
      title: props.editRecordId ? '进度修改成功' : '进度更新成功',
      icon: 'success',
    })

    // 通知父组件保存成功
    emit('save-success', Number(currentProgressValue.value))

    // 关闭弹窗
    closeModal()
  } catch (error) {
    console.error('更新进度失败：', error)
    uni.showToast({
      title: '更新进度失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.keyboard-content {
  background-color: var(--color-white);
  border-radius: var(--rounded-lg);
  padding: 30rpx;
  margin: 0 auto;
  box-shadow: var(--shadow-md);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.kr-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--color-gray-800);
  flex: 1;
}

.remark-btn {
  padding: 8rpx 20rpx;
  border-radius: var(--rounded-md);
  background-color: var(--color-primary-transparent-10);
  transition: all 0.2s ease;

  &:active {
    background-color: var(--color-primary-transparent-20);
  }
}

.remark-btn-text {
  font-size: 28rpx;
  color: var(--color-primary);
  font-weight: 500;
}

.remark-area {
  margin-bottom: 24rpx;
}

.remark-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid var(--color-gray-200);
  border-radius: var(--rounded-lg);
  font-size: 28rpx;
  min-height: 120rpx;
  background-color: var(--color-gray-50);
  color: var(--color-gray-800);
}

/* 分隔线样式 */
.divider {
  height: 1px;
  background-color: var(--color-gray-200);
  margin: 20rpx 0;
}

.popup-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}

.date-picker {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  background-color: var(--color-primary-transparent-10);
  border-radius: var(--rounded-md);
  transition: all 0.2s ease;

  &:active {
    background-color: var(--color-primary-transparent-20);
  }
}

.date-icon {
  margin-right: 8rpx;
  display: flex;
  align-items: center;
}

.date-text {
  font-size: 28rpx;
  color: var(--color-primary);
  font-weight: 500;
}

.progress-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.progress-display {
  height: 80rpx;
  width: 180rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-white);
  border: 2rpx solid #dcdfe6;
  border-radius: var(--rounded-md);
  transition: all 0.2s ease;
  cursor: pointer;

  &:active {
    border-color: var(--color-primary);
    background-color: var(--color-primary-transparent-5);
  }

  &.has-value {
    border-color: var(--color-primary);
  }
}

.progress-value {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--color-gray-800);
  text-align: center;

  .progress-display:not(.has-value) & {
    color: var(--color-gray-400);
    font-weight: 400;
  }
}
.confirm-button {
  height: 80rpx;
  padding: 0 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-primary);
  border-radius: var(--rounded-md);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;

  text {
    font-size: 28rpx;
    font-weight: 500;
    color: var(--color-white);
  }

  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }

  &.disabled {
    background-color: var(--color-gray-300);
    cursor: not-allowed;

    &:active {
      transform: none;
      opacity: 1;
    }
  }
}
</style>

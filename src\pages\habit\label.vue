<template>
  <view class="pages">
    <unicloud-db
      ref="udbRef"
      v-slot="{ data, loading, error, options }"
      :page-size="100"
      orderby="create_date desc"
      collection="habit-label"
      where="user_id==$cloudEnv_uid"
    >
      <view v-if="error">{{ error.message }}</view>
      <view v-else>
        <view v-for="(item, index) in data" class="habit-box">
          {{ item.title }}
          <view class="option-edit" @click="editLabel('edit', item)"> 编辑 </view>
          <view class="option-del" @click="delLabel(item)"> 删除 </view>
        </view>
      </view>
    </unicloud-db>
    <view class="submit-btn" @click="editLabel('new')"> 添加标签 </view>
  </view>
</template>
<script setup>
import { ref } from 'vue'
import { onReady, onPullDownRefresh } from '@dcloudio/uni-app'
const db = uniCloud.database()
const udbRef = ref()
// 编辑标签
const editLabel = (type, item) => {
  const isEdit = type === 'edit'
  uni.showModal({
    title: `${isEdit ? '编辑' : '添加'}标签`,
    content: isEdit ? item.title : '',
    editable: true,
    success({ content, confirm, cancel }) {
      if (confirm) {
        uni.showLoading({
          title: '提交中',
        })
        if (isEdit) {
          db.collection('habit-label')
            .doc(item._id)
            .update({
              title: content,
            })
            .then(() => {
              udbRef.value.loadData(
                {
                  clear: true,
                },
                () => {
                  uni.showToast({
                    title: `${isEdit ? '编辑' : '添加'}成功`,
                  })
                }
              )
            })
        } else {
          db.collection('habit-label')
            .add({
              title: content,
            })
            .then(() => {
              udbRef.value.loadData(
                {
                  clear: true,
                },
                () => {
                  uni.showToast({
                    title: `${isEdit ? '编辑' : '添加'}成功`,
                  })
                }
              )
            })
        }
      }
    },
  })
}
// 删除标签
const delLabel = (item) => {
  uni.showModal({
    title: `确认删除此标签吗？`,
    success({ content, confirm, cancel }) {
      if (confirm) {
        uni.showLoading({
          title: '删除中',
        })
        db.collection('habit-label')
          .doc(item._id)
          .remove()
          .then(() => {
            udbRef.value.loadData(
              {
                clear: true,
              },
              () => {
                uni.showToast({
                  title: `删除成功`,
                })
              }
            )
          })
      }
    },
  })
}
onReady(() => {
  console.log('show')
  // udbRef = this.$refs.udb
  // uni.startPullDownRefresh()
})
// 下拉刷新
onPullDownRefresh(async () => {
  console.log('onPullDownRefresh')
  udbRef.value.loadData(
    {
      clear: true,
    },
    () => {
      // 停止下拉刷新
      uni.stopPullDownRefresh()
    }
  )
})
</script>

<style scoped lang="scss">
page {
  padding: 10px;
}

.habit-box {
  display: flex;
  flex-direction: initial;
  margin-bottom: 10px;
  padding: 15px;
  border-radius: 6px;
  background: #f4f4f0;
  font-size: 14px;
  font-weight: 600;

  .option-edit {
    margin-left: auto;
    background-color: #f9c74a;
  }

  .option-del {
    margin-left: 10px;
    background-color: #ff645d;
  }

  .option-edit,
  .option-del {
    color: #fff;
    border-radius: 4px;
    font-size: 12px;
    width: 40px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.submit-btn {
  width: 100%;
  height: 35px;
  background: #2aae67;
  color: #fff;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
}
</style>

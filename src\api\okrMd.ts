// 添加
export const addOkrMd = async (params) => {
  const content = `---
title: ${params.title}
color: ${params.color.replace('#', '')}
startDate: ${params.startDate}
endDate: ${params.endDate}
status: ${params.status || '进行中'}
id: ${generateUUID()}
---`
  await reqLife.post('/createNote', {
    content,
    fileName: `OKR/O/${params.title}.md`,
  })
}
// 删除
export const deleteOkrMd = async (key) => {
  await reqLife.post('/deleteNote', { key })
}
// 获取
export const getOkrMd = async (key: any) => {
  const { data } = await reqLife.post(`/getNote`, {
    key,
  })
  return data
}
// 获取列表
export const getOkrListMd = async (keys: any) => {
  const { data } = await reqLife.post(`/getNote`, {
    key: keys,
  })
  let notes = data.filter((item) => item.status)
  notes = notes.map((item) => {
    return {
      ...parseYAML(item.data),
      key: item.key,
    }
  })
  console.log('ccccc', notes)
  return notes
}
// 更新
export const updateOkrMd = async (key, params) => {
  const content = await getOkrMd(key)
  console.log('编辑 uuuuu', content, params.title, key)
  await addOkrMd({ ...content, ...params })
  if (params.title && !key.includes(params.title)) {
    console.log('修改了标题，需要删除原文件')
    // 修改了标题，需要删除原文件
    await reqLife.post('/deleteNote', { key })
  }
}
/**
 * 获取菜单列表
 * @param path - 可选参数，用于过滤包含指定路径的菜单项
 * @returns 如果未提供 path 参数，则返回完整的菜单数据；如果提供了 path 参数，则返回包含指定路径的菜单项数组
 * 菜单数据数组的每个元素包含以下属性：
 * - ETag: 资源的实体标签，用于缓存验证
 * - Key: 文件的路径和名称
 * - LastModified: 文件的最后修改时间
 * - Owner: 文件所有者的信息，包含 ID 和 DisplayName
 * - Size: 文件的大小
 * - StorageClass: 文件的存储类型
 */
export const getMenuMd = async (path?: string) => {
  const { data } = await reqLife.get(`/getMenus`)
  if (!path) {
    return data
  }
  return data.filter((item) => item.Key.includes(path))
}

// 添加 kr
export const addTaskMd = async (params) => {
  const content = `---
okrId: ${params.okrId}
title: ${params.title}
content: ${params.content}
startDate: ${params.startDate}
endDate: ${params.endDate}
valType: ${params.valType}
initVal: ${params.initVal}
tgtVal: ${params.tgtVal}
id: ${generateUUID()}
---`
  await reqLife.post('/createNote', {
    content,
    fileName: `OKR/KR/${params.title}.md`,
  })
}
// 获取 kr 列表
export const getTaskListMd = async (okrId?: any) => {
  const krMenuList = await getMenuMd('OKR/KR')

  const { data } = await reqLife.post(`/getNote`, {
    key: keys,
  })
  let notes = data.filter((item) => item.status)
  notes = notes.map((item) => {
    return {
      ...parseYAML(item.data),
      key: item.key,
    }
  })
  console.log('ccccc', notes)
  return notes
}

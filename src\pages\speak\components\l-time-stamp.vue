<template>
  <div class="time-stamp">{{ formattedTime }}</div>
</template>

<script setup>
import { computed } from 'vue'
import dayjs from 'dayjs'

const props = defineProps({
  time: {
    type: [Number, String, Date],
    required: true,
  },
  format: {
    type: String,
    default: 'HH:mm',
  },
})

const formattedTime = computed(() => {
  return dayjs(props.time).format(props.format)
})
</script>

<style lang="scss" scoped>
.time-stamp {
  text-align: center;
  font-size: 12px;
  color: #8a8a8a;
  margin: 8px 0;
}
</style>

<template>
  <u-popup mode="bottom" height="50%" :model-value="show" @update:model-value="emits('update:show', $event)">
    <view class="bg-white p-2 mb-15">
      <view class="text-20 mb-2">
        <u-tag
          mr-2
          v-for="(item, index) in templateList"
          :key="index"
          :text="item.name"
          type="success"
          @click="setTemplate(index)"
        />
      </view>
      <editor v-if="show" id="editor" class="ql-container" placeholder="开始输入..." @ready="onEditorReady"></editor>
      <u-button type="primary" @click="onSubmit">提交</u-button>
      <view class="flex justify-between">
        <u-button v-if="!isNew" mr-2 mt-2 flex-1 type="error" @click="onDelete">删除</u-button>
        <u-button mt-2 flex-1 type="warning" @click="closePop">关闭</u-button>
      </view>
    </view>
  </u-popup>
</template>
<script setup>
const props = defineProps({
  show: {
    type: Boolean,
    required: false,
  },
  type: {
    type: Number,
    default: 0,
  },
  date: {
    type: String,
    default: '',
  },
  info: {
    type: Object,
    default: () => ({}),
  },
})
// TODO: editor 组件每次都要重新初始化，非常耗费性能
const emits = defineEmits('update:show', 'onSubmit')

const [memoForm, resetMemoForm] = useParams({
  content: '',
})

const isNew = computed(() => !props.info._id)
let editorCtx = null
const setTemplate = (index) => {
  editorCtx.setContents({
    html: templateList.value[index].value,
  })
}

// 模版
const templateList = ref([
  {
    name: '反思',
    value:
      '<p>今天干了啥？</p><p><br></p><p>做的好不好？如果时光回溯，是否有更好的做法？</p><p><br></p><p>当前目标进度如何？是否需要调整？</p><p><br></p><p>明天干啥？</p>',
  },
])

// 编辑器初始化
const onEditorReady = () => {
  // #ifdef APP-PLUS || H5 ||MP-WEIXIN
  uni
    .createSelectorQuery()
    .select('#editor')
    .context((res) => {
      editorCtx = res.context
      if (props.info.content) {
        editorCtx.setContents({
          html: props.info.content,
        })
      }
    })
    .exec()
  // #endif
}

// 提交
const onSubmit = async () => {
  console.log('提交')
  editorCtx.getContents({
    async success({ html, text, delta }) {
      if (!html) {
        uni.showToast({
          title: '请输入内容',
          icon: 'none',
        })
        return
      }
      console.log('html============')
      console.log(html)
      const params = {
        content: html,
        type: props.type,
      }

      if (props.info._id) params._id = props.info._id
      if (props.date) params.date = props.date

      await putMemoApi(params)
      emits('onSubmit')
      editorCtx.clear()

      closePop()
    },
  })
}
// 删除
const onDelete = async () => {
  await delMemoApi(props.info._id)
  emits('onSubmit')
  closePop()
}

// 关闭弹窗
const closePop = () => {
  emits('update:show', false)
  editorCtx = null
}
</script>

<style lang="scss"></style>

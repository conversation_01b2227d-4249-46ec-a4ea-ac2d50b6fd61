# 任务：日记数据结构设计与实现

- **所属功能模块**: `diary-data`
- **任务 ID**: `task-diary-data-01`
- **优先级**: 高

## 任务描述
为了支持日记多次输入、问答、总结等复杂功能，需要调整现有 `Memo` 表的数据存储方式。本任务的目标是将 `Memo.content` 字段从纯文本扩展为存储 JSON 对象的序列化字符串，并建立相应的数据处理逻辑。

## 技术实现详情

### 1. 定义 `DiaryContent` 接口
在 `src/api/dataSchema/typings.d.ts` 文件中，定义 `DiaryContent` 及其相关接口。

```typescript
// src/api/dataSchema/typings.d.ts

/**
 * 单次日记条目
 */
export interface DiaryEntry {
  content: string; // 日记内容
  type: 'initial' | 'append' | 'legacy'; // 条目类型：'initial' 首次输入，'append' 追加输入，'legacy' 兼容旧数据
  timestamp: string; // ISO 8601 格式的时间戳
}

/**
 * 问答记录
 */
export interface QuestionAnswer {
  question: string; // 问题
  answer: string;   // 答案
  timestamp: string; // ISO 8601 格式的时间戳
}

/**
 * AI 总结
 */
export interface DiarySummary {
  content: string;    // 总结内容
  generated_at: string; // 生成时间
}

/**
 * 日记内容的完整结构，作为 Memo.content 的内容
 */
export interface DiaryContent {
  entries: DiaryEntry[];
  questions_answers: QuestionAnswer[];
  summary: DiarySummary | null;
}
```

### 2. 数据处理与兼容
- **数据处理**: 创建数据处理工具函数，负责 `DiaryContent` 对象的序列化（转为 JSON 字符串存入数据库）和反序列化（从数据库读取 JSON 字符串并解析为对象）。
- **向后兼容**: 实现逻辑，当读取旧的纯文本日记时，能自动转换为新的 `DiaryContent` 格式。例如，将旧文本作为 `entries` 数组的第一个元素：`{ entries: [{ content: "旧日记...", type: "legacy", timestamp: ... }], questions_answers: [], summary: null }`。

### 3. 代码修改范围
为实现以上功能，需要对以下文件进行修改：

-   **`src/api/dataSchema/typings.d.ts`**:
    -   新增 `DiaryEntry`, `QuestionAnswer`, `DiarySummary`, `DiaryContent` 等接口定义。
    -   修改 `Memo` 表的类型定义，将 `content` 字段类型声明为 `string` (存储 JSON 字符串)。

-   **`src/api/diary.ts` (新建文件)**:
    -   创建此文件，统一管理所有日记相关的数据库操作。
    -   **明确禁止修改 `src/api/memo.ts`**，新的日记逻辑应完全在此新文件中实现。
    -   实现 `getDiary` 函数：从数据库获取日记，对 `content` 字段进行 JSON `parse`，并处理旧的纯文本数据及非法 JSON 格式。
    -   实现 `addDiaryEntry` 函数：向指定日记添加新条目 (`DiaryEntry`)。
    -   实现 `updateDiary` 函数：将 `DiaryContent` 对象进行 JSON `stringify` 操作后更新到数据库。

-   **`src/pages/memo/diary_edit.vue`**:
    -   **必须调用 `src/api/diary.ts` 中的函数**进行数据获取和更新。
    -   重构该页面，使其能够处理和展示 `DiaryContent` 对象，而不仅仅是纯文本。
    -   UI 上需要支持展示多次输入的 `entries` 列表。
    -   提供追加输入新日记条目的功能。
    -   （远期）支持展示问答记录和 AI 总结。

-   **数据迁移 (可选，但推荐)**:
    -   考虑在 `src/common/appInit.js` 或创建一个新的 `migrateDiaryData.js` 模块中，添加一次性或启动时检查的迁移逻辑。
    -   该逻辑负责将数据库中所有旧的纯文本 `content` 转换为新的 JSON 结构，确保数据一致性。

## 验收标准
- `DiaryContent` 接口在代码中清晰定义。
- 数据读写操作能正确地进行序列化和反序列化。
- 应用能够正常读取和展示旧的纯文本日记数据。
- 新创建的日记数据在数据库中以符合 `DiaryContent` 结构的 JSON 字符串形式存储。
- 整个过程不能导致现有日记功能出现数据丢失或渲染错误。

## 依赖关系
- 无

## 状态追踪
- 未开始 
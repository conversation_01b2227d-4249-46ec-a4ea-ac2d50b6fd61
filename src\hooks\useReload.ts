import { Tools } from '@/utils/tools'

/**
 * 监听服务器数据更新，自动执行回调函数
 * @param {Function} callback 回调函数
 * @param {Object} options 配置项
 * @param {Boolean} options.immediate 是否立即执行回调函数，默认为 true
 */
export default (callback: () => void, { immediate } = { immediate: true }) => {
  // TODO 会不会卸载不掉，不是同一个函数？

  const initFn = ({ isRefresh } = { isRefresh: true }) => {
    uni.stopPullDownRefresh()
    if (isRefresh) {
      callback()
    }
  }

  // 手动触发同步
  onPullDownRefresh(() => {
    const isSync = toolCloudSync.isOpen()
    console.log('是否开启云同步：', isSync)
    if (isSync) {
      // 开启云同步，同步服务器
      console.log('触发云同步')
      syncToServer()
    } else {
      // 没开启云同步，直接触发回调
      console.log('未开启云同步，直接刷新')
      setTimeout(() => {
        initFn()
      }, 500)
    }
  })
  onLoad(() => {
    uni.$on('updateCloud', initFn) // 订阅服务器更新
    if (immediate) initFn()
  })
  onUnload(() => {
    uni.$off('updateCloud', initFn) // 取消订阅服务器更新
  })
}

# 任务：KR 详情页统计视图 - 周期数据聚合逻辑

## 任务描述
此任务是在 UI 验收通过后，实现统计视图功能的真实数据处理逻辑。重点是在`l-statistics.vue`组件中，替换模拟数据，根据传入的任务循环规则（`repeatFlag`），调用`rrrrule.ts`中的工具函数，对进度记录（`recList`）进行分组聚合，最终计算出每个周期的计划量和完成量。

## 所属功能模块
OKR 统计视图

## 技术实现详情

1.  **引入依赖**
    -   在 `l-statistics.vue` 的 `<script setup>` 中，从 `src/utils/rrrrule.ts` 引入 `getOccurrences` 函数。
    -   引入 `dayjs` 用于日期处理。

2.  **真实数据处理实现**
    -   创建一个 `computed` 属性（例如 `chartData`），当 `taskDetail` prop 发生变化时，它会自动重新计算。
    -   在 `chartData` 的计算逻辑中，检查 `taskDetail` 和 `taskDetail.repeatFlag` 是否存在。如果不存在，则返回空数组。
    -   当数据为空时，确保组件显示一个合适的缺省图，而不是使用模拟数据。

3.  **数据计算 (核心)**
    -   调用 `getOccurrences(taskDetail.repeatFlag, taskDetail.startDate, taskDetail.endDate)` 来获取按周期分组的日期 `groupedDates`。
    -   遍历 `getOccurrences` 返回的 `groupedDates` 数组。`groupedDates` 的每个子数组都代表一个周期。
    -   **对于每个周期（子数组），执行以下计算**：
        -   **计算完成量 (Completed)**: 筛选出 `taskDetail.recList` 中 `recTime` 在当前周期日期范围内的所有记录，然后将这些记录的 `val` 值累加。
        -   **计算计划量 (Plan)**: 根据循环规则计算当前周期的计划量。
            -   对于 `DAILY`, `WEEKLY`, `MONTHLY`, `INTERVAL_DAILY` 等发生日明确的类型，计划量是周期内发生日的总数乘以 `dailyTarget`。
            -   对于 `WEEKLY_N_TIMES`, `MONTHLY_N_TIMES`, `N_DAYS` 等基于次数的类型，计划量是规则定义的次数（`count` 或 `totalRequired`）。
        -   **生成 X 轴标签**: 根据周期子数组的第一个和最后一个日期，生成标签。如果周期只有一天，格式为 "MM-DD"；如果有多天，格式为 "MM/DD-MM/DD"。
    -   `chartData` 的最终返回值应该是一个对象数组，结构类似：`[{ label: '10/10-10/16', plan: 7, completed: 5 }, ...]`。

4.  **总览数据计算**
    -   创建另外两个 `computed` 属性，`totalPlan` 和 `totalCompleted`，它们分别遍历 `chartData` 的结果，计算并返回总的计划量和完成量。

5.  **与 UI 集成**
    -   确保图表渲染和交互效果不变
    -   添加适当的加载状态，确保数据处理过程不影响用户体验
    -   当没有数据或数据为空时，显示缺省图，提示用户"暂无统计数据"
    -   不使用模拟数据作为后备，真实数据决定显示内容

## 验收标准
-   UI 验收必须先通过，再开始此任务
-   `l-statistics.vue` 组件能够根据不同类型的 `repeatFlag` 正确调用 `getOccurrences`。
-   对于 `DAILY` 类型的任务，聚合后的数据以天为单位。
-   对于 `WEEKLY_N_TIMES` 类型的任务，聚合后的数据以周为单位。
-   对于 `N_DAYS` 类型的任务，聚合后的数据以 N 天为单位。
-   每个周期计算出的"完成量"与该周期内实际的进度记录总和一致。
-   每个周期计算出的"计划量"符合规则定义。
-   最终生成的 `chartData` 数组结构正确，可以用于图表渲染。
-   可以通过 `console.log` 输出 `chartData` 的结果，并手动验证其准确性。
-   当数据为空时，应该显示缺省图，而不是使用模拟数据。

## 依赖关系
-   **上游依赖**: `task-kr-statistics-01.md`, `task-kr-statistics-02.md`
-   **下游依赖**: 无

## 优先级
中 
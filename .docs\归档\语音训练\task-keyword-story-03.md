# 任务：实现训练记录保存逻辑

- **任务ID**: `task-keyword-story-03`
- **所属功能模块**: 语音训练 - 关键词讲故事
- **优先级**: 高
- **状态**: 未开始
- **依赖关系**: 无

---

## 1. 任务描述
根据需求文档，在AI成功生成评价后，需要将本次的训练记录保存到 `chatRecord` 数据库表中。本任务专注于实现此核心保存逻辑。

## 2. 技术实现详情
### 引入数据库API
-   在 `src/pages/speak/keyword-story-page.vue` 的 `<script setup>` 中，引入数据库操作模块。
    ```javascript
    import { db } from '@/api/database';
    ```

### 创建保存函数
-   在 `keyword-story-page.vue` 的 `<script setup>` 中，创建一个新的 `async` 函数 `saveTrainingRecord`。
    ```javascript
    const saveTrainingRecord = async (keywords, evaluation) => {
      if (!keywords || !evaluation) return;

      const recordToSave = {
        type: 'keywordStory',
        keywords: keywords,
        evaluation: evaluation,
      };

      try {
        await db.add('chatRecord', {
          title: keywords.join(', '),
          content: JSON.stringify(recordToSave),
        });
        console.log('训练记录保存成功！');
      } catch (error) {
        console.error('训练记录保存失败:', error);
      }
    };
    ```

### 修改评价处理逻辑
-   在 `handleTextSubmit` 函数中，成功获取 `newEvaluation` 后，调用保存函数。
    ```javascript
    // ...
    if (newEvaluation) {
      evaluationHistory.value.push(newEvaluation);
      currentEvaluationId.value = newEvaluation.id;
      inputMessage.value = '';
      uni.vibrateShort();
      // 新增调用
      saveTrainingRecord(keywords.value, newEvaluation);
    }
    // ...
    ```
-   在 `handleTranscriptionEnd` 函数中，成功获取 `newEvaluation` 后，调用保存函数。
     ```javascript
    // ...
    if (newEvaluation) {
      evaluationHistory.value.push(newEvaluation);
      currentEvaluationId.value = newEvaluation.id;
      uni.vibrateShort();
      // 新增调用
      saveTrainingRecord(keywords.value, newEvaluation);
    }
    // ...
    ```

## 3. 验收标准
-   [ ] `db` 模块被成功引入。
-   [ ] `saveTrainingRecord` 函数被正确创建。
-   [ ] 在进行一次完整的文本或语音复述后，当AI反馈出现时，`saveTrainingRecord` 函数被调用。
-   [ ] 控制台输出 “训练记录保存成功！”
-   [ ] 检查应用数据库（如通过SQLite工具），`chatRecord` 表中新增了一条记录。
-   [ ] 新记录的 `title` 字段是关键词的字符串拼接，`content` 字段是符合 `TrainingRecord` 结构的JSON字符串。 
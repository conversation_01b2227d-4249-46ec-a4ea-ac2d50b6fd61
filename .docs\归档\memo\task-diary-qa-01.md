# 任务：问答交互UI开发

- **所属功能模块**: `diary-qa`
- **任务ID**: `task-diary-qa-01`
- **优先级**: 中

## 任务描述
开发一个全局的 `z-question-answer` 组件，用于实现AI引导的问答式日记流程。该组件需要提供一个沉浸式的对话界面。

## 技术实现详情
1.  **`z-question-answer` 组件**:
    - **UI**:
        - 采用全屏弹窗或独立页面的形式，以创造专注的对话环境。
        - 界面模仿即时通讯应用，使用对话气泡展示AI提问和用户回答。
        - AI提出的问题在左侧，用户输入框和回答在右侧。
        - 底部提供文本输入框和"发送"按钮。
    - **交互**:
        - 组件启动时，显示第一个AI提出的问题。
        - 用户输入答案并发送后，该答案显示在右侧，随后AI的下一个问题出现在左侧。
        - 支持"跳过此问题"的选项。
        - 提供明确的"完成问答"按钮来结束流程。
    - **Props/Events**:
        - **Props**: 接收一个问题数组 `questions`。
        - **Events**: 当用户完成问答后，通过 `emit` 事件（如 `complete`）返回一个包含所有问答对（`questions_answers`）的数组。

## 验收标准
- `z-question-answer` 组件能根据传入的模拟问题数组，以对话形式逐条展示。
- 用户可以输入回答，并看到问答记录在界面上累积。
- "跳过"和"完成"功能正常，并能触发相应事件。
- 问答完成时，能正确返回包含所有有效回答的数组。
- UI动效流畅，交互体验接近主流的聊天应用。

## 依赖关系
- 无。可独立开发。

## 状态追踪
- 未开始 
# 背景

因为循环类型包含了很多自定义的类型，使用 rrule 库的话，需要拓展自定义类型，会导致代码太过复杂，所以决定重构自定义的 rrule 库

# 需求

- 每种类型使用一个特定的字段区分
- 每种类型带上自己需要的字段

## 循环类型定义

### 1. 每天

- 类型标识：`DAILY`
- 必要字段：

### 2. 每周几

- 类型标识：`WEEKLY`
- 必要字段：
  - `byweekday`: 指定星期几，使用星期缩写 ['MO', 'TU', 'WE', 'TH', 'FR', 'SA', 'SU']，分别对应 0-6

### 3. 每月几号

- 类型标识：`MONTHLY`
- 必要字段：
  - `bymonthday`: 指定日期 [1-31]

### 4. 每隔几天

- 类型标识：`INTERVAL_DAILY`
- 必要字段：
  - `interval`: 间隔天数，大于等于 2

### 5. 每周几天

- 类型标识：`WEEKLY_N_TIMES`
- 必要字段：
  - `count`: 每周需要完成的天数

### 6. 每月几天

- 类型标识：`MONTHLY_N_TIMES`
- 必要字段：
  - `count`: 每月需要完成的天数

### 7. 每几天

- 类型标识：`N_DAYS`
- 必要字段：
  - `interval`: 周期天数

### 8. 每几个周

- 类型标识：`N_WEEKS`
- 必要字段：
  - `interval`: 周期周数

### 9. 每几个月

- 类型标识：`N_MONTHS`
- 必要字段：
  - `interval`: 周期月数

## rrule 库用到的函数

### 基础转换函数

- `toText(rule)`: 将 RRule 选项对象或字符串转换为中文自然语言描述

### 日期计算函数

- `getOccurrences(rule, options)`: 获取指定时间范围内的所有重复日期
- `getNextOccurrence(rule, after)`: 获取下一个重复日期
- `isOccurrence(rule, date)`: 判断指定日期是否匹配重复规则

### 任务进度计算函数

- `getTaskProgressData(date, rule, progressRecords)`: 获取指定日期、指定任务的进度数据

# 需求补充 v1

## getTaskProgressData 函数优化

在函数里，从入参提取的 RuleType 判断循环类型，并返回不同的数据：

### 每天

返回 isOccurrenceToday

- 实现思路：直接返回 true，每天都需要显示

### 每周几

一周内，完成指定的星期，比如每周的周一、周三循环。
需要返回的字段跟实现思路：

- isOccurrenceToday：使用 dayjs 获取当前日期属于周几，与 rule 里的 byweekday 进行匹配
- completedToday：在 progressRecords 中筛选今天的记录并合计数量

### 每月几

一月内，完成指定的日期，比如每月的 1 号、5 号循环。
需要返回的字段跟实现思路：

- isOccurrenceToday：使用 dayjs 获取当前日期是几号，与 rule 里的 bymonthday 进行匹配
- completedToday：在 progressRecords 中筛选今天的记录并合计数量

### 每隔几天

举例：计划 1 月 1 号开始，每隔一天，每天完成 5 章，则 1 月 1 号、1 月 3 号、1 月 5 号...每天需完成 5 章。
需要返回的字段跟实现思路：

- isOccurrenceToday：计算当前日期与开始日期的天数差，然后判断是否能被间隔天数整除。如果能整除，则表示今天是任务日，返回 true；否则返回 false
- completedToday：在 progressRecords 中筛选今天的记录并合计数量，如果今天不是任务日则返回 0

### 每周几天（WEEKLY_N_TIMES）

在一周内，需要完成指定的天数，比如每周 3 天，每天完成 100 道题，意思就是在这周内总共要完成 3 天，每天完成 100 道题即可，无需指定是哪天完成，未完成之前周期内每天都显示，完成之后周期内都不显示
需要返回的字段跟实现思路：

- totalDays: 本周总共要完成的天数，基本上是从 rule.count 获取，但需要考虑特殊情况：如果本周剩余可执行天数少于 count，则以剩余可执行天数为准。例如：每周 6 天，但开始时间是周六，那么本周期内 totalDays 应为 2 天（周六和周日）而非 6 天
- completedToday: 今天已经完成的数量，在 progressRecords 中筛选今天的记录并合计数量
- completedDays: 本周已完成的天数，计算方法是获取本周的所有进度记录，按日期分组合计，然后统计合计数量大于等于 totalRequired（当天需要完成的总数）的天数
- isOccurrenceToday: 根据本周已完成天数决定，如果本周已完成的天数 < 规定天数，则返回 true，否则返回 false

实现步骤：

1. 获取当前日期（date 参数）并使用 dayjs 计算所在周的起始日期和结束日期
2. 根据起始日期和结束日期生成周期标识符 periodKey（如：2023-W12）
3. 从 progressRecords 中筛选出本周期内的所有记录
4. 将筛选出的记录按日期分组，统计每天的完成总量
5. 计算 totalDays：
   - 如果规则开始日期在当前周内，则需要计算从开始日期到周末的天数，与 rule.count 比较取较小值
   - 如果规则开始日期在当前周之前，则直接使用 rule.count
6. 计算已完成天数（completedDays）：统计完成量大于等于 totalRequired 的不同日期数量
7. 计算今日完成量（completedToday）：统计当天日期的记录总和
8. 判断今天是否应该显示（isOccurrenceToday）：
   - 如果本周已完成天数小于规定天数（completedDays < totalDays），返回 true
   - 如果本周已完成天数已达到规定天数（completedDays >= totalDays），返回 false
   - 特殊情况：如果今天已有完成记录（completedToday > 0），无论如何都返回 true
9. 返回包含上述计算结果的对象

### 每月几天（MONTHLY_N_TIMES）

在一个月的周期内，需要完成指定的天数，比如每月 5 天，每天完成 100 道题，意思就是在这个月内总共要完成 5 天，每天完成 100 道题即可，无需指定是哪天完成，未完成之前月周期内每天都显示，完成之后月周期内都不显示。
需要返回的字段跟实现思路：

- totalDays: 本月总共要完成的天数，基本上是从 rule.count 获取，但需要考虑特殊情况：如果本月剩余可执行天数少于 count，则以剩余可执行天数为准。例如：每月 20 天，但开始时间是 20 号，那么本月期内 totalDays 应为月底前的剩余天数而非 20 天
- completedToday: 今天已经完成的数量，在 progressRecords 中筛选今天的记录并合计数量
- completedDays: 本月已完成的天数，计算方法是获取本月的所有进度记录，按日期分组合计，然后统计合计数量大于等于 totalRequired（当天需要完成的总数）的天数
- isOccurrenceToday: 根据本月已完成天数决定，如果本月已完成的天数 < 规定天数，则返回 true，否则返回 false

实现步骤：

1. 获取当前日期（date 参数）并使用 dayjs 计算所在月的起始日期和结束日期
2. 根据起始日期和结束日期生成周期标识符 periodKey（如：2023-M06）
3. 从 progressRecords 中筛选出本月期内的所有记录
4. 将筛选出的记录按日期分组，统计每天的完成总量
5. 计算 totalDays：
   - 如果规则开始日期在当前月内，则需要计算从开始日期到月底的天数，与 rule.count 比较取较小值
   - 如果规则开始日期在当前月之前，则直接使用 rule.count
6. 计算已完成天数（completedDays）：统计完成量大于等于 totalRequired 的不同日期数量
7. 计算今日完成量（completedToday）：统计当天日期的记录总和
8. 判断今天是否应该显示（isOccurrenceToday）：
   - 如果本月已完成天数小于规定天数（completedDays < totalDays），返回 true
   - 如果本月已完成天数已达到规定天数（completedDays >= totalDays），返回 false
   - 特殊情况：如果今天已有完成记录（completedToday > 0），无论如何都返回 true
9. 返回包含上述计算结果的对象

### 每几天（N_DAYS）

在指定的天数周期内，需要完成指定的次数，比如每 3 天，完成 100 道题，意思就是在这 3 天内总共要完成 100 道题，而不是每天都要完成 100 道题。
需要返回的字段跟实现思路：

- totalCount: 本周期内需要完成的总次数，从 totalRequired 获取
- completedCount: 本周期内已经完成的次数，在 progressRecords 中筛选当前周期的记录并合计数量
- dayOfPeriod: 当前是本周期的第几天，计算方法是 (当前日期 - 周期起始日期) + 1
- totalDays: 本周期的总天数，从 rule.interval 获取
- completedToday: 今天已经完成的数量，在 progressRecords 中筛选今天的记录并合计数量
- isOccurrenceToday: 根据周期内已完成次数决定，如果本周期已完成的次数 < 总次数（completedCount < totalCount），则返回 true，否则返回 false。特殊情况：如果今天已有完成记录（completedToday > 0），无论如何都返回 true

实现步骤：

1. 获取当前日期（date 参数）
2. 基于规则的开始日期和间隔天数（interval），计算当前日期所在的周期：
   - 计算从开始日期到当前日期的天数差
   - 使用整除计算当前是第几个周期
   - 根据周期序号和开始日期计算当前周期的起始日期和结束日期
3. 根据起始日期和结束日期生成周期标识符 periodKey（如：2023-P5-3D）
4. 从 progressRecords 中筛选出本周期内的所有记录，并计算总完成次数（completedCount）
5. 计算当前是周期内的第几天（dayOfPeriod）：当前日期与周期起始日期的差值 + 1
6. 计算今日完成量（completedToday）：统计当天日期的记录总和
7. 判断今天是否应该显示（isOccurrenceToday）：
   - 如果本周期已完成次数小于总次数（completedCount < totalCount），返回 true
   - 如果本周期已完成次数已达到总次数（completedCount >= totalCount），返回 false
   - 特殊情况：如果今天已有完成记录（completedToday > 0），无论如何都返回 true
8. 返回包含上述计算结果的对象

### 每几个周（N_WEEKS）

在指定的周数周期内，需要完成指定的次数，比如每两周完成 20 章，意思就是在这两周内总共要完成 20 章，而不是每天都要完成 20 章。
需要返回的字段跟实现思路：

- totalCount: 本周期内需要完成的总次数，从 totalRequired 获取
- completedCount: 本周期内已经完成的次数，在 progressRecords 中筛选当前周期的记录并合计数量
- weekOfPeriod: 当前处于周期的第几周，计算方法是 (当前周数 - 周期起始周数) + 1
- totalWeeks: 本周期的总周数，从 rule.interval 获取
- completedToday: 今天已经完成的数量，在 progressRecords 中筛选今天的记录并合计数量
- isOccurrenceToday: 根据周期内已完成次数决定，如果本周期已完成的次数 < 总次数（completedCount < totalCount），则返回 true，否则返回 false。特殊情况：如果今天已有完成记录（completedToday > 0），无论如何都返回 true

实现步骤：

1. 获取当前日期（date 参数）
2. 基于规则的开始日期和间隔周数（interval），计算当前日期所在的周期：
   - 计算从开始日期到当前日期的周数差
   - 使用整除计算当前是第几个周期
   - 根据周期序号和开始日期计算当前周期的起始日期和结束日期
3. 根据起始日期和结束日期生成周期标识符 periodKey（如：2023-P5-2W）
4. 从 progressRecords 中筛选出本周期内的所有记录，并计算总完成次数（completedCount）
5. 计算当前是周期内的第几周（weekOfPeriod）：当前周数与周期起始周数的差值 + 1
6. 计算今日完成量（completedToday）：统计当天日期的记录总和
7. 判断今天是否应该显示（isOccurrenceToday）：
   - 如果本周期已完成次数小于总次数（completedCount < totalCount），返回 true
   - 如果本周期已完成次数已达到总次数（completedCount >= totalCount），返回 false
   - 特殊情况：如果今天已有完成记录（completedToday > 0），无论如何都返回 true
8. 返回包含上述计算结果的对象

### 每几个月（N_MONTHS）

在指定的月数周期内，需要完成指定的次数，比如每两个月完成 20 章，意思就是在这两个月内总共要完成 20 章，而不是每天都要完成 20 章。
需要返回的字段跟实现思路：

- totalCount: 本周期内需要完成的总次数，从 totalRequired 获取
- completedCount: 本周期内已经完成的次数，在 progressRecords 中筛选当前周期的记录并合计数量
- monthOfPeriod: 当前处于周期的第几月，计算方法是 (当前月份 - 周期起始月份) + 1
- totalMonths: 本周期的总月数，从 rule.interval 获取
- completedToday: 今天已经完成的数量，在 progressRecords 中筛选今天的记录并合计数量
- isOccurrenceToday: 根据周期内已完成次数决定，如果本周期已完成的次数 < 总次数（completedCount < totalCount），则返回 true，否则返回 false。特殊情况：如果今天已有完成记录（completedToday > 0），无论如何都返回 true

实现步骤：

1. 获取当前日期（date 参数）
2. 基于规则的开始日期和间隔月数（interval），计算当前日期所在的周期：
   - 计算从开始日期到当前日期的月数差
   - 使用整除计算当前是第几个周期
   - 根据周期序号和开始日期计算当前周期的起始日期和结束日期
3. 根据起始日期和结束日期生成周期标识符 periodKey（如：2023-P5-2M）
4. 从 progressRecords 中筛选出本周期内的所有记录，并计算总完成次数（completedCount）
5. 计算当前是周期内的第几月（monthOfPeriod）：当前月份与周期起始月份的差值 + 1
6. 计算今日完成量（completedToday）：统计当天日期的记录总和
7. 判断今天是否应该显示（isOccurrenceToday）：
   - 如果本周期已完成次数小于总次数（completedCount < totalCount），返回 true
   - 如果本周期已完成次数已达到总次数（completedCount >= totalCount），返回 false
   - 特殊情况：如果今天已有完成记录（completedToday > 0），无论如何都返回 true
8. 返回包含上述计算结果的对象

## getOccurrences 函数重构优化

`getOccurrences` 函数需要根据不同的循环类型，计算指定日期范围内的所有符合规则的日期。

### 实现思路

针对不同的循环类型，实现方式如下：

#### 1. 每天（DAILY）

- 从起始日期到结束日期，每一天都符合规则
- 实现步骤：
  1. 使用 dayjs 计算起始日期和结束日期之间的天数差
  2. 生成这个范围内的每一天，添加到结果数组中
  3. count 值等于天数差加 1（包含起始日和结束日）

#### 2. 每周几（WEEKLY）

- 从起始日期到结束日期，筛选出符合指定星期几的日期
- 实现步骤：
  1. 遍历从起始日期到结束日期的每一天
  2. 使用 dayjs 获取当天是星期几
  3. 检查是否在规则的 byweekday 数组中
  4. 如果匹配，则添加到结果数组中
  5. count 值等于符合条件的日期数量

#### 3. 每月几号（MONTHLY）

- 从起始日期到结束日期，筛选出符合指定日期的日期
- 实现步骤：
  1. 遍历从起始日期到结束日期的每一天
  2. 使用 dayjs 获取当天是几号
  3. 检查是否在规则的 bymonthday 数组中
  4. 如果匹配，则添加到结果数组中
  5. count 值等于符合条件的日期数量

#### 4. 每隔几天（INTERVAL_DAILY）

- 从规则的开始日期起，每隔指定天数生成一个日期
- 实现步骤：
  1. 确定计算的起点（规则的开始日期或函数参数的起始日期，取较晚者）
  2. 计算从规则开始日期到计算起点的天数差
  3. 计算第一个符合条件的日期（需要考虑天数差与间隔的关系）
  4. 从第一个符合条件的日期开始，每隔指定天数生成一个日期，直到结束日期
  5. count 值等于符合条件的日期数量

#### 5. 每周几天（WEEKLY_N_TIMES）

- 这种类型需要将周期内的每一天都添加到日期数组中
- 实现步骤：
  1. 遍历从起始日期到结束日期的每一天，全部添加到结果数组中
  2. 计算 count 值：
     - 计算从开始日期到结束日期之间的完整周数
     - 对于每个完整周，count 值增加规则中指定的天数（rule.count）
     - 对于不完整的周（如开始日期不是周一或结束日期不是周日），需要根据实际可用天数计算 count
     - 例如：如果规则是每周完成 3 天，从周三开始到下周二结束，则 count 为 5（因为包含了第一周的 5 天和第二周的 2 天，但每周最多只需完成 3 天）

#### 6. 每月几天（MONTHLY_N_TIMES）

- 这种类型需要将周期内的每一天都添加到日期数组中
- 实现步骤：
  1. 遍历从起始日期到结束日期的每一天，全部添加到结果数组中
  2. 计算 count 值：
     - 计算从开始日期到结束日期之间的完整月数
     - 对于每个完整月，count 值增加规则中指定的天数（rule.count）
     - 对于不完整的月（如开始日期不是月初或结束日期不是月末），需要根据实际可用天数计算 count
     - 例如：如果规则是每月完成 5 天，从 15 号开始到下月 3 号结束，则 count 为 8（因为包含了第一个月的 16 天和第二个月的 3 天，第一个月完成 5 天，第二个月只需完成 3 天）

#### 7. 每几天（N_DAYS）

- 这种类型需要将周期内的每一天都添加到日期数组中
- 实现步骤：
  1. 遍历从起始日期到结束日期的每一天，全部添加到结果数组中
  2. 计算 count 值：
     - 计算从开始日期到结束日期之间的总天数
     - 将总天数除以周期天数（interval）并向上取整，得到周期数
     - count 值就是周期数

#### 8. 每几个周（N_WEEKS）

- 这种类型需要将周期内的每一天都添加到日期数组中
- 实现步骤：
  1. 遍历从起始日期到结束日期的每一天，全部添加到结果数组中
  2. 计算 count 值：
     - 计算从开始日期到结束日期之间的总周数（包括不完整的周）
     - 将总周数除以周期周数（interval）并向上取整，得到周期数
     - count 值就是周期数
     - 例如：如果开始时间是 6 月 2 日（周五），结束时间是 6 月 21 日（周六），interval = 1，则总周数为 3（第一周 3 天，第二周 7 天，第三周 6 天），count = 3

#### 9. 每几个月（N_MONTHS）

- 这种类型需要将周期内的每一天都添加到日期数组中
- 实现步骤：
  1. 遍历从起始日期到结束日期的每一天，全部添加到结果数组中
  2. 计算 count 值：
     - 计算从开始日期到结束日期之间的总月数（包括不完整的月）
     - 将总月数除以周期月数（interval）并向上取整，得到周期数
     - count 值就是周期数
     - 例如：如果开始时间是 6 月 15 日，结束时间是 8 月 5 日，interval = 1，则总月数为 3（6 月 16 天，7 月 31 天，8 月 5 天），count = 3

### 返回值

函数返回一个对象，包含两个属性：

1. `dates`: 字符串数组，包含所有符合规则的日期，格式为 `YYYY-MM-DD`
2. `count`: 数值，表示根据规则计算出的总次数（根据不同规则类型有不同的计算方式）

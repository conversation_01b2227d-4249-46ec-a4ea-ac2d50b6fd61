# V1.1 - 执行上下文管理 - 使用示例

## 1. 功能概述

V1.1 版本在原有意图识别基础上，新增了执行上下文管理功能，能够：
- 自动生成执行计划
- 逐步执行工具调用
- 实时推送执行状态
- 智能管理上下文数据

## 2. 使用场景

### 2.1 查找项目任务
**用户输入**: "查看okr项目的任务"

**执行流程**:
1. **意图识别**: find_task
2. **执行计划生成**: 2步骤计划
   - 步骤1: 获取项目列表 (getProjects)
   - 步骤2: 获取项目任务 (getTasks)
3. **执行过程**: 逐步执行并推送状态

**SSE 消息流**:
```javascript
// 1. 意图识别阶段
{ type: 'intent_type', intentType: 'find_task' }
{ type: 'intent_content_start', content: '查看okr项目的任务' }

// 2. 执行计划阶段
{ 
  type: 'execution_plan_start', 
  plan: { planId: 'xxx', totalSteps: 2 } 
}

// 3. 步骤执行阶段
{ 
  type: 'execution_step', 
  step: { 
    stepId: 'step1', 
    description: '获取项目列表', 
    toolName: 'getProjects' 
  } 
}

{ 
  type: 'step_result', 
  stepId: 'step1', 
  result: { 
    success: true, 
    data: [
      { id: 'proj-1', name: 'OKR项目' },
      { id: 'proj-2', name: '日常任务' }
    ] 
  } 
}

{ 
  type: 'execution_step', 
  step: { 
    stepId: 'step2', 
    description: '获取项目下的任务', 
    toolName: 'getTasks' 
  } 
}

{ 
  type: 'step_result', 
  stepId: 'step2', 
  result: { 
    success: true, 
    tasks: [
      { id: 'task-1', title: '制定Q1目标', completed: false },
      { id: 'task-2', title: '更新KR进度', completed: false }
    ] 
  } 
}

// 4. 执行完成
{ 
  type: 'execution_complete', 
  plan: { status: 'completed' },
  contextData: ['targetProject', 'taskCount', 'uncompletedTasks']
}
```

### 2.2 直接查找任务
**用户输入**: "查看我的任务"

**执行流程**:
1. **意图识别**: find_task
2. **执行计划生成**: 1步骤计划
   - 步骤1: 获取任务列表 (getTasks)
3. **执行过程**: 直接获取任务

**SSE 消息流**:
```javascript
{ type: 'intent_type', intentType: 'find_task' }
{ type: 'intent_content_start', content: '查看我的任务' }
{ type: 'execution_plan_start', plan: { totalSteps: 1 } }
{ type: 'execution_step', step: { toolName: 'getTasks' } }
{ type: 'step_result', result: { tasks: [...] } }
{ type: 'execution_complete' }
```

### 2.3 普通聊天
**用户输入**: "你好，今天天气怎么样？"

**执行流程**:
1. **意图识别**: chat
2. **跳过执行**: 直接返回AI回复
3. **无执行计划**: 保持原有行为

## 3. 前端集成示例

### 3.1 SSE 事件监听
```javascript
// 监听 SSE 事件
sseChannel.on('message', (data) => {
  switch (data.type) {
    case 'intent_type':
      console.log('识别意图:', data.intentType)
      break
      
    case 'execution_plan_start':
      console.log('开始执行计划:', data.plan)
      showExecutionProgress(data.plan.totalSteps)
      break
      
    case 'execution_step':
      console.log('执行步骤:', data.step)
      updateStepStatus(data.step.stepId, 'executing')
      break
      
    case 'step_result':
      console.log('步骤结果:', data.result)
      updateStepStatus(data.stepId, 'completed')
      displayStepResult(data.result)
      break
      
    case 'execution_complete':
      console.log('执行完成:', data.plan)
      hideExecutionProgress()
      showFinalResult(data.contextData)
      break
      
    case 'step_error':
      console.error('步骤错误:', data.error)
      updateStepStatus(data.stepId, 'failed')
      showError(data.error)
      break
  }
})
```

### 3.2 执行进度显示
```javascript
function showExecutionProgress(totalSteps) {
  const progressBar = document.getElementById('execution-progress')
  progressBar.style.display = 'block'
  progressBar.innerHTML = `
    <div class="progress-header">正在执行任务...</div>
    <div class="progress-steps">
      ${Array.from({length: totalSteps}, (_, i) => 
        `<div class="step" id="step-${i+1}">步骤 ${i+1}</div>`
      ).join('')}
    </div>
  `
}

function updateStepStatus(stepId, status) {
  const stepElement = document.getElementById(stepId)
  if (stepElement) {
    stepElement.className = `step ${status}`
  }
}
```

## 4. 上下文数据使用

### 4.1 自动提取的上下文数据
```javascript
// 执行完成后可获取的上下文数据
const contextData = {
  'targetProject': { id: 'proj-1', name: 'OKR项目' },
  'taskCount': 2,
  'uncompletedTasks': [
    { id: 'task-1', title: '制定Q1目标', completed: false }
  ]
}
```

### 4.2 上下文数据应用
- **智能筛选**: 自动识别用户关注的项目
- **统计信息**: 提供任务数量和完成状态统计
- **后续对话**: 为下一轮对话提供上下文支持

## 5. 错误处理

### 5.1 工具调用失败
```javascript
// SSE 错误消息
{
  type: 'step_error',
  stepId: 'step-1',
  error: '获取项目列表失败：网络超时',
  timestamp: 1640995200000
}

// 执行失败
{
  type: 'execution_failed',
  plan: { status: 'failed', error: '...' },
  error: '执行计划失败',
  timestamp: 1640995200000
}
```

### 5.2 前端错误处理
```javascript
function handleExecutionError(error) {
  // 显示错误信息
  showNotification('执行失败: ' + error, 'error')
  
  // 隐藏进度条
  hideExecutionProgress()
  
  // 提供重试选项
  showRetryButton()
}
```

## 6. 性能优化建议

### 6.1 前端优化
- 使用防抖处理用户输入
- 缓存执行结果避免重复调用
- 合理显示加载状态

### 6.2 体验优化
- 提供执行进度可视化
- 支持执行过程中断
- 显示预估执行时间

## 7. 调试技巧

### 7.1 开启调试日志
```javascript
// 在云函数中查看详细日志
console.log('AI 完整返回内容：', fullContent)
console.log('提取的意图类型：', intentType)
console.log('执行计划：', executionPlan)
console.log('上下文数据：', Array.from(context.contextData.keys()))
```

### 7.2 前端调试
```javascript
// 监听所有 SSE 消息
sseChannel.on('message', (data) => {
  console.log('SSE Message:', data.type, data)
})
```

## 8. 注意事项

### 8.1 V1.1 版本限制
- 当前使用模拟数据，非真实 API 调用
- 不支持动态参数解析
- create_task 功能暂未实现

### 8.2 最佳实践
- 合理处理执行超时情况
- 提供用户友好的错误提示
- 保持界面响应性

## 9. 升级路径

V1.2 版本将提供：
- 真实 API 调用集成
- 动态参数解析支持
- 更丰富的工具集合
- 完善的错误恢复机制

当前 V1.1 版本的集成代码在 V1.2 升级时无需大幅修改，主要是数据格式的细微调整。

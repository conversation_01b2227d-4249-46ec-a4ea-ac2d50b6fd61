<template>
  <view>
    <!-- 添加账单记录 -->
    <uni-popup ref="popRef" type="bottom" background-color="rgba(0,0,0,0)" @change="onChange">
      <view class="bg-white rounded-t-2">
        <view class="text-35 text-center py-4" style="border-bottom: 1px solid #eee">请选择月份</view>
        <scroll-view :scroll-top="scrollTop" scroll-y="true" class="h-800 pb-4">
          <view v-for="(year, index) in yearList" :key="index" class="px-4">
            <view class="text-center color-gray mt-5 mb-2">{{ year }}</view>
            <view class="flex flex-wrap">
              <view v-for="(month, index) in 12" :key="index" class="w-1/4 p-1 text-center">
                <view @click="onSubmit(year, month)" class="rounded-1 p-4" :style="{
                  background: isCurMonth(year, month) ? '#3eb575' : '#f5f5f5',
  color: isCurMonth(year, month) ? 'white' : ''
}">
                  <view>{{ month }}月</view>
                  <view class="text-22 mt-1" :class="{ 'color-gray': !isCurMonth(year, month) }">{{
                    getMonthExpense(year, month) }}</view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="z-bottom"></view>
    </uni-popup>
  </view>
</template>

<script setup>
import currency from 'currency.js'

const popRef = ref(null)
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  date: {
    type: String,
    default: '',
  },
})
const emits = defineEmits(['update:open', 'update:date'])

const yearList = ref([])
const bills = ref([])

const isCurMonth = (year, month) => {
  const d = dayjs(props.date).format('YYYY-MM')
  return dayjs(`${year}-${month}`).isSame(d, 'month')
}

const getMonthExpense = (year, month) => {
  const monthBills = bills.value.filter(bill => {
    const billDate = dayjs(bill.date)
    return billDate.year() === year && billDate.month() + 1 === month && bill.type === 'expense'
  })
  const total = monthBills.reduce((sum, bill) => {
    return currency(sum).add(bill.amount)
  }, currency(0))
  return `¥${total.format({ symbol: '' })}`
}

onMounted(async () => {
  bills.value = await getBillListApi()
  const years = new Set(bills.value.map(bill => new Date(bill.date).getFullYear()))
  yearList.value = Array.from(years).sort((a, b) => a - b)
})

watch(
  () => props.open,
  (val) => {
    if (val) {
      popRef.value.open('bottom')
    } else {
      popRef.value.close()
    }
  }
)
const onChange = (e) => {
  emits('update:open', e.show)
}
const onSubmit = (year, month) => {
  emits('update:date', `${year}-${String(month).padStart(2, '0')}`)
  popRef.value.close()
}
</script>

<style lang="scss"></style>

# 任务：扩展周概览弹窗UI

- **所属功能模块**: 周计划与复盘
- **优先级**: 高
- **状态**: 待办

## 任务描述
根据需求文档，扩展位于 `/pages/okr/today.vue` 内的现有"周概览"弹窗，为其增加标签页功能，以容纳"周目标"、"周概览"和"周复盘"三个视图。此任务仅关注UI结构的搭建和静态展示。

## 技术实现详情
1.  **修改组件**: 定位并修改 `/pages/okr/today.vue` 中的周概览弹窗组件。
2.  **添加标签页**:
    - 引入或创建一个简单的标签页组件。
    - 创建三个标签页，分别为"周目标"、"周概览"、"周复盘"。
    - 确保标签页切换功能正常，并带有平滑的CSS过渡效果。
3.  **内容布局**:
    - "周概览"标签页暂时保留原有内容。
    - "周目标"和"周复盘"标签页需要包含一个用于显示内容的区域和一个"编辑"按钮。
    - 使用 Font Awesome 图标库为编辑按钮添加图标。

## 验收标准
- 弹窗中成功渲染出三个标签页，且默认选中"周概览"。
- 点击不同标签页，能够切换到对应的视图。
- "周目标"和"周复盘"视图中正确显示了内容区域和"编辑"按钮。
- 页面布局与现有应用风格保持一致，无样式问题。
- "编辑"按钮此时无需实现跳转功能。

## 依赖关系
- 无 
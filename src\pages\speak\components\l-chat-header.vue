<template>
  <z-page-navbar
    :title="title"
    right-button-type="more"
    @back="onBack"
    @menu-item-click="handleMenuClick"
  >
    <template #menu-items>
      <div class="action-menu-item" @click="handleMenuClick('record')">
        <i class="fas fa-history"></i>
        <span>聊天记录</span>
      </div>
      <div class="action-menu-item" @click="handleMenuClick('reset')">
        <i class="fas fa-sync-alt"></i>
        <span>重置对话</span>
      </div>
    </template>
  </z-page-navbar>
</template>

<script setup>
import ZPageNavbar from '@/components/z-page-navbar.vue'

defineProps({
  title: {
    type: String,
    default: '聊天',
  },
  unreadCount: {
    type: [Number, String],
    default: 0,
  },
})

const emit = defineEmits(['back', 'reset', 'record'])

const onBack = () => {
  emit('back')
}

const handleMenuClick = (action) => {
  if (action === 'reset') {
    emit('reset')
  } else if (action === 'record') {
    emit('record')
  }
}
</script>

<style lang="scss" scoped>
.action-menu-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  font-size: 15px;
  color: var(--color-gray-800);

  i {
    margin-right: 10px;
    color: var(--color-primary);
  }

  &.delete {
    color: var(--color-danger);
    i {
      color: var(--color-danger);
    }
  }
}
</style>

import os
import sys
import time
import json
import shutil
import subprocess
import dayjs
import inquirer
import requests
from pathlib import Path
from tools import upload_file, remove_js_comments, add_zero
from conf import conf
from log import log, succeed, info, error, gray, loading

# 全局变量
current_time = ''
new_version_code = ''
upload_file_name = ''
upload_file_path = ''
task_list = []

# 删除目录及其内容
def delete_dir(path):
    if os.path.exists(path):
        for file in os.listdir(path):
            cur_path = os.path.join(path, file)
            if os.path.isdir(cur_path):
                delete_dir(cur_path)
            else:
                os.remove(cur_path)
        os.rmdir(path)

# 执行命令并返回结果
def exec_promise(cmd):
    cmd = cmd.replace('\r', ' ').replace('\n', ' ')
    try:
        result = subprocess.run(cmd, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return result.stdout
    except subprocess.CalledProcessError as e:
        raise e

# 统计任务耗时
def calc_elapsed_time(task_name):
    spinner = loading(f"{task_name}\n")
    start_time = time.time()
    interval_id = None

    def update_spinner():
        nonlocal interval_id
        while True:
            elapsed_time = time.time() - start_time
            minutes, seconds = divmod(elapsed_time, 60)
            time_str = f"{int(minutes):02}:{int(seconds):02}"
            spinner.text = f"[耗时 {time_str}] {task_name}\n"
            time.sleep(1)

    interval_id = threading.Thread(target=update_spinner)
    interval_id.start()

    def stop_spinner():
        nonlocal interval_id
        interval_id.join()
        spinner.stop()
        elapsed_time = time.time() - start_time
        minutes, seconds = divmod(elapsed_time, 60)
        return f"{int(minutes):02}:{int(seconds):02}"

    return stop_spinner

# 打开 HbuilderX
async def open_hbuilderx(config, index):
    try:
        log(f"({index}) 打开 HbuilderX")
        stop_spinner = calc_elapsed_time('正在打开 HbuilderX')
        await exec_promise(config['script_openHbuilder'])
        time = stop_spinner()
        succeed(f"打开 HbuilderX 成功 {time}")
        if int(time.split(':')[1]) >= 1:
            stop_spinner2 = calc_elapsed_time('HbuilderX 正在初始化中...')
            time.sleep(7)
            stop_spinner2()
    except Exception as e:
        error('打开 HbuilderX 失败')
        error(e)
        sys.exit(1)

# 关闭 HbuilderX
async def close_hbuilderx(config, index):
    try:
        log(f"({index}) 关闭 HbuilderX")
        stop_spinner = calc_elapsed_time('正在关闭 HbuilderX')
        await exec_promise(config['script_closeHbuilder'])
        time = stop_spinner()
        succeed(f"关闭 HbuilderX 成功 {time}")
        time.sleep(1)
    except Exception as e:
        error('关闭 HbuilderX 失败')
        error(e)
        sys.exit(1)

# 运行开发环境
async def run_dev(config, index):
    try:
        log(f"({index}) 运行开发环境")
        stop_spinner = calc_elapsed_time('正在运行开发环境')
        await exec_promise(config['script_runDev'])
        time = stop_spinner()
        succeed(f"运行开发环境 {time}")
    except Exception as e:
        error('运行开发环境失败')
        error(e)
        sys.exit(1)

# 切换服务端环境
async def cut_server(config, index):
    try:
        log(f"({index}) 切换服务端环境")
        stop_spinner = calc_elapsed_time('正在切换 服务端环境')
        await exec_promise(config['cutServer'])
        env_path = os.path.join(config['envPath'], 'env.js')
        envm_path = os.path.join(config['envPath'], 'env.mjs')
        s_env_path = os.path.join(config['envPath'], f'env.{env}.js')
        os.remove(env_path)
        os.remove(envm_path)
        shutil.copyfile(s_env_path, env_path)
        shutil.copyfile(s_env_path, envm_path)
        time = stop_spinner()
        succeed(f"已切换到 {env} 环境")
    except Exception as e:
        error('切换服务端环境失败')
        error(e)
        sys.exit(1)

# 修改 manifestFile 中的 appid
async def cut_appid(config, index):
    try:
        log(f"({index}) 修改 manifestFile appid")
        stop_spinner = calc_elapsed_time('正在修改 manifestFile appid')
        with open(config['manifestFile'], 'r', encoding='utf-8') as f:
            manifest_data = json.load(f)
        manifest_data['appid'] = config['APPID']
        with open(config['manifestFile'], 'w', encoding='utf-8') as f:
            json.dump(manifest_data, f, indent=4)
        global new_version_code
        new_version_code = manifest_data['versionCode']
        time = stop_spinner()
        succeed(f"已修改 manifestFile appid: {config['APPID']}")
    except Exception as e:
        error('���改失败')
        error(e)
        sys.exit(1)

# 更新版本号
def update_version_code(config):
    try:
        with open(config['manifestFile'], 'r', encoding='utf-8') as f:
            manifest_data = json.load(f)
        manifest_data['versionCode'] += 1
        manifest_data['versionName'] = '.'.join(str(manifest_data['versionCode']))
        with open(config['manifestFile'], 'w', encoding='utf-8') as f:
            json.dump(manifest_data, f, indent=4)
        global new_version_code
        new_version_code = manifest_data['versionCode']
    except Exception as e:
        print(e)

# 构建 Wgt 包
async def build_wgt(config, index):
    delete_dir(config['wgtPath'])
    try:
        update_version_code(config)
        log(f"({index}) 构建 wgt 包")
        stop_spinner = calc_elapsed_time('正在构建 wgt 包')
        await exec_promise(config['script_buildWgt'])
        time = stop_spinner()
        succeed(f"wgt 包构建成功，耗时 {time}")
        name, suffix = config['wgtName'].split('.')
        now_time = time.localtime()
        upload_file_name = f"{name}__{now_time.tm_year}-{now_time.tm_mon}-{now_time.tm_mday}-{now_time.tm_hour}-{now_time.tm_min}-{now_time.tm_sec}__v{new_version_code}.{suffix}"
        upload_file_path = os.path.join(config['wgtPath'], config['wgtName'])
    except Exception as e:
        error('构建 Wgt 失败')
        error(e)
        sys.exit(1)

# 构建 apk 包
async def build_apk(config, index):
    try:
        for key, value in config['androidConfig'].items():
            config['script_buildApk'] += f" {key} {value}"
        delete_dir(config['apkPath'])
        update_version_code(config)
        log(f"({index}) 构建 apk 包")
        stop_spinner = calc_elapsed_time('正在构建 apk 包')
        await exec_promise(config['script_buildApk'])
        time = stop_spinner()
        succeed(f"apk 包构建成功，耗时 {time}")
        apk_name = os.listdir(config['apkPath'])[0]
        upload_file_path = os.path.join(config['apkPath'], apk_name)
        name, suffix = apk_name.split('.')
        upload_file_name = f"{name}__v{new_version_code}.{suffix}"
    except Exception as e:
        error('构建 apk 失败')
        error(e)
        sys.exit(1)

# 发布 H5
async def update_h5(config, index):
    try:
        log(f"({index}) 发布 H5")
        stop_spinner = calc_elapsed_time('正在发布 H5')
        await exec_promise(config['script_buildH5'])
        time = stop_spinner()
        succeed(f"H5 发布成功，耗时 {time}")
    except Exception as e:
        error('发布 H5 失败')
        error(e)
        sys.exit(1)

# 上传新版本
async def update_version(config, index):
    try:
        log(f"({index}) 上传新版本")
        env = __import__('src.config.env')
        deploy_task = platform
        location = await upload_file(upload_file_path, upload_file_name)
        version_name = '.'.join(str(new_version_code))
        params = {
            'appid': config['APPID'],
            'title': '版本' + version_name,
            'contents': '发布时间：' + dayjs().format('MM-DD HH:mm:ss'),
            'type': 'wgt' if deploy_task == 'app:wgt' else 'native_app',
            'version': version_name,
            'url': f"https://{location}",
            'is_mandatory': True,
        }
        response = requests.post(f"{env.default.baseHost}/app/update", json=params)
        data = response.json()
        if data:
            info(f"版本号：{version_name}")
    except Exception as e:
        error('上传新版本失败')
        error(e)
        sys.exit(1)

# 输出打包信息
def output_build_info():
    log('\n')
    succeed('项目部署成功！')
    info(f"部署环境 {'开发环境' if env == 'dev' else '生产环境'}")
    info(f"部署时间 {dayjs().format('YYYY-MM-DD HH:mm:ss')}")
    info(f"部署耗时 {dayjs(time.time() - current_time).format('mm:ss')}")

# 命令行交互式选择部署任务
async def select_deploy_task():
    questions = [
        {
            'type': 'list',
            'name': 'deployTask',
            'message': '请选择部署任务',
            'choices': [
                {'name': '发布 wgt', 'value': 'updateWgt'},
                {'name': '发布 apk', 'value': 'updateApk'},
            ],
        },
    ]
    answers = inquirer.prompt(questions)
    return answers['deployTask']

# 创建任务列表
async def create_task_list():
    deploy_task = platform
    task_list.append(cut_server)
    task_list.append(cut_appid)

    if model == 'dev':
        task_list.append(close_hbuilderx)
        task_list.append(open_hbuilderx)
        task_list.append(run_dev)
        return

    if deploy_task == 'all':
        task_list.append(open_hbuilderx)
        task_list.append(update_h5)
        task_list.append(build_apk)
        task_list.append(update_version)
        task_list.append(output_build_info)
    if deploy_task == 'app:wgt':
        task_list.append(open_hbuilderx)
        task_list.append(build_wgt)
        task_list.append(update_version)
        task_list.append(output_build_info)
    if deploy_task == 'app:apk':
        task_list.append(open_hbuilderx)
        task_list.append(build_apk)
        task_list.append(update_version)
        task_list.append(output_build_info)
    if deploy_task == 'H5':
        task_list.append(open_hbuilderx)
        task_list.append(update_h5)
        task_list.append(output_build_info)

# 执行任务列表
async def execute_task_list(config):
    global current_time
    current_time = time.time()
    for index, execute in enumerate(task_list):
        await execute(config, index + 1)
    sys.exit(0)

if __name__ == '__main__':
    try:
        create_task_list()
        c = {}
        dev, prod, *config = conf
        if env == 'dev':
            c = {**config, **dev}
        else:
            c = {**config, **prod}
        execute_task_list(c)
    except Exception as e:
        error(e)
        sys.exit(1)

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信息中转站</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css">
    <style>
        :root {
            --primary-color: #4285f4;
            --primary-light: #e8f0fe;
            --primary-dark: #3367d6;
            --success-color: #34a853;
            --warning-color: #fbbc05;
            --danger-color: #ea4335;
            --gray-light: #f5f5f5;
            --gray: #9e9e9e;
            --dark: #202124;
            --shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            --radius: 8px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
            background-color: var(--gray-light);
            color: var(--dark);
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 15px;
            transition: all 0.3s ease;
        }
        
        header {
            margin-bottom: 20px;
            text-align: center;
        }
        
        h1 {
            font-size: 28px;
            margin-bottom: 10px;
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .description {
            color: var(--gray);
            font-size: 14px;
            margin-bottom: 20px;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .card {
            background-color: #fff;
            border-radius: var(--radius);
            padding: 20px;
            box-shadow: var(--shadow);
        }
        
        .channel-info {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
            padding: 15px;
            background-color: var(--primary-light);
            border-radius: var(--radius);
            margin-bottom: 20px;
        }
        
        .channel-label {
            font-size: 14px;
            color: var(--primary-dark);
        }
        
        #channelIdDisplay {
            font-weight: 500;
            color: var(--primary-color);
            background-color: rgba(255, 255, 255, 0.5);
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .input-section {
            position: relative;
        }
        
        .input-group {
            position: relative;
            margin-bottom: 15px;
        }
        
        textarea {
            width: 100%;
            min-height: 120px;
            border: 1px solid #ddd;
            border-radius: var(--radius);
            padding: 12px;
            font-size: 16px;
            transition: all 0.3s;
            resize: vertical;
        }
        
        textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }
        
        .char-count {
            position: absolute;
            right: 10px;
            bottom: 10px;
            font-size: 12px;
            color: var(--gray);
            background-color: rgba(255, 255, 255, 0.8);
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        .buttons {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 16px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn-secondary {
            background-color: white;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }
        
        .btn-secondary:hover {
            background-color: var(--primary-light);
        }
        
        .btn-warn {
            background-color: var(--warning-color);
        }
        
        .btn-warn:hover {
            background-color: #f2a600;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
        }
        
        .btn-danger:hover {
            background-color: #d32f2f;
        }
        
        .btn i {
            font-size: 14px;
        }
        
        .history-section {
            max-height: 600px;
            overflow-y: auto;
        }
        
        .history-title {
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .history-actions {
            display: flex;
            gap: 8px;
        }
        
        .history-list {
            list-style: none;
        }
        
        .message-item {
            position: relative;
            padding: 15px;
            border-radius: var(--radius);
            background-color: #fff;
            margin-bottom: 15px;
            border-left: 3px solid var(--primary-color);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            transition: all 0.2s;
        }
        
        .message-item:hover {
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
        }
        
        .message-content {
            margin-bottom: 8px;
            word-break: break-all;
            font-size: 16px;
            white-space: pre-wrap;
            line-height: 1.5;
        }
        
        .message-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: var(--gray);
        }
        
        .message-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            background-color: transparent;
            color: var(--gray);
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        
        .action-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-color: var(--primary-light);
        }
        
        .action-btn i {
            font-size: 12px;
        }
        
        .no-messages {
            text-align: center;
            color: var(--gray);
            padding: 30px 0;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            background-color: var(--success-color);
            color: white;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 100;
            transform: translateY(-100px);
            opacity: 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .notification.show {
            transform: translateY(0);
            opacity: 1;
        }
        
        .theme-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: white;
            color: var(--dark);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow);
            cursor: pointer;
            z-index: 10;
        }
        
        /* 黑暗模式 */
        body.dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }
        
        body.dark-mode .card {
            background-color: #1e1e1e;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }
        
        body.dark-mode h1 {
            color: #8ab4f8;
        }
        
        body.dark-mode .channel-info {
            background-color: rgba(66, 133, 244, 0.1);
        }
        
        body.dark-mode #channelIdDisplay {
            background-color: rgba(66, 133, 244, 0.15);
            color: #8ab4f8;
        }
        
        body.dark-mode textarea {
            background-color: #2d2d2d;
            border-color: #444;
            color: #e0e0e0;
        }
        
        body.dark-mode .btn-secondary {
            color: #8ab4f8;
            border-color: #8ab4f8;
            background-color: transparent;
        }
        
        body.dark-mode .btn-secondary:hover {
            background-color: rgba(138, 180, 248, 0.1);
        }
        
        body.dark-mode .message-item {
            background-color: #2d2d2d;
            border-color: #8ab4f8;
        }
        
        body.dark-mode .action-btn {
            border-color: #444;
            color: #aaa;
        }
        
        body.dark-mode .action-btn:hover {
            background-color: rgba(138, 180, 248, 0.1);
            color: #8ab4f8;
        }
        
        body.dark-mode .theme-toggle {
            background-color: #333;
            color: #f5f5f5;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            .card {
                padding: 15px;
            }
            
            .buttons {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
            
            .history-title {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .history-actions {
                width: 100%;
                justify-content: space-between;
            }
        }
        
        @media (max-width: 480px) {
            .message-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
            
            .message-actions {
                width: 100%;
                justify-content: space-between;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>信息中转站</h1>
        <p class="description">在不同设备间快速传递文本信息</p>
    </header>
    
    <div class="container">
        <div class="channel-info">
            <span class="channel-label">频道 ID:</span> 
            <span id="channelIdDisplay">加载中...</span>
            <button id="copyChannelBtn" class="action-btn">
                <i class="fa fa-copy"></i> 复制链接
            </button>
            <button id="newChannelBtn" class="action-btn">
                <i class="fa fa-plus-circle"></i> 新建频道
            </button>
        </div>
        
        <div class="card input-section">
            <div class="input-group">
                <textarea id="messageInput" placeholder="在此输入要传输的文本..."></textarea>
                <div class="char-count">0 字符</div>
            </div>
            <div class="buttons">
                <button id="submitBtn" class="btn">
                    <i class="fa fa-paper-plane"></i> 提交
                </button>
                <div>
                    <button id="clearInputBtn" class="btn btn-secondary">
                        <i class="fa fa-eraser"></i> 清空输入
                    </button>
                    <button id="pasteBtn" class="btn btn-secondary">
                        <i class="fa fa-clipboard"></i> 粘贴
                    </button>
                </div>
            </div>
        </div>
        
        <div class="card history-section">
            <div class="history-title">
                <h2>最近信息</h2>
                <div class="history-actions">
                    <button id="refreshBtn" class="action-btn">
                        <i class="fa fa-refresh"></i> 刷新
                    </button>
                    <button id="clearHistoryBtn" class="action-btn">
                        <i class="fa fa-trash"></i> 清除历史
                    </button>
                </div>
            </div>
            <div id="messageList" class="history-list">
                <div class="no-messages">暂无信息</div>
            </div>
        </div>
    </div>
    
    <button id="themeToggle" class="theme-toggle" title="切换暗/亮模式">
        <i class="fa fa-moon-o"></i>
    </button>
    
    <div id="notification" class="notification">
        <i class="fa fa-check-circle"></i>
        <span id="notificationText">操作成功</span>
    </div>

    <script>
        // DOM 元素
        const messageInput = document.getElementById('messageInput');
        const submitBtn = document.getElementById('submitBtn');
        const messageList = document.getElementById('messageList');
        const channelIdDisplay = document.getElementById('channelIdDisplay');
        const copyChannelBtn = document.getElementById('copyChannelBtn');
        const clearHistoryBtn = document.getElementById('clearHistoryBtn');
        const newChannelBtn = document.getElementById('newChannelBtn');
        const clearInputBtn = document.getElementById('clearInputBtn');
        const pasteBtn = document.getElementById('pasteBtn');
        const refreshBtn = document.getElementById('refreshBtn');
        const themeToggle = document.getElementById('themeToggle');
        const notification = document.getElementById('notification');
        const notificationText = document.getElementById('notificationText');
        const charCount = document.querySelector('.char-count');
        
        // 工具函数
        function showNotification(message, type = 'success') {
            notificationText.textContent = message;
            
            // 设置通知颜色
            notification.style.backgroundColor = 
                type === 'success' ? 'var(--success-color)' : 
                type === 'error' ? 'var(--danger-color)' :
                type === 'warning' ? 'var(--warning-color)' : 'var(--primary-color)';
            
            notification.classList.add('show');
            
            // 3 秒后自动隐藏
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }
        
        // 生成或获取频道 ID
        function getOrCreateChannelId() {
            const urlParams = new URLSearchParams(window.location.search);
            let channelId = urlParams.get('channel');
            
            if (!channelId) {
                channelId = Math.random().toString(36).substring(2, 10);
                // 更新 URL，不刷新页面
                const newUrl = window.location.protocol + "//" + 
                               window.location.host + 
                               window.location.pathname + 
                               '?channel=' + channelId;
                window.history.pushState({path: newUrl}, '', newUrl);
            }
            
            return channelId;
        }
        
        // 从 localStorage 获取消息
        function getMessages() {
            const storageKey = `transferMessages_${channelId}`;
            const messagesJson = localStorage.getItem(storageKey);
            return messagesJson ? JSON.parse(messagesJson) : [];
        }
        
        // 保存消息到 localStorage
        function saveMessage(message) {
            const messages = getMessages();
            // 限制最大保存数量为 50 条
            if (messages.length >= 50) {
                messages.pop(); // 移除最旧的消息
            }
            messages.unshift(message); // 添加到开头
            
            const storageKey = `transferMessages_${channelId}`;
            localStorage.setItem(storageKey, JSON.stringify(messages));
        }
        
        // 删除单条消息
        function deleteMessage(index) {
            const messages = getMessages();
            if (index >= 0 && index < messages.length) {
                messages.splice(index, 1);
                const storageKey = `transferMessages_${channelId}`;
                localStorage.setItem(storageKey, JSON.stringify(messages));
                renderMessages();
                showNotification('已删除消息');
            }
        }
        
        // 渲染消息列表
        function renderMessages() {
            const messages = getMessages();
            
            if (messages.length === 0) {
                messageList.innerHTML = '<div class="no-messages">暂无信息</div>';
                return;
            }
            
            messageList.innerHTML = '';
            
            messages.forEach((message, index) => {
                const div = document.createElement('div');
                div.className = 'message-item';
                
                // 格式化日期时间
                const date = new Date(message.createTime);
                const formattedTime = `${date.getFullYear()}-${padZero(date.getMonth()+1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date.getSeconds())}`;
                
                div.innerHTML = `
                    <div class="message-content">${escapeHtml(message.content)}</div>
                    <div class="message-meta">
                        <span>${formattedTime}</span>
                        <div class="message-actions">
                            <button class="action-btn copy-btn" data-index="${index}">
                                <i class="fa fa-copy"></i> 复制
                            </button>
                            <button class="action-btn edit-btn" data-index="${index}">
                                <i class="fa fa-pencil"></i> 编辑
                            </button>
                            <button class="action-btn delete-btn" data-index="${index}">
                                <i class="fa fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                `;
                
                messageList.appendChild(div);
            });
            
            // 添加按钮事件
            document.querySelectorAll('.copy-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    const content = messages[index].content;
                    copyToClipboard(content, '已复制到剪贴板');
                });
            });
            
            document.querySelectorAll('.edit-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    messageInput.value = messages[index].content;
                    messageInput.focus();
                    updateCharCount();
                    
                    // 滚动到输入框
                    window.scrollTo({
                        top: messageInput.offsetTop - 20,
                        behavior: 'smooth'
                    });
                    
                    // 可选：显示"正在编辑"状态
                    messageInput.setAttribute('data-editing-index', index);
                    submitBtn.innerHTML = '<i class="fa fa-save"></i> 更新';
                });
            });
            
            document.querySelectorAll('.delete-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    if (confirm('确定要删除这条消息吗？')) {
                        deleteMessage(index);
                    }
                });
            });
        }
        
        // 提交新消息
        function submitMessage() {
            const content = messageInput.value.trim();
            if (!content) {
                showNotification('请输入内容', 'warning');
                return;
            }
            
            const editingIndex = messageInput.getAttribute('data-editing-index');
            
            if (editingIndex !== null) {
                // 编辑现有消息
                const messages = getMessages();
                if (editingIndex >= 0 && editingIndex < messages.length) {
                    messages[editingIndex].content = content;
                    messages[editingIndex].updateTime = new Date().toISOString();
                    
                    const storageKey = `transferMessages_${channelId}`;
                    localStorage.setItem(storageKey, JSON.stringify(messages));
                    
                    messageInput.removeAttribute('data-editing-index');
                    submitBtn.innerHTML = '<i class="fa fa-paper-plane"></i> 提交';
                    
                    showNotification('消息已更新');
                }
            } else {
                // 创建新消息
                const message = {
                    content,
                    channelId,
                    createTime: new Date().toISOString(),
                };
                
                saveMessage(message);
                showNotification('消息已提交');
            }
            
            renderMessages();
            messageInput.value = '';
            updateCharCount();
        }
        
        // 复制内容到剪贴板
        function copyToClipboard(text, message = '已复制') {
            navigator.clipboard.writeText(text)
                .then(() => {
                    showNotification(message);
                })
                .catch(err => {
                    console.error('无法复制内容: ', err);
                    showNotification('复制失败，请手动复制', 'error');
                    
                    // 回退方法：创建临时文本区域
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    
                    try {
                        document.execCommand('copy');
                        showNotification(message);
                    } catch (err) {
                        showNotification('复制失败，请手动复制', 'error');
                    }
                    
                    document.body.removeChild(textArea);
                });
        }
        
        // 复制当前页面链接
        function copyChannelLink() {
            const url = window.location.href;
            copyToClipboard(url, '频道链接已复制');
        }
        
        // 创建新频道
        function createNewChannel() {
            if (confirm('创建新频道将会清空当前消息记录。确定继续吗？')) {
                const newChannelId = Math.random().toString(36).substring(2, 10);
                const newUrl = window.location.protocol + "//" + 
                               window.location.host + 
                               window.location.pathname + 
                               '?channel=' + newChannelId;
                               
                window.location.href = newUrl; // 直接跳转到新频道
            }
        }
        
        // 更新字符计数
        function updateCharCount() {
            const count = messageInput.value.length;
            charCount.textContent = `${count} 字符`;
            
            // 可选：当字符数超过某个阈值时改变颜色
            if (count > 1000) {
                charCount.style.color = 'var(--danger-color)';
            } else if (count > 500) {
                charCount.style.color = 'var(--warning-color)';
            } else {
                charCount.style.color = 'var(--gray)';
            }
        }
        
        // 切换暗/亮模式
        function toggleTheme() {
            const isDarkMode = document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', isDarkMode ? 'true' : 'false');
            
            // 更新图标
            themeToggle.innerHTML = isDarkMode ? 
                '<i class="fa fa-sun-o"></i>' : 
                '<i class="fa fa-moon-o"></i>';
                
            showNotification(`已切换到${isDarkMode ? '暗黑' : '明亮'}模式`);
        }
        
        // 从剪贴板粘贴
        async function pasteFromClipboard() {
            try {
                const text = await navigator.clipboard.readText();
                messageInput.value = text;
                updateCharCount();
                messageInput.focus();
                showNotification('已从剪贴板粘贴');
            } catch (err) {
                console.error('无法访问剪贴板：', err);
                showNotification('无法访问剪贴板，请手动粘贴', 'error');
            }
        }
        
        // 辅助函数：数字补零
        function padZero(num) {
            return num.toString().padStart(2, '0');
        }
        
        // 辅助函数：HTML 转义
        function escapeHtml(unsafe) {
            return unsafe
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }
        
        // 初始化
        let channelId = getOrCreateChannelId();
        channelIdDisplay.textContent = channelId;
        
        // 加载保存的主题偏好
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            themeToggle.innerHTML = '<i class="fa fa-sun-o"></i>';
        }
        
        // 事件监听
        submitBtn.addEventListener('click', submitMessage);
        copyChannelBtn.addEventListener('click', copyChannelLink);
        clearHistoryBtn.addEventListener('click', function() {
            if (confirm('确定要清除所有历史记录吗？')) {
                const storageKey = `transferMessages_${channelId}`;
                localStorage.removeItem(storageKey);
                renderMessages();
                showNotification('历史记录已清除');
            }
        });
        newChannelBtn.addEventListener('click', createNewChannel);
        clearInputBtn.addEventListener('click', function() {
            messageInput.value = '';
            updateCharCount();
            
            // 如果正在编辑，取消编辑状态
            if (messageInput.hasAttribute('data-editing-index')) {
                messageInput.removeAttribute('data-editing-index');
                submitBtn.innerHTML = '<i class="fa fa-paper-plane"></i> 提交';
            }
        });
        pasteBtn.addEventListener('click', pasteFromClipboard);
        refreshBtn.addEventListener('click', function() {
            renderMessages();
            showNotification('已刷新消息列表');
        });
        themeToggle.addEventListener('click', toggleTheme);
        
        // 监听输入框变化，更新字符计数
        messageInput.addEventListener('input', updateCharCount);
        
        // 监听键盘事件
        messageInput.addEventListener('keydown', function(e) {
            // Ctrl+Enter 提交
            if (e.ctrlKey && e.key === 'Enter') {
                submitMessage();
            }
        });
        
        // 点击频道 ID 复制
        channelIdDisplay.addEventListener('click', function() {
            copyToClipboard(channelId, '频道 ID 已复制');
        });
        
        // 页面加载时渲染消息和初始化字符计数
        document.addEventListener('DOMContentLoaded', function() {
            renderMessages();
            updateCharCount();
        });
    </script>
</body>
</html> 
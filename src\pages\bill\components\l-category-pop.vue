<template>
  <view>
    <uni-popup ref="popRef" type="bottom" background-color="rgba(0,0,0,0)" @change="onChange">
      <view class="bg-white rounded-t-2">
        <view class="text-35 text-center py-4" style="border-bottom: 1px solid #eee">请选择分类</view>
        <scroll-view :scroll-top="scrollTop" scroll-y="true" class="h-800 pb-4">
          <view class="px-4 my-4">
            <view class="flex flex-wrap">
              <!-- 添加全部类型选项 -->
              <view class="w-1/3 p-1 text-center">
                <view 
                  @click="onSubmit(null)" 
                  class="rounded-1 p-4" 
                  :style="{
                    background: !props.modelValue ? '#3eb575' : '#f5f5f5',
                    color: !props.modelValue ? 'white' : ''
                  }"
                >
                  <view>全部类型</view>
                </view>
              </view>
              <!-- 原有的分类列表 -->
              <view v-for="category in categoryList" :key="category._id" class="w-1/3 p-1 text-center">
                <view 
                  @click="onSubmit(category)" 
                  class="rounded-1 p-4" 
                  :style="{
                    background: isSelected(category._id) ? '#3eb575' : '#f5f5f5',
                    color: isSelected(category._id) ? 'white' : ''
                  }"
                >
                  <view>{{ category.name }}</view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="z-bottom"></view>
    </uni-popup>
  </view>
</template>

<script setup>
const popRef = ref(null)
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: [String, Object],
    default: '',
  }
})

const emits = defineEmits(['update:open', 'update:modelValue'])

const categoryList = ref([])

const isSelected = (id) => {
  if (!props.modelValue) return false
  return props.modelValue._id === id
}

onMounted(async () => {
  categoryList.value = await getCategoryApi()
})

watch(
  () => props.open,
  (val) => {
    if (val) {
      popRef.value.open('bottom')
    } else {
      popRef.value.close()
    }
  }
)

const onChange = (e) => {
  emits('update:open', e.show)
}

const onSubmit = (category) => {
  emits('update:modelValue', category)
  popRef.value.close()
}
</script>

<style lang="scss"></style>

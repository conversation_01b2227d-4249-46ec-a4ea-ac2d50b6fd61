<template>
  <view class="detail-pane-container">
    <template v-if="detail">
      <view class="header">
        <h3>{{ detail.title }}</h3>
      </view>
      <view class="content">
        <view class="detail-item">
          <label>描述:</label>
          <text>{{ detail.description }}</text>
        </view>
        <view class="detail-item">
          <label>截止日期:</label>
          <text>{{ detail.date }}</text>
        </view>
        <view class="detail-item">
          <label>状态:</label>
          <text class="status">{{ detail.status }}</text>
        </view>
        <view class="detail-item">
          <label>负责人:</label>
          <text>{{ detail.owner }}</text>
        </view>
      </view>
      <view class="actions">
        <button class="edit-button">编辑</button>
        <button class="delete-button">删除</button>
      </view>
    </template>
    <template v-else>
      <view class="empty-state">
        <i class="fas fa-hand-pointer"></i>
        <p>请从左侧列表中选择一项以查看详情</p>
      </view>
    </template>
  </view>
</template>

<script setup>
import { ref, watch, computed } from 'vue'

const props = defineProps({
  itemId: String,
})

// 模拟的数据源
const allTasks = {
  'task-1': {
    id: 'task-1',
    title: '完成Q3季度报告',
    description: '整理所有数据并提交给管理层。',
    date: '2023-09-15',
    status: '进行中',
    owner: '张三',
  },
  'task-2': {
    id: 'task-2',
    title: '新功能UI设计',
    description: '设计PC端仪表盘的用户界面。',
    date: '2023-09-20',
    status: '未开始',
    owner: '李四',
  },
  'task-3': {
    id: 'task-3',
    title: '修复登录Bug',
    description: '解决用户反馈的无法登录问题。',
    date: '2023-09-18',
    status: '已完成',
    owner: '王五',
  },
  'task-4': {
    id: 'task-4',
    title: '准备产品演示',
    description: '为下周的会议准备产品演示幻灯片。',
    date: '2023-09-22',
    status: '进行中',
    owner: '赵六',
  },
}

const detail = computed(() => {
  return props.itemId ? allTasks[props.itemId] : null
})
</script>

<style scoped>
.detail-pane-container {
  padding: 20px;
  background-color: #fff;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.header h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 15px;
}
.content {
  flex-grow: 1;
}
.detail-item {
  margin-bottom: 15px;
}
.detail-item label {
  font-weight: 600;
  display: block;
  margin-bottom: 5px;
  color: #333;
}
.detail-item text {
  font-size: 14px;
  color: #666;
}
.status {
  padding: 2px 8px;
  border-radius: 12px;
  background-color: #e6f7ff;
  color: #1890ff;
  font-size: 12px;
}
.actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}
.edit-button,
.delete-button {
  flex: 1;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  background-color: #fff;
}
.delete-button {
  background-color: #fff1f0;
  border-color: #ffa39e;
  color: #cf1322;
}
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
  text-align: center;
}
.empty-state i {
  font-size: 48px;
  margin-bottom: 20px;
}
</style>

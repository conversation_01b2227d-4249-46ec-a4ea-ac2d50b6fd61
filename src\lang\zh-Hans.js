export default {
  tabbar: '列表,宫格,通讯录,我的',
  agreementsTitle: '用户服务协议,隐私政策',
  common: {
    wechatFriends: '微信好友',
    wechatBbs: '微信朋友圈',
    weibo: '微博',
    more: '更多',
    agree: '同意',
    copy: '复制',
    wechatApplet: '微信小程序',
    cancelShare: '取消分享',
    updateSucceeded: '更新成功',
    phonePlaceholder: '请输入手机号',
    verifyCodePlaceholder: '请输入验证码',
    newPasswordPlaceholder: '请输入新密码',
    confirmNewPasswordPlaceholder: '请确认新密码',
    confirmPassword: '请确认密码',
    verifyCodeSend: '验证码已通过短信发送至',
    passwordDigits: '密码为6 - 20位',
    getVerifyCode: '获取验证码',
    noAgree: '你未同意隐私政策协议',
    gotIt: '知道了',
    login: '登录',
    error: '错误',
    complete: '完成',
    submit: '提交',
    formatErr: '手机号码格式不正确',
    sixDigitCode: '请输入6位验证码',
    resetNavTitle: '重置密码',
  },
  list: {
    inputPlaceholder: '请输入搜索内容',
  },
  search: {
    cancelText: '取消',
    searchHistory: '搜索历史',
    searchDiscovery: '搜索发现',
    deleteAll: '全部删除',
    delete: '删除',
    deleteTip: '确认清空搜索历史吗？',
    complete: '完成',
    searchHiddenTip: '当前搜索发现已隐藏',
  },
  grid: {
    grid: '宫格组件',
    visibleToAll: '所有人可见',
    invisibleToTourists: '游客不可见',
    adminVisible: '管理员可见',
    clickTip: '点击第',
    clickTipGrid: '个宫格',
  },
  mine: {
    showText: '文字',
    signIn: '普通签到',
    signInByAd: '看广告签到',
    toEvaluate: '去评分',
    readArticles: '阅读过的文章',
    myScore: '我的积分',
    invite: '分销推荐',
    feedback: '问题与反馈',
    settings: '设置',
    checkUpdate: '检查更新',
    about: '关于',
    clicked: '你点击了',
    checkScore: '请登录后查看积分',
    currentScore: '当前积分为',
    noScore: '当前无积分',
    notLogged: '未登录',
  },
  userinfo: {
    navigationBarTitle: '个人资料',
    ProfilePhoto: '头像',
    nickname: '昵称',
    notSet: '未设置',
    phoneNumber: '手机号',
    notSpecified: '未绑定',
    setNickname: '设置昵称',
    setNicknamePlaceholder: '请输入要设置的昵称',
    bindPhoneNumber: '本机号码一键绑定',
    bindOtherLogin: '其他号码绑定',
    noChange: '没有变化',
    uploading: '正在上传',
    requestFail: '请求服务失败',
    setting: '设置中',
    deleteSucceeded: '删除成功',
    setSucceeded: '设置成功',
  },
  smsCode: {
    resendVerifyCode: '重新发送',
    phoneErrTip: '手机号格式错误',
    sendSuccessTip: '短信验证码发送成功',
  },
  loadMore: {
    noData: '暂无数据',
    noNetwork: '网络异常',
    toSet: '前往设置',
    error: '错误',
  },
  uniFeedback: {
    navigationBarTitle: '问题与反馈',
    msgTitle: '留言内容',
    imgTitle: '图片列表',
    contacts: '联系人',
    phone: '联系电话',
    submit: '提交',
  },
  settings: {
    navigationBarTitle: '设置',
    userInfo: '账号资料',
    changePassword: '修改密码',
    clearTmp: '清理缓存',
    pushServer: '推送功能',
    fingerPrint: '指纹解锁',
    facial: '人脸解锁',
    deactivate: '注销账号',
    logOut: '退出登录',
    login: '登录',
    failTip: '认证失败请重试',
    authFailed: '认证失败',
    changeLanguage: '切换语言',
    please: '请用',
    successText: '成功',
    deviceNoOpen: '设备未开启',
    fail: '失败',
    tips: '提示',
    exitLogin: '是否退出登录?',
    clearing: '清除中',
    clearedSuccessed: '清除成功',
    confirmText: '确定',
    cancelText: '取消',
  },
  deactivate: {
    cancelText: '取消',
    nextStep: '下一步',
    navigationBarTitle: '注销提示',
  },
  about: {
    sacnQR: '扫描二维码，您的朋友也可以下载',
    client: '客户端',
    and: '和',
    about: '关于',
  },
  invite: {
    download: '下载',
  },
  login: {
    phoneLogin: '登录后即可展示自己',
    phoneLoginTip: '未注册的手机号验证通过后将自动注册',
    getVerifyCode: '获取验证码',
  },
  uniQuickLogin: {
    accountLogin: '账号登录',
    SMSLogin: '短信验证码',
    wechatLogin: '微信登录',
    appleLogin: '苹果登录',
    oneClickLogin: '一键登录',
    QQLogin: 'QQ登录',
    xiaomiLogin: '小米登录',
    getProviderFail: '获取服务供应商失败',
    loginErr: '登录服务初始化错误',
    chooseOtherLogin: '点击了第三方登录',
  },
  pwdLogin: {
    pwdLogin: '用户名密码登录',
    placeholder: '请输入手机号/用户名',
    passwordPlaceholder: '请输入密码',
    verifyCodePlaceholder: '请输入验证码',
    login: '登录',
    forgetPassword: '忘记密码',
    register: '注册账号',
  },
  register: {
    navigationBarTitle: '注册',
    usernamePlaceholder: '请输入用户名',
    nicknamePlaceholder: '请输入用户昵称',
    registerAndLogin: '注册并登录',
    passwordDigitsPlaceholder: '请输入6-20位密码',
    passwordAgain: '再次输入密码',
  },
  listDetail: {
    follow: '点击关注',
    newsErr: '出错了，新闻ID为空',
  },
  newsLog: {
    navigationBarTitle: '阅读记录',
  },
  bindMobile: {
    navigationBarTitle: '绑定手机号码',
  },
}

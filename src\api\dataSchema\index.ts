export const dataConfig = {
  // 数据库版本，升级版本的时候需要修改
  webVersion: 5,
  databaseName: 'database',
}

// 表字段配置
export const tableSchema: any = {
  okrObject: {
    _id: {
      aType: 'TEXT',
      wType: '&',
      primaryKey: true,
    },
    title: {
      aType: 'TEXT',
      defaultVal: '',
    },
    content: {
      aType: 'TEXT',
      defaultVal: '',
    },
    color: {
      aType: 'TEXT',
      defaultVal: '',
    },
    startDate: {
      aType: 'TEXT',
      defaultVal: '',
    },
    endDate: {
      aType: 'TEXT',
      defaultVal: '',
    },
    status: {
      aType: 'TEXT',
      defaultVal: 'pending',
    },
    feasibility: {
      aType: 'TEXT',
      defaultVal: '',
    },
    motivation: {
      aType: 'TEXT',
      defaultVal: '',
    },
    isDirty: {
      aType: 'INTEGER',
      defaultVal: 0,
      system: true,
    },
    createTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    updateTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    deleteTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
  },
  task: {
    _id: {
      aType: 'TEXT',
      wType: '&',
      primaryKey: true,
    },
    title: {
      aType: 'TEXT',
      defaultVal: '',
    },
    content: {
      aType: 'TEXT',
      defaultVal: '',
    },
    type: {
      aType: 'TEXT',
      defaultVal: '',
    },
    okrId: {
      aType: 'TEXT',
      defaultVal: '',
    },
    parentId: {
      aType: 'TEXT',
      defaultVal: '',
    },
    startDate: {
      aType: 'TEXT',
      defaultVal: '',
    },
    endDate: {
      aType: 'TEXT',
      defaultVal: '',
    },
    unit: {
      aType: 'TEXT',
      defaultVal: '',
    },
    status: {
      aType: 'INTEGER',
      defaultVal: 0,
    },
    repeatFlag: {
      aType: 'TEXT',
      defaultVal: '',
    },
    // compDates: {
    //   aType: 'TEXT',
    //   defaultVal: '',
    // },
    compInfos: {
      aType: 'TEXT',
      defaultVal: '',
    },
    initVal: {
      aType: 'REAL',
      defaultVal: 0,
    },
    tgtVal: {
      aType: 'REAL',
      defaultVal: 0,
    },
    weight: {
      aType: 'REAL',
      defaultVal: 0,
    },
    recList: {
      aType: 'TEXT',
      defaultVal: '',
    },

    progVal: {
      aType: 'REAL',
      defaultVal: 0,
    },
    valType: {
      aType: 'TEXT',
      defaultVal: 'sum',
    },
    dailyTarget: {
      aType: 'REAL',
      defaultVal: 0,
    },
    completeTime: {
      aType: 'TEXT',
      defaultVal: '',
    },
    dueTime: {
      aType: 'TEXT',
      defaultVal: '',
    },
    createTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    updateTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    deleteTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    isDirty: {
      aType: 'INTEGER',
      defaultVal: 0,
      system: true,
    },
  },
  badHabit: {
    _id: {
      aType: 'TEXT',
      wType: '&',
      primaryKey: true,
    },
    title: {
      aType: 'TEXT',
      defaultVal: '',
    },
    content: {
      aType: 'TEXT',
      defaultVal: '',
    },
    type: {
      aType: 'TEXT',
      defaultVal: '',
    },
    createTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    updateTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    deleteTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    isDirty: {
      aType: 'INTEGER',
      defaultVal: 0,
      system: true,
    },
  },
  memo: {
    _id: {
      aType: 'TEXT',
      wType: '&',
      primaryKey: true,
    },
    parentId: {
      aType: 'TEXT',
      defaultVal: '',
    },
    title: {
      aType: 'TEXT',
      defaultVal: '',
    },
    content: {
      aType: 'TEXT',
      defaultVal: '',
    },
    type: {
      aType: 'TEXT',
      defaultVal: '',
    },
    date: {
      aType: 'TEXT',
      defaultVal: '',
    },
    aiAnalysis: {
      aType: 'TEXT',
      defaultVal: '',
    },
    tagsList: {
      aType: 'TEXT',
      defaultVal: '',
    },
    createTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    updateTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    deleteTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    isDirty: {
      aType: 'INTEGER',
      defaultVal: 0,
      system: true,
    },
  },
  okrConfig: {
    _id: {
      aType: 'TEXT',
      wType: '&',
    },
    key: {
      aType: 'TEXT',
      wType: '&',
      primaryKey: true,
    },
    value: {
      aType: 'TEXT',
      defaultVal: '',
    },
    deleteTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    createTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    updateTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    isDirty: {
      aType: 'INTEGER',
      defaultVal: 0,
      system: true,
    },
  },
  bill: {
    _id: {
      aType: 'TEXT',
      wType: '&',
      primaryKey: true,
    },
    amount: {
      aType: 'REAL',
      defaultVal: 0,
    },
    type: {
      aType: 'TEXT',
      defaultVal: 'expense',
    },
    category: {
      aType: 'TEXT',
      defaultVal: '',
    },
    merchants: {
      aType: 'TEXT',
      defaultVal: '',
    },
    transactionId: {
      aType: 'TEXT',
      defaultVal: '',
    },
    remark: {
      aType: 'TEXT',
      defaultVal: '',
    },
    date: {
      aType: 'TEXT',
      defaultVal: '',
    },
    account: {
      aType: 'TEXT',
      defaultVal: '',
    },
    deleteTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    createTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    updateTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    isDirty: {
      aType: 'INTEGER',
      defaultVal: 0,
      system: true,
    },
  },
  category: {
    _id: {
      aType: 'TEXT',
      wType: '&',
      primaryKey: true,
    },
    name: {
      aType: 'TEXT',
      defaultVal: '',
    },
    parentId: {
      aType: 'TEXT',
      defaultVal: '',
    },
    deleteTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    createTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    updateTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    isDirty: {
      aType: 'INTEGER',
      defaultVal: 0,
      system: true,
    },
  },
  chatRecord: {
    _id: {
      aType: 'TEXT',
      wType: '&',
      primaryKey: true,
    },
    title: {
      aType: 'TEXT',
      defaultVal: '',
    },
    content: {
      aType: 'TEXT',
      defaultVal: '',
    },
    deleteTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    createTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    updateTime: {
      aType: 'TEXT',
      defaultVal: '',
      system: true,
    },
    isDirty: {
      aType: 'INTEGER',
      defaultVal: 0,
      system: true,
    },
  },
}

# KrEdit 组件重构改动指南

## 背景

为了使用自定义的 rrrrule.ts 库替代原有的 RRule 库，需要对 src/pages/okr/krEdit.vue 组件进行相应修改。由于循环类型包含了很多自定义类型，使用原生 rrule 库进行拓展较为复杂，因此采用了自定义实现的 rrrrule 库。

## 改动内容

### 1. 导入修改

```js
// 原导入
import { RRuleHelper } from '@/utils/rrule'

// 改为
import * as rrule from '@/utils/rrrrule'
```

### 2. 循环设置相关修改

```js
// 原初始值
const loopSetting = ref('RRULE:FREQ=DAILY') // 直接存储 RRULE 字符串

// 改为
const loopSetting = ref('type=DAILY') // 使用新格式的循环规则
```

### 3. 循环文本显示计算属性修改

```js
// 原循环文本显示
const loopSettingText = computed(() => {
  console.log('loopSettingText', loopSetting.value)
  if (!loopSetting.value) return '不重复'
  return RRuleHelper.toText(loopSetting.value)
})

// 改为
const loopSettingText = computed(() => {
  console.log('loopSettingText', loopSetting.value)
  if (!loopSetting.value) return '不重复'
  return rrule.toText(loopSetting.value)
})
```

### 4. 任务发生次数计算函数修改

```js
// 原任务发生次数计算函数
const calculateTaskOccurrences = (rruleString, startDateStr, endDateStr) => {
  // 默认至少执行一次
  if (!rruleString || !startDateStr || !endDateStr) return 1

  try {
    const startDate = new Date(startDateStr)
    const endDate = new Date(endDateStr)

    // 使用 RRuleHelper 获取日期范围内的所有事件
    const occurrences = RRuleHelper.getOccurrences(rruleString, {
      after: startDate,
      before: endDate,
    })

    // 至少返回 1，表示任务至少执行一次
    return Math.max(1, occurrences.length)
  } catch (error) {
    console.error('计算任务循环次数出错：', error)
    return 1 // 出错时返回默认值
  }
}

// 改为
const calculateTaskOccurrences = (ruleString, startDateStr, endDateStr) => {
  // 默认至少执行一次
  if (!ruleString || !startDateStr || !endDateStr) return 1

  try {
    // 使用新的 rrrrule.getOccurrences 获取日期范围内的所有事件
    const result = rrule.getOccurrences(ruleString, startDateStr, endDateStr)
    
    // 至少返回 1，表示任务至少执行一次
    return Math.max(1, result.count)
  } catch (error) {
    console.error('计算任务循环次数出错：', error)
    return 1 // 出错时返回默认值
  }
}
```

### 5. l-loop-popup 循环设置弹窗组件修改

需要修改 `components/l-loop-popup.vue` 组件，使其能够处理新的循环规则格式：

1. 修改组件接收的初始值格式，处理新格式的循环规则
2. 修改组件输出的规则格式，将之前的 RRule 字符串格式改为自定义格式
3. 调整界面显示，确保能够正确展示和设置对应的循环类型
4. 更新循环类型转换逻辑，确保与 rrrrule.ts 中定义的类型一致

### 6. 确保数据库存储格式兼容

在保存 KR 数据时，确保 `repeatFlag` 字段使用的是新格式：

```js
// 保存 KR 数据
const krData = {
  // ... 其他字段
  repeatFlag: loopSetting.value, // 确保这里是新格式的循环规则字符串
}

// 打印保存接口字段
console.log('保存到数据库的数据：', JSON.stringify(krData, null, 2))
```

### 7. 向后兼容处理
无需向后兼容！！！

## 测试建议

1. 测试新建 KR 时各种循环设置的正确保存与显示
2. 测试编辑旧有 KR 时循环规则的正确转换与显示
3. 测试每日目标量的计算是否正确
4. 验证所有循环类型在保存后能正确加载
5. 确保向后兼容，旧数据能够正确显示和编辑

## 注意事项

1. 循环类型定义请参考 src/utils/rrrrule.ts 中的 RuleType 定义

在 fetchTasksByDate 函数的第五步：格式化任务数据用于显示 之前，需要按照循环类型添加一些属性：

### 每周几
一周内，完成指定的星期，比如每周的周一、周三循环
- 今天总数：今天需要完成的总数
- 今天已完成：今天已经完成的数量

### 每月几
一月内，完成指定的日期，比如每月的 1 号、5 号循环
- 今天总数：今天需要完成的总数
- 今天已完成：今天已经完成的数量

### 每隔几天
举例：计划 1 月 1 号开始，每隔一天，每天完成 5 章，则 1 月 1 号、1 月 3 号、1 月 5 号...每天需完成 5 章
- 今天总数：今天需要完成的总数
- 今天已完成：今天已经完成的数量

### 每周几天（自定义）
在一周内，需要完成指定的天数，比如每周 3 天，每天完成 100 道题，意思就是在这周内总共要完成 3 天，每天完成 100 道题即可，无需指定是哪天完成。需要的属性：
  - 总天数：本周总共要完成的天数
  - 已完成天数：本周已完成的天数
  - 今天总数：今天需要完成的总数
  - 今晚已完成：今天已经完成的数量
  
### 每月几天
在一个月的周期内，需要完成指定的天数，比如每月 5 天，每天完成 100 道题，意思就是在这个月内总共要完成 5 天，每天完成 100 道题即可，无需指定是哪天完成。需要的属性：
  - 总天数：本月总共要完成的天数
  - 已完成天数：本月已完成的天数
  - 今天总数：今天需要完成的总数
  - 今天已完成：今天已经完成的数量

### 每几天
在指定的天数周期内，需要完成指定的次数，比如每 3 天，完成 100 道题，意思就是在这 3 天内总共要完成 100 道题，而不是每天都要完成 100 道题。需要的属性：
  - 总次数：本周期内需要完成的总次数
  - 已完成次数：本周期内已经完成的次数
  - 第几天：当前是本周期的第几天
  - 总天数：本周期的天数

### 每几个周（自定义）
举例：每两周完成 20 章，则在 2 周内，完成 20 章即可
- 总次数：本周期内需要完成的总次数
- 已完成次数：本周期内已经完成的次数
- 第几周：当前处于周期的第几周
- 总周期：本周期有几周

### 每几个月（自定义）
举例：每两周完成 20 章，则在 2 周内，完成 20 章即可
- 总次数：本周期内需要完成的总次数
- 已完成次数：本周期内已经完成的次数
- 第几周：当前处于周期的第几周
- 总周期：本周期有几周
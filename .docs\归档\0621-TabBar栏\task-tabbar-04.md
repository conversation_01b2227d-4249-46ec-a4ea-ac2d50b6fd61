# 任务：实现页面切换机制和 URL 参数处理

## 任务信息

- **所属功能模块**: TabBar 栏自定义
- **优先级**: 高
- **预估工时**: 3 人日
- **状态**: 待开发

## 任务描述

实现主页中的页面切换机制，使用动态组件加载不同 tab 对应的页面内容。同时实现 URL 参数处理功能，确保页面刷新时能够根据 URL 参数恢复到正确的 tab 页面。该任务是 TabBar 栏自定义功能的核心部分，需要确保在多种场景下的稳定性。

## 技术实现详情

### 1. 实现动态组件加载

在主页文件`pages/index/index.vue`中实现动态组件加载机制：

```vue
<template>
  <div class="container">
    <!-- 内容区域 -->
    <div class="content-area">
      <keep-alive>
        <component :is="currentComponent" v-if="currentComponent"></component>
        <div v-else class="loading">加载中...</div>
      </keep-alive>
    </div>

    <!-- TabBar区域 -->
    <div class="tabbar-container">
      <tabBar v-model="currentTab" :tabs="tabs" @change="handleTabChange" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, defineAsyncComponent } from 'vue'
import TabBar from './component/tabBar.vue'
import { router } from '@/utils/tools'

// 使用异步组件，延迟加载各个页面
const OkrList = defineAsyncComponent(() => import('@/pages/okr/okrList.vue'))
const Today = defineAsyncComponent(() => import('@/pages/okr/today.vue'))
const Analysis = defineAsyncComponent(() => import('@/pages/okr/data-analysis.vue'))
const Diary = defineAsyncComponent(() => import('@/pages/memo/diary.vue'))
const Setting = defineAsyncComponent(() => import('@/pages/setting/setting.vue'))

// TabBar数据定义
const tabs = [
  {
    id: 'okr',
    text: '目标',
    icon: 'icon-goal',
    activeIcon: 'icon-goal-fill',
    path: 'pages/okr/okrList',
    component: OkrList,
  },
  {
    id: 'today',
    text: '今天',
    icon: 'icon-task',
    activeIcon: 'icon-task-fill',
    path: 'pages/okr/today',
    component: Today,
  },
  {
    id: 'analysis',
    text: '分析',
    icon: 'icon-analysis',
    activeIcon: 'icon-analysis-fill',
    path: 'pages/okr/data-analysis',
    component: Analysis,
  },
  {
    id: 'diary',
    text: '日记',
    icon: 'icon-diary',
    activeIcon: 'icon-diary-fill',
    path: 'pages/memo/diary',
    component: Diary,
  },
  {
    id: 'setting',
    text: '设置',
    icon: 'icon-setting',
    activeIcon: 'icon-setting-fill',
    path: 'pages/setting/setting',
    component: Setting,
  },
]

// 当前选中的tab
const currentTab = ref('okr')

// 计算当前应该显示的组件
const currentComponent = computed(() => {
  const tab = tabs.find((item) => item.id === currentTab.value)
  return tab ? tab.component : null
})

// 处理tab切换
const handleTabChange = (tabId) => {
  currentTab.value = tabId

  // 更新URL参数，不刷新页面
  updateUrlParams(tabId)
}

// 更新URL参数
const updateUrlParams = (tabId) => {
  // 使用封装的router工具
  router.replace({
    path: '/pages/index/index',
    query: { tab: tabId },
  })
}

// 从URL参数解析tab值
const parseUrlParams = () => {
  try {
    // 获取当前页面URL参数
    const query =
      uni.getEnv() === 'H5' ? window.location.search : getCurrentPages()[getCurrentPages().length - 1]?.options || {}

    let tab = ''

    // H5环境
    if (uni.getEnv() === 'H5' && query) {
      const params = new URLSearchParams(query)
      tab = params.get('tab')
    }
    // 非H5环境
    else if (query.tab) {
      tab = query.tab
    }

    // 如果URL中有有效的tab参数，则使用该参数
    if (tab && tabs.some((t) => t.id === tab)) {
      currentTab.value = tab
    }
  } catch (error) {
    console.error('解析URL参数出错:', error)
  }
}

// 在组件挂载时解析URL参数
onMounted(() => {
  parseUrlParams()
})
</script>
```

### 2. 添加页面切换过渡效果

为提升用户体验，添加页面切换过渡效果：

```vue
<template>
  <div class="container">
    <!-- 内容区域 -->
    <div class="content-area">
      <transition name="fade" mode="out-in">
        <keep-alive>
          <component :is="currentComponent" v-if="currentComponent"></component>
          <div v-else class="loading">加载中...</div>
        </keep-alive>
      </transition>
    </div>

    <!-- TabBar区域 -->
    <!-- ... 略 ... -->
  </div>
</template>

<style scoped>
/* 添加过渡效果样式 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--color-gray-400);
}
</style>
```

### 3. 实现页面状态保持

确保页面切换时能够保持各个 tab 页的状态：

```vue
<script setup>
// 在<script setup>部分添加
import { provide } from 'vue'

// 提供当前tab信息给子组件使用
provide('currentTab', currentTab)

// 记录每个tab页的滚动位置
const scrollPositions = ref({})

// 在tab切换前保存当前页面滚动位置
const saveScrollPosition = (tabId) => {
  const currentTabId = currentTab.value
  const scrollContainer = document.querySelector('.content-area')
  if (scrollContainer) {
    scrollPositions.value[currentTabId] = scrollContainer.scrollTop
  }
}

// 在tab切换后恢复目标页面的滚动位置
const restoreScrollPosition = (tabId) => {
  setTimeout(() => {
    const scrollContainer = document.querySelector('.content-area')
    if (scrollContainer && scrollPositions.value[tabId] !== undefined) {
      scrollContainer.scrollTop = scrollPositions.value[tabId]
    }
  }, 50)
}

// 更新tab切换处理函数
const handleTabChange = (tabId) => {
  if (tabId !== currentTab.value) {
    saveScrollPosition(currentTab.value)
    currentTab.value = tabId
    updateUrlParams(tabId)
    restoreScrollPosition(tabId)
  }
}
</script>
```

### 4. 处理外部链接导航

确保外部链接能够正确带参数导航到主页：

```javascript
// 在项目的导航工具函数中添加支持
// utils/tools.ts 中的 router.push 方法扩展

// 原始导航方法扩展，支持tab参数
export const router = {
  // 其他方法...

  // 扩展push方法，支持tab参数
  push(url, params) {
    // 如果是主页，且有tab参数，则以query形式传递
    if (url === '/pages/index/index' && params?.tab) {
      const tabId = params.tab
      delete params.tab // 从常规params中移除tab

      // 构建query字符串
      const queryStr = Object.keys(params || {})
        .map((key) => `${key}=${encodeURIComponent(params[key])}`)
        .join('&')

      // 构建完整URL
      const fullUrl = `${url}?tab=${tabId}${queryStr ? '&' + queryStr : ''}`

      // 调用原始导航方法
      uni.navigateTo({
        url: fullUrl,
      })
    } else {
      // 原始逻辑
      // ...
    }
  },
}
```

## 验收标准

1. 点击 TabBar 能够正确切换页面内容
2. 页面切换时有平滑的过渡动画
3. 页面刷新时能够根据 URL 参数恢复到正确的 tab
4. 外部链接能够正确导航到主页并显示指定的 tab
5. 页面切换时能够保持各个 tab 页的状态（如滚动位置）
6. 性能表现良好，不出现明显的加载延迟
7. 在不同浏览器和设备上功能稳定

## 依赖关系

- 依赖 task-tabbar-02 完成（主页框架）
- 依赖 task-tabbar-03 完成（TabBar 组件）
- task-tabbar-05 依赖此任务完成（性能优化）

## 注意事项

1. 确保 URL 参数处理在 H5 和 App 环境中都能正常工作
2. 注意异步组件加载可能导致的性能问题
3. 确保页面刷新不会丢失当前 tab 状态
4. 考虑兼容性问题，确保在不同环境下都能正确工作

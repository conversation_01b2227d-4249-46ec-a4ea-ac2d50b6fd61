# MCP 功能迁移到 AI 云函数技术方案

## 1. 项目概述

本文档详细描述将 `didatodolist-mcp/` 目录中的 MCP (Model Context Protocol) 功能迁移到现有 AI 云函数架构的完整技术方案。

### 1.1 迁移目标

- 将 MCP 实现的丰富功能集成到现有的 uniCloud AI 云函数架构中
- 保持现有系统的稳定性和兼容性
- 扩展 AI 云函数的工具能力，支持更复杂的任务管理和数据分析
- 统一工具调用接口，提升用户体验

### 1.2 技术背景

**当前架构：**
- AI 云函数：`uniCloud-aliyun/cloudfunctions/ai/index.obj.js`
- 工具注册表：`TOOL_REGISTRY` 配置驱动的工具管理
- 底层云函数：`dida-todo` 云函数提供基础 API 调用

**MCP 架构：**
- Python 实现的 FastMCP 服务器
- 模块化工具注册：task_tools, project_tools, tag_tools, goal_tools, analytics_tools
- 直接 API 调用：通过 base_api 模块与滴答清单 API 交互

## 2. 功能分析对比

### 2.1 MCP 功能模块清单

| 模块 | 工具数量 | 主要功能 | 实现复杂度 |
|------|----------|----------|------------|
| **base_api** | 4 | API 请求封装和认证 | 中等 |
| **task_tools** | 4 | 任务 CRUD 操作 | 中等 |
| **project_tools** | 4 | 项目 CRUD 操作 | 中等 |
| **tag_tools** | 6 | 标签管理和操作 | 中等 |
| **goal_tools** | 6 | 目标管理系统 | 高 |
| **analytics_tools** | 7 | 数据分析和统计 | 高 |

### 2.2 详细功能对比表

#### 2.2.1 API 请求封装和认证功能

| 功能 | MCP 实现 | 当前 AI 云函数 | 迁移状态 |
|------|----------|----------------|----------|
| HTTP 请求封装 | ✅ `make_request` | ❌ | **需迁移** |
| 请求头管理 | ✅ `get_headers` | ❌ | **需迁移** |
| Token 管理 | ✅ `get_token` | ❌ | **需迁移** |
| 错误处理 | ✅ `handle_error` | ❌ | **需迁移** |

#### 2.2.2 任务管理功能

| 功能 | MCP 实现 | 当前 AI 云函数 | 迁移状态 |
|------|----------|----------------|----------|
| 获取任务列表 | ✅ `get_tasks` | ❌ `getTasks` | **需迁移** |
| 创建任务 | ✅ `create_task` | ❌ `createTask` | **需迁移** |
| 更新任务 | ✅ `update_task` | ❌ | **需迁移** |
| 删除任务 | ✅ `delete_task` | ❌ | **需迁移** |

#### 2.2.3 项目管理功能

| 功能     | MCP 实现             | 当前 AI 云函数       | 迁移状态    |
| ------ | ------------------ | --------------- | ------- |
| 获取项目列表 | ✅ `get_projects`   | ❌ `getProjects` | **需迁移** |
| 获取项目详情 | ✅ `get_project`    | ❌ `getProject`  | **需迁移** |
| 创建项目   | ✅ `create_project` | ❌               | **需迁移** |
| 更新项目   | ✅ `update_project` | ❌               | **需迁移** |
| 删除项目   | ✅ `delete_project` | ❌               | **需迁移** |

#### 2.2.4 标签管理功能

| 功能 | MCP 实现 | 当前 AI 云函数 | 迁移状态 |
|------|----------|----------------|----------|
| 获取标签列表 | ✅ `get_tags` | ❌ | **需迁移** |
| 创建标签 | ✅ `create_tag` | ❌ | **需迁移** |
| 更新标签 | ✅ `update_tag` | ❌ | **需迁移** |
| 删除标签 | ✅ `delete_tag` | ❌ | **需迁移** |
| 重命名标签 | ✅ `rename_tag` | ❌ | **需迁移** |
| 合并标签 | ✅ `merge_tags` | ❌ | **需迁移** |

#### 2.2.5 目标管理功能（全新功能）

| 功能 | MCP 实现 | 当前 AI 云函数 | 迁移状态 |
|------|----------|----------------|----------|
| 创建目标 | ✅ `create_goal` | ❌ | **需迁移** |
| 获取目标列表 | ✅ `get_goals` | ❌ | **需迁移** |
| 获取目标详情 | ✅ `get_goal` | ❌ | **需迁移** |
| 更新目标 | ✅ `update_goal` | ❌ | **需迁移** |
| 删除目标 | ✅ `delete_goal` | ❌ | **需迁移** |
| 任务目标匹配 | ✅ `match_task_with_goals` | ❌ | **需迁移** |

#### 2.2.6 数据分析功能（全新功能）

| 功能 | MCP 实现 | 当前 AI 云函数 | 迁移状态 |
|------|----------|----------------|----------|
| 目标统计 | ✅ `get_goal_statistics` | ❌ | **需迁移** |
| 目标进度历史 | ✅ `get_goal_progress` | ❌ | **需迁移** |
| 任务统计 | ✅ `get_task_statistics` | ❌ | **需迁移** |
| 关键词提取 | ✅ `extract_task_keywords` | ❌ | **需迁移** |
| 目标完成预测 | ✅ `predict_goal_completion` | ❌ | **需迁移** |
| 目标报告生成 | ✅ `generate_goal_report` | ❌ | **需迁移** |
| 周总结生成 | ✅ `generate_weekly_summary` | ❌ | **需迁移** |

## 3. 迁移方案设计

### 3.1 整体架构设计

```
AI 云函数 (index.obj.js)
├── 工具注册表 (TOOL_REGISTRY)
│   ├── 现有工具 (4个)
│   └── 新增工具 (23个)
├── 执行引擎 (executor.js)
└── 底层云函数调用
    ├── dida-todo (扩展)
    ├── dida-tag (新增)
    ├── dida-goal (新增)
    └── dida-analytics (新增)
```

### 3.2 云函数扩展策略

#### 3.2.1 新建 `dida-base` 云函数（基础 API 封装）

**主要方法：**
```javascript
module.exports = {
  // HTTP 请求封装
  makeRequest: async function(params) { /* 统一的 HTTP 请求封装 */ },

  // 请求头管理
  getHeaders: function(params) { /* 获取标准请求头 */ },

  // Token 管理
  getToken: async function(params) { /* 获取和管理访问令牌 */ },
  validateToken: async function(params) { /* 验证令牌有效性 */ },
  refreshToken: async function(params) { /* 刷新访问令牌 */ },

  // 错误处理
  handleError: function(error) { /* 统一错误处理和格式化 */ },

  // 响应数据标准化
  formatResponse: function(data) { /* 标准化响应数据格式 */ }
}
```

#### 3.2.2 重构 `dida-todo` 云函数（全量重新实现）

**重新实现所有方法：**
```javascript
// 任务管理功能（全量重新实现）
getTasks: async function(params) { /* 重新实现任务列表获取 */ }
createTask: async function(params) { /* 重新实现任务创建 */ }
updateTask: async function(params) { /* 实现任务更新 */ }
deleteTask: async function(params) { /* 实现任务删除 */ }

// 项目管理功能（全量重新实现）
getProjects: async function(params) { /* 重新实现项目列表获取 */ }
getProject: async function(params) { /* 重新实现项目详情获取 */ }
createProject: async function(params) { /* 实现项目创建 */ }
updateProject: async function(params) { /* 实现项目更新 */ }
deleteProject: async function(params) { /* 实现项目删除 */ }
```

#### 3.2.4 新建 `dida-tag` 云函数

**主要方法：**
```javascript
module.exports = {
  getTags: async function(params) { /* 获取标签列表 */ },
  createTag: async function(params) { /* 创建标签 */ },
  updateTag: async function(params) { /* 更新标签 */ },
  deleteTag: async function(params) { /* 删除标签 */ },
  renameTag: async function(params) { /* 重命名标签 */ },
  mergeTags: async function(params) { /* 合并标签 */ }
}
```

#### 3.2.5 新建 `dida-goal` 云函数

**主要方法：**
```javascript
module.exports = {
  createGoal: async function(params) { /* 创建目标 */ },
  getGoals: async function(params) { /* 获取目标列表 */ },
  getGoal: async function(params) { /* 获取目标详情 */ },
  updateGoal: async function(params) { /* 更新目标 */ },
  deleteGoal: async function(params) { /* 删除目标 */ },
  matchTaskWithGoals: async function(params) { /* 任务目标匹配 */ }
}
```

#### 3.2.6 新建 `dida-analytics` 云函数

**主要方法：**
```javascript
module.exports = {
  getGoalStatistics: async function(params) { /* 目标统计 */ },
  getGoalProgress: async function(params) { /* 目标进度历史 */ },
  getTaskStatistics: async function(params) { /* 任务统计 */ },
  extractTaskKeywords: async function(params) { /* 关键词提取 */ },
  predictGoalCompletion: async function(params) { /* 目标完成预测 */ },
  generateGoalReport: async function(params) { /* 目标报告生成 */ },
  generateWeeklySummary: async function(params) { /* 周总结生成 */ }
}
```

### 3.3 工具注册表扩展

#### 3.3.1 重构 TOOL_REGISTRY 配置（全量重新实现）

```javascript
const TOOL_REGISTRY = {
  // 基础 API 封装工具
  makeRequest: {
    cloudFunction: 'dida-base',
    method: 'makeRequest',
    description: '统一的 HTTP 请求封装',
    parameters: {
      url: { type: 'string', required: true, description: '请求 URL' },
      method: { type: 'string', required: true, description: '请求方法' },
      data: { type: 'object', required: false, description: '请求数据' },
      headers: { type: 'object', required: false, description: '自定义请求头' }
    },
    metadata: {
      estimatedTime: 1000,
      category: 'base'
    }
  },

  // 任务管理工具（全量重新实现）
  getTasks: {
    cloudFunction: 'dida-todo',
    method: 'getTasks',
    description: '获取任务列表',
    parameters: {
      mode: { type: 'string', required: false, description: '任务模式' },
      keyword: { type: 'string', required: false, description: '关键词筛选' },
      priority: { type: 'number', required: false, description: '优先级筛选' },
      projectName: { type: 'string', required: false, description: '项目名称筛选' },
      completed: { type: 'boolean', required: false, description: '是否已完成' }
    },
    metadata: {
      estimatedTime: 2000,
      category: 'query'
    }
  },

  createTask: {
    cloudFunction: 'dida-todo',
    method: 'createTask',
    description: '创建新任务',
    parameters: {
      title: { type: 'string', required: true, description: '任务标题' },
      content: { type: 'string', required: false, description: '任务内容' },
      priority: { type: 'number', required: false, description: '优先级 (0-5)' },
      projectName: { type: 'string', required: false, description: '项目名称' },
      tagNames: { type: 'array', required: false, description: '标签名称列表' },
      startDate: { type: 'string', required: false, description: '开始日期' },
      dueDate: { type: 'string', required: false, description: '截止日期' },
      isAllDay: { type: 'boolean', required: false, description: '是否全天任务' },
      reminder: { type: 'string', required: false, description: '提醒设置' }
    },
    metadata: {
      estimatedTime: 2500,
      category: 'action'
    }
  },

  // 项目管理工具（全量重新实现）
  getProjects: {
    cloudFunction: 'dida-todo',
    method: 'getProjects',
    description: '获取项目列表',
    parameters: {},
    metadata: {
      estimatedTime: 1500,
      category: 'query'
    }
  },

  getProject: {
    cloudFunction: 'dida-todo',
    method: 'getProject',
    description: '获取项目详情',
    parameters: {
      projectIdOrName: { type: 'string', required: true, description: '项目 ID 或名称' }
    },
    metadata: {
      estimatedTime: 1500,
      category: 'query'
    }
  },
  
  // 任务管理扩展
  updateTask: {
    cloudFunction: 'dida-todo',
    method: 'updateTask',
    description: '更新任务信息',
    parameters: {
      taskIdOrTitle: { type: 'string', required: true, description: '任务 ID 或标题' },
      title: { type: 'string', required: false, description: '新任务标题' },
      content: { type: 'string', required: false, description: '新任务内容' },
      priority: { type: 'number', required: false, description: '优先级 (0-5)' },
      projectName: { type: 'string', required: false, description: '项目名称' },
      tagNames: { type: 'array', required: false, description: '标签名称列表' },
      startDate: { type: 'string', required: false, description: '开始日期' },
      dueDate: { type: 'string', required: false, description: '截止日期' },
      isAllDay: { type: 'boolean', required: false, description: '是否全天任务' },
      reminder: { type: 'string', required: false, description: '提醒设置' },
      status: { type: 'number', required: false, description: '任务状态' }
    },
    metadata: {
      estimatedTime: 2500,
      category: 'action'
    }
  },
  
  deleteTask: {
    cloudFunction: 'dida-todo',
    method: 'deleteTask',
    description: '删除任务',
    parameters: {
      taskIdOrTitle: { type: 'string', required: true, description: '任务 ID 或标题' }
    },
    metadata: {
      estimatedTime: 2000,
      category: 'action'
    }
  }
  
  // 项目管理扩展
  createProject: {
    cloudFunction: 'dida-todo',
    method: 'createProject',
    description: '创建新项目',
    parameters: {
      name: { type: 'string', required: true, description: '项目名称' },
      color: { type: 'string', required: false, description: '项目颜色' }
    },
    metadata: {
      estimatedTime: 2500,
      category: 'action'
    }
  },

  updateProject: {
    cloudFunction: 'dida-todo',
    method: 'updateProject',
    description: '更新项目信息',
    parameters: {
      projectIdOrName: { type: 'string', required: true, description: '项目 ID 或名称' },
      name: { type: 'string', required: false, description: '新项目名称' },
      color: { type: 'string', required: false, description: '新项目颜色' }
    },
    metadata: {
      estimatedTime: 2000,
      category: 'action'
    }
  },

  deleteProject: {
    cloudFunction: 'dida-todo',
    method: 'deleteProject',
    description: '删除项目',
    parameters: {
      projectIdOrName: { type: 'string', required: true, description: '项目 ID 或名称' }
    },
    metadata: {
      estimatedTime: 2000,
      category: 'action'
    }
  },

  // 标签管理工具
  getTags: {
    cloudFunction: 'dida-tag',
    method: 'getTags',
    description: '获取标签列表',
    parameters: {},
    metadata: {
      estimatedTime: 1500,
      category: 'query'
    }
  },

  createTag: {
    cloudFunction: 'dida-tag',
    method: 'createTag',
    description: '创建新标签',
    parameters: {
      name: { type: 'string', required: true, description: '标签名称' },
      color: { type: 'string', required: false, description: '标签颜色' }
    },
    metadata: {
      estimatedTime: 2000,
      category: 'action'
    }
  },

  updateTag: {
    cloudFunction: 'dida-tag',
    method: 'updateTag',
    description: '更新标签信息',
    parameters: {
      tagIdOrName: { type: 'string', required: true, description: '标签 ID 或名称' },
      name: { type: 'string', required: false, description: '新标签名称' },
      color: { type: 'string', required: false, description: '新标签颜色' }
    },
    metadata: {
      estimatedTime: 2000,
      category: 'action'
    }
  },

  deleteTag: {
    cloudFunction: 'dida-tag',
    method: 'deleteTag',
    description: '删除标签',
    parameters: {
      tagIdOrName: { type: 'string', required: true, description: '标签 ID 或名称' }
    },
    metadata: {
      estimatedTime: 2000,
      category: 'action'
    }
  },

  renameTag: {
    cloudFunction: 'dida-tag',
    method: 'renameTag',
    description: '重命名标签',
    parameters: {
      oldName: { type: 'string', required: true, description: '旧标签名称' },
      newName: { type: 'string', required: true, description: '新标签名称' }
    },
    metadata: {
      estimatedTime: 2000,
      category: 'action'
    }
  },

  mergeTags: {
    cloudFunction: 'dida-tag',
    method: 'mergeTags',
    description: '合并标签',
    parameters: {
      sourceName: { type: 'string', required: true, description: '源标签名称' },
      targetName: { type: 'string', required: true, description: '目标标签名称' }
    },
    metadata: {
      estimatedTime: 3000,
      category: 'action'
    }
  },

  // 目标管理工具
  createGoal: {
    cloudFunction: 'dida-goal',
    method: 'createGoal',
    description: '创建新目标',
    parameters: {
      title: { type: 'string', required: true, description: '目标标题' },
      type: { type: 'string', required: true, description: '目标类型 (phase/permanent/habit)' },
      keywords: { type: 'string', required: true, description: '关键词，逗号分隔' },
      description: { type: 'string', required: false, description: '目标描述' },
      dueDate: { type: 'string', required: false, description: '截止日期 (YYYY-MM-DD)' },
      startDate: { type: 'string', required: false, description: '开始日期 (YYYY-MM-DD)' },
      frequency: { type: 'string', required: false, description: '频率 (习惯目标必填)' },
      relatedProjects: { type: 'array', required: false, description: '相关项目 IDs' }
    },
    metadata: {
      estimatedTime: 3000,
      category: 'action'
    }
  },

  getGoals: {
    cloudFunction: 'dida-goal',
    method: 'getGoals',
    description: '获取目标列表',
    parameters: {
      type: { type: 'string', required: false, description: '目标类型筛选' },
      status: { type: 'string', required: false, description: '目标状态筛选' },
      keywords: { type: 'string', required: false, description: '关键词筛选' }
    },
    metadata: {
      estimatedTime: 1500,
      category: 'query'
    }
  },

  getGoal: {
    cloudFunction: 'dida-goal',
    method: 'getGoal',
    description: '获取目标详情',
    parameters: {
      goalId: { type: 'string', required: true, description: '目标 ID' }
    },
    metadata: {
      estimatedTime: 1500,
      category: 'query'
    }
  },

  updateGoal: {
    cloudFunction: 'dida-goal',
    method: 'updateGoal',
    description: '更新目标',
    parameters: {
      goalId: { type: 'string', required: true, description: '目标 ID' },
      title: { type: 'string', required: false, description: '新标题' },
      type: { type: 'string', required: false, description: '新类型' },
      status: { type: 'string', required: false, description: '新状态' },
      keywords: { type: 'string', required: false, description: '新关键词' },
      description: { type: 'string', required: false, description: '新描述' },
      dueDate: { type: 'string', required: false, description: '新截止日期' },
      startDate: { type: 'string', required: false, description: '新开始日期' },
      frequency: { type: 'string', required: false, description: '新频率' }
    },
    metadata: {
      estimatedTime: 2500,
      category: 'action'
    }
  },

  deleteGoal: {
    cloudFunction: 'dida-goal',
    method: 'deleteGoal',
    description: '删除目标',
    parameters: {
      goalId: { type: 'string', required: true, description: '目标 ID' }
    },
    metadata: {
      estimatedTime: 2000,
      category: 'action'
    }
  },

  matchTaskWithGoals: {
    cloudFunction: 'dida-goal',
    method: 'matchTaskWithGoals',
    description: '匹配任务与目标',
    parameters: {
      taskTitle: { type: 'string', required: true, description: '任务标题' },
      taskContent: { type: 'string', required: false, description: '任务内容' },
      projectId: { type: 'string', required: false, description: '项目 ID' }
    },
    metadata: {
      estimatedTime: 2000,
      category: 'query'
    }
  },

  // 数据分析工具
  getGoalStatistics: {
    cloudFunction: 'dida-analytics',
    method: 'getGoalStatistics',
    description: '获取目标统计信息',
    parameters: {
      forceRefresh: { type: 'boolean', required: false, description: '是否强制刷新缓存' }
    },
    metadata: {
      estimatedTime: 3000,
      category: 'query'
    }
  },

  getGoalProgress: {
    cloudFunction: 'dida-analytics',
    method: 'getGoalProgress',
    description: '获取目标进度历史',
    parameters: {
      goalId: { type: 'string', required: true, description: '目标 ID' }
    },
    metadata: {
      estimatedTime: 2000,
      category: 'query'
    }
  },

  getTaskStatistics: {
    cloudFunction: 'dida-analytics',
    method: 'getTaskStatistics',
    description: '获取任务统计信息',
    parameters: {
      days: { type: 'number', required: false, description: '统计天数范围' },
      forceRefresh: { type: 'boolean', required: false, description: '是否强制刷新缓存' }
    },
    metadata: {
      estimatedTime: 3000,
      category: 'query'
    }
  },

  extractTaskKeywords: {
    cloudFunction: 'dida-analytics',
    method: 'extractTaskKeywords',
    description: '从任务中提取关键词',
    parameters: {
      limit: { type: 'number', required: false, description: '返回关键词数量' },
      forceRefresh: { type: 'boolean', required: false, description: '是否强制刷新缓存' }
    },
    metadata: {
      estimatedTime: 2500,
      category: 'query'
    }
  },

  predictGoalCompletion: {
    cloudFunction: 'dida-analytics',
    method: 'predictGoalCompletion',
    description: '预测目标完成情况',
    parameters: {
      goalId: { type: 'string', required: true, description: '目标 ID' },
      forceRefresh: { type: 'boolean', required: false, description: '是否强制刷新缓存' }
    },
    metadata: {
      estimatedTime: 3000,
      category: 'query'
    }
  },

  generateGoalReport: {
    cloudFunction: 'dida-analytics',
    method: 'generateGoalReport',
    description: '生成目标报告',
    parameters: {
      goalId: { type: 'string', required: true, description: '目标 ID' },
      forceRefresh: { type: 'boolean', required: false, description: '是否强制刷新缓存' }
    },
    metadata: {
      estimatedTime: 4000,
      category: 'query'
    }
  },

  generateWeeklySummary: {
    cloudFunction: 'dida-analytics',
    method: 'generateWeeklySummary',
    description: '生成每周总结',
    parameters: {
      forceRefresh: { type: 'boolean', required: false, description: '是否强制刷新缓存' }
    },
    metadata: {
      estimatedTime: 5000,
      category: 'query'
    }
  }
}
```

## 4. 技术实现路径

### 4.1 实施阶段规划

#### 阶段一：基础 API 封装层（优先级：最高）
**目标：** 建立统一的 API 请求封装和认证管理
**工期：** 1 周

**实施内容：**
1. 创建 `dida-base` 云函数，实现：
   - `makeRequest` - 统一 HTTP 请求封装
   - `getHeaders` - 请求头管理
   - `getToken` - Token 获取和管理
   - `validateToken` - Token 有效性验证
   - `handleError` - 统一错误处理

2. 建立 Token 管理机制和缓存策略

3. 实现请求重试和错误恢复机制

#### 阶段二：核心功能重构（优先级：高）
**目标：** 全量重新实现任务和项目管理功能
**工期：** 2-3 周

**实施内容：**
1. 重构 `dida-todo` 云函数，重新实现所有方法：
   - `getTasks` - 任务列表获取（重新实现）
   - `createTask` - 任务创建（重新实现）
   - `updateTask` - 任务更新（新增）
   - `deleteTask` - 任务删除（新增）
   - `getProjects` - 项目列表获取（重新实现）
   - `getProject` - 项目详情获取（重新实现）
   - `createProject` - 项目创建（新增）
   - `updateProject` - 项目更新（新增）
   - `deleteProject` - 项目删除（新增）

2. 更新 AI 云函数的 `TOOL_REGISTRY`，重新配置所有工具

3. 全面测试基础 CRUD 操作的完整性和稳定性

#### 阶段三：标签管理功能（优先级：中）
**目标：** 实现完整的标签管理体系
**工期：** 1 周

**实施内容：**
1. 创建 `dida-tag` 云函数
2. 实现标签的完整 CRUD 操作
3. 实现标签重命名和合并功能
4. 集成到 AI 工具注册表

#### 阶段四：目标管理系统（优先级：高）
**目标：** 引入目标管理概念，提升任务组织能力
**工期：** 2-3 周

**实施内容：**
1. 创建 `dida-goal` 云函数
2. 实现目标的完整生命周期管理
3. 实现任务与目标的智能匹配算法
4. 建立目标数据存储机制（基于任务系统）

#### 阶段五：数据分析功能（优先级：中）
**目标：** 提供数据洞察和智能分析
**工期：** 2-3 周

**实施内容：**
1. 创建 `dida-analytics` 云函数
2. 实现统计分析功能
3. 实现预测和报告生成
4. 优化性能和缓存机制

### 4.2 核心技术实现

#### 4.2.1 基础 API 封装层实现

**新建 dida-base 云函数：**

```javascript
// uniCloud-aliyun/cloudfunctions/dida-base/index.obj.js

const BASE_URL = 'https://api.dida365.com/open/v1'

module.exports = {
  _before: function () {
    // 通用预处理器
  },

  /**
   * 统一的 HTTP 请求封装
   * @param {object} params 参数对象
   * @param {string} params.url 请求 URL
   * @param {string} params.method 请求方法
   * @param {object} [params.data] 请求数据
   * @param {object} [params.headers] 自定义请求头
   * @param {string} [params.token] 访问令牌
   * @returns {object} 请求响应
   */
  makeRequest: async function(params) {
    console.log('--- 调用 makeRequest ---', params)
    const { url, method, data, headers = {}, token } = params || {}

    if (!url || !method) {
      return {
        errCode: 'INVALID_PARAMS',
        errMsg: 'URL 和请求方法都是必需的参数'
      }
    }

    try {
      // 1. 获取访问令牌
      const accessToken = token || await this.getToken()
      if (!accessToken) {
        return {
          errCode: 'TOKEN_ERROR',
          errMsg: '无法获取有效的访问令牌'
        }
      }

      // 2. 构建请求头（参考 MCP 实现格式）
      const requestHeaders = {
        'Accept': 'application/json',
        'Cookie': `t=${accessToken}`,
        'User-Agent': 'Apifox/1.0.0',
        'Content-Type': 'application/json',
        'Host': 'api.dida365.com',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        ...headers
      }

      // 3. 构建完整 URL
      const fullUrl = url.startsWith('http') ? url : `${BASE_URL}${url}`

      // 4. 发送请求
      const response = await uniCloud.httpclient.request(fullUrl, {
        method: method.toUpperCase(),
        headers: requestHeaders,
        data: data,
        dataType: 'json',
        timeout: 30000 // 30 秒超时
      })

      console.log('makeRequest 成功，响应数据：', response.data)

      return this.formatResponse(response.data)
    } catch (error) {
      console.error('makeRequest 失败：', error)
      return this.handleError(error)
    }
  },

  /**
   * 获取访问令牌
   * @param {object} [params] 参数对象
   * @param {boolean} [params.forceRefresh] 是否强制刷新
   * @returns {string} 访问令牌
   */
  getToken: async function(params = {}) {
    const { forceRefresh = false } = params

    try {
      // 1. 检查缓存的令牌
      if (!forceRefresh) {
        const cachedToken = await this.getCachedToken()
        if (cachedToken && await this.validateToken({ token: cachedToken })) {
          return cachedToken
        }
      }

      // 2. 使用预设令牌（实际项目中可能需要动态获取）
      const token = '94153c68-343d-49b8-8f4c-c9504cf86a22'

      // 3. 验证令牌有效性
      const isValid = await this.validateToken({ token })
      if (!isValid) {
        throw new Error('预设令牌无效')
      }

      // 4. 缓存令牌
      await this.cacheToken(token)

      return token
    } catch (error) {
      console.error('getToken 失败：', error)
      return null
    }
  },

  /**
   * 验证令牌有效性
   * @param {object} params 参数对象
   * @param {string} params.token 要验证的令牌
   * @returns {boolean} 是否有效
   */
  validateToken: async function(params) {
    const { token } = params || {}

    if (!token) {
      return false
    }

    try {
      // 通过调用一个简单的 API 来验证令牌
      const response = await uniCloud.httpclient.request(`${BASE_URL}/user/profile`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Cookie': `t=${token}`,
          'User-Agent': 'Apifox/1.0.0',
          'Content-Type': 'application/json',
          'Host': 'api.dida365.com'
        },
        dataType: 'json',
        timeout: 10000
      })

      return response.status === 200
    } catch (error) {
      console.error('validateToken 失败：', error)
      return false
    }
  },

  /**
   * 统一错误处理
   * @param {Error} error 错误对象
   * @returns {object} 标准化错误响应
   */
  handleError: function(error) {
    console.error('处理错误：', error)

    // 根据不同类型的错误返回不同的错误码
    if (error.code === 'TIMEOUT') {
      return {
        errCode: 'REQUEST_TIMEOUT',
        errMsg: '请求超时，请稍后重试',
        error: error.message
      }
    }

    if (error.response) {
      const status = error.response.status
      const data = error.response.data

      if (status === 401) {
        return {
          errCode: 'UNAUTHORIZED',
          errMsg: '认证失败，请检查访问令牌',
          error: data
        }
      }

      if (status === 403) {
        return {
          errCode: 'FORBIDDEN',
          errMsg: '权限不足，无法访问该资源',
          error: data
        }
      }

      if (status === 404) {
        return {
          errCode: 'NOT_FOUND',
          errMsg: '请求的资源不存在',
          error: data
        }
      }

      if (status >= 500) {
        return {
          errCode: 'SERVER_ERROR',
          errMsg: '服务器内部错误，请稍后重试',
          error: data
        }
      }
    }

    return {
      errCode: 'UNKNOWN_ERROR',
      errMsg: error.message || '未知错误',
      error: error
    }
  },

  /**
   * 标准化响应数据格式
   * @param {any} data 原始响应数据
   * @returns {object} 标准化响应
   */
  formatResponse: function(data) {
    return {
      success: true,
      data: data,
      timestamp: new Date().toISOString()
    }
  },

  // 辅助方法
  async getCachedToken() {
    // 实现令牌缓存获取逻辑
    // 可以使用 uniCloud 的数据库或缓存服务
    return null
  },

  async cacheToken(token) {
    // 实现令牌缓存存储逻辑
    // 设置合理的过期时间
  }
}
```

#### 4.2.2 重构 dida-todo 云函数示例

**全量重新实现 dida-todo 云函数：**

```javascript
// uniCloud-aliyun/cloudfunctions/dida-todo/index.obj.js

module.exports = {
  // 现有方法...

  /**
   * 更新任务
   * @param {object} params 参数对象
   * @param {string} params.taskIdOrTitle 任务 ID 或标题
   * @param {string} [params.title] 新任务标题
   * @param {string} [params.content] 新任务内容
   * @param {number} [params.priority] 优先级 (0-5)
   * @param {string} [params.projectName] 项目名称
   * @param {Array} [params.tagNames] 标签名称列表
   * @param {string} [params.startDate] 开始日期
   * @param {string} [params.dueDate] 截止日期
   * @param {boolean} [params.isAllDay] 是否全天任务
   * @param {string} [params.reminder] 提醒设置
   * @param {number} [params.status] 任务状态
   * @returns {object} 更新后的任务信息
   */
  updateTask: async function(params) {
    console.log('--- 调用 updateTask ---', params)
    const { taskIdOrTitle, ...updateData } = params || {}

    if (!taskIdOrTitle) {
      return {
        errCode: 'INVALID_PARAMS',
        errMsg: '任务 ID 或标题是必需的参数'
      }
    }

    try {
      // 1. 首先查找任务
      const task = await this.findTaskByIdOrTitle(taskIdOrTitle)
      if (!task) {
        return {
          errCode: 'TASK_NOT_FOUND',
          errMsg: '未找到指定的任务'
        }
      }

      // 2. 处理项目名称到项目 ID 的转换
      if (updateData.projectName) {
        const projectId = await this.getProjectIdByName(updateData.projectName)
        if (projectId) {
          updateData.projectId = projectId
          delete updateData.projectName
        }
      }

      // 3. 处理标签名称到标签 ID 的转换
      if (updateData.tagNames && Array.isArray(updateData.tagNames)) {
        const tagIds = await this.getTagIdsByNames(updateData.tagNames)
        updateData.tags = tagIds
        delete updateData.tagNames
      }

      // 4. 处理日期格式
      if (updateData.startDate) {
        updateData.startDate = this.formatDateForAPI(updateData.startDate)
      }
      if (updateData.dueDate) {
        updateData.dueDate = this.formatDateForAPI(updateData.dueDate)
      }

      // 5. 发送更新请求
      const url = `${BASE_URL}/task/${task.id}`
      const response = await uniCloud.httpclient.request(url, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        data: updateData,
        dataType: 'json'
      })

      console.log('updateTask 成功，响应数据：', response.data)

      return {
        success: true,
        data: response.data
      }
    } catch (error) {
      console.error('updateTask 失败：', error)
      return {
        errCode: 'UPDATE_TASK_FAILED',
        errMsg: error.message || '更新任务失败',
        error: error.response ? error.response.data : null
      }
    }
  },

  /**
   * 删除任务
   * @param {object} params 参数对象
   * @param {string} params.taskIdOrTitle 任务 ID 或标题
   * @returns {object} 删除操作的响应
   */
  deleteTask: async function(params) {
    console.log('--- 调用 deleteTask ---', params)
    const { taskIdOrTitle } = params || {}

    if (!taskIdOrTitle) {
      return {
        errCode: 'INVALID_PARAMS',
        errMsg: '任务 ID 或标题是必需的参数'
      }
    }

    try {
      // 1. 首先查找任务
      const task = await this.findTaskByIdOrTitle(taskIdOrTitle)
      if (!task) {
        return {
          errCode: 'TASK_NOT_FOUND',
          errMsg: '未找到指定的任务'
        }
      }

      // 2. 发送删除请求
      const url = `${BASE_URL}/task/${task.id}`
      const response = await uniCloud.httpclient.request(url, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        dataType: 'json'
      })

      console.log('deleteTask 成功，响应数据：', response.data)

      return {
        success: true,
        data: response.data,
        message: '任务删除成功'
      }
    } catch (error) {
      console.error('deleteTask 失败：', error)
      return {
        errCode: 'DELETE_TASK_FAILED',
        errMsg: error.message || '删除任务失败',
        error: error.response ? error.response.data : null
      }
    }
  },

  // 辅助方法
  async findTaskByIdOrTitle(taskIdOrTitle) {
    // 实现任务查找逻辑
    // 可以先尝试作为 ID 查找，失败后作为标题查找
  },

  async getProjectIdByName(projectName) {
    // 实现项目名称到 ID 的转换
  },

  async getTagIdsByNames(tagNames) {
    // 实现标签名称到 ID 的转换
  },

  formatDateForAPI(dateString) {
    // 实现日期格式转换
  }
}
```

#### 4.2.2 新建 dida-goal 云函数示例

```javascript
// uniCloud-aliyun/cloudfunctions/dida-goal/index.obj.js

const BASE_URL = 'https://api.dida365.com/open/v1'
const token = '94153c68-343d-49b8-8f4c-c9504cf86a22'

// 目标管理项目名称
const GOAL_PROJECT_NAME = "🎯 目标管理"

module.exports = {
  _before: function () {
    // 通用预处理器
  },

  /**
   * 创建新目标
   * @param {object} params 参数对象
   * @param {string} params.title 目标标题
   * @param {string} params.type 目标类型 (phase/permanent/habit)
   * @param {string} params.keywords 关键词，逗号分隔
   * @param {string} [params.description] 目标描述
   * @param {string} [params.dueDate] 截止日期 (YYYY-MM-DD)
   * @param {string} [params.startDate] 开始日期 (YYYY-MM-DD)
   * @param {string} [params.frequency] 频率 (习惯目标必填)
   * @param {Array} [params.relatedProjects] 相关项目 IDs
   * @returns {object} 创建的目标信息
   */
  createGoal: async function(params) {
    console.log('--- 调用 createGoal ---', params)
    const { title, type, keywords, description, dueDate, startDate, frequency, relatedProjects } = params || {}

    if (!title || !type || !keywords) {
      return {
        errCode: 'INVALID_PARAMS',
        errMsg: '目标标题、类型和关键词都是必需的参数'
      }
    }

    try {
      // 1. 确保目标管理项目存在
      const projectId = await this.ensureGoalProjectExists()

      // 2. 准备元数据
      const metadata = {
        type: type,
        keywords: this.normalizeKeywords(keywords),
        start_date: startDate,
        frequency: frequency
      }
      const metadataStr = this.formatMetadata(metadata)

      // 3. 组合任务内容
      const fullContent = description
        ? `${description}\n\n--- Metadata ---\n${metadataStr}`
        : `--- Metadata ---\n${metadataStr}`

      // 4. 创建任务（作为目标）
      const taskData = {
        title: title,
        content: fullContent,
        projectId: projectId,
        priority: 3, // 默认中等优先级
        status: 0
      }

      // 处理日期
      if (startDate) {
        taskData.startDate = this.formatDateForAPI(startDate)
      }
      if (dueDate) {
        taskData.dueDate = this.formatDateForAPI(dueDate)
      }

      // 5. 发送创建请求
      const url = `${BASE_URL}/task`
      const response = await uniCloud.httpclient.request(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        data: taskData,
        dataType: 'json'
      })

      console.log('createGoal 成功，响应数据：', response.data)

      return {
        success: true,
        data: this.formatGoalResponse(response.data)
      }
    } catch (error) {
      console.error('createGoal 失败：', error)
      return {
        errCode: 'CREATE_GOAL_FAILED',
        errMsg: error.message || '创建目标失败',
        error: error.response ? error.response.data : null
      }
    }
  },

  // 辅助方法
  async ensureGoalProjectExists() {
    // 确保目标管理项目存在，不存在则创建
  },

  normalizeKeywords(keywords) {
    // 标准化关键词格式
  },

  formatMetadata(metadata) {
    // 格式化元数据为字符串
  },

  formatDateForAPI(dateString) {
    // 格式化日期为 API 所需格式
  },

  formatGoalResponse(taskData) {
    // 将任务数据格式化为目标响应格式
  }
}
```

### 4.3 数据存储策略

#### 4.3.1 目标数据存储方案

**基于任务系统的目标存储：**
- 利用现有的滴答清单任务系统存储目标数据
- 在专门的"🎯 目标管理"项目中创建任务来表示目标
- 通过任务的 `content` 字段存储目标的元数据信息

**元数据格式：**
```
目标描述内容

--- Metadata ---
[Type: phase] [Keywords: 学习,技能提升] [Start_date: 2024-01-01] [Frequency: daily]
```

#### 4.3.2 数据同步机制

**缓存策略：**
- 在云函数中实现内存缓存，减少 API 调用
- 设置合理的缓存过期时间（如 5 分钟）
- 提供强制刷新机制

**数据一致性：**
- 通过统一的数据访问层确保数据一致性
- 实现乐观锁机制防止并发更新冲突

### 4.4 兼容性保证

#### 4.4.1 向后兼容

**现有工具保持不变：**
- 现有的 4 个工具（getProjects, getTasks, createTask, getProject）保持完全兼容
- 不修改现有工具的参数结构和返回格式
- 确保现有的 AI 对话功能不受影响

**渐进式升级：**
- 新功能通过新工具提供，不影响现有功能
- 支持新旧工具并存，逐步迁移

#### 4.4.2 错误处理

**统一错误格式：**
```javascript
{
  errCode: 'ERROR_TYPE',
  errMsg: '用户友好的错误信息',
  error: '详细的技术错误信息（可选）'
}
```

**错误类型定义：**
- `INVALID_PARAMS` - 参数错误
- `NOT_FOUND` - 资源不存在
- `API_ERROR` - API 调用失败
- `PERMISSION_DENIED` - 权限不足
- `INTERNAL_ERROR` - 内部错误

## 5. 实施计划和里程碑

### 5.1 详细时间规划

| 阶段 | 工期 | 开始时间 | 结束时间 | 交付物 |
|------|------|----------|----------|--------|
| **阶段一** | 1 周 | Week 1 | Week 1 | dida-base 云函数（API 封装层） |
| **阶段二** | 3 周 | Week 2 | Week 4 | 重构的 dida-todo 云函数 |
| **阶段三** | 1 周 | Week 5 | Week 5 | dida-tag 云函数 |
| **阶段四** | 3 周 | Week 6 | Week 8 | dida-goal 云函数 |
| **阶段五** | 3 周 | Week 9 | Week 11 | dida-analytics 云函数 |
| **测试集成** | 1 周 | Week 12 | Week 12 | 完整系统测试 |

### 5.2 关键里程碑

#### 里程碑 1：基础 API 封装完成（Week 1）
- ✅ dida-base 云函数实现
- ✅ 统一 HTTP 请求封装
- ✅ Token 管理和认证机制
- ✅ 错误处理和响应标准化

#### 里程碑 2：核心功能重构完成（Week 4）
- ✅ dida-todo 云函数全量重新实现
- ✅ 任务和项目 CRUD 操作完整实现
- ✅ AI 工具注册表全面更新
- ✅ 基础功能稳定性测试通过

#### 里程碑 3：标签管理上线（Week 5）
- ✅ dida-tag 云函数实现
- ✅ 标签完整生命周期管理
- ✅ 标签重命名和合并功能
- ✅ 与现有系统集成测试

#### 里程碑 4：目标管理系统（Week 8）
- ✅ dida-goal 云函数实现
- ✅ 目标创建和管理功能
- ✅ 任务目标匹配算法
- ✅ 目标数据存储机制

#### 里程碑 5：数据分析平台（Week 11）
- ✅ dida-analytics 云函数实现
- ✅ 统计分析功能
- ✅ 预测和报告生成
- ✅ 性能优化完成

#### 里程碑 6：系统上线（Week 12）
- ✅ 完整系统集成测试
- ✅ 性能和稳定性测试
- ✅ 用户验收测试
- ✅ 生产环境部署

### 5.3 风险评估和应对

#### 高风险项

**1. API 兼容性风险**
- **风险描述：** 滴答清单 API 变更可能影响功能实现
- **应对措施：** 建立 API 版本监控，实现适配层隔离变更影响
- **预案：** 准备 API 降级方案，确保核心功能可用

**2. 性能风险**
- **风险描述：** 大量数据分析可能影响云函数性能
- **应对措施：** 实现分页查询、缓存机制、异步处理
- **预案：** 准备性能监控和自动扩容方案

#### 中风险项

**3. 数据一致性风险**
- **风险描述：** 多个云函数操作同一数据可能产生不一致
- **应对措施：** 实现统一的数据访问层和事务机制
- **预案：** 建立数据修复和回滚机制

**4. 用户体验风险**
- **风险描述：** 新功能可能影响现有用户体验
- **应对措施：** 渐进式发布，A/B 测试，用户反馈收集
- **预案：** 快速回滚机制，用户支持响应

## 6. 总结

### 6.1 预期收益

**功能完整性提升：**
- 从 4 个有问题的基础工具重构为 31 个完整工具
- 覆盖 API 封装、任务、项目、标签、目标、分析六大功能模块
- 提供完整的任务管理生态系统和基础设施

**用户体验改善：**
- 解决现有工具的稳定性问题
- 支持更复杂的任务管理场景
- 提供数据洞察和智能分析
- 实现目标驱动的任务管理

**技术架构优化：**
- 统一的 API 请求封装和认证管理
- 标准化的错误处理和响应格式
- 模块化的云函数架构
- 可扩展的数据存储方案

### 6.2 成功标准

**技术指标：**
- 所有工具响应时间 < 3 秒
- 系统可用性 > 99.5%
- API 调用成功率 > 99%
- Token 管理稳定性 > 99.9%

**功能指标：**
- 31 个工具全部正常工作
- 现有功能问题完全解决
- 支持并发用户数 > 100
- 错误处理覆盖率 100%

**用户指标：**
- 用户满意度 > 90%
- 功能使用率 > 60%
- 错误报告 < 1%

### 6.3 后续规划

**短期优化（1-3 个月）：**
- 性能监控和优化
- 用户反馈收集和功能改进
- 新功能的用户培训和推广

**中期扩展（3-6 个月）：**
- 移动端适配优化
- 更多数据分析维度
- 第三方系统集成

**长期发展（6-12 个月）：**
- AI 智能推荐系统
- 团队协作功能
- 企业级功能扩展

---

**文档版本：** v1.0
**创建时间：** 2024-01-15
**最后更新：** 2024-01-15
**负责人：** AI 开发团队
```
```

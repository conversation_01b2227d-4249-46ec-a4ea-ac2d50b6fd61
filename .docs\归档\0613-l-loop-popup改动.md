# l-loop-popup 组件重构改动指南

## 背景

为了配合使用自定义的 `rrrrule.ts` 库替代原有的 RRule 库，需要对 `src/pages/okr/components/l-loop-popup.vue` 组件进行相应修改。循环设置弹窗组件是用户进行循环规则配置的关键界面，需要适配新的规则格式。

## 改动内容

### 1. 导入修改

```js
// 原导入
import { RRule } from 'rrule'
import RRuleHelper, { CustomRuleType } from '@/utils/rrule'

// 改为
import * as rrule from '@/utils/rrrrule'
```

### 2. 内部数据结构修改

```js
// 移除 RRule 常量依赖，改用字符串常量
// 例如，将
const rruleOptions = {
  freq: RRule.DAILY,
}

// 改为
const ruleObj = {
  type: 'DAILY',
}
```

### 3. 解析循环规则字符串函数修改

```js
// 原解析函数
const parseInitialValue = () => {
  try {
    if (!props.initialValue || props.initialValue === '不重复') {
      frequency.value = 'none'
      return
    }

    const rruleString = props.initialValue.replace('RRULE:', '')
    
    // 以下是 RRule 格式解析逻辑...
    // ...
  } catch (error) {
    console.error('解析 RRULE 出错：', error)
    frequency.value = 'daily'
  }
}

// 改为
const parseInitialValue = () => {
  try {
    if (!props.initialValue || props.initialValue === '不重复') {
      frequency.value = 'none'
      return
    }

    // 解析新格式的循环规则字符串
    const ruleObj = rrule.parseRule(props.initialValue)
    
    // 基于解析后的对象设置界面状态
    switch (ruleObj.type) {
      case 'DAILY':
        frequency.value = 'daily'
        break
      
      case 'WEEKLY':
        frequency.value = 'weekly'
        if (ruleObj.byweekday && ruleObj.byweekday.length > 0) {
          selectedWeekdays.value = []
          // 将 MO,TU,WE 等转换为索引
          const dayMap = { MO: 0, TU: 1, WE: 2, TH: 3, FR: 4, SA: 5, SU: 6 }
          ruleObj.byweekday.forEach(day => {
            if (dayMap[day] !== undefined) {
              selectedWeekdays.value.push(dayMap[day])
            }
          })
        }
        break
      
      case 'MONTHLY':
        frequency.value = 'monthly'
        if (ruleObj.bymonthday && ruleObj.bymonthday.length > 0) {
          selectedMonthDays.value = [...ruleObj.bymonthday]
        }
        break
      
      case 'INTERVAL_DAILY':
        frequency.value = 'interval'
        if (ruleObj.interval) {
          intervalDays.value = ruleObj.interval
        }
        break
      
      case 'WEEKLY_N_TIMES':
        frequency.value = 'weekly_n_times'
        if (ruleObj.count) {
          weeklyTimes.value = ruleObj.count
        }
        break
      
      case 'MONTHLY_N_TIMES':
        frequency.value = 'monthly_n_times'
        if (ruleObj.count) {
          monthlyTimes.value = ruleObj.count
        }
        break
      
      case 'N_DAYS':
        frequency.value = 'n_days'
        if (ruleObj.interval) {
          nDays.value = ruleObj.interval
        }
        break
      
      default:
        frequency.value = 'daily' // 默认每天
    }
  } catch (error) {
    console.error('解析循环规则出错：', error)
    frequency.value = 'daily' // 出错时默认为每天
  }
}
```

### 4. 生成循环规则字符串函数修改

```js
// 原生成函数
const generateRRuleString = () => {
  if (frequency.value === 'none') {
    return ''
  }

  let rruleOptions = {}

  switch (frequency.value) {
    case 'daily':
      rruleOptions = {
        freq: RRule.DAILY,
      }
      break
    // 其他 case 分支...
  }

  return RRuleHelper.toString(rruleOptions)
}

// 改为
const generateRuleString = () => {
  if (frequency.value === 'none') {
    return ''
  }

  let ruleObj = {}

  switch (frequency.value) {
    case 'daily':
      ruleObj = {
        type: 'DAILY'
      }
      break
    
    case 'weekly':
      // 将界面选择的星期几索引转换为 rrrrule.ts 中的 WeekdayCode
      const weekdays = []
      const weekdayCodes = ['MO', 'TU', 'WE', 'TH', 'FR', 'SA', 'SU']
      selectedWeekdays.value.forEach(index => {
        weekdays.push(weekdayCodes[index])
      })
      
      ruleObj = {
        type: 'WEEKLY',
        byweekday: weekdays
      }
      break
    
    case 'monthly':
      ruleObj = {
        type: 'MONTHLY',
        bymonthday: [...selectedMonthDays.value]
      }
      break
    
    case 'interval':
      ruleObj = {
        type: 'INTERVAL_DAILY',
        interval: intervalDays.value,
        startDate: props.startDate || new Date().toISOString().split('T')[0] // 使用当前日期或提供的开始日期
      }
      break
    
    case 'weekly_n_times':
      ruleObj = {
        type: 'WEEKLY_N_TIMES',
        count: weeklyTimes.value
      }
      break
    
    case 'monthly_n_times':
      ruleObj = {
        type: 'MONTHLY_N_TIMES',
        count: monthlyTimes.value
      }
      break
    
    case 'n_days':
      ruleObj = {
        type: 'N_DAYS',
        interval: nDays.value,
        startDate: props.startDate || new Date().toISOString().split('T')[0] // 使用当前日期或提供的开始日期
      }
      break
  }

  // 将对象转换为字符串格式
  return objectToRuleString(ruleObj)
}

// 新增：将规则对象转换为字符串的辅助函数
const objectToRuleString = (obj) => {
  if (!obj || !obj.type) return ''
  
  const parts = [`type=${obj.type}`]
  
  if (obj.byweekday && obj.byweekday.length > 0) {
    parts.push(`byweekday=${obj.byweekday.join(',')}`)
  }
  
  if (obj.bymonthday && obj.bymonthday.length > 0) {
    parts.push(`bymonthday=${obj.bymonthday.join(',')}`)
  }
  
  if (obj.interval) {
    parts.push(`interval=${obj.interval}`)
  }
  
  if (obj.count) {
    parts.push(`count=${obj.count}`)
  }
  
  if (obj.startDate) {
    parts.push(`startDate=${obj.startDate}`)
  }
  
  return parts.join(';')
}
```

### 5. 确认按钮处理函数修改

```js
// 原确认按钮处理
const handleConfirm = () => {
  const rruleString = frequency.value === 'none' ? '' : generateRRuleString()
  console.log('rruleString', rruleString)
  emit('confirm', rruleString)
  showPopup.value = false
}

// 改为
const handleConfirm = () => {
  const ruleString = frequency.value === 'none' ? '' : generateRuleString()
  console.log('ruleString', ruleString)
  emit('confirm', ruleString)
  showPopup.value = false
}
```

### 6. 星期几映射修改

需要确保星期几的索引映射与 `rrrrule.ts` 中的定义一致。

```js
// 确保星期几的映射与 rrrrule.ts 一致
const weekdayCodes = ['MO', 'TU', 'WE', 'TH', 'FR', 'SA', 'SU']
```

## 测试建议

1. 测试各种循环类型的规则设置和展示
2. 验证循环规则在 krEdit 页面中能正确显示和保存
3. 测试界面交互，确保用户设置的循环规则能正确转换为字符串格式
4. 测试弹窗组件能正确解析和显示从 krEdit 页面传入的初始值

## 注意事项

1. 确保组件内部使用的规则类型和字段与 `rrrrule.ts` 中定义的完全一致
2. 组件需要处理 `startDate` 参数，将其用于需要起始日期的循环类型中
3. 保持界面友好性，确保用户能直观理解各种循环类型的设置

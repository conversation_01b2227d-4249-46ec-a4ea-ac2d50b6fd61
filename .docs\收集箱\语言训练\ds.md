<think>
我们正在处理一个需求：为现有的关键词讲故事页面（`src/pages/speak/keyword-story-page.vue`）添加自定义功能，包括关键字的内置、随机生成以及指定领域。
 根据用户要求，我们需要打开思维，思考可以添加什么功能，如何集成，如何规划，以及思路是什么。

 首先，分析当前页面的功能：
   - 当前页面可以随机生成关键词（通过`getRandomKeywords`函数），然后用户根据这些关键词讲故事。
   - 用户可以通过文本或语音输入故事，然后得到 AI 的评价。

 用户希望添加的功能：
   - 关键字可以内置（即提供预设的关键词列表）
   - 可以随机生成（已有）
   - 可以指定某个领域（例如：科技、教育、生活等）

 扩展思路：
  1. 关键词来源多样化：
      - 内置关键词：提供多个预设的关键词列表，每个列表对应一个领域（如科技、教育、生活等）。
      - 随机生成：保留现有的随机生成，但可以基于选定的领域来生成。
      - 用户自定义：允许用户输入自己的关键词。

  2. 领域选择：
      - 在页面上添加一个领域选择器（例如下拉菜单或选项卡），让用户选择领域。领域包括：通用（默认）、科技、教育、生活、娱乐等。
      - 根据选择的领域，从对应的内置关键词列表中随机选取关键词，或者用户可以选择内置的某个关键词组。

  3. 关键词管理：
      - 允许用户从内置的关键词组中选择一组关键词，也可以自己输入关键词（多个关键词用逗号分隔）。
      - 提供关键词编辑功能：在关键词展示区域，允许用户删除某个关键词，或者添加新的关键词。

  4. 功能集成规划：
      - 在关键词展示区域的上方，添加一个工具栏，包含：
          * 领域选择器（下拉菜单）
          * 内置关键词组选择器（当选择领域后，显示该领域下的预设关键词组列表，用户可以选择一组）
          * 自定义关键词输入（一个输入框，用户可以输入多个关键词，用逗号分隔，然后添加到当前关键词列表）
      - 在关键词标签旁边添加删除按钮（小叉号），允许用户删除不想要的关键词。

  5. 状态管理：
      - 需要新增状态：
          * `selectedDomain`：当前选定的领域（字符串）
          * `builtinKeywordGroups`：一个对象，按领域存储内置的关键词组（从服务器获取或本地定义）
          * `selectedKeywordGroup`：当前选中的内置关键词组（一个关键词数组）
          * `customKeywords`：用户自定义输入的关键词（字符串，需要分割成数组）
      - 注意：当前的关键词列表（`keywords`）应该由三部分组成：
          * 从内置关键词组中选中的关键词（如果用户选择了一组）
          * 随机生成的关键词（如果用户点击了随机生成，且没有选择内置组）
          * 用户自定义添加的关键词

  6. 数据获取：
      - 内置关键词组可以存储在本地（一个 JSON 文件），也可以从服务器获取。考虑到项目结构，建议先存储在本地，例如在`src/constants/keywords.js`中定义。
      - 定义多个领域的关键词组，例如：
          const builtinKeywords = {
            technology: ['人工智能', '区块链', '云计算', '大数据', '物联网'],
            education: ['老师', '学生', '课堂', '考试', '学习'],
            life: ['家庭', '旅行', '美食', '健康', '运动'],
            // ...其他领域
          };

  7. 用户操作流程：
      - 用户进入页面，默认显示一个领域选择器（默认选中“通用”领域）。
      - 用户可以选择领域，然后可以选择该领域下的内置关键词组（通过一个选择器选择，选择后关键词列表更新为选中的关键词组）。
      - 用户也可以点击“随机生成”按钮，此时会根据选定的领域从该领域的关键词中随机选取（如果选定了领域，则从该领域的关键词中随机；如果未选定或通用，则从所有关键词中随机）。
      - 用户还可以在自定义输入框中输入关键词（多个用逗号分隔），然后点击添加按钮，将这些关键词添加到当前关键词列表。
      - 每个关键词标签旁边有一个删除按钮，可以删除该关键词。

  8. 界面设计：
      - 在关键词展示区域上方，添加一行工具栏，包含：
          * 领域选择：使用`<select>`或`<uni-data-select>`（如果使用 uni-app 组件）
          * 内置关键词组选择：当领域选择后，显示该领域下的关键词组选择器（可以是下拉菜单或按钮列表）
          * 自定义输入框和添加按钮
      - 在关键词标签上添加删除图标（小叉号），点击可删除。

  9. 代码修改点：
      - 在`<template>`部分，关键词展示区域上方添加工具栏。
      - 在`<script>`部分，新增状态变量（如`selectedDomain`, `builtinKeywordGroups`, `selectedKeywordGroup`, `customInput`等）。
      - 修改`generateKeywords`函数，使其根据选定的领域生成关键词。
      - 新增函数：`selectDomain`（处理领域选择）、`selectBuiltinGroup`（处理内置关键词组选择）、`addCustomKeywords`（处理添加自定义关键词）、`removeKeyword`（处理删除关键词）。

  10. 注意事项：
      - 当前页面已经比较复杂，添加功能时要注意保持代码可维护性。
      - 考虑移动端屏幕空间，工具栏可能需要折叠或者使用更紧凑的布局。

 由于用户要求打开思维，我们还可以考虑以下增强功能：
   - 保存用户自定义的关键词组，以便下次使用。
   - 分享关键词组功能。
   - 关键词难度分级（初级、中级、高级），根据难度生成不同深度的故事。

 但根据需求，我们先实现基本功能：领域选择、内置关键词组选择、自定义关键词输入和删除。

 接下来，我们规划具体的代码修改方案。

 由于用户要求的是思路和规划，我们不需要立即编写代码，但可以给出大致的代码结构示例。

 但是，请注意：用户要求我们使用`@pm-agent.mdc`来添加需求，因此我们需要按照需求文档管理规范，在`.docs`目录下创建或修改需求文档。

 然而，用户当前并没有要求创建需求文档，而是要求我们思考功能。因此，我们可以先提供思路，然后询问用户是否需要我们创建需求文档。

 但是，根据用户指令，我们被要求“帮我想想可以添加什么功能？怎么集成，怎么规划，思路是什么？”，所以我们先回答思路。

 总结回答：
  1. 功能扩展点：
      - 领域选择：让用户可以选择关键词的领域。
      - 内置关键词组：为每个领域提供多个预设的关键词组，用户可以选择一组。
      - 自定义关键词：允许用户输入自己的关键词（多个用逗号分隔），并添加到当前关键词列表。
      - 关键词管理：允许用户删除单个关键词。

  2. 集成方式：
      - 在关键词展示区域上方添加工具栏，包含领域选择器、内置关键词组选择器（在领域选择后出现）、自定义输入框和添加按钮。
      - 每个关键词标签上添加删除按钮。

  3. 规划思路：
      - 定义内置关键词组（按领域分类）。
      - 新增状态变量来管理领域、选中的内置组、自定义输入等。
      - 修改随机生成函数，使其支持按领域生成。
      - 添加事件处理函数：领域选择、内置组选择、添加自定义关键词、删除关键词。

  4. 界面布局调整（简要）：
      - 工具栏布局（使用 flex 布局）：
          <view class="keywords-toolbar">
            <view class="toolbar-section">
              <label>领域：</label>
              <select v-model="selectedDomain" @change="handleDomainChange">
                <option value="general">通用</option>
                <option value="technology">科技</option>
                <option value="education">教育</option>
                ...
              </select>
            </view>
            <view class="toolbar-section" v-if="selectedDomain !== 'general'">
              <label>内置词组：</label>
              <select v-model="selectedGroupId" @change="handleBuiltinGroupChange">
                <option v-for="group in domainGroups" :key="group.id" :value="group.id">{{ group.name }}</option>
              </select>
            </view>
            <view class="toolbar-section">
              <input v-model="customInput" placeholder="输入关键词，逗号分隔" />
              <button @click="addCustomKeywords">添加</button>
            </view>
          </view>

      - 关键词标签添加删除按钮：
          <view class="floating-keyword-tag" ...>
            {{ keyword }}
            <i class="fas fa-times remove-keyword" @click.stop="removeKeyword(index)"></i>
          </view>

  5. 状态管理：
      - 当前关键词列表`keywords`将变为一个计算属性，由三部分组成：内置组选中的关键词、随机生成的关键词、自定义添加的关键词。或者我们用一个数组来维护，通过不同的操作来修改这个数组。

 注意：由于当前页面已经有一个`keywords`数组（ref），我们可以直接修改这个数组来添加或删除关键词。

 具体操作：
   - 选择内置组：将`keywords`设置为选中的内置组的关键词数组。
   - 随机生成：将`keywords`设置为根据选定领域随机生成的关键词数组（覆盖当前）。
   - 添加自定义：将用户输入的关键词（分割成数组）添加到`keywords`数组的末尾。
   - 删除：根据索引从`keywords`数组中删除一个关键词。

 这样，`keywords`数组就是当前显示的所有关键词。

 6. 重置功能：
     当前的`handleReset`函数需要扩展，将新增的状态变量也重置。

 7. 保存记录：
     在保存训练记录时，我们还需要保存领域和内置组 ID（如果有的话），以便恢复。

 由于这是一个思路规划，我们不需要写完整代码，但我们可以提供伪代码或示例代码片段。

 最后，我们可以询问用户是否希望我们根据这个思路创建需求文档，或者直接进行开发。

 但是，根据工作区规则，我们不允许修改`.docs`文件夹之外的文件，除非用户明确要求。因此，我们只提供思路，并询问下一步。

 然而，用户当前并没有要求创建需求文档，所以我们先回答思路。

 根据用户规则，我们始终用中文回答。

 回答如下：
</think>
基于您的需求，我将从功能扩展、集成方案和规划思路三个方面进行分析，帮助您为关键词讲故事页面添加自定义功能：

### 一、可添加的功能扩展
1. **关键词来源多样化**
   - **内置词库**：按领域（科技/教育/生活等）分类的预设关键词库
   - **自定义输入**：允许用户手动输入关键词
   - **智能推荐**：基于用户历史记录推荐相关领域关键词
   - **词库管理**：收藏/保存常用关键词组合

2. **领域定制化**
   - **领域选择器**：添加领域选择下拉菜单（科技/教育/商业等）
   - **领域专属词库**：不同领域使用不同的关键词生成算法
   - **领域适配评价**：根据领域调整 AI 评价标准（如科技类侧重逻辑性）

3. **故事生成增强**
   - **故事长度控制**：添加滑块控制生成故事的长度
   - **故事风格选择**：幽默/正式/简洁等不同叙事风格
   - **多语言支持**：扩展英语等外语关键词故事功能

### 二、集成方案
```vue:src/pages/speak/keyword-story-page.vue
// ... existing code ...
<script setup>
// 新增状态
const domains = ref(['科技', '教育', '生活', '商业', '娱乐']) // 可用领域
const selectedDomain = ref('通用') // 当前选中领域
const customKeywords = ref('') // 自定义关键词输入
const storyLength = ref(3) // 故事长度控制（1-5 级）

// 领域感知的关键词生成
const generateKeywords = async () => {
  if (selectedDomain.value !== '通用') {
    // 调用领域特定 API
    keywords.value = await getDomainKeywords(selectedDomain.value, 3)
  } else {
    keywords.value = getRandomKeywords(3)
  }
}

// 添加自定义关键词
const addCustomKeywords = () => {
  if (customKeywords.value) {
    const newKeywords = customKeywords.value.split(/[,，]/)
    keywords.value = [...keywords.value, ...newKeywords]
    customKeywords.value = ''
  }
}
</script>

<template>
  <!-- 在关键词容器上方添加控制区 -->
  <view class="control-container">
    <!-- 领域选择器 -->
    <view class="domain-selector">
      <text>领域：</text>
      <picker :range="domains" @change="(e) => selectedDomain = domains[e.detail.value]">
        <view class="picker">{{ selectedDomain }} <i class="fas fa-caret-down"></i></view>
      </picker>
    </view>
    
    <!-- 自定义关键词输入 -->
    <view class="custom-keywords">
      <input 
        v-model="customKeywords" 
        placeholder="输入自定义关键词，用逗号分隔"
        @confirm="addCustomKeywords"
      />
      <button @click="addCustomKeywords">添加</button>
    </view>
  </view>
  
  <!-- 现有关键词容器 -->
  <view class="keywords-container">
    // ... existing code ...
  </view>
</template>

<style scoped>
/* 新增控制区样式 */
.control-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.domain-selector {
  display: flex;
  align-items: center;
}

.custom-keywords {
  display: flex;
  gap: 8px;
}

.custom-keywords input {
  flex: 1;
  border: 1px solid #eee;
  padding: 8px 12px;
  border-radius: 8px;
}
</style>
```

### 三、实现思路与规划

1. **分层架构设计**
   ```mermaid
   graph TD
     A[UI 层] --> B[业务逻辑层]
     B --> C[数据服务层]
     C --> D[领域词库系统]
   ```

2. **分阶段实施计划**
   | 阶段 | 功能 | 预计耗时 |
   |------|------|----------|
   | 1    | 领域选择器 + 基础词库 | 2 天 |
   | 2    | 自定义关键词管理 | 1 天 |
   | 3    | 领域适配评价体系 | 3 天 |
   | 4    | 用户词库收藏功能 | 2 天 |

3. **关键技术点**
   - **词库服务**：创建`keywordService.js`处理领域词库逻辑
   - **本地存储**：使用 uni-app 的 Storage API 保存用户自定义词库
   - **智能推荐**：基于用户历史记录实现协同过滤推荐
   - **领域适配**：为不同领域创建专属的评价 prompt 模板

4. **风险评估**
   - **词库维护**：建立词库更新机制防止内容过时
   - **性能优化**：大数据量词库采用懒加载策略
   - **用户体验**：添加引导教程帮助用户理解新功能

### 四、扩展建议
1. **社交功能**：允许用户分享自定义词库/故事
2. **难度分级**：为关键词添加难度标签（初级/中级/高级）
3. **成就系统**：完成特定领域故事获得成就徽章
4. **AI 辅助创作**：添加"帮我续写"功能拓展故事内容

需要我进一步细化某个功能点的实现方案吗？或者我们可以选择优先实现某个核心功能模块。
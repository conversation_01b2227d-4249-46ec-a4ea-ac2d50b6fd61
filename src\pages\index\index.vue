<template>
  <div class="container">
    <!-- 内容区域 -->
    <div
      class="content-area"
      :animation="animationData"
      :style="{ transform: `scale(${contentScale})`, opacity: contentOpacity }"
      :class="{ 'scale-transition': useCssTransition }"
    >
      <keep-alive :include="cacheList">
        <component :is="currentComponent" v-if="currentComponent" :key="currentTabIndex" />
        <div v-else class="loading">
          <div class="loading-indicator"></div>
          <span>加载中...</span>
        </div>
      </keep-alive>
    </div>

    <!-- TabBar 区域 -->
    <div class="tabbar-container">
      <tabBar v-model="currentTabIndex" :tabMapping="tabMapping" @change="handleTabChange" :isSyncing="isSyncing" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, provide, watch, onBeforeUnmount } from 'vue'
import tabBar from './component/tabBar.vue'
import { router, getPlatform } from '@/utils/tools'
import useReload from '@/hooks/useReload'

// 添加统一的日志函数
const logInfo = (message, data) => {
  console.log(`[INDEX] ${message}`, data || '')
}

const logError = (message, error) => {
  console.error(`[INDEX-ERROR] ${message}`, error)
}

// 动画相关变量
const animationData = ref({})
let animation = null
// CSS 动画控制变量
const contentOpacity = ref(1)
const contentScale = ref(1) // 修改为 scale 控制
const useCssTransition = ref(true)

// 创建动画实例
const createAnimationInstance = () => {
  try {
    return uni.createAnimation({
      duration: 300,
      timingFunction: 'ease-out',
      delay: 0,
    })
  } catch (error) {
    logError('创建动画实例失败', error)
    return null
  }
}

// 初始化动画实例
const initAnimation = () => {
  animation = createAnimationInstance()
}

// 执行淡出动画 (缩小)
const slideOut = () => {
  // 适中的缩放效果
  const scaleValue = 0.98 // 缩小到 98% 大小，效果适中

  // CSS 过渡动画
  contentOpacity.value = 0.8 // 适中的透明度变化
  contentScale.value = scaleValue

  // API 动画
  if (!animation) return Promise.resolve()

  return new Promise((resolve) => {
    animation.opacity(0.8).scale(scaleValue).step()
    animationData.value = animation.export()
    setTimeout(resolve, 175) // 适中的过渡时间
  })
}

// 执行淡入动画 (放大)
const slideIn = () => {
  // 从小到大放大，适中的初始值
  const initialScale = 0.98 // 从 98% 大小开始，效果适中

  // 先将元素设置为缩小的尺寸
  contentScale.value = initialScale

  // 执行放大动画，适中的延迟
  setTimeout(() => {
    contentOpacity.value = 1
    contentScale.value = 1 // 恢复到正常大小

    if (animation) {
      animation.opacity(1).scale(1).step()
      animationData.value = animation.export()
    }
  }, 12) // 适中的延迟
}

// 同步状态标志
const isSyncing = ref(false)

// 在开始阶段直接导入所有页面组件，避免异步加载的问题
import OkrList from '@/pages/okr/okrList.vue'
import Today from '@/pages/okr/today.vue'
import SpeakIndex from '@/pages/speak/index.vue'
import Diary from '@/pages/memo/diary.vue'
import Setting from '@/pages/setting/setting.vue'

// 后备组件作为普通对象定义
const FallbackComponent = {
  name: 'FallbackComponent',
  template: `
    <div class="fallback-component">
      <div class="fallback-message">内容暂时无法加载</div>
      <button class="fallback-button" @click="reload">重试</button>
    </div>
  `,
  setup() {
    const reload = () => {
      logInfo('执行页面重载')
      try {
        // 尝试使用不同的方法重新加载页面
        if (typeof location !== 'undefined' && location.reload) {
          location.reload()
        } else if (typeof uni !== 'undefined') {
          const pages = getCurrentPages()
          const route = pages[pages.length - 1]?.route || 'pages/index/index'
          uni.reLaunch({ url: `/${route}` })
        }
      } catch (error) {
        logError('重载失败', error)
      }
    }
    return { reload }
  },
}

// TabBar 数据和组件映射
const tabMapping = [
  {
    id: 0,
    key: 'goals',
    path: 'pages/okr/okrList',
    component: OkrList,
    title: '目标',
    name: 'OkrList',
    icon: 'fas fa-bullseye',
  },
  {
    id: 1,
    key: 'speak',
    path: 'pages/speak/index',
    component: SpeakIndex,
    title: '表达',
    name: 'SpeakIndex',
    icon: 'fas fa-comment-dots',
  },
  {
    id: 2,
    key: 'today',
    path: 'pages/okr/today',
    component: Today,
    title: '今天',
    name: 'Today',
    icon: 'fas fa-tasks',
  },
  {
    id: 3,
    key: 'diary',
    path: 'pages/memo/diary',
    component: Diary,
    title: '日记',
    name: 'Diary',
    icon: 'fas fa-book',
  },
  {
    id: 4,
    key: 'setting',
    path: 'pages/setting/setting',
    component: Setting,
    title: '设置',
    name: 'Setting',
    icon: 'fas fa-cog',
  },
]

// 缓存组件名称列表
const cacheList = ref(['OkrList', 'Today', 'SpeakIndex'])

// 最大缓存数量
const MAX_CACHE_SIZE = 3

// 当前选中的 tab 索引
const currentTabIndex = ref(1)

// 提供一个刷新触发器
const reloadTrigger = ref({
  count: 0,
  tabKey: computed(() => tabMapping[currentTabIndex.value]?.key),
})
provide('reloadTrigger', reloadTrigger)

// 上一个 tab 索引
const previousTabIndex = ref(0)

// 检测当前运行环境
let platform, isIOS, isAndroid, isAppPlus
// 获取平台信息
const { isWeb } = getPlatform()

// 安全执行方法，包含错误处理
const safeExecute = (fn, fallback = null) => {
  try {
    return fn()
  } catch (error) {
    logError('操作执行失败', error)
    return fallback
  }
}

// 计算当前应显示的组件
const currentComponent = computed(() => {
  return safeExecute(() => {
    const tab = tabMapping[currentTabIndex.value]
    return tab ? tab.component : null
  }, FallbackComponent)
})

// 处理 tab 变更事件
const handleTabChange = async (index, path) => {
  logInfo('handleTabChange 被调用', { index, currentIndex: currentTabIndex.value })

  if (index !== currentTabIndex.value) {
    logInfo('Tab 需要切换')

    try {
      // 执行淡出动画 (不再需要方向参数)
      await slideOut()

      // 保存上一个索引
      previousTabIndex.value = currentTabIndex.value

      // 更新当前 tab
      currentTabIndex.value = index
      logInfo('当前 tab 已更新', currentTabIndex.value)

      // 移除缓存和频率相关逻辑
      // updateCacheList(index)
      // updateSwitchFrequency(index)

      // 更新页面标题
      logInfo('设置页面标题', tabMapping[index].title)
      uni.setNavigationBarTitle({ title: tabMapping[index].title })

      // 更新 URL 参数，不刷新页面
      updateUrlParams(index)

      // 移除滚动位置恢复
      // restoreScrollPosition(index)

      // 执行淡入动画
      slideIn()

      logInfo('Tab 切换完成')
    } catch (error) {
      logError('Tab 切换过程中出错', error)
      // 如果发生错误，确保内容仍然可见
      contentOpacity.value = 1
      contentScale.value = 1
      if (animation) {
        animation.opacity(1).scale(1).step()
        animationData.value = animation.export()
      }
    }
  } else {
    logInfo('Tab 无需切换（相同索引）')
  }
}

// 更新 URL 参数
const updateUrlParams = (tabIndex) => {
  safeExecute(() => {
    // 只在 H5 环境中执行
    if (isWeb && typeof window !== 'undefined' && window.history) {
      // 获取当前 tab 的 key
      const tabKey = tabMapping[tabIndex].key

      // 获取基础 URL 和 hash 部分
      const baseUrl = window.location.href.split('#')[0]
      const hashPath = window.location.hash.split('?')[0] || '#/'

      // 构建新的 URL 格式：baseUrl#/path?tab=key
      const newUrl = `${baseUrl}${hashPath}?tab=${tabKey}`

      // 替换当前历史记录，不增加新记录
      window.history.replaceState(
        {
          tabIndex,
          tabKey,
          isTabSwitch: true, // 标记这是一个 Tab 切换操作
        },
        tabMapping[tabIndex].title,
        newUrl
      )

      logInfo('更新 URL 参数', { tabIndex, tabKey, newUrl })
    }
  })
}

// 从 URL 参数解析 tab 值
const parseUrlParams = () => {
  safeExecute(() => {
    // H5 环境
    if (isWeb && typeof window !== 'undefined') {
      // 处理基于 hash 路由的情况
      let tabKey = null

      // 兼容不同的 URL 格式
      // 1. 尝试从 hash 部分获取参数
      if (window.location.hash && window.location.hash.includes('?')) {
        const hashQuery = window.location.hash.split('?')[1]
        const params = new URLSearchParams(hashQuery)
        tabKey = params.get('tab')
      }

      // 2. 如果 hash 中没找到，尝试从普通 search 参数获取
      if (!tabKey) {
        tabKey = new URLSearchParams(window.location.search).get('tab')
      }

      console.log('parseUrlParams: 检测到 URL 参数 tab=', tabKey)

      if (tabKey) {
        // 查找对应 key 的 tab 索引
        const foundIndex = tabMapping.findIndex((tab) => tab.key === tabKey)
        if (foundIndex !== -1) {
          console.log('parseUrlParams: 找到对应的 tab 索引', foundIndex)
          currentTabIndex.value = foundIndex
          // 移除缓存相关
          // updateCacheList(foundIndex)

          // 确保设置正确的页面标题
          uni.setNavigationBarTitle({ title: tabMapping[foundIndex].title })
        } else {
          console.warn('parseUrlParams: 未找到匹配的 tab key', tabKey)
        }
      } else {
        console.log('parseUrlParams: URL 中没有 tab 参数')
      }
    }
    // 非 H5 环境
    else {
      const pages = getCurrentPages()
      const curPage = pages[pages.length - 1]
      const options = curPage?.options || {}

      if (options.tab) {
        console.log('parseUrlParams: 小程序环境检测到 tab 参数', options.tab)
        // 查找对应 key 的 tab 索引
        const foundIndex = tabMapping.findIndex((tab) => tab.key === options.tab)
        if (foundIndex !== -1) {
          currentTabIndex.value = foundIndex
          // 移除缓存相关
          // updateCacheList(foundIndex)
        }
      }
    }

    // 设置页面标题
    uni.setNavigationBarTitle({ title: tabMapping[currentTabIndex.value].title })
  })
}

// 监听 URL 变化
const setupUrlListener = () => {
  if (isWeb && typeof window !== 'undefined') {
    // 在 popstate 事件中处理导航
    window.addEventListener('popstate', (event) => {
      console.log('检测到 popstate 事件，重新解析 URL 参数', event.state)

      // 获取历史状态
      const state = event.state || {}

      // 如果历史记录包含 tab 信息，直接使用
      if (state.tabIndex !== undefined) {
        logInfo('从历史状态恢复 tab', state)
        currentTabIndex.value = state.tabIndex

        // 设置正确的页面标题
        uni.setNavigationBarTitle({ title: tabMapping[state.tabIndex].title })

        // 移除缓存相关
        // updateCacheList(state.tabIndex)
      } else {
        // 否则从 URL 参数解析
        parseUrlParams()
      }
    })

    // 在页面加载时解析 URL 参数
    parseUrlParams()
  }
}

// 监听 tab 变化，更新缓存
// watch(currentTabIndex, (newVal) => {
//   updateCacheList(newVal)
// })
const uniPlatform = ref('web')
// 兼容性处理
const setupPlatformCompatibility = () => {
  logInfo('设置平台兼容性')
  safeExecute(() => {
    let systemInfo = { platform: 'unknown' }
    try {
      systemInfo = uni.getSystemInfoSync()
      logInfo('获取系统信息成功', systemInfo)
    } catch (error) {
      logError('获取系统信息失败', error)
    }

    platform = systemInfo.platform
    uniPlatform.value = systemInfo.uniPlatform
    isIOS = platform === 'ios'
    isAndroid = platform === 'android'
    isAppPlus = !!systemInfo.uniPlatform

    logInfo('当前运行环境', platform, { platform, isIOS, isAndroid, isWeb, isAppPlus })

    // 只在 H5 环境中执行 DOM 操作
    if (isWeb) {
      logInfo('在 H5 环境设置 DOM 相关操作')
      safeExecute(() => {
        if (isIOS && document && document.documentElement) {
          // iOS 特定样式调整
          document.documentElement.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)')
        } else if (isAndroid && document && document.documentElement) {
          // Android 特定样式调整
          document.documentElement.style.setProperty('--safe-area-inset-bottom', '0px')
        }

        // 添加浏览器历史状态管理
        if (window && window.addEventListener) {
          window.addEventListener('popstate', parseUrlParams)
        }
      })
    } else {
      logInfo('非 H5 环境，跳过 DOM 操作')
    }
  })
}

// 清理资源
onBeforeUnmount(() => {
  safeExecute(() => {
    // 清理资源
    if (isWeb && typeof window !== 'undefined' && window.removeEventListener) {
      window.removeEventListener('popstate', parseUrlParams)
    }

    // 移除缓存数据清理
    // scrollPositions.value = {}

    // 取消同步状态监听
    uni.$off('syncStatus')

    // 取消页面显示事件监听
    uni.$off('page-show')

    // 取消获取活跃 tab 信息的监听
    uni.$off('get-active-tab')
  })
})

// 在组件挂载时解析 URL 参数并设置平台兼容性
onMounted(() => {
  logInfo('组件挂载')
  setupPlatformCompatibility()
  setupUrlListener() // 替换原来的 parseUrlParams() 调用

  // 初始化动画实例
  initAnimation()

  // 设置初始不透明度和缩放比例
  if (animation) {
    animation.opacity(1).scale(1).step()
    animationData.value = animation.export()
  }

  // 确保初始时内容可见
  contentOpacity.value = 1
  contentScale.value = 1

  // 监听同步状态
  uni.$on('syncStatus', ({ isSyncing: status }) => {
    logInfo('同步状态变更', status)
    isSyncing.value = status
  })

  // 监听页面显示事件，处理从内部页面返回的情况
  uni.$on('page-show', () => {
    logInfo('页面显示事件触发，可能从内部页面返回')
    // 解析当前 URL 参数，确保显示正确的 tab
    parseUrlParams()
  })

  // 为内部页面提供当前激活的 tab 信息
  uni.$on('get-active-tab', (callback) => {
    if (typeof callback === 'function') {
      callback({
        tabIndex: currentTabIndex.value,
        tabKey: tabMapping[currentTabIndex.value]?.key,
      })
    }
  })
})

useReload(() => {
  console.log('useReload-----index, 触发当前 tab 更新')
  reloadTrigger.value.count++
})
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  overflow: hidden; /* 防止内容溢出 */
}

.content-area {
  flex: 1;
  overflow-y: auto;
  position: relative; /* 作为过渡动画的容器 */
  will-change: transform, opacity; /* 启用硬件加速 */
  -webkit-overflow-scrolling: touch; /* 提升 iOS 滚动体验 */
  transform-origin: center center; /* 确保从中心点缩放 */
}

/* CSS 过渡效果 */
.scale-transition {
  transition: transform 250ms ease-out, opacity 250ms ease-out;
}

/* 让内容组件占满内容区域 */
.content-area > div {
  height: 100%;
  width: 100%;
}

.loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
  font-size: 14px;
}

.loading-indicator {
  width: 24px;
  height: 24px;
  border: 2px solid #eee;
  border-top-color: #64b6f7;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.tabbar-container {
  height: 50px;
  background-color: #fff;
  padding-bottom: var(--safe-area-inset-bottom, env(safe-area-inset-bottom)); /* 适配底部安全区 */
  will-change: transform; /* 启用硬件加速 */
  z-index: 100;
}

/* 后备组件样式 */
.fallback-component {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.fallback-message {
  font-size: 16px;
  color: #666;
  margin-bottom: 15px;
}

.fallback-button {
  background-color: #64b6f7;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
}

/* 异步组件状态样式 */
.async-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #666;
  font-size: 14px;
}

.async-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #ff4d4f;
  font-size: 14px;
}
</style>

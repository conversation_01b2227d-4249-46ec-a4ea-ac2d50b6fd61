<template>
  <editor
    :id="editorId"
    :class="editorClass"
    :read-only="readOnly"
    :placeholder="placeholder"
    :show-img-size="showImgSize"
    :show-img-toolbar="showImgToolbar"
    :show-img-resize="showImgResize"
    @ready="handleReady"
    @focus="emitFocus"
    @blur="emitBlur"
    @input="handleInput"
    @statuschange="emitStatusChange"
  ></editor>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  modelValue: String,
  editorId: {
    type: String,
    default: 'editor',
  },
  editorClass: {
    type: String,
    default: 'ql-container p-[10px] rounded-lg w-full bg-white mb-4',
  },
  readOnly: Boolean,
  placeholder: String,
  showImgSize: Boolean,
  showImgToolbar: Boolean,
  showImgResize: Boolean,
})

const emit = defineEmits(['update:modelValue', 'ready', 'focus', 'blur', 'statuschange'])

const editorCtx = ref(null)

const handleReady = () => {
  uni
    .createSelectorQuery()
    .select(`#${props.editorId}`)
    .context((res) => {
      editorCtx.value = res.context
      emit('ready')
    })
    .exec()
}

const handleInput = (e) => {
  emit('update:modelValue', e.detail.text)
}

const emitFocus = (e) => {
  emit('focus', e.detail)
}

const emitBlur = (e) => {
  emit('blur', e.detail)
}

const emitStatusChange = (e) => {
  emit('statuschange', e.detail)
}

defineExpose({
  editorCtx,
})
</script>

<template>
  <div class="db-sync-container">
    <z-page-navbar title="数据同步" />

    <!-- 数据同步状态卡片 -->
    <div class="sync-status-card">
      <div class="sync-status-header">
        <div class="sync-title">数据同步状态</div>
        <div class="view-database-btn" @click="goToDatabaseView"><i class="fas fa-database"></i> 查看数据库表</div>
      </div>

      <div class="sync-time"><i class="fas fa-history"></i> 上次同步：{{ lastSyncTime }}</div>

      <div class="sync-status-body">
        <div class="sync-count-box">
          <div class="sync-count">{{ totalDirtyCount }}</div>
          <div class="sync-label">未同步数据（条）</div>
        </div>
        <div class="sync-button">
          <button class="cloud-sync-btn" @click="sync" :disabled="loading || !hasUnsyncedData">
            <i class="fas fa-cloud-upload-alt"></i> 同步到云端
            <div class="loading-spinner" v-if="loading"></div>
          </button>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="chart-card" v-if="hasUnsyncedData && !loading">
      <div class="card-title">数据分布</div>
      <div class="chart-container">
        <qiun-data-charts type="ring" :opts="chartOpts" :chartData="chartData" />
      </div>

      <!-- 表格明细 -->
      <div class="data-detail">
        <div class="detail-header" @click="toggleTableExpand">
          <span>未同步数据明细</span>
          <i :class="isTableExpanded ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
        </div>

        <div class="table-container" v-if="isTableExpanded">
          <div class="table-header">
            <div class="th-table">数据表</div>
            <div class="th-count">数据量</div>
            <div class="th-time">最后更新</div>
          </div>
          <div class="table-body">
            <div v-for="(item, index) in tableData" :key="index" class="table-row">
              <div class="td-table">{{ item.tableName }}</div>
              <div class="td-count">{{ item.count }}条</div>
              <div class="td-time">{{ item.lastUpdate }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="!hasUnsyncedData && !loading">
      <div class="success-icon">
        <i class="fas fa-check-circle"></i>
      </div>
      <div class="empty-text">所有数据已同步</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import db from '@/api/database'
import { tableSchema } from '@/api/dataSchema'
import { syncToServer } from '@/api/syncServer'
import { toolGetNetworkType, router } from '@/utils/tools'
import dayjs from 'dayjs'
import { ref, computed, onMounted } from 'vue'

// 数据状态
const dataByTable = ref<Record<string, any[]>>({})
const lastSyncTime = ref('')
const loading = ref(false)
const isTableExpanded = ref(false)

// 跳转到数据库表查看页面
const goToDatabaseView = () => {
  router.push('/pages/setting/db')
}

// 同步按钮点击事件
const sync = async () => {
  loading.value = true
  try {
    await syncToServer()
    uni.showToast({
      title: '同步成功',
      icon: 'success',
    })
  } catch (error) {
    uni.showToast({
      title: '同步失败',
      icon: 'error',
    })
    console.error('同步失败：', error)
  } finally {
    loading.value = false
    init()
  }
}

// 初始化数据
const init = async () => {
  const syncTime = uni.getStorageSync('lastSyncTime')
  lastSyncTime.value = syncTime ? dayjs(syncTime).format('YYYY-MM-DD HH:mm:ss') : '从未同步'
  dataByTable.value = {}

  // 获取每个表的未同步数据
  for (const tableName of Object.keys(tableSchema)) {
    try {
      const res = await db.table(tableName).where().toArray()
      const dirtyItems = res.filter((item: any) => item.isDirty === 1)
      if (dirtyItems.length > 0) {
        dataByTable.value[tableName] = dirtyItems
      }
    } catch (error) {
      console.error(`获取${tableName}表数据失败:`, error)
    }
  }
}

// 切换表格展开/收起状态
const toggleTableExpand = () => {
  isTableExpanded.value = !isTableExpanded.value
}

// 计算属性：是否有未同步数据
const hasUnsyncedData = computed(() => {
  return Object.keys(dataByTable.value).length > 0
})

// 计算属性：未同步数据总条数
const totalDirtyCount = computed(() => {
  let count = 0
  Object.values(dataByTable.value).forEach((items) => {
    count += items.length
  })
  return count
})

// 计算属性：图表数据
const chartData = computed(() => {
  const series = [
    {
      data: Object.entries(dataByTable.value).map(([tableName, items]) => {
        return {
          name: tableName,
          value: items.length,
        }
      }),
    },
  ]
  return { series }
})

// 计算属性：表格数据
const tableData = computed(() => {
  return Object.entries(dataByTable.value).map(([tableName, items]) => {
    // 获取该表中最后更新的一条数据的时间
    let lastUpdateTime = ''
    if (items.length > 0) {
      const latestItem = items.reduce((latest, current) => {
        const latestTime = latest.updateTime ? new Date(latest.updateTime).getTime() : 0
        const currentTime = current.updateTime ? new Date(current.updateTime).getTime() : 0
        return currentTime > latestTime ? current : latest
      }, items[0])
      lastUpdateTime = latestItem.updateTime ? dayjs(latestItem.updateTime).format('MM-DD HH:mm') : ''
    }

    return {
      tableName,
      count: items.length,
      lastUpdate: lastUpdateTime,
    }
  })
})

// 图表配置
const chartOpts = {
  color: ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452'],
  padding: [5, 5, 5, 5],
  dataLabel: true,
  enableScroll: false,
  legend: {
    show: true,
    position: 'bottom',
    lineHeight: 25,
  },
  extra: {
    ring: {
      ringWidth: 30,
      activeOpacity: 0.5,
      activeRadius: 10,
      offsetAngle: 0,
      labelWidth: 15,
      border: false,
      borderWidth: 3,
      borderColor: '#FFFFFF',
    },
  },
}

// 生命周期钩子
onMounted(init)
</script>

<style scoped lang="scss">
.db-sync-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20rpx;
}

.sync-status-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 30rpx 0;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.sync-status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;

  .sync-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .view-database-btn {
    font-size: 24rpx;
    color: #5cadff;
    cursor: pointer;
    display: flex;
    align-items: center;

    i {
      margin-right: 6rpx;
      font-size: 26rpx;
    }

    &:hover {
      opacity: 0.8;
    }
  }
}

.sync-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;

  i {
    margin-right: 6rpx;
  }
}

.sync-status-body {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sync-count-box {
  .sync-count {
    font-size: 64rpx;
    font-weight: 600;
    color: #333;
    line-height: 1.2;
  }

  .sync-label {
    font-size: 24rpx;
    color: #999;
    margin-top: 8rpx;
  }
}

.cloud-sync-btn {
  background-color: #5cadff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  position: relative;

  i {
    margin-right: 10rpx;
    font-size: 32rpx;
  }

  &:disabled {
    background-color: #c8c9cc;
    opacity: 0.7;
  }

  .loading-spinner {
    width: 30rpx;
    height: 30rpx;
    border: 4rpx solid rgba(255, 255, 255, 0.3);
    border-top-color: white;
    border-radius: 50%;
    margin-left: 16rpx;
    animation: spin 1s infinite linear;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.chart-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.card-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.chart-container {
  height: 500rpx;
}

.data-detail {
  margin-top: 30rpx;
  border-top: 1px solid #eee;
  padding-top: 20rpx;

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 28rpx;
    color: #333;
    padding: 10rpx 0;
    font-weight: 500;
    cursor: pointer;
  }
}

.table-container {
  margin-top: 16rpx;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999;
  border-bottom: 1px solid #eee;
}

.table-body {
  .table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    padding: 20rpx 0;
    font-size: 26rpx;
    color: #333;
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
    }
  }
}

.empty-state {
  background-color: white;
  border-radius: 16rpx;
  padding: 60rpx 30rpx;
  margin: 30rpx 0;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .success-icon {
    font-size: 80rpx;
    color: #52c41a;
    margin-bottom: 20rpx;
  }

  .empty-text {
    font-size: 30rpx;
    color: #333;
  }
}
</style>

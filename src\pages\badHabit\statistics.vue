<template>
  <div class="statistics">
    <!-- <z-text-swiper></z-text-swiper> -->
    <view class="m-3">
      <u-subsection :list="navs" v-model="currentNav"></u-subsection>
    </view>
    <view class="flex justify-around items-center rr bg-white m-3 p-3 text-25">
      <view class="text-center">
        <view class="text-gray">完成数</view>
        <view class="text-35 mt-1">2</view>
      </view>
      <view class="w-1 h-40 bg-gray"></view>
      <view class="text-center">
        <view class="text-gray">完成率</view>
        <view class="text-35 mt-1">100%</view>
      </view>
    </view>
    <view v-if="['week', 'month'].includes(navStatus)" class="bg-white rr bg-white m-3 p-3">
      <view>完成率趋势（%）</view>
      <view class="w-[100%]">
        <qiun-data-charts type="line" :opts="opts" :chartData="chartData" />
      </view>
    </view>
    <view class="bg-white rr bg-white m-3 p-3">
      <view>分类统计</view>
      <view class="w-[100%]">
        <qiun-data-charts type="ring" :opts="classOpts" :chartData="classChartData" />
      </view>
    </view>
  </div>
</template>

<script lang="ts" setup>
const { data: statsData, run: getTaskList } = useRequest(getTaskListApi, {
  async formatter(data) {
    data = data.filter((item) => item.endDate >= curDate.value || !item.endDate)
    const stats = {
      // 未完成
      uncompleted: data.filter((item) => !item.completed).length,
    }
    return data
  },
})
const currentNav = ref(0)
const navs = ref([
  {
    name: '今日',
  },
  {
    name: '本周',
  },
  {
    name: '本月',
  },
])
const navStatus = computed(() => {
  if (currentNav.value === 0) {
    return 'today'
  } else if (currentNav.value === 1) {
    return 'week'
  } else {
    return 'month'
  }
})

const opts = {
  color: ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'],
  padding: [15, 10, 0, 15],
  enableScroll: false,
  legend: {},
  xAxis: {
    disableGrid: true,
  },
  yAxis: {
    disableGrid: true,
    disabled: true,
    // gridType: 'dash',
    dashLength: 2,
    showTitle: false,
  },
  extra: {
    line: {
      type: 'curve',
      width: 2,
      activeType: 'hollow',
    },
  },
}
const chartData = ref({
  // autocorrect: false
  categories: ['1日', '2日', '3日', '4日', '5日', '6日', '今天'],
  series: [
    {
      name: '完成率',
      data: [100, 80, 95, 150, 112, 132, 0],
    },
  ],
})

const classOpts = {
  rotate: false,
  rotateLock: false,
  color: ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'],
  padding: [5, 5, 5, 5],
  dataLabel: false, // 是否在环上显示数据
  enableScroll: false,
  legend: {
    show: true, // 是否显示标签
    position: 'bottom',
    lineHeight: 25,
  },
  title: {
    name: '完成数量',
    fontSize: 10,
    color: '#666666',
  },
  subtitle: {
    name: '70',
    fontSize: 20,
    color: '#7cb5ec',
  },
  extra: {
    ring: {
      ringWidth: 30,
      activeOpacity: 0.5,
      activeRadius: 10,
      offsetAngle: 0,
      labelWidth: 15,
      border: false,
      borderWidth: 3,
      borderColor: '#FFFFFF',
    },
  },
}

const classChartData = ref({
  series: [
    {
      data: [
        { name: '一班1515151', value: 50 },
        { name: '二班一班1515151', value: 30 },
        { name: '三班', value: 20 },
        { name: '四班', value: 18 },
        { name: '五班', value: 8 },
      ],
    },
  ],
})

onMounted(async () => {
  // await getTaskList(`startDate <= '${curDate.value}' && type == 'todo'`)
})
</script>

<style scoped lang="scss"></style>

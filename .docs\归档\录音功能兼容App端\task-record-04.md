# 入口文件与条件编译

## 任务描述

重构`useRecord.ts`作为入口文件，通过 uniapp 条件编译技术根据不同的运行环境选择合适的录音实现，确保录音功能能够在 H5 和 App 环境下无缝切换。

## 所属功能模块

录音功能兼容 App 端

## 技术实现详情

### 1. 重构入口文件

修改`src/hooks/useRecord.ts`文件，使用条件编译整合 H5 和 App 两端的实现：

```typescript
import { ref } from 'vue'
import type { Ref } from 'vue'
import { UseRecordOptions, UseRecordReturn } from './types/record'

// #ifdef H5
import useRecordH5 from './useRecordH5'
// #endif

// #ifdef APP-PLUS
import useRecordApp from './useRecordApp'
// #endif

/**
 * 录音 Hook，自动根据运行环境选择合适的实现
 *
 * @param options 录音配置选项
 * @returns 录音控制对象
 */
export default function useRecord(options?: UseRecordOptions): UseRecordReturn {
  // #ifdef H5
  return useRecordH5(options)
  // #endif

  // #ifdef APP-PLUS
  return useRecordApp(options)
  // #endif
}
```

### 2. 添加兼容性检查工具

创建`src/hooks/utils/useRecordCompat.ts`文件，用于检查当前环境的录音功能兼容性：

```typescript
import { ref, computed } from 'vue'
import { getPlatformInfo, checkRecordSupport } from './platform'
import { RecordErrorType } from './recordErrors'

/**
 * 录音功能兼容性检查 Hook
 *
 * @returns 录音功能兼容性信息
 */
export default function useRecordCompat() {
  const platformInfo = getPlatformInfo()
  const isSupported = ref(checkRecordSupport())
  const errorReason = ref<RecordErrorType | null>(null)

  // 检查录音权限
  const checkPermission = async () => {
    try {
      if (platformInfo.isH5) {
        // H5 环境下检查权限
        if (navigator && navigator.permissions) {
          const result = await navigator.permissions.query({ name: 'microphone' as PermissionName })
          return result.state === 'granted' || result.state === 'prompt'
        }
        // 对于不支持 permissions API 的浏览器，只能在使用时检查
        return true
      } else if (platformInfo.isApp) {
        // App 环境下检查权限
        return new Promise<boolean>((resolve) => {
          uni.authorize({
            scope: 'scope.record',
            success: () => resolve(true),
            fail: () => resolve(false),
          })
        })
      }
      return false
    } catch (error) {
      console.error('检查录音权限失败：', error)
      return false
    }
  }

  // 请求录音权限
  const requestPermission = async () => {
    try {
      if (platformInfo.isH5) {
        // H5 环境下请求权限
        if (navigator && navigator.mediaDevices) {
          try {
            await navigator.mediaDevices.getUserMedia({ audio: true })
            return true
          } catch (err) {
            if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
              errorReason.value = RecordErrorType.PERMISSION_DENIED
            } else {
              errorReason.value = RecordErrorType.INITIALIZATION_FAILED
            }
            return false
          }
        }
        errorReason.value = RecordErrorType.NOT_SUPPORTED
        return false
      } else if (platformInfo.isApp) {
        // App 环境下请求权限
        return new Promise<boolean>((resolve) => {
          uni.authorize({
            scope: 'scope.record',
            success: () => {
              resolve(true)
            },
            fail: (err) => {
              errorReason.value = RecordErrorType.PERMISSION_DENIED
              resolve(false)
            },
          })
        })
      }
      errorReason.value = RecordErrorType.NOT_SUPPORTED
      return false
    } catch (error) {
      console.error('请求录音权限失败：', error)
      errorReason.value = RecordErrorType.UNKNOWN_ERROR
      return false
    }
  }

  // 打开设置页面
  const openSettings = () => {
    if (platformInfo.isApp) {
      uni.openSetting({
        success: (res) => {
          console.log('打开设置页面成功', res)
        },
        fail: (err) => {
          console.error('打开设置页面失败', err)
        },
      })
    } else {
      console.warn('当前平台不支持打开设置页面')
    }
  }

  // 获取兼容性信息
  const compatInfo = computed(() => ({
    isH5: platformInfo.isH5,
    isApp: platformInfo.isApp,
    isSupported: isSupported.value,
    platform: platformInfo.platform,
    errorReason: errorReason.value,
  }))

  return {
    isSupported,
    errorReason,
    compatInfo,
    checkPermission,
    requestPermission,
    openSettings,
  }
}
```

### 3. 实现录音工厂函数

创建`src/hooks/utils/recordFactory.ts`文件，使用工厂模式创建录音实例，用于更复杂的场景：

```typescript
import useRecord from '../useRecord'
import { UseRecordOptions, UseRecordReturn } from '../types/record'
import useRecordCompat from './useRecordCompat'
import { ref } from 'vue'
import { RecordError } from './recordErrors'

/**
 * 创建录音实例配置选项
 */
export interface CreateRecordOptions extends UseRecordOptions {
  /** 自动请求权限 */
  autoRequestPermission?: boolean
  /** 自动检查兼容性 */
  checkCompat?: boolean
}

/**
 * 录音工厂函数，创建录音实例
 *
 * @param options 配置选项
 * @returns 录音实例创建结果
 */
export async function createRecord(options?: CreateRecordOptions) {
  const { autoRequestPermission = true, checkCompat = true, ...recordOptions } = options || {}

  // 状态
  const loading = ref(true)
  const error = ref<Error | null>(null)
  const instance = ref<UseRecordReturn | null>(null)

  try {
    if (checkCompat) {
      // 检查兼容性
      const { isSupported, requestPermission } = useRecordCompat()

      if (!isSupported.value) {
        throw new RecordError.notSupported()
      }

      // 请求权限
      if (autoRequestPermission) {
        const hasPermission = await requestPermission()
        if (!hasPermission) {
          throw new RecordError.permissionDenied()
        }
      }
    }

    // 创建录音实例
    instance.value = useRecord(recordOptions)
  } catch (err) {
    error.value = err instanceof Error ? err : new Error(String(err))
  } finally {
    loading.value = false
  }

  return {
    loading,
    error,
    instance,
  }
}
```

### 4. 创建测试用例

创建`tests/unit/useRecord.test.js`文件，用于测试条件编译和自动选择功能：

```javascript
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import useRecord from '@/hooks/useRecord'

// 根据不同平台进行测试
describe('useRecord', () => {
  // 模拟平台环境
  const originalPlatform = process.env.UNI_PLATFORM

  afterEach(() => {
    // 恢复环境
    process.env.UNI_PLATFORM = originalPlatform
  })

  test('应该导出所有必要的方法和状态', async () => {
    const wrapper = mount({
      template: '<div></div>',
      setup() {
        const record = useRecord()
        return { record }
      },
    })

    const record = wrapper.vm.record

    // 验证返回的接口是否完整
    expect(record.isRecording).toBeDefined()
    expect(record.isPaused).toBeDefined()
    expect(record.duration).toBeDefined()
    expect(record.volume).toBeDefined()
    expect(record.recordBlob).toBeDefined()
    expect(record.recordURL).toBeDefined()

    expect(typeof record.startRecording).toBe('function')
    expect(typeof record.pauseRecording).toBe('function')
    expect(typeof record.resumeRecording).toBe('function')
    expect(typeof record.stopRecording).toBe('function')
    expect(typeof record.playRecording).toBe('function')
    expect(typeof record.getAudioWaveform).toBe('function')
    expect(typeof record.cancelRecording).toBe('function')
    expect(typeof record.destroy).toBe('function')
  })
})
```

### 5. 更新类型导出

创建`src/hooks/index.ts`文件，统一导出录音相关的函数和类型：

```typescript
// 导出录音 Hook
export { default as useRecord } from './useRecord'

// 按需导出各平台实现
// #ifdef H5
export { default as useRecordH5 } from './useRecordH5'
// #endif

// #ifdef APP-PLUS
export { default as useRecordApp } from './useRecordApp'
// #endif

// 导出工具函数
export { default as useRecordCompat } from './utils/useRecordCompat'
export { createRecord } from './utils/recordFactory'

// 导出类型定义
export * from './types/record'
export * from './utils/recordErrors'
```

## 验收标准

1. 入口文件`useRecord.ts`使用条件编译正确导入和使用各平台实现
2. 编译后的代码在不同平台上只包含当前平台所需的代码
3. 所有导出的 API 接口保持一致
4. 兼容性检查和权限管理工具正常工作
5. 统一导出的类型定义和工具函数易于使用
6. 测试用例通过
7. 各平台打包后的体积合理，没有引入不必要的代码

## 依赖关系

- 上游依赖：
  - task-record-01 (类型定义与接口设计)
  - task-record-02 (H5 录音功能迁移)
  - task-record-03 (App 端录音功能实现)
- 下游依赖：
  - task-record-05 (业务代码集成与测试)

## 优先级

高 - 作为整合两端实现的关键任务

## 状态追踪

- [ ] 已重构 useRecord.ts 入口文件
- [ ] 已添加兼容性检查工具
- [ ] 已实现录音工厂函数
- [ ] 已创建测试用例
- [ ] 已更新类型导出
- [ ] 已进行不同平台的打包测试
- [ ] 已完成代码审查

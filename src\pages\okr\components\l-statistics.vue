<template>
  <div class="statistics-container">
    <!-- 图表区域 -->
    <div class="chart-container">
      <div v-if="chartData.length > 0">
        <!-- 水平滚动容器 -->
        <scroll-view scroll-x="true" class="chart-scroll-view" :scroll-left="0" show-scrollbar="false">
          <div class="chart-inner" :style="{ width: `${Math.max(100, chartData.length * 35)}px` }">
            <!-- 柱状图 -->
            <div class="chart-bars">
              <div class="bar-group" v-for="(item, index) in chartData" :key="index">
                <div class="completed-value">{{ item.completed }}</div>
                <div class="bar-container">
                  <div class="single-bar">
                    <div class="plan-part" :style="{ height: `${getBarHeight(item.plan)}%` }">
                      <div class="plan-indicator" :style="{ bottom: `${getBarHeight(item.completed)}%` }"></div>
                    </div>
                    <div class="completed-part" :style="{ height: `${getBarHeight(item.completed)}%` }"></div>
                  </div>
                </div>
                <div class="bar-label" v-html="formatBarLabel(item.label)"></div>
              </div>
            </div>
          </div>
        </scroll-view>

        <!-- 图例 -->
        <div class="chart-legend">
          <div class="legend-item">
            <div class="legend-color plan-color"></div>
            <div class="legend-label">计划量</div>
          </div>
          <div class="legend-item">
            <div class="legend-color completed-color"></div>
            <div class="legend-label">完成量</div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-chart">
        <i class="fas fa-chart-bar"></i>
        <div>暂无统计数据</div>
        <div class="empty-hint">当前任务尚无进度记录或未设置循环规则</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import dayjs from 'dayjs'
import { getOccurrences } from '@/utils/rrrrule'

// 组件接口定义
interface Props {
  // 任务详情对象，包含 repeatFlag 和 recList 等信息
  taskDetail: any
}

interface ChartDataItem {
  label: string
  plan: number
  completed: number
}

// 记录项的接口定义
interface RecordItem {
  recTime?: string
  val?: number
  [key: string]: any
}

const props = defineProps<Props>()

// 格式化标签，将换行符替换为 HTML 换行标签
const formatBarLabel = (label: string): string => {
  return label.replace(/\n/g, '<br>')
}

// 添加调试日志，在组件挂载和 taskDetail 变化时打印信息
onMounted(() => {
  console.log('[l-statistics] 组件已挂载，当前 taskDetail:', props.taskDetail)
})

watch(
  () => props.taskDetail,
  (newVal) => {
    console.log('[l-statistics] taskDetail 已更新：', newVal)
  },
  { deep: true }
)

// 生成图表数据
const chartData = computed((): ChartDataItem[] => {
  if (!props.taskDetail || !props.taskDetail.repeatFlag || !props.taskDetail.startDate) {
    console.log('[l-statistics] 缺少必要数据，返回空数组')
    return []
  }

  // 获取结束日期，如果未设置则使用当前日期
  const endDate = props.taskDetail.endDate || dayjs().format('YYYY-MM-DD')

  console.log('[l-statistics] 开始处理数据，日期范围：', props.taskDetail.startDate, '至', endDate)

  // 调用 getOccurrences 获取按周期分组的日期
  const { groupedDates } = getOccurrences(props.taskDetail.repeatFlag, props.taskDetail.startDate, endDate)

  // 如果没有数据，返回空数组
  if (!groupedDates || groupedDates.length === 0) {
    console.log('[l-statistics] getOccurrences 返回空数据')
    return []
  }

  console.log('[l-statistics] 获取到分组日期数据，组数：', groupedDates.length)

  // 解析规则以获取相关属性（如 dailyTarget, totalRequired 等）
  const ruleString = props.taskDetail.repeatFlag
  const dailyTarget = props.taskDetail.dailyTarget || 1
  const totalRequired = props.taskDetail.totalRequired || 1

  console.log('[l-statistics] 规则信息：', {
    ruleString,
    dailyTarget,
    totalRequired,
  })

  // 生成图表数据
  const result: ChartDataItem[] = []

  groupedDates.forEach((dateGroup, index) => {
    // 跳过空组
    if (!dateGroup || dateGroup.length === 0) {
      return
    }

    // 计算标签：根据周期类型生成不同格式的标签
    let label = ''
    if (dateGroup.length === 1) {
      // 单日周期
      label = dayjs(dateGroup[0]).format('MM/DD')
    } else {
      // 多日周期（周、月等）- 使用换行格式
      const startDate = dayjs(dateGroup[0])
      const endDate = dayjs(dateGroup[dateGroup.length - 1])

      // 使用换行符分隔起始日期和结束日期，以便更好地显示
      label = `${startDate.format('MM/DD')}\n${endDate.format('MM/DD')}`
    }

    // 计算计划量：根据规则类型计算
    let plan = 0
    if (ruleString.includes('DAILY') || ruleString.includes('WEEKLY') || ruleString.includes('MONTHLY')) {
      // 对于有明确发生日的类型，计划量是日期数量乘以每日目标
      plan = dateGroup.length * dailyTarget
    } else if (ruleString.includes('_N_TIMES') || ruleString.includes('N_DAYS')) {
      // 对于基于次数的类型，计划量是规则中定义的总次数
      plan = totalRequired
    } else {
      // 默认情况，使用日期数量
      plan = dateGroup.length
    }

    // 计算完成量：筛选出日期范围内的所有记录并累加 val 值
    let completed = 0

    // 确保 recList 存在
    if (props.taskDetail.recList && Array.isArray(props.taskDetail.recList)) {
      // 获取周期的开始和结束日期
      const periodStart = dateGroup[0]
      const periodEnd = dateGroup[dateGroup.length - 1]

      // 筛选出该周期内的记录
      const periodRecords = props.taskDetail.recList.filter((rec: RecordItem) => {
        const recDate = rec.recTime?.substring(0, 10) // 提取日期部分 YYYY-MM-DD
        return recDate && recDate >= periodStart && recDate <= periodEnd
      })

      // 累加 val 值
      completed = periodRecords.reduce((sum: number, rec: RecordItem) => sum + (rec.val || 0), 0)

      if (periodRecords.length > 0) {
        console.log(`[l-statistics] 周期 ${index + 1} (${label}) 记录:`, {
          日期范围: [periodStart, periodEnd],
          记录数量: periodRecords.length,
          完成量: completed,
        })
      }
    }

    result.push({ label, plan, completed })
  })

  console.log('[l-statistics] 图表数据处理完成，数据项数量：', result.length)
  return result
})

// 计算最大值
const maxValue = computed(() => {
  const max = Math.max(
    ...chartData.value.map((item) => Math.max(item.plan, item.completed)),
    1 // 确保至少有一个值
  )
  return Math.ceil(max * 1.1) // 加 10% 的空间
})

// 获取柱子高度百分比
const getBarHeight = (value: number): number => {
  if (maxValue.value === 0) return 0
  return (value / maxValue.value) * 100
}
</script>

<style scoped>
.statistics-container {
  width: 100%;
  padding: 16px 0;
  font-family: var(--font-sans);
}

/* 图表容器样式 */
.chart-container {
  background-color: var(--color-bg-card);
  border-radius: var(--rounded-md);
  padding: 20px;
  box-shadow: var(--shadow-sm);
  position: relative;
  height: 280px; /* 增加整体高度，从 260px 增加到 280px */
}

.chart-scroll-view {
  width: 100%;
  height: 220px; /* 增加滚动视图高度，从 200px 增加到 220px */
  position: relative;
}

.chart-inner {
  height: 100%;
  min-width: 100%;
  position: relative;
  display: flex;
}

/* 柱状图样式 */
.chart-bars {
  flex: 1;
  display: flex;
  height: 100%;
  padding-bottom: 40px; /* 增加底部间距，从 30px 改为 40px，给标签预留更多空间 */
  align-items: flex-end;
}

.bar-group {
  flex: 1;
  min-width: 30px; /* 增加最小宽度，从 28px 改为 30px，防止标签挤压 */
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  position: relative;
  margin: 0 1px; /* 添加左右间距，使柱子之间有更明显的分隔 */
}

.completed-value {
  font-size: 10px;
  color: var(--color-primary);
  font-weight: 500;
  margin-bottom: 2px; /* 减小底部边距，从 4px 减为 2px */
}

.bar-container {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 140px; /* 减小柱子容器高度，从 160px 减为 140px */
  width: 100%;
  margin-bottom: 8px; /* 增加底部间距，从 2px 改为 8px，让标签与柱子之间有更多空间 */
}

.single-bar {
  width: 12px;
  height: 100%;
  position: relative;
  border-radius: 6px 6px 0 0;
  overflow: hidden;
  background-color: var(--color-primary-transparent-20);
  border: 1px solid var(--color-primary-light);
}

.plan-part {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: transparent;
  border-radius: 6px 6px 0 0;
}

.completed-part {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: var(--color-primary);
  border-radius: 0;
  min-height: 2px; /* 确保即使值为 0 也有一点高度 */
  transition: height 0.3s ease;
}

/* 对高完成度的柱子应用上方圆角 */
.completed-part[style*='height: 9'],
.completed-part[style*='height: 8'],
.completed-part[style*='height: 7'],
.completed-part[style*='height: 100%'] {
  border-radius: 6px 6px 0 0;
}

.plan-indicator {
  position: absolute;
  width: 100%;
  height: 1.5px;
  background-color: var(--color-primary);
  left: 0;
}

.bar-label {
  font-size: 10px;
  color: var(--color-text-secondary);
  margin-top: 4px; /* 增加顶部边距，从 2px 改为 4px */
  text-align: center;
  position: absolute;
  bottom: -4px; /* 将标签向下移动 4px */
  width: 30px; /* 固定宽度，与 bar-group 的 min-width 保持一致，从 28px 改为 30px */
  height: 28px; /* 增加高度，从 26px 改为 28px，容纳两行文本 */
  line-height: 1.2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  transform: scale(0.85);
  transform-origin: center top;
  overflow-wrap: break-word; /* 允许文本在必要时断行 */
  white-space: normal; /* 确保文本可以换行 */
}

/* 图例样式 */
.chart-legend {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 8px; /* 增加顶部边距，从 4px 改为 8px */
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 2px;
}

.plan-color {
  background-color: var(--color-primary-transparent-20);
  border: 1px solid var(--color-primary);
}

.completed-color {
  background-color: var(--color-primary);
}

.legend-label {
  font-size: 10px;
  color: var(--color-text-secondary);
}

/* 空状态样式 */
.empty-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  height: 100%;
}

.empty-chart i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-hint {
  font-size: 14px;
  margin-top: 8px;
  color: var(--color-gray-400);
}
</style>

<template>
  <view class="z-audio-player" :style="customStyle" @click="toggleText">
    <view v-if="showTitle && title" class="z-audio-player__title">
      {{ title }}
    </view>
    <view class="z-audio-player__controls">
      <view class="z-audio-player__play-btn" @click.stop="togglePlay" :style="buttonStyle">
        <i :class="isPlaying ? 'fas fa-pause' : 'fas fa-play'"></i>
      </view>
      <view class="z-audio-player__progress" @click.stop>
        <view
          class="z-audio-player__progress-bar"
          @touchstart.stop="onDragStart"
          @touchmove.prevent.stop="onDragMove"
          @touchend.stop="onDragEnd"
          @mousedown.stop="onDragStart"
          @mousemove.prevent.stop="onDragMove"
          @mouseup.stop="onDragEnd"
          @mouseleave.stop="onDragEnd"
        >
          <view
            class="z-audio-player__progress-inner"
            :class="{ 'no-transition': isDragging }"
            :style="{ width: `${progress}%`, backgroundColor: themeColor || 'var(--color-primary, #2979ff)' }"
          ></view>
        </view>
        <view v-if="showTime" class="z-audio-player__time">
          <text>{{ formatTime(currentTime) }}</text>
          <text>{{ formatTime(duration) }}</text>
        </view>
      </view>
    </view>

    <!-- 转录状态指示器 -->
    <view v-if="isTranscribing" class="z-audio-player__transcription-status">
      <view class="transcription-loading">
        <view class="transcription-loading-dot" v-for="i in 3" :key="i"></view>
      </view>
      <text class="transcription-text">正在转写中...</text>
    </view>

    <!-- 文案展示区域 -->
    <view
      v-if="showText && transcriptionSentences.length > 0"
      class="z-audio-player__text-container"
    >
      <view class="z-audio-player__text-content">
        <view v-for="(sentence, sIndex) in transcriptionSentences" :key="sIndex" class="text-sentence">
          <text
            v-for="(word, wIndex) in sentence.words"
            :key="wIndex"
            :class="{ 'text-word': true, 'text-word-read': isWordRead(word), 'text-word-reading': isWordReading(word) }"
            @click.stop="handleWordClick(word)"
          >
            {{ word.Word }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, watch, getCurrentInstance } from 'vue'
import request from '@/utils/request'

const props = defineProps({
  /**
   * 音频资源地址
   */
  src: {
    type: String,
    required: true,
  },
  /**
   * 是否自动播放
   */
  autoplay: {
    type: Boolean,
    default: false,
  },
  /**
   * 是否循环播放
   */
  loop: {
    type: Boolean,
    default: false,
  },
  /**
   * 音频标题
   */
  title: {
    type: String,
    default: '',
  },
  /**
   * 是否显示标题
   */
  showTitle: {
    type: Boolean,
    default: false,
  },
  /**
   * 是否显示时间
   */
  showTime: {
    type: Boolean,
    default: true,
  },
  /**
   * 主题颜色
   */
  themeColor: {
    type: String,
    default: '',
  },
  /**
   * 是否启用转写功能
   */
  enableTranscription: {
    type: Boolean,
    default: false,
  },
  /**
   * 初始化的带时间戳的句子数据
   */
  initialSentences: {
    type: Array,
    default: () => [],
  },
  /**
   * 高亮延迟，用于微调歌词与音频的同步，单位毫秒
   */
  highlightOffset: {
    type: Number,
    default: 150,
  },
})

const emit = defineEmits(['play', 'pause', 'ended', 'timeupdate', 'error', 'transcription-start', 'transcription-end'])

const instance = getCurrentInstance()

// 音频状态
const isPlaying = ref(false)
const duration = ref(0)
const currentTime = ref(0)
const audioContext = ref(null)
const isDragging = ref(false)
const wasPlaying = ref(false)
const progressBarRect = ref(null)

// 文案相关状态
const showText = ref(false)
// 移除 transcriptionText 状态，组件现在只处理带时间戳的句子
// const transcriptionText = ref(props.initialTranscription || '')
const autoTranscribeOnSrc = ref(false)

// 歌词相关状态
const transcriptionWords = ref([]) // 存储所有单词及时间信息
const transcriptionSentences = ref(props.initialSentences || []) // 直接用 prop 初始化

// 转写相关状态
const isTranscribing = ref(false)

// 切换文案显示状态
const toggleText = () => {
  // 只有当有转写文本时才允许展开或收起
  if (transcriptionSentences.value.length > 0) {
    showText.value = !showText.value
  }
}

const progress = computed(() => {
  if (duration.value <= 0) return 0
  return (currentTime.value / duration.value) * 100
})

// 自定义样式
const customStyle = computed(() => {
  return {
    borderColor: props.themeColor ? props.themeColor : 'var(--color-border, #e0e0e0)',
  }
})

// 按钮样式
const buttonStyle = computed(() => {
  if (!props.themeColor) return {}
  return {
    backgroundColor: props.themeColor,
  }
})

// 检查单词是否已读过
const isWordRead = (word) => {
  const highlightTime = currentTime.value * 1000 - props.highlightOffset
  return highlightTime >= word.OffsetStartMs
}

// 检查单词是否正在朗读
const isWordReading = (word) => {
  const highlightTime = currentTime.value * 1000 - props.highlightOffset
  return highlightTime >= word.OffsetStartMs && highlightTime <= word.OffsetEndMs
}

const handleWordClick = (word) => {
  if (!audioContext.value || typeof word.OffsetStartMs === 'undefined') {
    return
  }
  const position = word.OffsetStartMs / 1000
  audioContext.value.seek(position)
  currentTime.value = position // Immediately update UI
}

// 更新当前播放的单词
const updateCurrentWord = () => {
  if (transcriptionWords.value.length === 0) return

  // 界面会根据 currentTime 自动更新已读文字的颜色
}

// 初始化音频上下文
const initAudioContext = () => {
  // 立即重置播放状态，避免 UI 上显示上一个音频的旧状态
  isPlaying.value = false
  currentTime.value = 0
  duration.value = 0

  if (audioContext.value) {
    audioContext.value.destroy()
  }

  audioContext.value = uni.createInnerAudioContext()
  audioContext.value.src = props.src
  audioContext.value.autoplay = props.autoplay
  audioContext.value.loop = props.loop

  // 事件监听
  audioContext.value.onPlay(() => {
    isPlaying.value = true
    emit('play')
  })

  audioContext.value.onPause(() => {
    isPlaying.value = false
    emit('pause')
  })

  audioContext.value.onEnded(() => {
    isPlaying.value = false
    currentTime.value = 0
    emit('ended')
  })

  audioContext.value.onTimeUpdate(() => {
    if (isDragging.value) return
    currentTime.value = audioContext.value.currentTime
    updateCurrentWord() // 更新当前高亮单词
    emit('timeupdate', {
      currentTime: currentTime.value,
      duration: duration.value,
    })
  })

  audioContext.value.onCanplay(() => {
    duration.value = audioContext.value.duration
  })

  audioContext.value.onError((err) => {
    console.error('音频播放错误：', err)
    emit('error', err)
  })

  // 自动播放处理
  if (props.autoplay) {
    isPlaying.value = true
  }

  // 如果启用了自动转写且 URL 发生变化，自动开始转写
  if (props.enableTranscription && autoTranscribeOnSrc.value && props.src) {
    transcribeAudio(props.src)
  }
}

// 播放/暂停切换
const togglePlay = () => {
  if (!audioContext.value) return

  if (isPlaying.value) {
    audioContext.value.pause()
  } else {
    audioContext.value.play()
  }
}

// 更新进度
const updateProgress = (e) => {
  const rect = progressBarRect.value
  if (!rect || duration.value <= 0) {
    return
  }

  // 兼容移动设备和PC设备
  const clientX = e.touches ? e.touches[0].clientX : e.clientX
  const offsetX = clientX - rect.left
  const percentage = Math.max(0, Math.min(1, offsetX / rect.width))

  currentTime.value = percentage * duration.value
}

// 开始拖动
const onDragStart = (e) => {
  isDragging.value = true
  wasPlaying.value = isPlaying.value
  if (wasPlaying.value) {
    audioContext.value.pause()
  }

  uni
    .createSelectorQuery()
    .in(instance)
    .select('.z-audio-player__progress-bar')
    .boundingClientRect((rect) => {
      if (rect) {
        progressBarRect.value = rect
        updateProgress(e)
      } else {
        isDragging.value = false
        if (wasPlaying.value) {
          audioContext.value.play()
        }
      }
    })
    .exec()
}

// 拖动中
const onDragMove = (e) => {
  if (!isDragging.value) return
  updateProgress(e)
}

// 拖动结束
const onDragEnd = (e) => {
  if (!isDragging.value) {
    return
  }
  isDragging.value = false
  audioContext.value.seek(currentTime.value)
  if (wasPlaying.value) {
    audioContext.value.play()
  }
  progressBarRect.value = null // 清除位置信息
}

// 格式化时间 (秒 -> mm:ss)
const formatTime = (seconds) => {
  if (isNaN(seconds) || seconds < 0) return '00:00'

  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)

  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 转写音频
const transcribeAudio = async (audioUrl) => {
  if (!audioUrl || isTranscribing.value) return

  isTranscribing.value = true
  // transcriptionText.value = '' // 清空之前的转写文本 - 已移除
  transcriptionWords.value = [] // 清空之前的单词数据
  transcriptionSentences.value = [] // 清空之前的句子数据
  showText.value = true // 自动显示文本区域

  emit('transcription-start')

  try {
    const { data } = await request.post('/speak/asr', { url: audioUrl })
    await pollAsrResult(data.asrId)
  } catch (error) {
    console.error('音频转写失败：', error)
    isTranscribing.value = false
    emit('transcription-end', { success: false, error })
  }
}

// 轮询转写结果
const pollAsrResult = async (asrId) => {
  let retryCount = 0
  const maxRetries = 30
  const pollInterval = 1000

  const poll = async () => {
    try {
      const { data: result } = await request.post('/speak/getAsr', { taskId: asrId })
      const status = result.Data.StatusStr

      if (status === 'success') {
        // 处理转写结果
        const sentences = result.Data.ResultDetail

        // 提取文本
        const transcript = sentences.map((item) => item.FinalSentence).join('\n')
        // transcriptionText.value = transcript - 不再需要

        // 预处理时间戳：将每个单词的相对时间戳转换为绝对时间戳
        const structuredSentencesWithAbsoluteTimes = []

        sentences.forEach((sentence, index) => {
          if (sentence.Words && sentence.Words.length > 0) {
            const absoluteWords = sentence.Words.map((word) => ({
              ...word,
              OffsetStartMs: sentence.StartMs + word.OffsetStartMs,
              OffsetEndMs: sentence.StartMs + word.OffsetEndMs,
            }))

            structuredSentencesWithAbsoluteTimes.push({
              index,
              text: sentence.FinalSentence,
              startMs: sentence.StartMs,
              endMs: sentence.EndMs,
              words: absoluteWords,
            })
          }
        })

        transcriptionWords.value = structuredSentencesWithAbsoluteTimes.flatMap((s) => s.words)
        transcriptionSentences.value = structuredSentencesWithAbsoluteTimes

        isTranscribing.value = false
        showText.value = true
        // 传递更丰富的数据
        emit('transcription-end', { success: true, transcript, sentences: structuredSentencesWithAbsoluteTimes })
      } else if (status === 'failed') {
        isTranscribing.value = false
        emit('transcription-end', { success: false, error: new Error('语音转写失败') })
      } else {
        retryCount++
        if (retryCount < maxRetries) {
          setTimeout(poll, pollInterval)
        } else {
          isTranscribing.value = false
          emit('transcription-end', { success: false, error: new Error('语音转写超时') })
        }
      }
    } catch (error) {
      isTranscribing.value = false
      emit('transcription-end', { success: false, error })
    }
  }

  poll()
}

// 监听src变化，重新初始化音频上下文
watch(
  () => props.src,
  (newValue) => {
    initAudioContext()
  }
)

// 监听转写开关
watch(
  () => props.enableTranscription,
  (newValue) => {
    // 此处可以保留，用于在外部切换开关时，如果之前没转写过，则进行一次转写
    if (newValue && props.src && transcriptionSentences.value.length === 0) {
      transcribeAudio(props.src)
    }
  }
)

// 监听传入的句子数据变化
watch(
  () => props.initialSentences,
  (newSentences) => {
    if (newSentences && newSentences.length > 0) {
      transcriptionSentences.value = newSentences
      transcriptionWords.value = newSentences.flatMap((s) => s.words)
      showText.value = true
    } else {
      // 如果传入空数组，则清空
      transcriptionSentences.value = []
      transcriptionWords.value = []
    }
  }
)

onMounted(() => {
  initAudioContext()
  // 如果有初始句子，则展开文案区域
  if (props.initialSentences && props.initialSentences.length > 0) {
    showText.value = true
  }
})

onBeforeUnmount(() => {
  if (audioContext.value) {
    audioContext.value.destroy()
  }
})

// 监听循环播放属性变化
watch(
  () => props.loop,
  (newValue) => {
    if (audioContext.value) {
      audioContext.value.loop = newValue
    }
  }
)

// 暴露方法给父组件
defineExpose({
  play: () => {
    if (audioContext.value) {
      audioContext.value.play()
    }
  },
  pause: () => {
    if (audioContext.value) {
      audioContext.value.pause()
    }
  },
  stop: () => {
    if (audioContext.value) {
      audioContext.value.stop()
    }
  },
  seek: (position) => {
    if (audioContext.value) {
      audioContext.value.seek(position)
    }
  },
  getDuration: () => {
    return duration.value
  },
  getCurrentTime: () => {
    return currentTime.value
  },
  getStatus: () => {
    return {
      isPlaying: isPlaying.value,
      duration: duration.value,
      currentTime: currentTime.value,
    }
  },
  transcribe: () => {
    if (props.src) {
      transcribeAudio(props.src)
    }
  },
  getTranscription: () => {
    return transcriptionSentences.value
  },
})
</script>

<style>
.z-audio-player {
  width: 100%;
  border-radius: 12px;
  padding: 16px;
  background-color: var(--color-bg-light, #f5f7fa);
  /* border: 1px solid var(--color-border, #e9e9e9); */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  cursor: pointer;
}

.z-audio-player__title {
  font-size: 15px;
  color: var(--color-text-primary, #333);
  margin-bottom: 12px;
  font-weight: 600;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.z-audio-player__controls {
  display: flex;
  align-items: center;
}

.z-audio-player__play-btn {
  width: 46px;
  height: 46px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-primary, #007aff);
  color: #ffffff;
  margin-right: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 122, 255, 0.2);
}

.z-audio-player__play-btn:hover {
  transform: scale(1.05);
}

.z-audio-player__play-btn:active {
  transform: scale(0.98);
}

.z-audio-player__play-btn i {
  font-size: 22px;
}

.z-audio-player__play-btn i.fa-play {
  /* 微调播放图标位置 */
  transform: translateX(2px);
}

.z-audio-player__progress {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.z-audio-player__progress-bar {
  height: 8px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  margin-bottom: 8px;
  cursor: pointer;
}

.z-audio-player__progress-inner {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background-color: var(--color-primary, #007aff);
  border-radius: 4px;
  transition: width 0.1s linear;
}

.z-audio-player__progress-inner.no-transition {
  transition: none;
}

.z-audio-player__time {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  color: var(--color-text-secondary, #666);
  font-family: monospace;
  font-weight: 600;
}

/* 文案展示区域样式 */
.z-audio-player__text-container {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--color-border, #eaeaea);
  animation: fadeIn 0.3s ease;
}

.z-audio-player__text-content {
  font-size: 14px;
  color: var(--color-text-secondary, #666);
  line-height: 1.6;
  text-align: justify;
}

/* 句子和单词样式 */
.text-sentence {
  line-height: 1.8;
  margin-bottom: 1em;
}

.text-word {
  display: inline-block;
  color: var(--color-text-secondary, #666);
  transition: color 0.3s ease, transform 0.2s ease;
  transform-origin: bottom;
}

.text-word-read {
  color: var(--color-primary, #007aff);
}

.text-word-reading {
  transform: scale(1.1);
  font-weight: 600;
}

/* 转写状态指示器样式 */
.z-audio-player__transcription-status {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12px;
  padding: 8px 0;
  background-color: rgba(0, 122, 255, 0.08);
  border-radius: 8px;
}

.transcription-loading {
  display: flex;
  align-items: center;
}

.transcription-loading-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--color-primary, #007aff);
  margin: 0 2px;
  opacity: 0.6;
  animation: dotPulse 1.4s infinite ease-in-out;
}

.transcription-loading-dot:nth-child(1) {
  animation-delay: 0s;
}

.transcription-loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.transcription-loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

.transcription-text {
  margin-left: 8px;
  font-size: 13px;
  color: var(--color-primary, #007aff);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes dotPulse {
  0%,
  100% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}
</style>

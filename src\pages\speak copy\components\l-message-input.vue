<template>
  <div class="chat-input-container">
    <div class="input-area">
      <div class="icon-btn" @click="toggleRecording">
        <i :class="['fas', isRecording ? 'fa-stop-circle' : 'fa-microphone']"></i>
      </div>

      <div v-if="isRecording" class="recording-state" @click="pauseOrResumeRecording">
        <div class="wave-container">
          <div class="wave-bar" v-for="n in 7" :key="n" :style="{ height: getWaveHeight(n) + 'px' }"></div>
        </div>
        <span class="recording-timer">{{ formatDuration(duration) }}</span>
        <span class="recording-hint">{{ isPaused ? '已暂停' : '录音中...' }}</span>
      </div>

      <div v-else class="text-input-wrapper">
        <u-input
          v-model="inputValue"
          :placeholder="placeholder"
          @confirm="sendMessage"
          type="textarea"
          :auto-height="true"
          :height="20"
          :cursor-spacing="15"
          :border="false"
          :show-confirm-bar="false"
          :custom-style="{ backgroundColor: 'transparent', color: 'var(--color-gray-800)', fontSize: '15px' }"
        />
      </div>

      <div class="icon-btn send-btn" :class="{ active: inputValue.trim() || isRecording }" @click="handleSend">
        <i class="fas fa-paper-plane"></i>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted, nextTick, onUnmounted } from 'vue'
import useRecord from '@/hooks/useRecord'
import dayjs from 'dayjs'

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '输入消息...',
  },
})

const emit = defineEmits(['update:modelValue', 'send', 'send-audio'])

const inputValue = ref(props.modelValue)

// 使用录音 hook
const {
  isRecording,
  isPaused,
  volume,
  duration,
  recordBlob,
  startRecording,
  pauseRecording,
  resumeRecording,
  stopRecording,
  cancelRecording,
} = useRecord({
  maxDuration: 60000, // 最长录音 1 分钟
  mimeType: 'audio/webm',
})

// 记录当前音量，用于动态生成波形
const currentVolume = ref(0)
watch(volume, (newVolume) => {
  currentVolume.value = newVolume
})

// 双向绑定
watch(
  () => props.modelValue,
  (newValue) => {
    inputValue.value = newValue
  }
)

watch(inputValue, (newValue) => {
  emit('update:modelValue', newValue)
})

// 切换录音状态
const toggleRecording = async () => {
  if (isRecording.value) {
    // 停止录音
    try {
      const blob = await stopRecording()
      console.log('录音完成', blob.size)

      // 确保录音状态被正确重置
      isRecording.value = false
    } catch (error) {
      console.error('停止录音失败：', error)
      // 即使停止失败也重置录音状态
      isRecording.value = false
      cancelRecording()
    }
  } else {
    // 开始录音
    try {
      await startRecording()
      console.log('开始录音...')
    } catch (error) {
      console.error('开始录音失败：', error)
      // 可能是用户拒绝了麦克风权限
      uni.showModal({
        title: '录音失败',
        content: '无法启动录音，请检查麦克风权限',
        showCancel: false,
      })
    }
  }
}

// 暂停或继续录音
const pauseOrResumeRecording = () => {
  if (isPaused.value) {
    resumeRecording()
    console.log('继续录音')
  } else {
    pauseRecording()
    console.log('暂停录音')
  }
}

// 根据索引和当前音量生成波形高度
const getWaveHeight = (index) => {
  // 基础高度
  const baseHeight = 12

  // 如果暂停了，所有波形固定高度
  if (isPaused.value) {
    return baseHeight
  }

  // 根据当前音量计算高度倍数
  const volumeFactor = currentVolume.value / 100

  // 波形高度上限
  const maxHeight = 24

  // 使用索引创建错位效果
  const indexFactor = Math.sin(Date.now() / 500 + index * 0.7)

  // 计算最终高度
  return baseHeight + Math.max(0, indexFactor * volumeFactor * maxHeight)
}

// 格式化录音时长
const formatDuration = (ms) => {
  const totalSeconds = Math.floor(ms / 1000)
  const minutes = Math.floor(totalSeconds / 60)
    .toString()
    .padStart(2, '0')
  const seconds = (totalSeconds % 60).toString().padStart(2, '0')
  return `${minutes}:${seconds}`
}

// 处理发送
const handleSend = async () => {
  if (isRecording.value) {
    // 发送录音
    try {
      const blob = await stopRecording()
      console.log('发送录音', blob.size)
      emit('send-audio', { blob, duration: duration.value })

      // 重置录音状态
      cancelRecording()
      // 确保录音状态被正确重置
      isRecording.value = false
    } catch (error) {
      console.error('发送录音失败：', error)
      // 即使发送失败也重置录音状态
      cancelRecording()
      isRecording.value = false
    }
  } else if (recordBlob.value && !inputValue.value.trim()) {
    // 有录音文件但已停止录音状态，且输入框为空时才发送录音
    console.log('发送已录制的音频', recordBlob.value.size)
    emit('send-audio', { blob: recordBlob.value, duration: duration.value })
    // 发送后清空录音
    cancelRecording()
    // 确保 recordBlob 也被清空
    recordBlob.value = null
    isRecording.value = false
  } else if (inputValue.value.trim()) {
    // 发送文本，并确保清空之前的录音数据
    recordBlob.value = null
    sendMessage()
  }
}

// 发送消息
const sendMessage = () => {
  if (inputValue.value.trim()) {
    emit('send')
  }
}

// 在组件卸载时重置录音状态
onUnmounted(() => {
  if (isRecording.value) {
    cancelRecording()
    isRecording.value = false
  }
})
</script>

<style lang="scss" scoped>
.chat-input-container {
  padding: 8px 12px;
  background-color: var(--color-white);
  border-top: 1px solid var(--color-gray-200);
}

.input-area {
  display: flex;
  align-items: center;
  width: 100%;
  background-color: var(--color-gray-100);
  border-radius: 24px;
  padding: 4px;
}

.icon-btn {
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--color-gray-600);
  font-size: 18px;
  transition: all 0.2s;

  &:hover {
    color: var(--color-primary);
  }
}

.send-btn {
  background-color: var(--color-primary);
  color: var(--color-white);
  border-radius: 50%;
  opacity: 0.5;

  &.active {
    opacity: 1;
  }
}

.text-input-wrapper {
  flex: 1;
  margin: 0 4px;
  display: flex;
  align-items: center;
}

.recording-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  color: var(--color-primary);
  cursor: pointer;
}

.wave-container {
  display: flex;
  align-items: center;
  height: 24px;
}

.wave-bar {
  width: 3px;
  background-color: var(--color-primary);
  margin: 0 2px;
  border-radius: 1px;
  transition: height 0.1s ease-in-out;
}

.recording-timer {
  font-size: 14px;
  font-weight: 500;
}

.recording-hint {
  font-size: 12px;
  color: var(--color-gray-600);
  margin-left: 8px;
}
</style>

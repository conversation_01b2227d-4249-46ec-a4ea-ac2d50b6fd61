<template>
  <view class="content-area">
    <!-- 头部 -->
    <view class="status-bar-placeholder"></view>
    <view class="app-header">
      <text class="title">我的目标</text>
      <view>
        <i class="fas fa-plus add-icon" @click="addNewObjective"></i>
      </view>
    </view>

    <!-- 标签式筛选器 -->
    <view class="filter-tabs">
      <view
        v-for="tab in filterTabs"
        :key="tab.value"
        class="filter-tab"
        :class="{ active: activeFilter === tab.value }"
        @click="filterContent(tab.value)"
      >
        <text>{{ tab.label }}</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空数据提示 -->
    <view class="empty-container" v-else-if="filteredObjectives.length === 0">
      <i class="fas fa-clipboard-list empty-icon"></i>
      <text class="empty-text">暂无{{ filterTabs.find((tab) => tab.value === activeFilter)?.label || '' }}目标</text>
      <view class="empty-action" @click="addNewObjective">
        <text>创建新目标</text>
      </view>
    </view>

    <!-- 合并后的 OKR 列表 -->
    <view
      v-else
      v-for="objective in filteredObjectives"
      :key="objective._id"
      class="objective-card"
      :class="{ 'completed-card': objective.progress >= 100 }"
      @click="goToOkrDetail(objective)"
    >
      <view class="objective-content">
        <view class="objective-title">
          {{ objective.title }}
        </view>
        <view class="progress-container">
          <view
            class="progress-bar"
            :class="{ 'completed-bar': objective.progress >= 100 }"
            :style="{
              width: objective.progress + '%',
              background: objective.color
                ? `linear-gradient(90deg, ${objective.color}CC, ${objective.color})`
                : 'linear-gradient(90deg, var(--color-primary-light), var(--color-primary))',
            }"
          >
            <text class="progress-text">{{ objective.progress }}%</text>
          </view>
        </view>
        <view class="objective-meta">
          <view class="meta-item">
            <!-- <i class="fas fa-calendar-alt"></i> -->
            <text>截止：{{ objective.deadline }}</text>
            <view v-if="objective.totalKRs > 0" @click.stop="toggleKRList(objective)" class="kr-toggle">
              <text>{{ objective.completedKRs }}/{{ objective.totalKRs }} 关键结果</text>
            </view>
          </view>
        </view>
        <view class="kr-list" v-if="objective.showKRs">
          <view class="kr-list-header">
            <text>关键结果</text>
          </view>
          <view
            class="kr-item"
            v-for="(kr, index) in objective.keyResults"
            :key="index"
            :class="{ completed: kr.completed }"
            @click.stop="goToKrDetail(kr, objective)"
          >
            <view class="kr-item-content">
              <view class="kr-item-header">
                <text class="kr-progress">{{ kr.progress || 0 }}%</text>
                <text class="kr-description">{{ kr.description }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, inject, watch, type Ref } from 'vue'
import { router, getUrlParams } from '@/utils/tools'
import { getOkrListApi } from '@/api/okr'
import { getTaskListApi } from '@/api/task'
import { onShow, onPullDownRefresh } from '@dcloudio/uni-app'
import { calculateWeightedProgress } from '@/utils/okrCalculationUtils'
import { OKR_FILTER_TABS, OKR_STATUS } from '@/constants/status'

interface KeyResult {
  description: string
  completed: boolean
  _id?: string
  curVal?: number
  tgtVal?: number
  weight?: number
  progress?: number
}

interface Objective {
  _id: number | string
  title: string
  status: string
  deadline: string
  progress: number
  completedKRs: number
  totalKRs: number
  showKRs: boolean
  keyResults: KeyResult[]
  color?: string
}

// 筛选标签数据 - 使用统一的状态常量
const filterTabs = OKR_FILTER_TABS

// 注入包含刷新计数和当前 tab 的刷新触发器
const reloadTrigger = inject<Ref<{ count: number; tabKey: string }>>('reloadTrigger', ref({ count: 0, tabKey: '' }))

// 活动筛选器
const activeFilter = ref<string>(OKR_STATUS.IN_PROGRESS)

// 模拟 OKR 数据
const objectives = ref<Objective[]>([])

// 加载状态
const loading = ref(false)

// 判断当前页面是否激活
const isActiveTab = computed(() => {
  return reloadTrigger.value.tabKey === 'goals'
})

// 加载 OKR 列表数据
const loadOkrList = async () => {
  try {
    loading.value = true
    // 1. 获取目标列表
    const result = await getOkrListApi()
    // 2. 处理每个目标，获取其关键结果
    const processedObjectives = []
    for (const item of result) {
      // 获取当前目标的关键结果
      const tasks = await getTaskListApi(`okrId == "${item._id}" && deleteTime == ""`)
      // 筛选出关键结果（type 为 kr 的任务）
      const keyResultsList = tasks.filter((task: any) => task.type === 'kr' && task.parentId === '')
      // 计算已完成的关键结果数量
      const completedKRs = keyResultsList.filter((kr: any) => kr.status === 1).length
      // 格式化关键结果数据
      const formattedKeyResults = keyResultsList.map((kr: any) => {
        const curVal = kr.curVal || 0
        const tgtVal = kr.tgtVal || 0
        const progress = tgtVal > 0 ? Math.round((curVal / tgtVal) * 100) : 0

        return {
          description: kr.title || kr.description,
          completed: kr.status === 1,
          _id: kr._id,
          curVal: curVal,
          tgtVal: tgtVal,
          weight: kr.weight,
          progress: progress > 100 ? 100 : progress, // 限制进度不超过 100%
        }
      })
      // 计算进度百分比 - 使用加权计算
      let progress = 0
      if (formattedKeyResults.length > 0) {
        progress = calculateWeightedProgress(formattedKeyResults)
      } else {
        // 兼容旧数据格式
        progress = item.tgtVal > 0 ? Math.round((item.curVal / item.tgtVal) * 100) : 0
      }
      // 构建目标对象
      const objective = {
        _id: item._id,
        title: item.title,
        status: item.status || OKR_STATUS.IN_PROGRESS,
        deadline: item.endDate || '未设置',
        progress: progress > 100 ? 100 : progress,
        completedKRs: completedKRs,
        totalKRs: keyResultsList.length,
        showKRs: false,
        keyResults: formattedKeyResults,
        color: item.color || '', // 添加颜色信息
      }
      processedObjectives.push(objective)
    }
    objectives.value = processedObjectives
  } catch (error) {
    console.error('获取 OKR 列表失败：', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 在页面显示时加载数据
onShow(() => {
  console.log('onShowokr', isActiveTab.value)
  if (isActiveTab.value) {
    loadOkrList()
  }
})

// 监听刷新信号和当前页面是否激活
watch(
  () => [reloadTrigger.value.count, isActiveTab.value],
  ([count, isActive]) => {
    console.log('监听到刷新触发：', count, '当前页是否激活：', isActive, '当前 tab:', reloadTrigger.value.tabKey)
    if (isActive) {
      console.log('OKR 列表页接到了刷新通知，且当前为激活页面')
      loadOkrList()
    } else {
      console.log('OKR 列表页接到了刷新通知，但当前不是激活页面')
    }
  },
  { deep: true }
)

// 过滤目标函数
const filteredObjectives = computed(() => {
  return objectives.value.filter((obj) => obj.status === activeFilter.value)
})

// 展开/收起 KR 列表
const toggleKRList = (objective: Objective) => {
  objective.showKRs = !objective.showKRs
}

// 筛选内容
const filterContent = (filter: string) => {
  activeFilter.value = filter
}

// 添加新目标
const addNewObjective = () => {
  const url = '/pages/okr/okrEdit'
  router.push(url)
}

const goToOkrDetail = (objective: Objective) => {
  router.push(`/pages/okr/okrDetail?id=${objective._id}`)
}

// 跳转到关键结果详情页
const goToKrDetail = (kr: KeyResult, objective: Objective) => {
  if (kr._id) {
    router.push(`/pages/okr/krDetail?id=${kr._id}&okrId=${objective._id}`)
  } else {
    uni.showToast({
      title: '无法获取关键结果 ID',
      icon: 'none',
    })
  }
}
</script>

<style scoped lang="scss">
// 全局样式
.content-area {
  /* padding: 32rpx; */ /* 移除内边距，交由 PC 布局容器控制 */
  min-height: 100vh;
  box-sizing: border-box;
  /* background-color: var(--color-bg); */ /* 移除背景色，交由 PC 布局容器控制 */
  padding: 30rpx;
}

// 头部样式
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.title {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(120deg, var(--color-primary), var(--color-primary-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: var(--color-primary); // 兼容性
  letter-spacing: -0.5px;
}

.add-icon {
  font-size: 24px;
  color: var(--color-gray-600);
  padding: 12px 12px;
  border-radius: 9999px;
  background: var(--color-gray-100);
  transition: background 0.2s;
  cursor: pointer;
  display: inline-block;
}

.add-icon:hover {
  background: var(--color-gray-200);
}

// 筛选标签容器
.filter-tabs {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: nowrap;
  justify-content: flex-start;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

.filter-tabs::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari and Opera */
}

.filter-tab {
  padding: 8px 14px;
  border-radius: var(--rounded-md);
  font-size: 14px;
  font-weight: 500;
  background: var(--color-gray-100);
  color: var(--color-gray-600);
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-sm);
  opacity: 0.85;
  transform: scale(0.95);
  text-align: center;
  outline: none;
  flex-shrink: 0;
  white-space: nowrap;

  &:hover {
    background: var(--color-gray-200);
    transform: scale(0.98);
    opacity: 0.95;
  }
}

.filter-tab.active {
  padding: 8px 16px;
  font-size: 14px;
  background: linear-gradient(135deg, var(--color-primary-light), var(--color-primary));
  color: #fff;
  box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);
  opacity: 1;
  transform: scale(1);
  z-index: 1;
  font-weight: 600;
}

// 目标卡片
.objective-card {
  background-color: var(--color-white);
  border-radius: 20px; // 增加卡片圆角
  margin-bottom: 20px;
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  position: relative; // 添加相对定位以支持水印定位
}

// 完成卡片样式
.completed-card {
  background-image: none; // 移除重复背景
  position: relative;
  box-shadow: 0 2px 12px rgba(var(--color-primary-rgb, 99, 102, 241), 0.15);
  border: 1px solid rgba(var(--color-primary-rgb, 99, 102, 241), 0.2);
  overflow: hidden; // 确保水印不会溢出卡片
}

.completed-card::after {
  content: '已完成';
  position: absolute;
  top: 0;
  right: 0;
  padding: 40px 30px;
  transform: translate(20%, -25%) rotate(30deg);
  font-size: 22px;
  font-weight: bold;
  color: rgba(var(--color-primary-rgb, 99, 102, 241), 0.15);
  pointer-events: none;
  z-index: 0;
  white-space: nowrap;
}

// objective-content 样式
.objective-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px;
  position: relative;
  z-index: 2; // 确保内容在水印上方
}

.objective-title {
  font-size: 17px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: 12px;
  line-height: 1.4;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.progress-container {
  height: 12px;
  background-color: #eef0fa;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 16px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary-light), var(--color-primary));
  border-radius: 6px;
  transition: width 0.3s ease;
  position: relative;
  min-width: 30px;
  /* 确保即使进度很低也能显示文本 */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(var(--color-primary-rgb), 0.3);
}

.progress-text {
  color: #fff;
  font-size: 10px;
  font-weight: 600;
  position: absolute;
  right: 6px;
  white-space: nowrap;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.completed-bar {
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
}

.meta-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 0 2px;
}

.meta-item i {
  margin-right: 6px;
  color: var(--color-primary);
  font-size: 14px;
}

.meta-item text {
  font-size: 13px;
  color: var(--color-gray-500);
}

.kr-toggle {
  font-size: 13px;
  color: var(--color-primary-light);
  font-weight: 500;
  cursor: pointer;
  margin-left: auto;
  padding: 2px 8px;
  border-radius: 12px;
  background-color: rgba(var(--color-primary-rgb), 0.08);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.kr-toggle:hover {
  color: var(--color-primary);
  background-color: rgba(var(--color-primary-rgb), 0.12);
  text-decoration: none;
}

.toggle-icon {
  margin-left: 6px;
  font-size: 12px;
  transition: transform 0.3s ease;
}

.kr-list {
  margin-top: 16px;
  border-top: 1px solid var(--color-gray-200);
  padding-top: 16px;
  position: relative;
  z-index: 3; // 确保列表在水印上方
  animation: fadeIn 0.3s ease;
}

.kr-list-header {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-gray-700);
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  padding-left: 2px;
}

.kr-list-header::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 14px;
  background: linear-gradient(to bottom, var(--color-primary-light), var(--color-primary));
  border-radius: 2px;
  margin-right: 8px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.kr-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;
  padding: 12px;
  position: relative;
  z-index: 3; // 确保列表项在水印上方
  background-color: rgba(var(--color-gray-100-rgb, 243, 244, 246), 0.6);
  border-radius: 8px;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.kr-item:hover {
  background-color: rgba(var(--color-gray-200-rgb, 229, 231, 235), 0.7);
  transform: translateX(2px);
}

.kr-item.completed {
  border-left-color: var(--color-success, #10b981);
}

.kr-item:last-child {
  margin-bottom: 0;
}

.kr-item i {
  margin-right: 10px;
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.kr-item i.fa-check-circle {
  color: var(--color-success, #10b981);
}

.kr-item i.fa-circle {
  color: var(--color-gray-400);
  opacity: 0.8;
}

.kr-item-content {
  width: 100%;
}

.kr-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.kr-description {
  font-size: 14px;
  color: var(--color-gray-800);
  line-height: 1.5;
  flex: 1;
  margin-left: 10px;
}

.kr-progress {
  font-size: 14px;
  color: var(--color-primary);
  font-weight: 600;
  margin-right: 10px;
  min-width: 45px;
  text-align: left;
  flex-shrink: 0;
}

// 加载状态样式
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 4px solid var(--color-primary);
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-left: 10px;
  font-size: 14px;
  color: var(--color-gray-500);
}

// 空数据提示样式
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.empty-icon {
  font-size: 48px;
  color: var(--color-gray-400);
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
  color: var(--color-gray-500);
  margin-bottom: 10px;
}

.empty-action {
  padding: 8px 16px;
  border-radius: var(--rounded-md);
  background: linear-gradient(135deg, var(--color-primary-light), var(--color-primary));
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}
</style>

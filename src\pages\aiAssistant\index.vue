﻿<template>
    <view class="ai-chat-page">
        <l-message-list ref="messageListRef" :messages="messages" />
        <view class="message-input-wrapper">
            <z-message-input v-model="inputValue" @send="handleSendMessageStream" @send-audio="handleSendAudio"
                placeholder="有什么想问的..." cloud-path="aiAssistant/" :disabled="isStreaming" />
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import LMessageList from './components/l-message-list.vue'
import ZMessageInput from '@/components/z-message-input/z-message-input.vue'
import { nextTick } from 'vue'

const inputValue = ref('')
const messageListRef = ref(null)
const messages = ref([
  {
    _id: '1',
    content: '你好！我是你的 AI 助手，有什么可以帮助你的吗？',
    type: 'text',
    isUser: false,
    time: new Date().toISOString(),
  },
])

const aiApi = uniCloud.importObject('ai', {
  customUI: true, // 取消自动展示的交互提示界面
})

// 流式聊天状态管理
const isStreaming = ref(false)
const currentStreamingMessageId = ref(null)
const retryCount = ref(0)
const maxRetries = 3
const connectionStatus = ref('disconnected') // connecting/connected/error

onUnmounted(() => {
    // 组件卸载时，如果仍在流式传输，需要处理中断逻辑
    // 例如，关闭可能存在的 SSE 通道
})

const addMessage = (message) => {
  messages.value.push({
    ...message,
    _id: Date.now().toString(),
    time: new Date().toISOString(),
  })
  nextTick(() => {
    messageListRef.value?.scrollToBottom()
  })
}

// 处理流式消息接收
const handleStreamMessage = (data) => {
    console.log('收到流式消息：', data)

    switch (data.type) {
        case 'start':
            const startMessage = {
                _id: Date.now().toString(),
                content: '',
                type: 'text',
                isUser: false,
                streaming: true,
                time: new Date().toISOString(),
            }
            messages.value.push(startMessage)
            currentStreamingMessageId.value = startMessage._id
            isStreaming.value = true

      nextTick(() => {
        messageListRef.value?.scrollToBottom()
      })
      break

    case 'intent_type':
      // 处理意图类型，更新状态为对应的处理中状态
      if (currentStreamingMessageId.value) {
        const messageIndex = messages.value.findIndex((msg) => msg._id === currentStreamingMessageId.value)
        if (messageIndex !== -1) {
          messages.value[messageIndex].intentType = data.intentType
          console.log('intentType', data.intentType)
          // 根据不同意图类型更新状态提示
          switch (data.intentType) {
            case 'task':
              messages.value[messageIndex].content = '执行任务中...'
              break
            case 'chat':
              messages.value[messageIndex].content = '思考回复中...'
              break
            default:
              messages.value[messageIndex].content = '处理中...'
          }

          nextTick(() => {
            messageListRef.value?.scrollToBottom()
          })
        }
      }
      break
    
    case 'content_chunk':
        if (currentStreamingMessageId.value) {
            const messageIndex = messages.value.findIndex((msg) => msg._id === currentStreamingMessageId.value)
            if (messageIndex !== -1) {
                if (messages.value[messageIndex].content === '思考回复中...') {
                    messages.value[messageIndex].content = ''
                }
                messages.value[messageIndex].content += data.content
                nextTick(() => {
                    messageListRef.value?.scrollToBottom()
                })
            }
        }
        break

    case 'end':
      if (currentStreamingMessageId.value) {
        const messageIndex = messages.value.findIndex((msg) => msg._id === currentStreamingMessageId.value)
        if (messageIndex !== -1) {
          // 统一处理所有意图类型
          messages.value[messageIndex].content = data.content
          messages.value[messageIndex].streaming = false
          messages.value[messageIndex].statusMessage = false
        }
      }
      isStreaming.value = false
      currentStreamingMessageId.value = null
      console.log('流式数据接收完成')
      break

        case 'error':
            if (currentStreamingMessageId.value) {
                const messageIndex = messages.value.findIndex(
                    (msg) => msg._id === currentStreamingMessageId.value
                )
                if (messageIndex !== -1) {
                    messages.value[messageIndex].content = `抱歉，发生了错误：${data.error}`
                    messages.value[messageIndex].streaming = false
                }
            }
            isStreaming.value = false
            currentStreamingMessageId.value = null
            console.error('流式聊天错误：', data.error)
            break
    }
}

// 使用复用的 SSE Channel 进行流式聊天
const handleSendMessageStreamWithRetry = async (userMessage, historyMessages, attempt = 1) => {
    let timeoutId = null

    try {
        connectionStatus.value = 'connecting'

        const channel = new uniCloud.SSEChannel()

        // 设置 5 分钟超时
        timeoutId = setTimeout(() => {
            console.log('流式聊天超时，5 分钟未收到响应')
            handleStreamMessage({
                type: 'error',
                error: '请求超时（5 分钟），请重新尝试'
            })
            channel.close()
        }, 300000)

        channel.on('open', () => {
            console.log('SSE Channel 连接成功')
            connectionStatus.value = 'connected'
            retryCount.value = 0
        })

        channel.on('message', (data) => {
            if (timeoutId) {
                clearTimeout(timeoutId)
                timeoutId = null
            }
            handleStreamMessage(data)
        })

        channel.on('end', (data) => {
            if (timeoutId) {
                clearTimeout(timeoutId)
                timeoutId = null
            }
            handleStreamMessage(data)
        })

        channel.on('error', (error) => {
            console.error('SSE Channel 错误：', error)
            connectionStatus.value = 'error'

            if (timeoutId) {
                clearTimeout(timeoutId)
                timeoutId = null
            }

            if (attempt < maxRetries) {
                console.log(`连接失败，准备第${attempt + 1}次重试...`)
                setTimeout(() => {
                    handleSendMessageStreamWithRetry(userMessage, historyMessages, attempt + 1)
                }, 1000 * attempt)
            } else {
                handleStreamMessage({
                    type: 'error',
                    error: `连接失败，已重试${maxRetries}次：${error.message || '连接错误'}`
                })
            }
        })

        channel.on('close', () => {
            console.log('SSE Channel 连接关闭')
            if (connectionStatus.value !== 'error') {
                connectionStatus.value = 'disconnected'
            }
            if (timeoutId) {
                clearTimeout(timeoutId)
                timeoutId = null
            }
        })

        await channel.open()
        console.log('SSE Channel 已开启')

        const response = await aiApi.chatStreamSSE({
            message: userMessage,
            messages: historyMessages,
            channel: channel,
        })

        console.log('流式聊天接口调用结果：', response)

        if (response.errCode !== 0) {
            throw new Error(response.errMsg || '调用 AI 接口失败')
        }

    } catch (error) {
        console.error(`流式聊天失败（第${attempt}次尝试）：`, error)
        connectionStatus.value = 'error'

        if (timeoutId) {
            clearTimeout(timeoutId)
            timeoutId = null
        }

        if (attempt < maxRetries) {
            console.log(`接口调用失败，准备第${attempt + 1}次重试...`)
            retryCount.value = attempt
            setTimeout(() => {
                handleSendMessageStreamWithRetry(userMessage, historyMessages, attempt + 1)
            }, 1000 * attempt)
        } else {
            handleStreamMessage({
                type: 'error',
                error: `请求失败，已重试${maxRetries}次：${error.message || '网络连接异常，请检查网络后重试'}`
            })
        }
    }
}

// 使用 SSE Channel 的流式聊天
const handleSendMessageStream = async () => {
    if (!inputValue.value.trim() || isStreaming.value) return

    addMessage({
        content: inputValue.value,
        type: 'text',
        isUser: true,
    })

    const userMessage = inputValue.value
    inputValue.value = ''

  const historyMessages = messages.value
    .filter((msg) => !msg.loading && !msg.streaming && msg.type === 'text')
    .map((msg) => ({
      role: msg.isUser ? 'user' : 'assistant',
      content: msg.content,
    }))

  await handleSendMessageStreamWithRetry(userMessage, historyMessages)
}

const handleSendAudio = (audioData) => {
  addMessage({
    type: 'audio',
    isUser: true,
    audioUrl: audioData.tempFileURL,
    audioDuration: audioData.duration,
  })

  addMessage({
    type: 'text',
    isUser: false,
    loading: true,
  })

    setTimeout(() => {
    messages.value = messages.value.filter((m) => !m.loading)
    addMessage({
      content: '我收到了你的语音，正在思考如何回复...',
      type: 'text',
      isUser: false,
    })
  }, 1500)
}

// 任务确认相关函数已移除，因为不再需要确认步骤
</script>

<style lang="scss" scoped>
.ai-chat-page {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f4f5f7;
}

.message-input-wrapper {
    background-color: #fff;
}

.channel-status-indicator {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 20px;
    padding: 8px 16px;

    .status-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 1.5s infinite;
        }

        .status-text {
            color: #fff;
            font-size: 12px;
        }

        &.connecting .status-dot {
            background-color: #ffa500;
        }

        &.error .status-dot {
            background-color: #ff4757;
        }

        &.disconnected .status-dot {
            background-color: #747d8c;
        }
    }
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}
</style>

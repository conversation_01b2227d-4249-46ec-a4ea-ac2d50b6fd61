<template>
  <view class="main-content-container">
    <!-- 根据 activeTab 动态加载组件 -->
    <OkrListPage v-if="activeTab === 'goals'" />
    <SpeakIndexPage v-else-if="activeTab === 'speak'" />
    <!-- 其他 Tab 的占位内容 -->
    <view v-else class="placeholder">
      <view class="placeholder-content">
        <i class="fas" :class="placeholderIcon"></i>
        <h2>{{ placeholderTitle }}</h2>
        <p>此功能模块的内容待后续实现</p>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, computed } from 'vue'
import OkrListPage from '@/pages/okr/okrList.vue'
import SpeakIndexPage from '@/pages/speak/index.vue'

const props = defineProps({
  activeTab: String,
})

const placeholderTitle = computed(() => {
  if (props.activeTab === 'goals') return '目标列表'
  if (props.activeTab === 'setting') return '设置'
  return '未选择'
})

const placeholderIcon = computed(() => {
  if (props.activeTab === 'goals') return 'fa-bullseye'
  if (props.activeTab === 'setting') return 'fa-cog'
  return 'fa-box-open'
})
</script>

<style scoped>
.main-content-container {
  height: 100%;
  width: 100%;
  background-color: #f0f2f5;
  padding: 30rpx;
}
.placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #b0b0b0;
  text-align: center;
}
.placeholder-content i {
  font-size: 48px;
  margin-bottom: 24px;
}
.placeholder-content h2 {
  font-size: 22px;
  margin-bottom: 12px;
  color: #777;
}
</style>

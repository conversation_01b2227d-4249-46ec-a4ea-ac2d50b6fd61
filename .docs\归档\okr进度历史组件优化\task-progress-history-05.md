# 任务：OKR进度历史组件记录编辑和删除功能实现

## 任务描述
实现进度历史组件中记录的编辑和删除功能，完善组件与父组件的交互，实现数据同步和更新。

## 所属功能模块
OKR进度历史组件

## 技术实现详情

### 父子组件事件交互
1. 在`l-progress-history.vue`组件中添加事件通知：
   ```javascript
   // 组件中已定义的编辑和删除方法
   editRecord(record) {
     this.$emit('edit-record', record);
   },
   
   deleteRecord(record) {
     uni.showModal({
       title: '确认删除',
       content: '确定要删除这条进度记录吗？',
       success: (res) => {
         if (res.confirm) {
           this.$emit('delete-record', record);
         }
       }
     });
   }
   ```

2. 在`krDetail.vue`中处理子组件事件：
   ```html
   <l-progress-history 
     :kr-id="id" 
     :initial-records="progressHistory"
     @edit-record="handleEditRecord"
     @delete-record="handleDeleteRecord"
   />
   ```

   ```javascript
   methods: {
     // 已有方法...
     
     /**
      * 处理编辑进度记录事件
      * @param {Object} record 要编辑的记录
      */
     handleEditRecord(record) {
       this.showProgressModal = true;
       this.editingRecord = record;
       this.editingProgress = record.progress;
       this.progressDesc = record.desc || '';
     },
     
     /**
      * 处理删除进度记录事件
      * @param {Object} record 要删除的记录
      */
     handleDeleteRecord(record) {
       this.deleteProgressRecord(record._id);
     }
   }
   ```

### 记录编辑模态框
1. 在`l-progress-history.vue`组件中添加记录编辑模态框：
   ```html
   <!-- 进度编辑模态框 -->
   <z-confirm-modal
     v-model="showEditModal"
     title="编辑进度"
     confirmText="保存"
     @confirm="confirmEditRecord"
   >
     <view class="edit-modal-content">
       <view class="form-item">
         <view class="form-label">进度值</view>
         <view class="form-input">
           <input type="number" v-model="editingProgress" placeholder="请输入进度值(0-100)" />
           <text class="unit">%</text>
         </view>
       </view>
       <view class="form-item">
         <view class="form-label">备注</view>
         <view class="form-input">
           <textarea v-model="editingDesc" placeholder="请输入备注信息(选填)" />
         </view>
       </view>
     </view>
   </z-confirm-modal>
   ```

2. 添加编辑记录相关的数据和方法：
   ```javascript
   data() {
     return {
       // 已有数据...
       
       // 编辑记录相关
       showEditModal: false,
       editingRecord: null,
       editingProgress: 0,
       editingDesc: '',
     }
   },
   
   methods: {
     // 已有方法...
     
     /**
      * 编辑记录（打开编辑模态框）
      * @param {Object} record 要编辑的记录
      */
     editRecord(record) {
       this.editingRecord = record;
       this.editingProgress = record.progress;
       this.editingDesc = record.desc || '';
       this.showEditModal = true;
     },
     
     /**
      * 确认编辑记录
      */
     confirmEditRecord() {
       if (!this.editingRecord) return;
       
       // 验证进度值
       const progress = parseInt(this.editingProgress);
       if (isNaN(progress) || progress < 0 || progress > 100) {
         uni.showToast({
           title: '进度值必须在0-100之间',
           icon: 'none'
         });
         return;
       }
       
       // 构建更新后的记录
       const updatedRecord = {
         ...this.editingRecord,
         progress,
         desc: this.editingDesc.trim()
       };
       
       // 触发更新事件，由父组件处理实际的API调用
       this.$emit('update-record', updatedRecord);
       
       // 清理状态
       this.showEditModal = false;
       this.editingRecord = null;
     }
   }
   ```

### 记录操作API集成
1. 在`krDetail.vue`中实现更新和删除记录的API调用：
   ```javascript
   /**
    * 更新进度记录
    * @param {Object} record 更新后的记录
    */
   async updateProgressRecord(record) {
     try {
       uni.showLoading({ title: '更新中...' });
       
       // 调用API更新记录
       await this.$api.okr.updateProgressRecord(record);
       
       // 更新本地数据
       const index = this.progressHistory.findIndex(item => item._id === record._id);
       if (index !== -1) {
         this.progressHistory.splice(index, 1, record);
       }
       
       // 刷新子组件数据
       this.$refs.progressHistory.refreshData();
       
       uni.showToast({ title: '更新成功', icon: 'success' });
     } catch (error) {
       console.error('更新进度记录失败', error);
       uni.showToast({ title: '更新失败', icon: 'none' });
     } finally {
       uni.hideLoading();
     }
   },
   
   /**
    * 删除进度记录
    * @param {String} recordId 记录ID
    */
   async deleteProgressRecord(recordId) {
     try {
       uni.showLoading({ title: '删除中...' });
       
       // 调用API删除记录
       await this.$api.okr.deleteProgressRecord(recordId);
       
       // 更新本地数据
       this.progressHistory = this.progressHistory.filter(item => item._id !== recordId);
       
       // 刷新子组件数据
       this.$refs.progressHistory.refreshData();
       
       uni.showToast({ title: '删除成功', icon: 'success' });
     } catch (error) {
       console.error('删除进度记录失败', error);
       uni.showToast({ title: '删除失败', icon: 'none' });
     } finally {
       uni.hideLoading();
     }
   }
   ```

2. 确保在组件引用中添加ref：
   ```html
   <l-progress-history 
     ref="progressHistory"
     :kr-id="id" 
     :initial-records="progressHistory"
     @edit-record="handleEditRecord"
     @delete-record="handleDeleteRecord"
     @update-record="updateProgressRecord"
   />
   ```

### 样式实现
1. 添加编辑模态框样式：
   ```css
   .edit-modal-content {
     padding: 10px 0;
   }
   
   .form-item {
     margin-bottom: 16px;
   }
   
   .form-label {
     font-size: 14px;
     margin-bottom: 8px;
     color: #333;
   }
   
   .form-input {
     position: relative;
   }
   
   .form-input input,
   .form-input textarea {
     width: 100%;
     padding: 8px 12px;
     border: 1px solid #e8e8e8;
     border-radius: 4px;
     font-size: 14px;
   }
   
   .form-input textarea {
     height: 100px;
   }
   
   .form-input .unit {
     position: absolute;
     right: 12px;
     top: 8px;
     color: #999;
   }
   ```

### 数据刷新机制
1. 在`l-progress-history.vue`中添加数据刷新方法：
   ```javascript
   /**
    * 刷新记录数据
    * 可以由父组件调用，在增删改操作后刷新列表
    */
   refreshData() {
     this.loading = true;
     
     // 使用关键结果ID获取最新进度记录
     this.$api.okr.getProgressHistory(this.krId)
       .then(records => {
         this.records = records || [];
         // 更新筛选后的记录和有记录的日期
         this.updateDateHasRecord();
         this.filterRecords();
       })
       .catch(error => {
         console.error('获取进度记录失败', error);
         uni.showToast({
           title: '获取记录失败',
           icon: 'none'
         });
       })
       .finally(() => {
         this.loading = false;
       });
   }
   ```

2. 确保组件初始化时处理初始数据：
   ```javascript
   // 在created或mounted钩子中
   created() {
     // 如果有初始数据，直接使用
     if (this.initialRecords && this.initialRecords.length) {
       this.records = [...this.initialRecords];
       this.updateDateHasRecord();
       this.filterRecords();
     } else {
       // 否则从API加载数据
       this.refreshData();
     }
   }
   ```

## 验收标准
1. 进度记录编辑功能正常工作，能够修改进度值和备注信息
2. 进度记录删除功能正常工作，有确认提示，删除后列表自动更新
3. 组件与父组件之间事件通信正确，数据同步无误
4. 进度值输入有验证，确保在0-100范围内
5. 编辑和删除操作有适当的加载状态和结果反馈
6. 编辑模态框UI美观，表单布局合理

## 依赖关系
- 上游依赖：task-progress-history-01.md, task-progress-history-03.md, task-progress-history-04.md
- 无下游依赖

## 优先级
中

## 估计工作量
1.5人日 
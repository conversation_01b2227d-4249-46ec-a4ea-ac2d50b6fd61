const path = require('path')

const spaceIdByDev = 'mp-5ccd4649-d147-484b-aa06-adc19448de97'
const spaceIdByProd = 'mp-c6815a6a-de20-45ad-a345-2e804f127311'
const SecretId = 'AKIDkUOEuK8l9HBrNhu0wd5JLAmBTXNLOKD6'
const SecretKey = 'lHeprlTAh4MRVgnkuZ6ZuBKH4iL2hKxQ'
const Bucket = 'app-1257140447'
const Region = 'ap-guangzhou'

module.exports = {
  SecretId,
  SecretKey,
  Bucket,
  Region,
  // wgt 安装包所在位置
  wgtPath: path.join(__dirname, '../', 'unpackage', 'release'),
  // apk 安装包所在位置
  apkPath: path.join(__dirname, '../', 'dist', 'release', 'apk'),
  // manifest.json 所在路径
  manifestFile: path.join(__dirname, '../', 'src', 'manifest.json'),
  // 环境变量文件夹路径，index.js 上传新版本函数也需要修改！！！
  envPath: path.join(__dirname, '../', 'src', 'config'),
  // 运行脚本
  script_openHbuilder: 'cli open',
  script_closeHbuilder: 'cli app quit',
  script_runDev: 'python ./deploy/runDev.py',
  script_buildWgt: 'cli publish --platform APP --type wgt --project okr-web',
  // 打包参数说明：https://hx.dcloud.net.cn/cli/pack?id=params
  script_buildApk: 'cli pack --project okr-web --platform android --safemode true --isconfusion true',
  // 列举云数据库
  script_listDb: 'cli cloud functions --list db --prj okr-web --provider aliyun --cloud',
  dev: {
    appName: 'OKR 开发版',
    // UNIAPP APPID
    APPID: '__UNI__41B9DE2',
    // 增量包名字
    wgtName: '__UNI__41B9DE2.wgt',
    spaceId: spaceIdByDev,
    // 切换服务空间，assignspace：服务空间 id
    cutServer: `cli cloud functions --prj okr-web --provider aliyun --assignspace ${spaceIdByDev}`,

    // 【脚本】打包上传 H5
    script_buildH5: [
      'cli publish --platform h5 --project okr-web',
      `cli hosting deploy --provider aliyun --space ${spaceIdByDev} --source ${path.join(
        __dirname,
        '../',
        'dist',
        'build',
        'web'
      )}  --prefix /`,
    ],
    // 安卓打包参数
    androidConfig: {
      // 包名
      '--android.packagename': 'uni.UNI41B9DE2',
      //安卓打包证书别名，自有证书打包填写的参数
      '--android.certalias': '__uni__41b9de2',
      //安卓打包证书文件路径，自有证书打包填写的参数
      '--android.certfile': path.join(__dirname, 'assets', 'dev_7ca889b162b1309f0665dd61d0ba7529.keystore'), // 测试证书路径
      //安卓打包证书密码，自有证书打包填写的参数
      '--android.certpassword': 'sgjnJPWx',
    },
  },
  prod: {
    appName: 'OKR',
    // UNIAPP APPID
    APPID: '__UNI__97DEADA',
    // 增量包名字
    wgtName: '__UNI__97DEADA.wgt',
    spaceId: spaceIdByProd,
    // 切换服务空间，assignspace：服务空间 id
    cutServer: `cli cloud functions --prj okr-web --provider aliyun --assignspace ${spaceIdByProd}`,

    // 【脚本】打包上传 H5
    script_buildH5: [
      'cli publish --platform h5 --project okr-web',
      `cli hosting deploy --provider aliyun --space ${spaceIdByProd} --source ${path.join(
        __dirname,
        '../',
        'dist',
        'build',
        'web'
      )}  --prefix /`,
    ],
    // script_buildH5:
    //   'cli publish --platform h5 --project okr-web --webHosting true  --provider aliyun --spaceId mp-c6815a6a-de20-45ad-a345-2e804f127311',
    // 安卓打包参数
    androidConfig: {
      // 包名
      '--android.packagename': 'uni.UNI97DEADA',
      //安卓打包证书别名，自有证书打包填写的参数
      '--android.certalias': '__uni__97deada',
      //安卓打包证书文件路径，自有证书打包填写的参数
      '--android.certfile': path.join(__dirname, 'assets', '7a23b34ef797a72cb0670d8f1617907f.keystore'), // 正式证书路径
      //安卓打包证书密码，自有证书打包填写的参数
      '--android.certpassword': '9j6FpEgy',
    },
  },
}

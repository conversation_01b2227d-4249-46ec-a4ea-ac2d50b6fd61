<template>
  <view class="pages">
    <uni-calendar
      class="uni-calendar--hook"
      :selected="info.selected"
      :show-month="true"
      @change="change"
      @monthSwitch="monthSwitch"
    />
    <button style="margin-top: 10px; width: 100%" type="primary" @click="onEdit">编辑</button>
  </view>
  <z-habit-card v-model:visible="habitVisible" :habit="curHabit" @onUpdate="onUpdateHabit"></z-habit-card>
  <!-- 	<uni-popup ref="popup" @maskClick="popup.close()">
		<view class="habit-content">
			<<editor id="editor" class="ql-container" placeholder="开始输入..." @ready="onEditorReady"></editor> id="editor" class="ql-container" placeholder="开始输入..." @ready="onEditorReady"></editor>
			<button @click="addContent">提交</button>
		</view>
	</uni-popup> -->
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue'
import { getRoute, formatDate } from '@/common/utils.js'
const habitVisible = ref(false)
const popup = ref()
const editorCtx = ref()
const db = uniCloud.database()
const info = reactive({
  lunar: true,
  range: true,
  insert: false,
  selected: [
    {
      date: formatDate(),
    },
  ],
})
const formData = ref({
  title: '',
  type: 0,
  label_id: '',
})
const curHabit = ref()
const onUpdateHabit = () => {
  init()
}
const change = async ({ fulldate }) => {
  uni.showLoading({
    title: '请求中',
  })
  const startTime = new Date(fulldate + ' 00:00:00').getTime()
  const endTime = new Date(fulldate + ' 23:59:59').getTime()
  const dbCmd = db.command
  const { result } = await db
    .collection('habit-clocking')
    .where({
      user_id: db.getCloudEnv('$cloudEnv_uid'),
      clocking_date: dbCmd.gte(startTime).and(dbCmd.lte(endTime)),
      habit_id: getApp().globalData.urlParams.id,
    })
    .get({ getOne: true })
  uni.hideLoading()
  const h = result.data
  console.log(h)
  curHabit.value = {
    content: h ? h.content : '',
    isClocking: !!h,
    clocking_id: h ? h._id : '',
    habit_id: getApp().globalData.urlParams.id,
    clocking_date: startTime,
  }

  habitVisible.value = true
}

const onEdit = () => {
  const { id } = getApp().globalData.urlParams
  uni.navigateTo({
    url: `/pages/habit/edit?id=${id}`,
  })
}
const goPage = (path) => {
  uni.navigateTo({
    url: path,
  })
}

// 编辑器初始化
const onEditorReady = () => {
  // #ifdef MP-BAIDU
  editorCtx.value = requireDynamicLib('editorLib').createEditorContext('editor')
  // #endif

  // #ifdef APP-PLUS || H5 ||MP-WEIXIN
  uni
    .createSelectorQuery()
    .select('#editor')
    .context((res) => {
      editorCtx.value = res.context
    })
    .exec()
  // #endif
}

// 添加日志
const addContent = () => {
  uni.showLoading({
    title: '提交中',
  })
  editorCtx.value.getContents({
    success({ html }) {
      db.collection('habit-clocking')
        .where(`_id == '${habitDetail.value._id}'`)
        .update({
          content: html,
        })
        .then(() => {
          uni.hideLoading()
          // getHabitList()
          popup.value.close()
        })
    },
  })
}

let habitId = ''
const init = () => {
  const { id } = getApp().globalData.urlParams

  db.collection('habit-clocking')
    .where(`user_id==$cloudEnv_uid && habit_id=='${id}'`)
    .get()
    .then(({ result }) => {
      const dateList = result.data.map((e) => {
        return {
          date: formatDate(e.clocking_date),
        }
      })
      info.selected = [...new Set(dateList)]
      console.log(info.selected)
    })

  // 编辑状态
  if (id) {
    habitId = id
    db.collection('habit')
      .where({
        _id: id,
      })
      .get()
      .then(({ result }) => {
        formData.value = result.data[0]
        uni.setNavigationBarTitle({
          title: formData.value.title,
        })
      })
  }
}
onMounted(init)
</script>

<style lang="scss" scoped>
.habit-content {
  width: 300px;
  height: auto;
  padding: 20px;
  border-radius: 8px;
  background-color: #fff;
}
</style>

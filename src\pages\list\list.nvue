<template>
	<view class="pages">
		1231233333
		<view class="">
			dsfasfasdf
		</view>
		<view @click="a">
			sdfsafasfasfasdf
		</view>
	</view>
</template>

<script>
	let cdbRef;
	import statusBar from "@/uni_modules/uni-nav-bar/components/uni-nav-bar/uni-status-bar";

	import Gps from '@/uni_modules/json-gps/js_sdk/gps.js';
	const gps = new Gps(),
		db = uniCloud.database();

	export default {
		components: {
			statusBar
		},
		computed: {
			
		},
		data() {
			return {
				where: '"article_status" == 1',
				keyword: "",
				showRefresh: false,
				listHight: 0
			}
		},
		watch: {
			
		},
		async onReady() {
			
		},
		async onShow() {
			
		},
		methods: {
			a(){
				uni.showToast({
					title:'abccc'
				})
				uni.navigateTo({
					url: `/pages/habit/stats`
				});
			}
		},
	}
</script>

<style scoped>
	/* #ifndef APP-NVUE */
	view {
		display: flex;
		box-sizing: border-box;
		flex-direction: column;
	}

	/* #endif */
	.pages {
		background-color: #FFFFFF;
	}

	.avatar {
		width: 200rpx;
		height: 200rpx;
		margin-right: 10rpx;
	}

	.main {
		justify-content: space-between;
		flex: 1;
	}

	.title {
		font-size: 16px;
	}

	.info {
		flex-direction: row;
		justify-content: space-between;
	}

	.author,
	.last_modify_date {
		font-size: 14px;
		color: #999999;
	}

	.uni-search-box {
		background-color: #FFFFFF;
		position: sticky;
		height: 50px;
		top: 0;
		left: 0;
		/* #ifndef APP-PLUS */
		z-index: 9;
		/* #endif */
		/* #ifdef MP-WEIXIN */
		width: 580rpx;
		/* #endif */
	}

	.cover-search-bar {
		height: 50px;
		position: relative;
		top: -50px;
		margin-bottom: -50px;
		/* #ifndef APP-NVUE */
		z-index: 999;
		/* #endif */
	}
</style>
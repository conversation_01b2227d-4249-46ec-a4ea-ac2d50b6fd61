# H5 录音功能迁移

## 任务描述

将现有的`useRecord.ts`中的 H5 录音功能迁移到新的`useRecordH5.ts`文件中，并调整代码适配新的类型定义，确保功能完整性和兼容性。

## 所属功能模块

录音功能兼容 App 端

## 技术实现详情

### 1. 创建 H5 录音实现文件

创建`src/hooks/useRecordH5.ts`文件，将现有的录音逻辑迁移并适配新的接口：

```typescript
import { ref, onUnmounted, Ref } from 'vue'
import RecordRTC, { StereoAudioRecorder } from 'recordrtc'
import { UseRecordOptions, UseRecordReturn, RecordResult } from './types/record'
import { RecordError } from './utils/recordErrors'

/**
 * H5 环境下的录音 Hook 实现
 * @param options 录音配置选项
 * @returns 录音控制对象
 */
export default function useRecordH5(options?: UseRecordOptions): UseRecordReturn {
  // 默认录音配置
  const defaultOptions: UseRecordOptions = {
    sampleRate: 44100,
    numberOfAudioChannels: 1,
    desiredSampleRate: 16000,
    mimeType: 'audio/wav',
    maxDuration: 60000, // 默认 60 秒
    checkForInactiveTracks: true,
    disableLogs: true,
  }

  const mergedOptions = { ...defaultOptions, ...options }

  // 录音状态
  const isRecording = ref<boolean>(false)
  const isPaused = ref<boolean>(false)
  const duration = ref<number>(0)
  const volume = ref<number>(0)
  const recordBlob = ref<Blob | null>(null)
  const recordURL = ref<string>('')

  // 录音器和音频流
  let recorder: RecordRTC | null = null
  let audioStream: MediaStream | null = null
  let audioContext: AudioContext | null = null
  let analyser: AnalyserNode | null = null
  let durationTimer: number | null = null
  let volumeTimer: number | null = null

  // 开始录音
  const startRecording = async (): Promise<void> => {
    try {
      // 如果已经在录音，先停止之前的录音
      if (isRecording.value) {
        await stopRecording()
      }

      // 重置状态
      resetState()

      // 请求音频流
      audioStream = await navigator.mediaDevices.getUserMedia({ audio: true }).catch((error) => {
        if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
          throw RecordError.permissionDenied(error.message)
        }
        throw RecordError.initializationFailed(error.message)
      })

      // 创建录音实例
      recorder = new RecordRTC(audioStream, {
        type: 'audio',
        mimeType: mergedOptions.mimeType,
        sampleRate: mergedOptions.sampleRate,
        desiredSampleRate: mergedOptions.desiredSampleRate,
        numberOfAudioChannels: mergedOptions.numberOfAudioChannels,
        checkForInactiveTracks: mergedOptions.checkForInactiveTracks,
        disableLogs: mergedOptions.disableLogs,
        recorderType: StereoAudioRecorder,
        timeSlice: 1000, // 每秒触发一次 ondataavailable 事件
        ondataavailable: () => {
          // 可以在这里获取实时数据
        },
      })

      // 设置音频分析器
      setupAudioAnalyser()

      // 开始录音
      recorder.startRecording()

      // 更新状态
      isRecording.value = true
      isPaused.value = false

      // 开始计时
      startTimers()

      // 设置最大录音时长
      if (mergedOptions.maxDuration) {
        setTimeout(() => {
          if (isRecording.value && !isPaused.value) {
            stopRecording().catch(console.error)
          }
        }, mergedOptions.maxDuration)
      }
    } catch (error) {
      console.error('启动录音失败：', error)
      throw error instanceof RecordError ? error : RecordError.initializationFailed(error.message)
    }
  }

  // 暂停录音
  const pauseRecording = (): void => {
    if (recorder && isRecording.value && !isPaused.value) {
      recorder.pauseRecording()
      isPaused.value = true

      // 暂停计时器
      stopTimers()
    }
  }

  // 继续录音
  const resumeRecording = (): void => {
    if (recorder && isRecording.value && isPaused.value) {
      recorder.resumeRecording()
      isPaused.value = false

      // 继续计时
      startTimers()
    }
  }

  // 停止录音
  const stopRecording = (): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      if (!recorder || !isRecording.value) {
        reject(new RecordError(RecordError.recordingFailed('没有正在进行的录音')))
        return
      }

      // 停止计时器
      stopTimers()

      const recorderInstance = recorder // 保存当前 recorder 引用

      // 停止录音
      recorderInstance.stopRecording(() => {
        try {
          // 获取录音结果
          const blob = recorderInstance.getBlob()
          recordBlob.value = blob

          // 创建音频 URL
          if (recordURL.value) {
            URL.revokeObjectURL(recordURL.value)
          }
          recordURL.value = URL.createObjectURL(blob)

          // 更新状态
          isRecording.value = false
          isPaused.value = false

          // 停止音频流
          stopAudioStream()

          resolve(blob)
        } catch (error) {
          reject(RecordError.recordingFailed(error.message))
        }
      })
    })
  }

  // 播放录音
  const playRecording = (): void => {
    if (recordURL.value) {
      const audio = new Audio(recordURL.value)
      audio.play().catch((error) => {
        console.error('播放录音失败：', error)
      })
    }
  }

  // 获取音频波形数据
  const getAudioWaveform = (): Uint8Array | null => {
    if (!analyser) return null

    const dataArray = new Uint8Array(analyser.frequencyBinCount)
    analyser.getByteTimeDomainData(dataArray)
    return dataArray
  }

  // 取消录音
  const cancelRecording = (): void => {
    if (recorder && isRecording.value) {
      // 停止计时器
      stopTimers()

      // 停止录音但不保存
      recorder.stopRecording(() => {
        // 重置状态
        resetState()

        // 停止音频流
        stopAudioStream()
      })
    }
  }

  // 重置状态
  const resetState = (): void => {
    isRecording.value = false
    isPaused.value = false
    duration.value = 0
    volume.value = 0
    recordBlob.value = null

    if (recordURL.value) {
      URL.revokeObjectURL(recordURL.value)
      recordURL.value = ''
    }
  }

  // 设置音频分析器
  const setupAudioAnalyser = (): void => {
    if (!audioStream) return

    try {
      // 创建音频上下文
      audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

      // 创建分析器
      analyser = audioContext.createAnalyser()
      analyser.fftSize = 256

      // 连接音频流
      const source = audioContext.createMediaStreamSource(audioStream)
      source.connect(analyser)

      // 不连接到扬声器，避免回声
      // analyser.connect(audioContext.destination)
    } catch (error) {
      console.error('设置音频分析器失败：', error)
    }
  }

  // 停止音频流
  const stopAudioStream = (): void => {
    if (audioStream) {
      audioStream.getTracks().forEach((track) => track.stop())
      audioStream = null
    }

    if (audioContext) {
      audioContext.close().catch(console.error)
      audioContext = null
      analyser = null
    }
  }

  // 开始计时器
  const startTimers = (): void => {
    // 录音时长计时器
    if (!durationTimer) {
      durationTimer = window.setInterval(() => {
        duration.value += 100
      }, 100)
    }

    // 音量计算计时器
    if (!volumeTimer && analyser) {
      volumeTimer = window.setInterval(() => {
        if (analyser) {
          const dataArray = new Uint8Array(analyser.frequencyBinCount)
          analyser.getByteFrequencyData(dataArray)

          // 计算平均音量
          const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length

          // 将音量范围从 0-255 映射到 0-100
          volume.value = Math.round((average / 255) * 100)
        }
      }, 100)
    }
  }

  // 停止计时器
  const stopTimers = (): void => {
    if (durationTimer) {
      clearInterval(durationTimer)
      durationTimer = null
    }

    if (volumeTimer) {
      clearInterval(volumeTimer)
      volumeTimer = null
    }
  }

  // 清理资源
  const destroy = (): void => {
    // 停止录音
    if (isRecording.value) {
      cancelRecording()
    }

    // 停止计时器
    stopTimers()

    // 清理 URL
    if (recordURL.value) {
      URL.revokeObjectURL(recordURL.value)
      recordURL.value = ''
    }

    // 停止音频流
    stopAudioStream()

    // 销毁录音器
    if (recorder) {
      recorder.destroy()
      recorder = null
    }
  }

  // 组件卸载时自动清理
  onUnmounted(() => {
    destroy()
  })

  return {
    // 状态
    isRecording: isRecording as Readonly<Ref<boolean>>,
    isPaused: isPaused as Readonly<Ref<boolean>>,
    duration: duration as Readonly<Ref<number>>,
    volume: volume as Readonly<Ref<number>>,
    recordBlob: recordBlob as Readonly<Ref<RecordResult | null>>,
    recordURL: recordURL as Readonly<Ref<string>>,

    // 方法
    startRecording,
    pauseRecording,
    resumeRecording,
    stopRecording,
    playRecording,
    getAudioWaveform,
    cancelRecording,
    destroy,
  }
}
```

### 2. 添加单元测试

创建`tests/unit/useRecordH5.test.js`文件，用于测试 H5 录音功能：

```javascript
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import useRecordH5 from '@/hooks/useRecordH5'

// 模拟 mediaDevices API
const mockMediaStream = {
  getTracks: jest.fn().mockReturnValue([{ stop: jest.fn() }]),
}

// 模拟 RecordRTC
jest.mock('recordrtc', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      startRecording: jest.fn(),
      pauseRecording: jest.fn(),
      resumeRecording: jest.fn(),
      stopRecording: jest.fn((cb) => cb()),
      getBlob: jest.fn(() => new Blob(['test'], { type: 'audio/wav' })),
      destroy: jest.fn(),
    })),
    StereoAudioRecorder: jest.fn(),
  }
})

describe('useRecordH5', () => {
  // 模拟 AudioContext 和 AnalyserNode
  global.AudioContext = jest.fn().mockImplementation(() => ({
    createAnalyser: jest.fn().mockReturnValue({
      fftSize: 0,
      frequencyBinCount: 128,
      getByteFrequencyData: jest.fn(),
      getByteTimeDomainData: jest.fn((array) => {
        for (let i = 0; i < array.length; i++) {
          array[i] = Math.floor(Math.random() * 255)
        }
      }),
    }),
    createMediaStreamSource: jest.fn().mockReturnValue({
      connect: jest.fn(),
    }),
    close: jest.fn().mockResolvedValue(undefined),
  }))

  // 模拟媒体设备API
  global.navigator.mediaDevices = {
    getUserMedia: jest.fn().mockResolvedValue(mockMediaStream),
  }

  // 模拟URL对象
  global.URL.createObjectURL = jest.fn((blob) => `blob:${blob}`)
  global.URL.revokeObjectURL = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('应该初始化所有状态为默认值', async () => {
    const wrapper = mount({
      template: '<div></div>',
      setup() {
        const record = useRecordH5()
        return { record }
      },
    })

    expect(wrapper.vm.record.isRecording.value).toBe(false)
    expect(wrapper.vm.record.isPaused.value).toBe(false)
    expect(wrapper.vm.record.duration.value).toBe(0)
    expect(wrapper.vm.record.volume.value).toBe(0)
    expect(wrapper.vm.record.recordBlob.value).toBe(null)
    expect(wrapper.vm.record.recordURL.value).toBe('')
  })

  test('startRecording 应该正确初始化录音', async () => {
    const wrapper = mount({
      template: '<div></div>',
      setup() {
        const record = useRecordH5()
        return { record }
      },
    })

    await wrapper.vm.record.startRecording()

    expect(navigator.mediaDevices.getUserMedia).toHaveBeenCalledWith({ audio: true })
    expect(wrapper.vm.record.isRecording.value).toBe(true)
    expect(wrapper.vm.record.isPaused.value).toBe(false)
  })

  test('stopRecording 应该停止录音并返回 Blob', async () => {
    const wrapper = mount({
      template: '<div></div>',
      setup() {
        const record = useRecordH5()
        return { record }
      },
    })

    await wrapper.vm.record.startRecording()
    const blob = await wrapper.vm.record.stopRecording()

    expect(blob).toBeInstanceOf(Blob)
    expect(wrapper.vm.record.isRecording.value).toBe(false)
    expect(wrapper.vm.record.recordBlob.value).toEqual(blob)
    expect(wrapper.vm.record.recordURL.value).toBeTruthy()
  })

  test('pauseRecording 和 resumeRecording 应该正确更新状态', async () => {
    const wrapper = mount({
      template: '<div></div>',
      setup() {
        const record = useRecordH5()
        return { record }
      },
    })

    await wrapper.vm.record.startRecording()
    wrapper.vm.record.pauseRecording()

    expect(wrapper.vm.record.isPaused.value).toBe(true)

    wrapper.vm.record.resumeRecording()

    expect(wrapper.vm.record.isPaused.value).toBe(false)
  })

  test('cancelRecording 应该重置所有状态', async () => {
    const wrapper = mount({
      template: '<div></div>',
      setup() {
        const record = useRecordH5()
        return { record }
      },
    })

    await wrapper.vm.record.startRecording()
    wrapper.vm.record.cancelRecording()

    expect(wrapper.vm.record.isRecording.value).toBe(false)
    expect(wrapper.vm.record.isPaused.value).toBe(false)
    expect(wrapper.vm.record.duration.value).toBe(0)
    expect(wrapper.vm.record.recordBlob.value).toBe(null)
    expect(wrapper.vm.record.recordURL.value).toBe('')
  })

  test('getAudioWaveform 应该返回音频波形数据', async () => {
    const wrapper = mount({
      template: '<div></div>',
      setup() {
        const record = useRecordH5()
        return { record }
      },
    })

    await wrapper.vm.record.startRecording()
    const waveform = wrapper.vm.record.getAudioWaveform()

    expect(waveform).toBeInstanceOf(Uint8Array)
    expect(waveform.length).toBe(128) // 根据模拟的 frequencyBinCount
  })

  test('destroy 应该清理所有资源', async () => {
    const wrapper = mount({
      template: '<div></div>',
      setup() {
        const record = useRecordH5()
        return { record }
      },
    })

    await wrapper.vm.record.startRecording()
    wrapper.vm.record.destroy()

    expect(wrapper.vm.record.isRecording.value).toBe(false)
    expect(mockMediaStream.getTracks()[0].stop).toHaveBeenCalled()
  })
})
```

### 3. 更新相关文档

更新录音功能的相关文档，包括接口文档和使用示例：

````markdown
# H5 录音功能使用指南

## 基本用法

```vue
<script setup>
import { useRecordH5 } from '@/hooks/useRecordH5'

const {
  isRecording,
  isPaused,
  duration,
  volume,
  recordURL,
  startRecording,
  pauseRecording,
  resumeRecording,
  stopRecording,
  cancelRecording,
  getAudioWaveform,
} = useRecordH5({
  maxDuration: 30000, // 最大录音时长 30 秒
  mimeType: 'audio/wav', // 音频格式
})

// 开始录音
const handleStartRecording = async () => {
  try {
    await startRecording()
    console.log('录音已开始')
  } catch (error) {
    console.error('录音启动失败', error)
  }
}

// 停止录音
const handleStopRecording = async () => {
  try {
    const blob = await stopRecording()
    console.log('录音已停止', blob)

    // 可以将 blob 上传到服务器或者进行其他处理
  } catch (error) {
    console.error('录音停止失败', error)
  }
}
</script>
```
````

## 高级用法

### 音频波形可视化

```vue
<template>
  <canvas ref="waveformCanvas" width="300" height="100"></canvas>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRecordH5 } from '@/hooks/useRecordH5'

const waveformCanvas = ref(null)
let animationFrame = null

const { isRecording, getAudioWaveform, startRecording, stopRecording } = useRecordH5()

// 绘制波形
const drawWaveform = () => {
  if (!isRecording.value || !waveformCanvas.value) {
    return
  }

  const canvas = waveformCanvas.value
  const ctx = canvas.getContext('2d')
  const waveform = getAudioWaveform()

  if (!waveform) {
    animationFrame = requestAnimationFrame(drawWaveform)
    return
  }

  // 清除画布
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // 设置绘制样式
  ctx.lineWidth = 2
  ctx.strokeStyle = '#4CAF50'
  ctx.beginPath()

  const sliceWidth = canvas.width / waveform.length
  let x = 0

  for (let i = 0; i < waveform.length; i++) {
    const v = waveform[i] / 128.0 // 将 0-255 转换为 0-2
    const y = (v * canvas.height) / 2

    if (i === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }

    x += sliceWidth
  }

  ctx.stroke()
  animationFrame = requestAnimationFrame(drawWaveform)
}

// 开始录音并绘制波形
const startRecordingWithWaveform = async () => {
  await startRecording()
  drawWaveform()
}

// 停止录音和波形绘制
const stopRecordingWithWaveform = async () => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
    animationFrame = null
  }
  return await stopRecording()
}

onUnmounted(() => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }
})
</script>
```

```

## 验收标准
1. 所有原有的录音功能在新文件中正常工作
2. 适配了新的类型定义和接口规范
3. 增强了错误处理和异常情况的管理
4. 单元测试全部通过，覆盖主要功能点
5. 文档更新完整，包含基础和高级用法示例
6. 代码风格统一，注释清晰

## 依赖关系
- 上游依赖: task-record-01 (类型定义与接口设计)
- 下游依赖: task-record-04 (入口文件与条件编译)

## 优先级
中 - 作为主要功能实现之一

## 状态追踪
- [ ] 已创建useRecordH5.ts文件
- [ ] 已完成代码迁移和适配
- [ ] 已创建单元测试
- [ ] 已更新相关文档
- [ ] 已完成代码审查
- [ ] 已通过验收测试
```

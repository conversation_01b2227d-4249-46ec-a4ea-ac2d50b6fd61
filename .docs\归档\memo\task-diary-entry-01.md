# 任务：日记时间轴UI开发

- **所属功能模块**: `diary-entry`
- **任务ID**: `task-diary-entry-01`
- **优先级**: 中

## 任务描述
开发用于展示当天多次日记输入的时间轴视图。这包括两个核心的局部组件：一个用于容纳所有日记条目的时间轴容器（`l-diary-timeline`），以及一个用于展示单条日记内容的卡片（`l-diary-card`）。

## 技术实现详情
1.  **`l-diary-card` 组件**:
    - 用于显示单条日记条目。
    - **Props**: 接收一个日记条目对象（如 `entry`），包含 `content`, `type`, `timestamp`, `media_attachments` 等。
    - **UI**:
        - 左侧显示时间戳（格式 `HH:mm`）。
        - 右侧展示内容，包括文本、图片等。
        - 根据内容类型（`type`）可显示不同图标或样式。
        - 初始阶段使用模拟数据进行开发。

2.  **`l-diary-timeline` 组件**:
    - 用于展示一整天的日记条目列表。
    - **Props**: 接收一个日记条目数组（如 `entries`）。
    - **UI**:
        - 垂直布局，将多个 `l-diary-card` 组件按时间顺序排列。
        - 组件之间用线条连接，形成时间轴外观。
        - 当天无内容时，应有优雅的空状态提示。
        - 初始阶段使用模拟数据进行开发。

## 验收标准
- `l-diary-card` 组件能根据传入的模拟数据正确渲染单条日记。
- `l-diary-timeline` 组件能正确渲染模拟的日记条目数组，形成时间轴视图。
- 组件代码结构清晰，Props 定义明确。
- 页面布局响应式，在不同尺寸屏幕上表现良好。
- 空状态显示正常。

## 依赖关系
- `task-diary-data-01`: 依赖其定义的 `DiaryContent.entries` 数据结构作为组件 Props 的设计依据。

## 状态追踪
- 未开始 
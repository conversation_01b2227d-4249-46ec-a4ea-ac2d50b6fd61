/**
 * 加解密类
 */
const CryptoJS = require("../utils/crypto-js-4.1.1.min.js")
export class EncryptManager {
  constructor() {}

  /**
   * md5加密
   * @param str 加密字符串
   */
  md5 = (str) => {
    // return md5(str);
    return CryptoJS.MD5(str).toString()
  }

  /**
   * AES加密
   * @param str 要加密的字符串
   * @param key 秘钥
   */
  aesEncode = (str, key) => {
    let encrypt = CryptoJS.AES.encrypt(str, CryptoJS.enc.Utf8.parse(key), {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    }).toString();
    return encrypt;
  }

  /**
   * AES解密
   */
  aesDecode = (str, key) => {
    let decrypt = CryptoJS.AES.decrypt(str, CryptoJS.enc.Utf8.parse(key), {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    }).toString(CryptoJS.enc.Utf8);
    return decrypt;
  }

  /**
   * base64加密
   */
  base64Encode = (str) => {
    let wordArray = CryptoJS.enc.Utf8.parse(str);
    let encrypt = CryptoJS.enc.Base64.stringify(wordArray);
    return encrypt;
  }

  /**
   * base64解密
   */
  base64Decode = (str) => {
    return CryptoJS.enc.Base64.parse(str).toString(CryptoJS.enc.Utf8);
  }

  /**
   * DES加密
   */
  desEncode = (str, key) => {
    let keyHex = CryptoJS.enc.Utf8.parse(key)
    let option = {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    }
    let encrypted = CryptoJS.DES.encrypt(str, keyHex, option)
    return encrypted.toString()
  }

  /**
   * DES解密
   */
  desDecode = (str, key) => {
    let keyHex = CryptoJS.enc.Utf8.parse(key);
    let options = {
      ciphertext: CryptoJS.enc.Base64.parse(str)
    };
    let mode = {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    };
    let decrypted = CryptoJS.DES.decrypt(options, keyHex, mode)
    return decrypted.toString(CryptoJS.enc.Utf8)
  }

  /**
   * RC4加密
   */
  rc4Encode = (str, key) => {
    return CryptoJS.RC4.encrypt(str, key).toString();
  }

  /**
   * RC4解密
   */
  rc4Decode = (str, key) => {
    return CryptoJS.RC4.decrypt(str, key).toString(CryptoJS.enc.Utf8);
  }

  /**
   * rabbit加密
   */
  rabbitEncode = (str, key) => {
    return CryptoJS.Rabbit.encrypt(str, key).toString();
  }

  /**
   * rabbit解密
   */
  rabbitDecode = (str, key) => {
    return CryptoJS.Rabbit.decrypt(str, key).toString(CryptoJS.enc.Utf8);
  }

  /**
   * tripleDES加密
   */
  tripleDESEncode = (str, key) => {
    return CryptoJS.TripleDES.encrypt(str, key).toString();
  }

  /**
   * tripleDES解密
   */
  tripleDESDecode = (str, key) => {
    return CryptoJS.TripleDES.decrypt(str, key).toString(CryptoJS.enc.Utf8);
  }

  /**
   * sha1
   */
  sha1 = (str) => {
    return CryptoJS.SHA1(str).toString();
  }

  /**
   * sha224
   */
  sha224 = (str) => {
    return CryptoJS.SHA224(str).toString()
  }

  /**
   * sha256
   */
  sha256 = (str) => {
    return CryptoJS.SHA256(str).toString()
  }

  /**
   * sha384
   */
  sha384 = (str) => {
    return CryptoJS.SHA384(str).toString()
  }

  /**
   * sha512
   */
  sha512 = (str) => {
    return CryptoJS.SHA512(str).toString()
  }

  /**
   * hmacSha1
   */
  hmacSha1 = (str, key) => {
    return CryptoJS.HmacSHA1(str, key).toString()
  }

  /**
   * hmacSha256
   */
  hmacSha256 = (str, key) => {
    return CryptoJS.HmacSHA256(str, key).toString()
  }

  /**
   * hmacSha512
   */
  hmacSha512 = (str, key) => {
    return CryptoJS.HmacSHA512(str, key).toString()
  }

  /**
   * hmacMd5
   */
  hmacMd5 = (str, key) => {
    return CryptoJS.HmacMD5(str, key).toString()
  }
}
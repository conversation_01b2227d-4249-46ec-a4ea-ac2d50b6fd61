<template>
  <view class="diary-page">
    <view class="status-bar-placeholder"></view>
    <!-- 日历区域 -->
    <view class="calendar-section">
      <z-calendar
        ref="cRef"
        @on-change="onChangeWeek"
        @on-show-today="(isShow) => (showTodayBtn = isShow)"
        :data-dates="hasDiaryList.map((item) => item.date)"
      />
    </view>

    <!-- 展示模式 -->
    <view class="content-area">
      <view v-if="memoForm.content" class="card">
        <view class="diary-title" @click="showTitleSelector">{{ title || dayText.date + ' ' + dayText.week }}</view>

        <!-- AI 分析部分 -->
        <view v-for="(item, index) in aiList" :key="index" class="analysis-section">
          <view class="section-title" @click="toggleAiSection(index)">
            <i class="fas fa-lightbulb"></i>
            <text>{{ item.tag }}</text>
          </view>
          <view
            v-if="aiSectionVisible[index]"
            class="section-content animate-fade-in-down"
            v-html="renderMarkdown(item.content)"
          ></view>
        </view>

        <!-- 日记内容 -->
        <view class="diary-content-section">
          <view class="section-title" @click="showDiaryContent = !showDiaryContent">
            <i class="fas fa-book"></i>
            <text>日记内容</text>
            <view class="toggle-btn">
              {{ showDiaryContent ? '收起' : '展开' }}
              <i class="fas" :class="showDiaryContent ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
            </view>
            <view v-if="showDiaryContent && polishContent" class="toggle-content-btn" @click.stop="toggleContentType">
              {{ isOriginalContent ? '查看润色' : '查看原文' }}
            </view>
          </view>
          <view
            v-if="showDiaryContent"
            class="section-content animate-fade-in-down"
            v-html="renderMarkdown(displayContent)"
          ></view>
          <view v-else class="section-placeholder">点击"展开"查看详细内容</view>
        </view>
      </view>
      <view v-else class="empty-state">
        <u-empty text="今天还没有记录" mode="data" margin-top="60"></u-empty>
        <view class="empty-hint" @click="goToFlow">点击右下角按钮开始写日记</view>
      </view>
    </view>

    <!-- 编辑按钮 -->
    <view class="add-diary-btn" @click="onEdit">
      <i class="fas fa-plus"></i>
    </view>

    <!-- 标题选择弹窗 -->
    <u-popup v-model="showTitlePopup" mode="center" border-radius="14" width="80%" :closeable="true">
      <view class="title-popup-content">
        <view class="popup-header">选择标题</view>
        <view class="title-tags">
          <view
            v-for="(item, index) in titleOptions"
            :key="index"
            class="title-tag"
            :class="{ active: item === selectedTitle }"
            @click="selectTitle(item)"
          >
            {{ item }}
          </view>
        </view>
        <view class="popup-footer">
          <view class="confirm-btn" @click="confirmTitleSelection">确认</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script setup>
import { renderMarkdown } from '@/utils/tools'
import { ref, onMounted, computed, defineComponent, reactive, onUnmounted, inject, watch } from 'vue'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { getDiaryApi, getDiaryListApi, getTagListApi, updateDiaryApi } from '@/api/memo'
import db from '@/api/database/index'
import useIsPC from '@/hooks/useIsPC'
import request from '@/utils/request'
import { getUrlParams } from '@/utils/tools'

// 注入刷新触发器
const reloadTrigger = inject('reloadTrigger', ref({count: 0, tabKey: ''}))

// 设置组件名称，符合 Vue 组件命名规范
defineComponent({
  name: 'MemoDiary',
})

// 判断当前页面是否激活
const isActiveTab = computed(() => {
  return reloadTrigger.value.tabKey === "diary"
})

// 设置 dayjs 为中文
dayjs.locale('zh-cn')

const hasDiaryList = ref([])
const curDay = ref('')
const cRef = ref()
const showInputPopup = ref(false)
const showDiaryContent = ref(false) // 控制日记内容显示/隐藏
const aiSectionVisible = reactive({}) // 控制 AI 分析部分的显示/隐藏
const isOriginalContent = ref(true) // 控制显示原文或润色内容，默认显示原文
const polishContent = ref('') // 保存润色后的内容

// 标题选择相关
const showTitlePopup = ref(false)
const titleOptions = ref([])
const selectedTitle = ref('')
const originalTitle = ref('') // 保存原始的完整标题字符串

// 当前显示的内容（原文或润色）
const displayContent = computed(() => {
  return isOriginalContent.value ? memoForm.value.content : polishContent.value
})

// 分离展示和编辑的数据
const memoForm = ref({}) // 用于展示
const editForm = ref({}) // 用于编辑

const sys = useIsPC()
const aiList = ref([])
// 页面数据
const editorCtx = ref(null)
const title = ref('')

// 添加轮询相关变量
// 记录当前轮询任务的标识符，用于在需要时取消
const currentPollTimer = ref(null)
// 记录当前轮询的日记 ID，用于验证轮询结果是否匹配当前查看的日记
const currentPollingDiaryId = ref(null)
// 记录当前查看的日期，用于验证轮询结果是否匹配当前查看的日期
const currentViewDate = ref(null)

const goToFlow = (category) => {
  uni.navigateTo({
    url: `/pages/speak/speak`,
  })
}

// 切换 AI 分析部分的显示/隐藏
const toggleAiSection = (index) => {
  aiSectionVisible[index] = !aiSectionVisible[index]
}

// 切换内容类型（原文/润色）
const toggleContentType = () => {
  isOriginalContent.value = !isOriginalContent.value
}

// 显示标题选择器
const showTitleSelector = () => {
  // 检查标题是否包含逗号，表示有多个选项
  if (title.value && title.value.includes(',') && memoForm.value.aiAnalysis) {
    // 保存原始标题
    originalTitle.value = title.value

    // 分割标题并去除空白
    titleOptions.value = title.value
      .split(',')
      .map((item) => item.trim())
      .filter((item) => item)

    // 默认选中当前显示的标题
    selectedTitle.value = titleOptions.value[0]

    // 显示选择弹窗
    showTitlePopup.value = true
  }
}

// 选择标题
const selectTitle = (title) => {
  selectedTitle.value = title
}

// 确认标题选择
const confirmTitleSelection = async () => {
  if (selectedTitle.value && memoForm.value._id) {
    try {
      // 获取当前日记数据
      const diary = await getDiaryApi(curDay.value)

      if (diary && diary.aiAnalysis) {
        // 解析 aiAnalysis JSON
        const aiAnalysisObj = JSON.parse(diary.aiAnalysis)

        // 更新 title 字段
        aiAnalysisObj.title = selectedTitle.value

        // 更新日记的 aiAnalysis 字段
        await updateDiaryApi(memoForm.value._id, {
          aiAnalysis: JSON.stringify(aiAnalysisObj),
        })

        // 更新当前显示的标题
        title.value = selectedTitle.value

        // 关闭弹窗
        showTitlePopup.value = false

        // 提示更新成功
        uni.showToast({
          title: '标题已更新',
          icon: 'success',
        })
      } else {
        throw new Error('日记数据或 AI 分析数据不存在')
      }
    } catch (error) {
      console.error('更新标题失败', error)
      uni.showToast({
        title: '更新失败',
        icon: 'error',
      })
    }
  }
}

const onChangeWeek = (date) => {
  // 清除之前的轮询
  if (currentPollTimer.value) {
    clearTimeout(currentPollTimer.value)
    currentPollTimer.value = null
    currentPollingDiaryId.value = null
  }
  
  // 设置新的当前日期
  currentViewDate.value = date
  
  // 获取新日期的数据
  getDay(date)
  weekSwitch()
}

/**
 * 获取指定日期的日记数据
 * @param {string} [date=''] - 日期字符串（格式：YYYY-MM-DD）
 * @returns {Promise<void>}
 */
const getDay = async (date = '') => {
  // 取消之前的轮询（如果存在）
  if (currentPollTimer.value) {
    clearTimeout(currentPollTimer.value)
    currentPollTimer.value = null
    currentPollingDiaryId.value = null
  }
  
  // 设置当前日期
  if (!date) {
    date = dayjs().format('YYYY-MM-DD')
  }
  curDay.value = date
  currentViewDate.value = date // 记录当前查看的日期
  showDiaryContent.value = false
  isOriginalContent.value = true
  
  // 先从本地获取数据
  const localData = await getDiaryApi(curDay.value)
  
  // 重置编辑表单，避免数据混淆
  editForm.value = { content: '' }
  
  // 判断是否需要查询云端（有本地数据的情况）
  if (localData?.content) {
    // 先显示本地数据
    memoForm.value = localData
    processAndDisplayData(localData)
    
    // 查询云端数据
    try {
      const cloudResponse = await request.post('/okr/getDiaryById', {
        _id: localData._id
      })
      
      // 验证是否仍在查看相同日期的日记
      if (currentViewDate.value !== curDay.value) {
        console.log('日期已切换，停止处理旧日期的数据')
        return
      }
      
      // 检查是否需要更新本地数据
      if (cloudResponse?.data) {
        const cloudData = cloudResponse.data
        
        // 如果云端有 AI 分析结果且与本地不同，更新本地数据
        if (cloudData.aiAnalysis && (!localData.aiAnalysis || cloudData.aiAnalysis !== localData.aiAnalysis)) {
          // 更新本地数据库
          await db.table('memo').update(localData._id, {
            aiAnalysis: cloudData.aiAnalysis
          })
          
          // 更新显示
          memoForm.value = {...localData, aiAnalysis: cloudData.aiAnalysis}
          processAndDisplayData(memoForm.value)
          
          console.log('从云端更新了 AI 分析结果')
        }
        // 如果云端没有 AI 分析结果，开始轮询
        else if (!cloudData.aiAnalysis) {
          startPolling(localData._id)
        }
      }
    } catch (error) {
      console.error('查询云端数据失败', error)
    }
  } else {
    // 没有本地数据
    memoForm.value = { content: '' }
    aiList.value = []
    polishContent.value = ''
  }
}

/**
 * 当前日期显示文本计算属性
 * @returns {{date: string, week: string}}
 */
const dayText = computed(() => {
  // 获取中文星期几
  const week = dayjs(curDay.value).format('d')
  const weekText = ['日', '一', '二', '三', '四', '五', '六']

  return {
    date: dayjs(curDay.value).format('YYYY-MM-DD'),
    week: `周${weekText[week]}`,
  }
})

/** 进入编辑模式
 * 跳转到日记编辑页面
 */
const onEdit = async () => {
  uni.vibrateShort()

  // 跳转到日记编辑页面
  if (memoForm.value && memoForm.value._id) {
    // 编辑模式
    uni.navigateTo({
      url: `/pages/memo/diary_edit?id=${memoForm.value._id}&date=${curDay.value}`,
    })
  } else {
    // 新建模式
    uni.navigateTo({
      url: `/pages/memo/diary_edit?date=${curDay.value}`,
    })
  }
}

/**
 * 获取 curDay 所在月的日记列表
 */
const weekSwitch = () => {
  const currentDay = dayjs(curDay.value || dayjs().format('YYYY-MM-DD'))

  // 计算当前所选日期所在月的起始日期和结束日期
  const startDate = currentDay.startOf('month').format('YYYY-MM-DD')
  const endDate = currentDay.endOf('month').format('YYYY-MM-DD')

  getDiaryListApi({
    startDate,
    endDate,
  }).then((res) => {
    hasDiaryList.value = res.map((item) => {
      return {
        date: item.date,
      }
    })
  })
}

/**
 * 从 JSON 格式的 AI 分析结果生成数据结构
 * @param {Object} diary - 包含 aiAnalysis 字段的日记对象
 * @returns {Promise<Array>} 处理后的 AI 分析数据数组
 */
const processAiAnalysis = async (diary) => {
  if (!diary || !diary.aiAnalysis) return []

  try {
    const aiAnalysisObj = JSON.parse(diary.aiAnalysis)
    const tagList = await getTagListApi()
    const result = []

    // 处理标题
    if (aiAnalysisObj['title']) {
      result.push({
        content: aiAnalysisObj['title'],
        parentId: 'title',
        type: 'aiDiary',
        tag: '标题',
        date: diary.date,
      })
    }

    // 处理润色内容
    if (aiAnalysisObj['polish']) {
      result.push({
        content: aiAnalysisObj['polish'],
        parentId: 'polish',
        type: 'aiDiary',
        tag: '润色',
        date: diary.date,
      })
    }

    // 处理标签内容
    for (const tagId in aiAnalysisObj) {
      if (tagId === 'title' || tagId === 'polish') continue

      const tag = tagList.find((t) => t._id === tagId)
      if (tag) {
        result.push({
          content: aiAnalysisObj[tagId],
          parentId: tagId,
          tag: tag.content,
          type: 'aiDiary',
          date: diary.date,
        })
      }
    }
    // 处理评估内容
    if (aiAnalysisObj['evaluate']) {
      result.push({
        content: aiAnalysisObj['evaluate'],
        parentId: 'evaluate',
        type: 'aiDiary',
        tag: '表达评估',
        date: diary.date,
      })
    }

    return result
  } catch (e) {
    console.error('解析 AI 分析数据失败', e)
    return []
  }
}

// 轮询云端数据
const startPolling = (diaryId) => {
  const pollInterval = 1000 // 1 秒间隔
  const maxPolls = 60 // 最多轮询 60 次（约 1 分钟）
  let pollCount = 0
  
  // 设置当前轮询的日记 ID
  currentPollingDiaryId.value = diaryId
  
  const pollCloudData = async () => {
    // 验证是否应该继续轮询
    if (currentViewDate.value !== curDay.value || !currentPollingDiaryId.value) {
      console.log('日期已切换或轮询已手动取消，停止轮询')
      currentPollTimer.value = null
      currentPollingDiaryId.value = null
      return
    }
    
    try {
      const cloudResponse = await request.post('/okr/getDiaryById', {
        _id: diaryId
      })
      
      pollCount++
      
      // 如果有 AI 分析结果或达到最大轮询次数，停止轮询
      if (cloudResponse?.data?.aiAnalysis || pollCount >= maxPolls) {
        // 有结果，更新本地数据
        if (cloudResponse?.data?.aiAnalysis) {
          // 再次验证是否仍在查看相同日记
          if (diaryId === currentPollingDiaryId.value && currentViewDate.value === curDay.value) {
            await db.table('memo').update(diaryId, {
              aiAnalysis: cloudResponse.data.aiAnalysis
            })
            
            // 更新显示
            if (diaryId === memoForm.value._id) { // 确保仍在查看相同日记
              memoForm.value = {...memoForm.value, aiAnalysis: cloudResponse.data.aiAnalysis}
              processAndDisplayData(memoForm.value)
              
              uni.showToast({
                title: 'AI 分析完成',
                icon: 'success',
                duration: 1500
              })
            }
          } else {
            console.log('日记 ID 不匹配或日期已切换，不更新显示')
            // 仍然更新数据库，但不更新 UI 显示
            await db.table('memo').update(diaryId, {
              aiAnalysis: cloudResponse.data.aiAnalysis
            })
          }
        } else if (pollCount >= maxPolls) {
          console.log('轮询达到最大次数，停止轮询')
          // 可选：显示轮询超时提示
          if (currentViewDate.value === curDay.value) {
            uni.showToast({
              title: '获取 AI 分析结果超时',
              icon: 'none',
              duration: 1500
            })
          }
        }
        
        currentPollTimer.value = null
        currentPollingDiaryId.value = null
        return
      }
      
      // 继续轮询
      currentPollTimer.value = setTimeout(pollCloudData, pollInterval)
    } catch (error) {
      console.error('轮询云端数据失败', error)
      currentPollTimer.value = null
      currentPollingDiaryId.value = null
    }
  }
  
  // 开始轮询
  currentPollTimer.value = setTimeout(pollCloudData, pollInterval)
}

// 提取处理数据的逻辑为单独函数
const processAndDisplayData = (data) => {
  title.value = ''
  polishContent.value = ''
  
  // 处理 AI 分析数据
  if (data.aiAnalysis) {
    processAiAnalysis(data).then(processedData => {
      // 设置标题
      const titleItem = processedData.find(item => item.parentId === 'title')
      if (titleItem) {
        title.value = titleItem.content
      }
      
      // 设置润色内容
      const polishItem = processedData.find(item => item.parentId === 'polish')
      if (polishItem) {
        polishContent.value = polishItem.content
      }
      
      // 过滤标题和润色项，只保留标签分析内容
      aiList.value = processedData.filter(
        item => item.parentId !== 'title' && item.parentId !== 'polish'
      )
      
      // 初始化所有 AI 分析部分为收起状态
      aiList.value.forEach((_, index) => {
        aiSectionVisible[index] = false
      })
    })
  } else {
    aiList.value = []
  }
}

// 页面生命周期钩子 - 离开页面时中止轮询
onUnmounted(() => {
  if (currentPollTimer.value) {
    clearTimeout(currentPollTimer.value)
    currentPollTimer.value = null
    currentPollingDiaryId.value = null
  }
})

// 监听应用切换到后台事件，中止轮询以节省资源
// 注意：这里使用 uni-app 的生命周期钩子，不需要导入
onHide(() => {
  if (currentPollTimer.value) {
    clearTimeout(currentPollTimer.value)
    currentPollTimer.value = null
    currentPollingDiaryId.value = null
    console.log('应用进入后台，停止轮询')
  }
})
const onInit = () => {
  // 获取当前日期
  curDay.value = dayjs().format('YYYY-MM-DD')
  getDay()
  // 获取当前月日记
  weekSwitch()
}

// 监听刷新触发和激活状态
watch(() => [reloadTrigger.value.count, isActiveTab.value], ([count, isActive]) => {
  console.log('日记页面监听到刷新触发：', count, '当前页是否激活：', isActive, '当前 tab:', reloadTrigger.value.tabKey)
  if (isActive) {
    console.log('日记页面接到了刷新通知，且当前为激活页面')
    onInit()
  } else {
    console.log('日记页面接到了刷新通知，但当前不是激活页面')
  }
}, { deep: true })

onShow(onInit)
</script>

<style lang="scss" scoped>
// 页面整体样式
.diary-page {
  min-height: 100vh;
  background-color: var(--color-gray-50);
  padding-bottom: calc(var(--window-bottom) + 40rpx);
}

.status-bar-placeholder {
  height: var(--status-bar-height);
  width: 100%;
}

// 内容区域
.content-area {
  padding: 0 30rpx;
}

// 卡片样式
.card {
  background-color: var(--color-white);
  border-radius: var(--rounded-lg);
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: var(--shadow-sm);
}

// 日记标题
.diary-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--color-gray-800);
  margin-bottom: 30rpx;
  border-left: 8rpx solid var(--color-primary);
  padding-left: 20rpx;
  position: relative;
}

// 标题选择弹窗
.title-popup-content {
  padding: 30rpx;
}

.popup-header {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
}

.title-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.title-tag {
  padding: 16rpx 24rpx;
  background-color: var(--color-gray-100);
  border-radius: 30rpx;
  font-size: 28rpx;
  color: var(--color-gray-700);
  transition: all 0.3s;
}

.title-tag.active {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.popup-footer {
  display: flex;
  justify-content: center;
}

.confirm-btn {
  padding: 16rpx 60rpx;
  background-color: var(--color-primary);
  color: var(--color-white);
  border-radius: 40rpx;
  font-size: 28rpx;
  text-align: center;
}

// 分析部分
.analysis-section {
  margin-bottom: 40rpx;
}

// 部分标题
.section-title {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-weight: 500;
  color: var(--color-gray-800);
  margin-bottom: 16rpx;
  padding-bottom: 10rpx;
  border-bottom: 1px solid var(--color-gray-100);

  i {
    margin-right: 10rpx;
    color: var(--color-primary);
  }
}

// 内容部分
.section-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: var(--color-gray-700);
  padding-left: 10rpx;
}

// 日记内容部分
.diary-content-section {
  margin-top: 40rpx;
  padding-top: 20rpx;
  border-top: 1px solid var(--color-gray-100);
}

// 切换按钮
.toggle-btn {
  margin-left: auto;
  font-size: 24rpx;
  color: var(--color-primary);
  display: flex;
  align-items: center;
  cursor: pointer;

  i {
    margin-left: 6rpx;
  }
}

// 切换内容类型按钮
.toggle-content-btn {
  margin-left: 20rpx;
  font-size: 24rpx;
  color: var(--color-orange-500);
  padding: 6rpx 16rpx;
  background-color: var(--color-orange-50);
  border-radius: 20rpx;
  cursor: pointer;
}

// 占位文本
.section-placeholder {
  font-size: 26rpx;
  color: var(--color-gray-400);
  padding: 20rpx 10rpx;
}

// 空状态
.empty-state {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--color-white);
  border-radius: var(--rounded-lg);
  box-shadow: var(--shadow-sm);
}

.empty-hint {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: var(--color-gray-400);
}

// 添加按钮
.add-diary-btn {
  position: fixed;
  right: 40rpx;
  bottom: calc(var(--window-bottom) + 40rpx);
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
  z-index: 100;

  i {
    font-size: 40rpx;
  }
}

// 动画效果
.animate-fade-in-down {
  animation: fadeInDown 0.3s ease-in-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Markdown 样式
:deep(.markdown-body) {
  font-size: 28rpx;
  line-height: 1.6;
}

:deep(p) {
  margin-bottom: 16rpx;
}
</style>

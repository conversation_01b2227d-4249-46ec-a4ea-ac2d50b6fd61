<template>
  <view class="calendar-container">
    <view class="calendar-header">
      <view class="month-selector" @click="toggleCalendarMode">
        <text class="month-text">{{ currentMonthText }}</text>
        <view class="calendar-icon-btn">
          <view :class="['arrow', isMonthMode ? 'arrow-up' : 'arrow-down']"></view>
        </view>
      </view>
    </view>
    
    <!-- 周历模式 -->
    <swiper v-if="!isMonthMode" class="week-calendar" circular :current="cIndex" @change="changeCalendar">
      <swiper-item v-for="(weeks, i) in calendarList" :key="i">
        <view class="calendar-week">
          <view 
            @click="setDay(item.date)" 
            class="calendar-day"
            :class="{
              'active': activeDate === item.date,
              'today': curDay === item.date && activeDate !== item.date,
              'has-data': item.hasData,
              'week-first-day': index === 0
            }"
            v-for="(item, index) in weeks" 
            :key="index"
          >
            <text class="day-name">{{ item.week }}</text>
            <view class="day-number" :class="{ 'weekend': index > 4 }">
              {{ formatDay(item.date, item.day) }}
            </view>
            <!-- 在每周第一天显示周数 -->
            <view v-if="index === 0" class="week-number-badge">
              {{ getWeekOfYear(item.date) }}周
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>

    <!-- 月历模式 -->
    <view v-if="isMonthMode">
      <!-- 先使用简单的占位元素，确保视图结构稳定 -->
      <view v-if="!monthCalendarReady" class="month-calendar-loading">
        <view class="loading-text">加载中...</view>
      </view>

      <!-- 月历内容，确保数据准备好后再渲染 -->
      <swiper
        v-else
        :class="['month-calendar', getMonthCalendarHeight]"
        circular
        :current="monthIndex"
        @change="changeMonthCalendar"
      >
        <swiper-item v-for="(monthData, i) in monthCalendarList" :key="i">
          <view class="week-header">
            <view v-for="(day, index) in weekDays" :key="index" class="week-day-header">
              {{ day }}
            </view>
          </view>
          <view class="month-grid">
            <view class="month-row" v-for="(week, weekIndex) in monthData.days" :key="weekIndex">
              <view
                v-for="(day, dayIndex) in week"
                :key="dayIndex"
                @click="day.date ? setDay(day.date) : null"
                class="month-day-item"
                :class="{
                  'month-day-empty': !day.date,
                  'today': day.date === curDay && day.date !== activeDate,
                  'active': day.date === activeDate,
                  'has-data': day.hasData,
                  'week-first-day': dayIndex === 0 && day.date,
                  'weekend': dayIndex > 4 && day.date
                }"
              >
                <text>{{ day.day || '' }}</text>
                <!-- 在每周第一天显示周数 -->
                <view v-if="dayIndex === 0 && day.date" class="week-number-badge">
                  {{ getWeekOfYear(day.date) }}周
                </view>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>
<script setup>
import { defineProps, defineEmits, watch, ref, computed, onMounted, nextTick } from 'vue'
import dayjs from 'dayjs'
const props = defineProps({
  dataDates: {
    type: Array,
    default: () => [],
  },
})
const emits = defineEmits('onChange', 'onShowToday')
const activeDate = ref()
const curDay = ref(dayjs().format('YYYY-MM-DD'))
const isMonthMode = ref(false)
const cIndex = ref(0)
const monthIndex = ref(1) // 默认显示当前月，索引为 1
const monthCalendarReady = ref(false) // 添加一个标记，表示月历数据是否已准备好
// 修改星期几的顺序，使周一为一周开始
const weekDays = ['一', '二', '三', '四', '五', '六', '日']
const monthCalendarList = ref([])
const currentMonthText = computed(() => {
  if (isMonthMode.value) {
    const currentMonthData = monthCalendarList.value[monthIndex.value]
    if (currentMonthData) {
      return dayjs(currentMonthData.yearMonth).format('YYYY 年 MM 月')
    }
  } else {
    // 周视图显示当前选中日期所在的月份
    return dayjs(activeDate.value).format('YYYY 年 MM 月')
  }
  return dayjs().format('YYYY 年 MM 月')
})

// 计算月历的高度，根据行数动态调整
const getMonthCalendarHeight = computed(() => {
  if (!monthCalendarList.value || monthCalendarList.value.length === 0) return 'h-600rpx'

  // 获取当前月的行数
  const currentMonthData = monthCalendarList.value[monthIndex.value]
  if (!currentMonthData || !currentMonthData.days) return 'h-600rpx'

  const rowCount = currentMonthData.days.length

  // 根据行数返回不同的高度类
  if (rowCount <= 5) {
    return 'h-600rpx'
  } else {
    return 'h-700rpx' // 6行的情况需要更高的高度
  }
})

// 切换日历模式
const toggleCalendarMode = () => {
  // 如果要切换到月历模式，先确保数据准备好
  if (!isMonthMode.value) {
    // 准备切换到月历，先重置标记
    monthCalendarReady.value = false

    // 延迟切换模式，确保过渡平滑
    setTimeout(() => {
      isMonthMode.value = true

      // 保存当前选中的日期
      const targetDate = activeDate.value || dayjs().format('YYYY-MM-DD')

      // 初始化月历数据
      initMonthCalendar(targetDate)
    }, 50)
  } else {
    // 直接切换到周历
    isMonthMode.value = false

    if (activeDate.value) {
      // 根据当前选中的日期重新定位周历
      const targetDate = activeDate.value

      // 计算包含目标日期的周起始日（周一）
      const targetDay = dayjs(targetDate)
      const dayOfWeek = targetDay.day() // 0 是周日，1-6 是周一至周六
      const weekStartDay = targetDay.subtract(dayOfWeek === 0 ? 6 : dayOfWeek - 1, 'day')

      // 重新初始化周历数据
      let monday = weekStartDay
      for (let k = 0; k < 3; k++) {
        calendarList.value[k] = []
        for (let i = 0; i < 7; i++) {
          const dayDate = monday.add(i, 'day')
          calendarList.value[k].push({
            week: weekEnum.charAt(i),
            day: dayDate.format('D'),
            date: dayDate.format('YYYY-MM-DD'),
          })
        }
        monday = weekStartDay.add((k + 1) * 7, 'day') // 修复日期计算错误
      }

      // 更新数据标记
      updateDataMarkers()

      // 设置当前索引为包含目标日期的周
      cIndex.value = 0
    }
  }
}

// 初始化月历
const initMonthCalendar = (targetDate = '') => {
  try {
    // 先重置标记
    monthCalendarReady.value = false

    // 如果提供了目标日期，则使用目标日期；否则使用当前日期
    const baseDate = targetDate ? dayjs(targetDate) : dayjs()

    // 获取目标日期所在月份
    const currentMonth = baseDate.startOf('month')
    const prevMonth = currentMonth.subtract(1, 'month')
    const nextMonth = currentMonth.add(1, 'month')

    // 直接在这里生成数据
    const data = [generateMonthData(prevMonth), generateMonthData(currentMonth), generateMonthData(nextMonth)]

    // 使用延时在下一帧更新数据
    setTimeout(() => {
      // 设置月份索引
      monthIndex.value = 1

      // 设置月历数据
      monthCalendarList.value = data

      // 更新数据标记
      updateMonthDataMarkers()

      // 确保选中的日期被保留
      if (targetDate) {
        activeDate.value = targetDate
      }

      // 再次延时标记数据已准备好，确保视图已更新
      setTimeout(() => {
        monthCalendarReady.value = true
      }, 50)
    }, 100)
  } catch (err) {
    console.error('初始化月历出错：', err)
    // 出错时也要设置标记，避免一直显示加载中
    monthCalendarReady.value = true
  }
}

// 生成月历数据
const generateMonthData = (date) => {
  const yearMonth = date.format('YYYY-MM')
  const daysInMonth = date.daysInMonth()

  // 获取月初是周几（0=周日，1=周一，...）
  let firstDayOfMonth = date.startOf('month').day()

  // 转换为以周一为开始的索引：周一=0, 周二=1, ..., 周日=6
  firstDayOfMonth = firstDayOfMonth === 0 ? 6 : firstDayOfMonth - 1

  const days = []
  let week = []

  // 添加月初前的空白
  for (let i = 0; i < firstDayOfMonth; i++) {
    week.push({})
  }

  // 添加月份的每一天
  for (let i = 1; i <= daysInMonth; i++) {
    const currentDate = `${yearMonth}-${i.toString().padStart(2, '0')}`
    week.push({
      day: i,
      date: currentDate,
      hasData: props.dataDates?.includes(currentDate) || false,
    })

    if (week.length === 7) {
      days.push([...week])
      week = []
    }
  }

  // 添加月末后的空白
  if (week.length > 0) {
    while (week.length < 7) {
      week.push({})
    }
    days.push(week)
  }

  return { yearMonth, days }
}

// 月历左右滑动处理
const changeMonthCalendar = (e) => {
  try {
    const index = e.detail?.current
    if (index === undefined || index === monthIndex.value) return

    // 获取当前显示的月份
    const currentMonthData = monthCalendarList.value[index]
    if (!currentMonthData || !currentMonthData.yearMonth) return

    const currentMonth = dayjs(currentMonthData.yearMonth)

    // 判断滑动方向
    let prevIndex, nextIndex
    if (index === 0) {
      prevIndex = 2
      nextIndex = 1
    } else if (index === 1) {
      prevIndex = 0
      nextIndex = 2
    } else {
      // index === 2
      prevIndex = 1
      nextIndex = 0
    }

    // 更新前一个月和后一个月的数据
    monthCalendarList.value[prevIndex] = generateMonthData(currentMonth.subtract(1, 'month'))
    monthCalendarList.value[nextIndex] = generateMonthData(currentMonth.add(1, 'month'))

    // 判断滑动后是否是当前月
    const currentMonthYM = monthCalendarList.value[index].yearMonth
    const nowMonthYM = dayjs().format('YYYY-MM')

    // 自动选中日期
    if (currentMonthYM === nowMonthYM) {
      // 如果是当前月份，选中今天
      setDay(dayjs().format('YYYY-MM-DD'))
    } else {
      // 如果不是当前月份，选中该月 1 号
      setDay(`${currentMonthYM}-01`)
    }

    // 更新索引
    monthIndex.value = index

    // 更新数据标记
    updateMonthDataMarkers()
  } catch (err) {
    console.error('切换月历出错：', err)
  }
}

// 更新月历数据标记
const updateMonthDataMarkers = () => {
  if (!props.dataDates || props.dataDates.length === 0) return

  monthCalendarList.value.forEach((monthData) => {
    monthData.days.forEach((week) => {
      week.forEach((day) => {
        if (day.date) {
          day.hasData = props.dataDates.includes(day.date)
        }
      })
    })
  })
}

// 设置日期
const setDay = (date) => {
  activeDate.value = date
  emits('onChange', date)
  const showToday = activeDate.value !== dayjs().format('YYYY-MM-DD')
  emits('onShowToday', showToday)
}

// 修改周几的枚举值为新的顺序
const weekEnum = '一二三四五六日'

const formatDay = (date, day) => {
  return day === '1' ? dayjs(date).format('M 月') : day
}

const calendarList = ref([])

let timer = null
const changeCalendar = async (e) => {
  clearTimeout(timer)
  let index = e?.detail?.current
  cIndex.value = index
  if (index === undefined) {
    // 初始化
    const today = dayjs()
    const todayDayOfWeek = today.day() // 获取今天是周几（0 是周日，1-6 是周一至周六）

    // 计算到周一的偏移量（如果是周日，需要往前推 6 天到上周一）
    const offsetToMonday = todayDayOfWeek === 0 ? 6 : todayDayOfWeek - 1

    // 计算本周一日期
    let monday = today.subtract(offsetToMonday, 'day')

    for (let k = 0; k < 3; k++) {
      calendarList.value[k] = []
      for (let i = 0; i < 7; i++) {
        calendarList.value[k].push({
          // 修改周排序，使周一成为第一项
          week: weekDays[i],
          day: monday.add(i, 'day').format('D'),
          date: monday.add(i, 'day').format('YYYY-MM-DD'),
        })
      }
      monday = monday.add(7, 'day')
    }

    index = 0
    cIndex.value = 0
    activeDate.value = today.format('YYYY-MM-DD')
  }
  // 当前展示日期的周一
  const curMonday = dayjs(calendarList.value[index][0].date)
  // 计算上一周
  const lastMonday = dayjs(curMonday).subtract(7, 'day')
  const lastIndex = index - 1 < 0 ? 2 : index - 1
  for (let i = 0; i < 7; i++) {
    calendarList.value[lastIndex][i] = {
      week: weekDays[i],
      day: lastMonday.add(i, 'day').format('D'),
      date: lastMonday.add(i, 'day').format('YYYY-MM-DD'),
    }
  }
  // 计算下一周
  const nextMonday = dayjs(curMonday).add(7, 'day')
  const nextIndex = index + 1 > 2 ? 0 : index + 1
  for (let i = 0; i < 7; i++) {
    calendarList.value[nextIndex][i] = {
      week: weekDays[i],
      day: nextMonday.add(i, 'day').format('D'),
      date: nextMonday.add(i, 'day').format('YYYY-MM-DD'),
    }
  }

  // 标记数据日期
  updateDataMarkers()

  // 判断是否滑动到其他周 || 当前日期是否是今天
  const showToday = !calendarList.value[index].some((item) => item.date === activeDate.value)

  // 设置日期
  timer = setTimeout(() => {
    if (calendarList.value[index].some((item) => item.date === curDay.value)) {
      setDay(curDay.value)
    } else {
      setDay(curMonday.format('YYYY-MM-DD'))
    }
  }, 600)

  emits('onShowToday', showToday)
}

// 更新日期数据标记
const updateDataMarkers = () => {
  if (!props.dataDates || props.dataDates.length === 0) return

  calendarList.value.forEach((weeks) => {
    weeks.forEach((day) => {
      day.hasData = props.dataDates.includes(day.date)
    })
  })
}

// 监听数据日期变化，更新标记
watch(
  () => props.dataDates,
  () => {
    updateDataMarkers()
    if (isMonthMode.value) {
      updateMonthDataMarkers()
    }
  },
  { deep: true }
)

// 初始化
const init = () => {
  changeCalendar()
  initMonthCalendar()
}

// 设置数据标记
const setDataMarkers = (dates) => {
  calendarList.value.forEach((weeks) => {
    weeks.forEach((day) => {
      day.hasData = dates.includes(day.date)
    })
  })

  if (isMonthMode.value) {
    monthCalendarList.value.forEach((monthData) => {
      monthData.days.forEach((week) => {
        week.forEach((day) => {
          if (day.date) {
            day.hasData = dates.includes(day.date)
          }
        })
      })
    })
  }
}

// 重新实现周数计算，不依赖 dayjs 插件
const getWeekOfYear = (dateString) => {
  if (!dateString) return ''

  const date = new Date(dateString)
  // 设置到当年的 1 月 1 日
  const yearStart = new Date(date.getFullYear(), 0, 1)

  // 计算从 1 月 1 日开始的天数
  const dayOfYear = Math.floor((date - yearStart) / (24 * 60 * 60 * 1000))

  // 获取 1 月 1 日是周几 (0-6)
  const firstDayOfWeek = yearStart.getDay()

  // 计算周数
  const weekNumber = Math.ceil((dayOfYear + firstDayOfWeek) / 7)

  return weekNumber
}

defineExpose({
  init,
  setDataMarkers,
})

onMounted(() => {
  changeCalendar()
  // 延迟初始化月历，确保组件已完全挂载
  setTimeout(() => {
    initMonthCalendar()
  }, 300)
})
</script>
<style scoped lang="scss">
.calendar-container {
  background-color: var(--color-white, #fff);
  border-radius: var(--rounded-lg, 12rpx);
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: var(--shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.month-selector {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.month-text {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--color-gray-800, #333);
  margin-right: 10rpx;
}

.calendar-icon-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-gray-100, #f5f5f5);
  border-radius: var(--rounded-full, 9999rpx);
}

.arrow {
  width: 0;
  height: 0;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
}

.arrow-down {
  border-top: 8rpx solid var(--color-primary, #64b6f7);
  border-bottom: none;
}

.arrow-up {
  border-bottom: 8rpx solid var(--color-primary, #64b6f7);
  border-top: none;
}

.week-calendar {
  height: 170rpx;
}

.calendar-week {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
}

.calendar-day {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  border-radius: var(--rounded-md, 8rpx);
  position: relative;
  transition: all 0.2s;
  
  &.active {
    .day-number {
      background-color: var(--color-primary, #64b6f7);
      color: white;
    }
  }
  
  &.today:not(.active) {
    .day-number {
      border: 2rpx solid var(--color-primary, #64b6f7);
      color: var(--color-primary, #64b6f7);
    }
  }
  
  &.has-data {
    &::after {
      content: '';
      position: absolute;
      top: 10rpx;
      right: 10rpx;
      width: 10rpx;
      height: 10rpx;
      background-color: red;
      border-radius: 50%;
    }
  }
}

.day-name {
  font-size: 24rpx;
  font-weight: 500;
  color: var(--color-gray-500, #999);
  margin-bottom: 8rpx;
}

.day-number {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--rounded-full, 9999rpx);
  font-size: 28rpx;
  
  &.weekend {
    color: var(--color-primary-light, #a8d4fb);
  }
}

.week-number-badge {
  position: absolute;
  bottom: -18rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 16rpx;
  color: var(--color-gray-500, #999);
  width: 40rpx;
  height: 18rpx;
  line-height: 18rpx;
  text-align: center;
}

.month-calendar {
  transition: height 0.3s ease;
}

.week-header {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 0 20rpx;
}

.week-day-header {
  width: 80rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 500;
  color: var(--color-gray-500, #999);
}

.month-grid {
  display: flex;
  flex-direction: column;
}

.month-row {
  display: flex;
  justify-content: space-between;
  margin: 10rpx 0;
  position: relative;
}

.month-day-item {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: var(--rounded-full, 9999rpx);
  position: relative;
  font-size: 28rpx;
  
  &.month-day-empty {
    background: transparent;
  }
  
  &.today:not(.active) {
    border: 2rpx solid var(--color-primary, #64b6f7);
    color: var(--color-primary, #64b6f7);
  }
  
  &.active {
    background-color: var(--color-primary, #64b6f7);
    color: white;
  }
  
  &.has-data {
    &::after {
      content: '';
      position: absolute;
      top: 5rpx;
      right: 5rpx;
      width: 10rpx;
      height: 10rpx;
      background-color: red;
      border-radius: 50%;
    }
  }
  
  &.weekend {
    color: var(--color-primary-light, #a8d4fb);
  }
}

.month-calendar-loading {
  height: 600rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-gray-100, #f8f8f8);
}

.loading-text {
  font-size: 28rpx;
  color: var(--color-gray-500, #999);
}
</style>

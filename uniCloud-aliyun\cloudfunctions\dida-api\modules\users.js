/**
 * 用户服务模块
 * 处理用户相关功能
 */

const {
	DIDA_API_BASE,
	DIDA_USER_APIS,
	REQUEST_CONFIG,
	buildDidaApiUrl,
} = require("../config");

const {
	logInfo,
	logError,
	createSuccessResponse,
	createErrorResponse,
	validateRequired,
	validateType,
	buildAuthHeaders,
	buildAuthCookies,
	safeJsonParse,
} = require("../utils");

/**
 * 获取用户信息
 * @param {string} authToken - 认证令牌
 * @param {string} csrfToken - CSRF令牌
 * @returns {object} 用户信息响应
 */
async function getUserProfile(authToken, csrfToken) {
	const methodName = "getUserProfile";
	logInfo(methodName, "开始获取用户信息");

	try {
		// 参数验证
		const authValidation = validateRequired(authToken, "authToken");
		if (authValidation) return authValidation;

		const csrfValidation = validateRequired(csrfToken, "csrfToken");
		if (csrfValidation) return csrfValidation;

		// 构建请求URL
		const apiUrl = buildDidaApiUrl(DIDA_USER_APIS.user_profile);

		// 构建请求头和cookies
		const headers = buildAuthHeaders(authToken, csrfToken);
		const cookies = buildAuthCookies(authToken, csrfToken);

		logInfo(methodName, "发起获取用户信息请求", { apiUrl });

		// 发起请求
		const response = await uniCloud.httpclient.request(apiUrl, {
			method: "GET",
			headers: headers,
			cookies: cookies,
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`获取用户信息失败，状态码: ${response.status}`);
		}

		// 解析响应数据
		const responseData = safeJsonParse(response.data, null);

		if (!responseData) {
			throw new Error("无法解析用户信息数据");
		}

		logInfo(methodName, "成功获取用户信息", {
			userId: responseData.id || "unknown",
		});

		// 处理用户信息数据
		const processedData = {
			// 基本信息
			basic_info: {
				id: responseData.id,
				username: responseData.username,
				email: responseData.email,
				nickname: responseData.nickname,
				avatar: responseData.avatar,
				phone: responseData.phone,
				gender: responseData.gender,
				birthday: responseData.birthday,
			},
			// 账户信息
			account_info: {
				is_premium: responseData.isPremium || false,
				premium_expire_date: responseData.premiumExpireDate,
				registration_date: responseData.registrationDate,
				last_login_time: responseData.lastLoginTime,
				login_count: responseData.loginCount || 0,
			},
			// 设置信息
			settings: {
				timezone: responseData.timezone || "Asia/Shanghai",
				language: responseData.language || "zh_CN",
				theme: responseData.theme || "light",
				notification_enabled: responseData.notificationEnabled || false,
				email_notification: responseData.emailNotification || false,
				push_notification: responseData.pushNotification || false,
			},
			// 统计信息
			statistics: {
				total_tasks: responseData.totalTasks || 0,
				completed_tasks: responseData.completedTasks || 0,
				total_projects: responseData.totalProjects || 0,
				total_habits: responseData.totalHabits || 0,
				focus_time: responseData.focusTime || 0,
				achievement_points: responseData.achievementPoints || 0,
				current_level: responseData.currentLevel || 0,
			},
			// 社交信息
			social_info: {
				followers_count: responseData.followersCount || 0,
				following_count: responseData.followingCount || 0,
				is_public_profile: responseData.isPublicProfile || false,
				share_statistics: responseData.shareStatistics || false,
			},
		};

		return createSuccessResponse("获取用户信息成功", processedData);
	} catch (error) {
		logError(methodName, "获取用户信息失败", error);
		throw error;
	}
}

// 导出用户服务模块函数
module.exports = {
	getUserProfile,
};

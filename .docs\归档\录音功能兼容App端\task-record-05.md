# 业务代码集成与测试

## 任务描述

更新 retell-page.vue 页面中的录音功能调用代码，使其能够使用新的兼容多端的录音 Hook，并完成多端测试验证，确保录音功能在 H5 和 App 环境下都能正常工作。

## 所属功能模块

录音功能兼容 App 端

## 技术实现详情

### 1. 修改业务代码

更新`src/pages/speak/retell-page.vue`中的录音功能调用代码：

```typescript
// 将原有的导入
import { ref, nextTick, onMounted } from 'vue'
// 替换为
import { ref, nextTick, onMounted, onBeforeUnmount } from 'vue'
// 导入新的录音 Hook
import { useRecord, useRecordCompat } from '@/hooks'
```

```typescript
// 删除现有的录音相关代码
const recorderManager = uni.getRecorderManager()
const innerAudioContext = uni.createInnerAudioContext()
innerAudioContext.autoplay = true

// 替换为新的录音 Hook 实现
const {
  isRecording,
  isPaused,
  duration,
  volume,
  recordBlob,
  recordURL,
  startRecording,
  stopRecording,
  cancelRecording,
  playRecording,
} = useRecord({
  maxDuration: 60000, // 最大录音时长 60 秒
  appOptions: {
    format: 'mp3', // App 端录音格式
  },
})

// 添加兼容性检查
const { isSupported, requestPermission, compatInfo } = useRecordCompat()

// 检查录音兼容性并请求权限
const checkRecordPermission = async () => {
  if (!isSupported.value) {
    uni.showModal({
      title: '不支持录音',
      content: '当前环境不支持录音功能',
      showCancel: false,
    })
    return false
  }

  const hasPermission = await requestPermission()
  if (!hasPermission) {
    uni.showModal({
      title: '需要权限',
      content: '需要录音权限才能使用该功能',
      confirmText: '去设置',
      success: (res) => {
        if (res.confirm) {
          // 跳转到设置页面
          // #ifdef APP-PLUS
          openSettings()
          // #endif
        }
      },
    })
    return false
  }

  return true
}
```

更新发送音频消息的函数：

```typescript
// 发送音频消息
const sendAudioMessage = async (audioData) => {
  try {
    let audioUrl = ''
    let estimatedDuration = 0
    let fileID = ''

    // 如果直接是录音结果（从 useRecord 获取）
    if (!audioData instanceof Blob && typeof audioData !== 'string') {
      // 开始录音
      if (!(await checkRecordPermission())) return

      try {
        await startRecording()
        uni.showToast({
          title: '开始录音',
          icon: 'none',
        })
      } catch (error) {
        uni.showModal({
          title: '录音失败',
          content: error.message || '无法启动录音',
          showCancel: false,
        })
        return
      }

      // 显示录音中状态
      uploadProgress.value.show = true
      uploadProgress.value.percent = 0

      // 录音进度模拟
      const progressTimer = setInterval(() => {
        if (uploadProgress.value.percent < 90) {
          uploadProgress.value.percent += 5
        }
      }, 1000)

      // 停止录音
      setTimeout(async () => {
        try {
          const result = await stopRecording()
          clearInterval(progressTimer)
          uploadProgress.value.percent = 100

          // 获取录音结果
          if (result instanceof Blob) {
            // H5 环境
            audioUrl = URL.createObjectURL(result)
            estimatedDuration = duration.value

            // 上传到云存储
            try {
              const cloudPath = `speak/${dayjs().format('YYYY-MM-DD')}_${Date.now()}.mp3`
              const uploadResult = await uniCloud.uploadFile({
                filePath: audioUrl,
                cloudPath: cloudPath,
                cloudPathAsRealPath: true,
                onUploadProgress: (progressEvent) => {
                  const percent = Math.round((progressEvent.loaded / progressEvent.total) * 100)
                  uploadProgress.value.percent = percent
                  if (percent === 100) {
                    setTimeout(() => {
                      uploadProgress.value.show = false
                    }, 500)
                  }
                },
              })

              // 获取完整的文件访问地址
              const fileUrl = await uniCloud.getTempFileURL({
                fileList: [uploadResult.fileID],
              })

              audioUrl = fileUrl.fileList[0].tempFileURL
              fileID = uploadResult.fileID
            } catch (uploadError) {
              console.error('上传音频失败：', uploadError)
              uni.showToast({
                title: '上传音频失败',
                icon: 'none',
              })
              return
            }
          } else {
            // App 环境，result 是文件路径
            audioUrl = result
            estimatedDuration = duration.value
            fileID = result

            // 上传到云存储
            try {
              const cloudPath = `speak/${dayjs().format('YYYY-MM-DD')}_${Date.now()}.mp3`
              const uploadResult = await uniCloud.uploadFile({
                filePath: result,
                cloudPath: cloudPath,
                cloudPathAsRealPath: true,
                onUploadProgress: (progressEvent) => {
                  const percent = Math.round((progressEvent.loaded / progressEvent.total) * 100)
                  uploadProgress.value.percent = percent
                  if (percent === 100) {
                    setTimeout(() => {
                      uploadProgress.value.show = false
                    }, 500)
                  }
                },
              })

              // 获取完整的文件访问地址
              const fileUrl = await uniCloud.getTempFileURL({
                fileList: [uploadResult.fileID],
              })

              audioUrl = fileUrl.fileList[0].tempFileURL
              fileID = uploadResult.fileID
            } catch (uploadError) {
              console.error('上传音频失败：', uploadError)
              uni.showToast({
                title: '上传音频失败',
                icon: 'none',
              })
              return
            }
          }

          // 处理后续逻辑
          processAudioMessage(audioUrl, estimatedDuration, fileID)
        } catch (error) {
          clearInterval(progressTimer)
          uploadProgress.value.show = false
          console.error('录音失败：', error)
          uni.showToast({
            title: '录音失败',
            icon: 'none',
          })
        }
      }, 3000) // 模拟 3 秒录音

      return
    }

    // 如果 audioData 是 Blob 对象或文件路径（已有的录音）
    if (audioData instanceof Blob) {
      // H5 环境下的处理
      audioUrl = URL.createObjectURL(audioData)
      estimatedDuration = audioData.duration || 3000 // 默认 3 秒

      // 上传到 uniCloud
      // ... 省略与上面相同的上传代码 ...
    } else if (typeof audioData === 'string') {
      // App 环境下已有录音文件路径
      audioUrl = audioData
      estimatedDuration = audioData.audioDuration || 3000 // 默认 3 秒
      fileID = audioData

      // 上传到 uniCloud
      // ... 省略与上面相同的上传代码 ...
    } else {
      // 如果 audioData 是消息对象，直接使用其属性
      audioUrl = audioData.audioUrl
      estimatedDuration = audioData.audioDuration
      fileID = audioData.audioUrl
    }

    // 处理后续逻辑
    processAudioMessage(audioUrl, estimatedDuration, fileID)
  } catch (error) {
    console.error('处理音频消息时出错：', error)
    uni.showToast({
      title: '处理音频失败',
      icon: 'none',
    })
  }
}

// 处理音频消息的后续逻辑
const processAudioMessage = (audioUrl, estimatedDuration, fileID) => {
  // 添加用户音频消息
  messages.value.push({
    type: 'audio',
    audioUrl: fileID,
    audioDuration: estimatedDuration,
    isUser: true,
    time: dayjs().valueOf(),
    isTranscribing: true, // 添加转写状态
  })

  // 滚动到底部
  nextTick(() => {
    messageListRef.value.scrollToBottom()
  })

  // 语音转文字请求
  // ... 原有的语音转文字逻辑 ...
}
```

确保在组件销毁时清理资源：

```typescript
// 清理资源
onBeforeUnmount(() => {
  // 如果有正在进行的录音，取消它
  if (isRecording.value) {
    cancelRecording()
  }
})
```

### 2. 修改消息组件

更新`src/pages/speak/components/l-message-input.vue`组件，以支持多端录音：

```vue
<template>
  <view class="message-input-container">
    <!-- 文本输入框 -->
    <view class="input-box">
      <textarea
        class="input"
        v-model="inputText"
        :disabled="isRecording"
        placeholder="输入消息..."
        auto-height
        confirm-type="send"
        @confirm="handleSend"
      />
    </view>

    <!-- 按钮区域 -->
    <view class="button-group">
      <!-- 录音按钮 -->
      <view
        class="record-button"
        :class="{ recording: isRecording, paused: isPaused }"
        @touchstart="handleRecordStart"
        @touchend="handleRecordEnd"
        @touchcancel="handleRecordCancel"
      >
        <i :class="isRecording ? 'fas fa-stop' : 'fas fa-microphone'"></i>
      </view>

      <!-- 发送按钮 -->
      <view class="send-button" :class="{ active: inputText.trim() }" @tap="handleSend">
        <i class="fas fa-paper-plane"></i>
      </view>
    </view>

    <!-- 录音状态 -->
    <view class="recording-status" v-if="isRecording">
      <view class="recording-time">{{ formatDuration(duration) }}</view>
      <view class="recording-volume">
        <view class="volume-bar" :style="{ width: volume + '%' }"></view>
      </view>
      <view class="recording-controls">
        <view class="recording-cancel" @tap="handleRecordCancel"> <i class="fas fa-times"></i> 取消 </view>
        <view class="recording-pause" @tap="handleRecordPause" v-if="!isPaused">
          <i class="fas fa-pause"></i> 暂停
        </view>
        <view class="recording-resume" @tap="handleRecordResume" v-else> <i class="fas fa-play"></i> 继续 </view>
        <view class="recording-finish" @tap="handleRecordEnd"> <i class="fas fa-check"></i> 完成 </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRecord } from '@/hooks'

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue', 'send', 'send-audio'])

// 文本输入值
const inputText = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

// 使用录音 hook
const {
  isRecording,
  isPaused,
  duration,
  volume,
  startRecording,
  pauseRecording,
  resumeRecording,
  stopRecording,
  cancelRecording,
  recordBlob,
} = useRecord({
  maxDuration: 60000, // 最大录音时长 60 秒
})

// 格式化时长显示
const formatDuration = (ms) => {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 开始录音
const handleRecordStart = async () => {
  try {
    await startRecording()
  } catch (error) {
    uni.showModal({
      title: '录音失败',
      content: error.message || '无法启动录音',
      showCancel: false,
    })
  }
}

// 暂停录音
const handleRecordPause = () => {
  pauseRecording()
}

// 继续录音
const handleRecordResume = () => {
  resumeRecording()
}

// 结束录音
const handleRecordEnd = async () => {
  if (isRecording.value) {
    try {
      await stopRecording()
      emit('send-audio', recordBlob.value)
    } catch (error) {
      uni.showToast({
        title: '录音失败',
        icon: 'none',
      })
    }
  }
}

// 取消录音
const handleRecordCancel = () => {
  if (isRecording.value) {
    cancelRecording()
  }
}

// 发送文本消息
const handleSend = () => {
  if (inputText.value.trim()) {
    emit('send')
  }
}
</script>

<style lang="scss" scoped>
.message-input-container {
  position: relative;
  padding: 10px;
  background-color: #fff;
  border-top: 1px solid #eee;

  .input-box {
    margin-right: 100px;
    background-color: #f5f5f5;
    border-radius: 18px;
    padding: 8px 12px;

    .input {
      width: 100%;
      font-size: 16px;
      min-height: 20px;
      max-height: 80px;
    }
  }

  .button-group {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    align-items: center;

    .record-button,
    .send-button {
      width: 40px;
      height: 40px;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 8px;
      color: white;

      &.recording {
        animation: pulse 1.5s infinite;
      }

      &.paused {
        animation: none;
        opacity: 0.7;
      }
    }

    .record-button {
      background-color: #f44336;
    }

    .send-button {
      background-color: #ccc;

      &.active {
        background-color: #2196f3;
      }
    }
  }

  .recording-status {
    margin-top: 10px;

    .recording-time {
      text-align: center;
      font-size: 14px;
      color: #555;
      margin-bottom: 5px;
    }

    .recording-volume {
      height: 10px;
      background-color: #eee;
      border-radius: 5px;
      overflow: hidden;
      margin-bottom: 10px;

      .volume-bar {
        height: 100%;
        background-color: #2196f3;
        transition: width 0.1s ease;
      }
    }

    .recording-controls {
      display: flex;
      justify-content: space-between;

      .recording-cancel,
      .recording-pause,
      .recording-resume,
      .recording-finish {
        padding: 5px 10px;
        font-size: 14px;
        border-radius: 4px;
        display: flex;
        align-items: center;

        i {
          margin-right: 5px;
        }
      }

      .recording-cancel {
        color: #f44336;
      }

      .recording-finish {
        color: #4caf50;
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
</style>
```

### 3. 添加多端测试用例

创建测试用例来验证录音功能在不同环境下的表现：

```typescript
// tests/e2e/specs/record.spec.js

describe('录音功能测试', () => {
  // 在 H5 环境下测试录音功能
  describe('H5 环境', () => {
    beforeAll(() => {
      // 模拟 navigator.mediaDevices.getUserMedia
      navigator.mediaDevices.getUserMedia = jest.fn().mockResolvedValue({
        getTracks: () => [
          {
            stop: jest.fn(),
          },
        ],
      })
    })

    it('应该能够开始录音', async () => {
      // 测试代码
    })

    it('应该能够停止录音', async () => {
      // 测试代码
    })

    it('应该正确处理权限拒绝', async () => {
      // 模拟权限拒绝
      navigator.mediaDevices.getUserMedia = jest.fn().mockRejectedValue({
        name: 'NotAllowedError',
        message: '权限被拒绝',
      })

      // 测试代码
    })
  })

  // 在 App 环境下测试录音功能
  describe('App 环境', () => {
    // App 环境的测试需要在真机上进行，这里提供测试指导
    it('手动测试：应能正常录音并获取文件路径', () => {
      // 1. 打开 retell-page 页面
      // 2. 点击录音按钮，开始录音
      // 3. 停止录音
      // 4. 验证是否收到录音文件路径
    })

    it('手动测试：应能正确处理权限拒绝', () => {
      // 1. 在设备设置中关闭应用的录音权限
      // 2. 打开 retell-page 页面
      // 3. 点击录音按钮
      // 4. 验证是否显示权限请求提示
    })
  })
})
```

### 4. 创建测试记录文档

创建`tests/manual/record-test-results.md`文件，用于记录手动测试结果：

```markdown
# 录音功能手动测试记录

## 测试环境

- 日期：2023-06-01
- 测试人员：[填写姓名]

## H5 环境测试

### 测试设备 1

- 设备：MacBook Pro
- 浏览器：Chrome 113.0.5672.126
- 测试结果：
  - [x] 能够正常开始录音
  - [x] 能够正常暂停和继续录音
  - [x] 能够正常停止录音并获取录音文件
  - [x] 权限请求和错误处理正常
  - [ ] 其他问题：[如有，请描述]

### 测试设备 2

- 设备：iPhone 13
- 浏览器：Safari 16.4.1
- 测试结果：
  - [x] 能够正常开始录音
  - [ ] 暂停和继续录音功能有问题：[描述问题]
  - [x] 能够正常停止录音并获取录音文件
  - [x] 权限请求和错误处理正常
  - [ ] 其他问题：[如有，请描述]

## App 环境测试

### 测试设备 1

- 设备：Pixel 6
- 系统版本：Android 12
- 测试结果：
  - [x] 能够正常开始录音
  - [x] 能够正常暂停和继续录音
  - [x] 能够正常停止录音并获取录音文件
  - [x] 权限请求和错误处理正常
  - [ ] 其他问题：[如有，请描述]

### 测试设备 2

- 设备：iPhone 12
- 系统版本：iOS 16.4.1
- 测试结果：
  - [x] 能够正常开始录音
  - [x] 能够正常暂停和继续录音
  - [x] 能够正常停止录音并获取录音文件
  - [ ] 权限请求提示文案需要调整，当前文案不够清晰
  - [ ] 其他问题：[如有，请描述]

## 发现的问题和建议

1. iOS Safari 浏览器中暂停和继续录音功能不可用，建议添加兼容性提示
2. Android App 录音时的音量显示不够精确，可能需要调整音量监测算法
3. iOS App 权限提示文案需要修改，当前文案不够清晰明确
4. 建议添加录音时长提示，让用户知道当前录制了多长时间

## 解决方案

1. 在 Safari 浏览器中禁用暂停和继续功能，改为只有开始和停止
2. 修改 App 音量监测逻辑，使用更准确的算法
3. 更新 iOS 权限提示文案为："需要使用麦克风来录制语音消息，请点击允许"
4. 在录音界面添加计时器显示当前录音时长
```

## 验收标准

1. retell-page.vue 中的录音功能已更新，使用新的 useRecord Hook
2. 录音功能在 H5 和 App 环境下都能正常工作
3. 权限管理和错误处理完善
4. UI 交互流畅，用户体验一致
5. 测试用例覆盖主要功能点
6. 完成了跨平台测试，并记录测试结果
7. 代码可读性良好，注释清晰

## 依赖关系

- 上游依赖：
  - task-record-01 (类型定义与接口设计)
  - task-record-02 (H5 录音功能迁移)
  - task-record-03 (App 端录音功能实现)
  - task-record-04 (入口文件与条件编译)

## 优先级

低 - 最后完成的集成与验证任务

## 状态追踪

- [ ] 已更新 retell-page.vue 中的录音功能
- [ ] 已修改 l-message-input.vue 组件
- [ ] 已添加测试用例
- [ ] 已创建测试记录文档
- [ ] 已在 H5 环境完成测试
- [ ] 已在 App 环境完成测试
- [ ] 已修复发现的问题
- [ ] 已完成代码审查

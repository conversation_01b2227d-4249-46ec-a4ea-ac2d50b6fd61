/**
 * 验证类
 */
export class ValidateManager {
  constructor() {}

  /**
   * 验证电子邮箱格式
   */
  email = (value) => {
    return /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(value)
  }

  /**
   * 验证手机格式
   */
  mobile = (value) => {
    return /^1([3456789]\d|4[5-9]|6[1-2,4-7]|7[0-8])\d{8}$/.test(value)
  }

  /**
   * 验证URL格式
   */
  url = (value) => {
    return /^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/
      .test(value)
  }

  /**
   * 身份证验证
   */
  idCard = (value) => {
    if (value.length == 15) {
      // 一代身份证
      let year = value.substring(6, 8);
      let month = value.substring(8, 10);
      let day = value.substring(10, 12);
      let temp_date = new Date(year, parseFloat(month) - 1, parseFloat(day));
      // 对于老身份证中的你年龄则不需考虑千年虫问题而使用getYear()方法
      if (temp_date.getYear() != parseFloat(year) || temp_date.getMonth() != parseFloat(month) - 1 || temp_date
        .getDate() != parseFloat(day)) {
        return false;
      } else {
        return true;
      }
    } else {
      // 二代身份证
      // 1 "验证通过!", 0 //校验不通过
      let format =
        /^(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\d{4}(([1][9]\d{2})|([2]\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\d{3}[0-9xX]$/;
      //号码规则校验
      if (!format.test(value)) {
        return false;
      }
      //区位码校验
      //出生年月日校验   前正则限制起始年份为1900;
      let year = value.substr(6, 4), //身份证年
        month = value.substr(10, 2), //身份证月
        date = value.substr(12, 2), //身份证日
        time = Date.parse(month + '-' + date + '-' + year), //身份证日期时间戳date
        now_time = Date.parse(new Date()), //当前时间戳
        dates = new Date(year, month, 0).getDate(); //身份证当月天数
      if (time > now_time || date > dates) {
        return false;
      }
      //校验码判断
      var c = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2); //系数
      var b = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'); //校验码对照表
      var id_array = value.split('');
      var sum = 0;
      for (var k = 0; k < 17; k++) {
        sum += parseInt(id_array[k]) * parseInt(c[k]);
      }
      if (id_array[17].toUpperCase() != b[sum % 11].toUpperCase()) {
        return false;
      }
      return true;
    }
  }

  /**
   * 车牌号
   */
  carNumber = (value) => {
    // 新能源车牌
    let xreg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/
    // 旧车牌
    let creg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/
    if (value.length === 7) {
      return creg.test(value)
    }
    if (value.length === 8) {
      return xreg.test(value)
    }
    return false
  }

  /**
   * 全部中文
   */
  chinese = (value) => {
    let reg = /^[\u4e00-\u9fa5]+$/gi
    return reg.test(value)
  }

  /**
   * 包含中文
   */
  containChinese = (value) => {
    if (/.*[\u4e00-\u9fa5]+.*$/.test(value)) {
      return true;
    }
    return false;
  }

  /**
   * 是否以数字开头
   * 规则：检查字符串是否是只是含有字母数字或下划线,不能以数字开头
   */
  isUsername = (value) => {
    let reg = /^[0-9a-zA-Z_]*$/g;
    let enOrNumber = reg.test(value);
    if (enOrNumber) {
      if (/^[0-9]/.test(value.substr(0, 1))) {
        return false;
      }
      return true;
    } else {
      return false;
    }
  }

  /**
   * 是否固定电话
   */
  telephone = (value) => {
    let reg = /^\d{3,4}-\d{7,8}(-\d{3,4})?$/
    return reg.test(value)
  }

  /**
   * 判断是否为空
   */
  empty = (value) => {
    switch (typeof value) {
      case 'undefined':
        return true
      case 'string':
        if (value.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g, '').length == 0) return true
        break
      case 'boolean':
        if (!value) return true
        break
      case 'number':
        if (value === 0 || isNaN(value)) return true
        break
      case 'object':
        if (value === null || value.length === 0) return true
        for (const i in value) {
          return false
        }
        return true
    }
    return false
  }

  /**
   * 图片格式
   */
  image = (value) => {
    let newValue = value.split('?')[0]
    let image_regexp = /\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i
    return image_regexp.test(newValue)
  }

  /**
   * 视频格式
   */
  video = (value) => {
    let video_regexp = /\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i
    return video_regexp.test(value)
  }

  /**
   * 全部字母
   */
  letter = (value) => {
    return /^[a-z]+$/i.test(value);
  }

  /**
   * 字母和空格
   */
  letterSpace = (value) => {
    return /^[a-z\s]*$/i.test(value);
  }

  /**
   * 字母和数字
   */
  letterNumeric = (value) => {
    return /^[a-z0-9]+$/i.test(value);
  }

  /**
   * 字母数字和空格
   */
  letterNumericSpace = (value) => {
    return /^[a-z0-9\s]*$/i.test(value);
  }

  /**
   * 数字和空格
   */
  numericSpace = (value) => {
    return /^[\d\s]*$/.test(value);
  }

  /**
   * 全部小写
   */
  lower = (value) => {
    return /^[a-z]+$/.test(value);
  }

  /**
   * 全部大写
   */
  upper = (value) => {
    return /^[A-Z]+$/.test(value);
  }

  /**
   * 判断是否是微信浏览器
   */
  isWechat = () => {
    let ua = navigator.userAgent.toLowerCase();
    if (ua.match(/MicroMessenger/i) == "micromessenger") {
      return true;
    } else {
      return false;
    }
  }

  /**
   * 判断是否是支付宝浏览器
   */
  isAlipay = () => {
    return /AlipayClient/.test(window.navigator.userAgent);
  }
}
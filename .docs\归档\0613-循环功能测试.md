# OKR 循环功能测试用例

## 1. 基本循环类型测试

### 1.1 每天 (DAILY) 测试

**初始设置：**

- 创建新关键结果，标题为"每天测试任务"
- 循环类型：每天 (`type=DAILY`)
- 日期范围：当前日期 ~ 当前日期 +7 天
- 量化目标：起始值=0，目标值=70
- 每日目标量：10

**详细测试步骤：**

1. 在 krEdit 页面创建上述任务
   - 确认进入编辑页面后所有字段默认值是否正确
   - 验证日期选择控件是否正常工作
   - 检查"循环设置"区域是否正确显示"每天"选项
2. 保存任务后返回主页面
   - 确认保存成功提示是否显示
   - 验证返回路由是否正确
3. 在"今日"视图确认任务显示
   - 检查任务卡片是否包含循环标识
   - 检查进度条初始状态是否为 0%
   - 记录当前日期进度值=5
   - 验证进度提交后任务卡片即时更新
4. 切换到明天日期，确认任务仍然显示
   - 使用日历控件切换日期
   - 验证明天的任务进度是否为初始状态
   - 记录明天日期进度值=10
5. 切换到后天日期，再记录一次进度值=10
6. 返回"今日"视图，检查任务进度显示
   - 验证三天的累计进度是否正确反映在总进度中
   - 检查任务卡片上的详细数据展示

**预期结果：**

- 任务在日期范围内的每一天都显示
- 今日进度显示格式："今日：5/10"，完成度 50%
- 明天进度显示格式："今日：10/10"，完成度 100%
- 后天进度显示格式："今日：10/10"，完成度 100%
- 总进度计算：(5+10+10)/70 = 25/70 ≈ 35.7%
- 总进度条颜色应根据完成比例显示对应颜色
- 任务卡片应显示循环图标和"每天"标记

### 1.2 每周几 (WEEKLY) 测试

**初始设置：**

- 创建新关键结果，标题为"每周几测试任务"
- 循环类型：每周几 (`type=WEEKLY; byweekday=MO,WE,FR`)
- 日期范围：当前日期 ~ 当前日期 +14 天
- 量化目标：起始值=0，目标值=60
- 每日目标量：10

**详细测试步骤：**

1. 在 krEdit 页面创建上述任务
   - 确认"循环设置"区域中"每周几"选项是否可选
   - 验证星期选择控件是否正常，可以选择多个星期
   - 检查选择周一、周三、周五后的 UI 反馈
2. 保存任务，在"今日"视图的日历中查看未来 14 天
   - 使用月视图查看整个测试周期
   - 确认循环日期是否在日历上有标记
3. 记录当前周内符合条件日期的任务显示情况
   - 截图或记录每个日期的任务显示状态
   - 特别关注周一、周三、周五是否正确显示任务
   - 检查其他日期是否正确隐藏任务
4. 在周一完成任务 (记录值=10)
   - 验证记录后周一任务状态变为"已完成"
   - 检查进度条更新是否正确
   - 确认总进度计算是否准确
5. 检查周三任务状态
   - 确认周一任务完成后不影响周三任务状态
   - 验证周三任务进度是否独立计算

**预期结果：**

- 任务严格在周一、周三、周五显示，其他日期不显示
- 任务卡片应包含"每周 一三五"的标识
- 两周内共显示 6 天该任务 (基于当前日期计算)
- 完成周一任务后，周三任务状态保持未完成
- 进度统计显示：当前完成 1 天/总共 6 天 ≈16.7%
- 每日目标量显示为 10 分/天

## 2. 间隔循环测试

### 2.1 每隔几天 (INTERVAL_DAILY) 测试

**初始设置：**

- 创建新关键结果，标题为"每隔几天测试任务"
- 循环类型：每隔几天 (`type=INTERVAL_DAILY; interval=3; startDate=当前日期`)
- 日期范围：当前日期 ~ 当前日期 +15 天
- 量化目标：起始值=0，目标值=60
- 每日目标量：10

**详细测试步骤：**

1. 在 krEdit 页面创建任务
   - 选择"每隔几天"循环类型
   - 设置间隔为 3 天
   - 确认开始日期设为当前日期
   - 检查系统是否正确计算并显示循环日期预览
2. 保存后，通过日历视图逐一检查以下日期：
   - 第 0 天 (当前日期) - 确认任务显示
   - 第 1 天 - 确认任务不显示
   - 第 2 天 - 确认任务不显示
   - 第 3 天 - 确认任务显示
   - 第 6 天 - 确认任务显示
   - 第 9 天 - 确认任务显示
3. 在第 0 天完成任务 (记录值=10)
   - 确认记录后任务状态更新
   - 验证总进度计算是否正确
4. 检查第 3 天任务状态
   - 确认第 0 天完成不影响第 3 天任务状态
   - 验证每个循环日的进度是否独立计算

**额外测试点：**

- 修改间隔日期后，验证所有未来日期是否正确重新计算
- 测试间隔设置为 1、7、30 等特殊值的情况
- 验证开始日期设置为过去日期时系统的处理方式

**预期结果：**

- 任务严格在第 0、3、6、9、12、15 天显示，共 6 天
- 任务卡片包含"每隔 3 天"的标识
- 其他日期不显示任务
- 完成第 0 天任务后，第 3 天任务状态为未完成
- 进度计算：10/60 = 16.7%
- 每日目标量显示为 10 分/天

## 3. 数量循环测试

### 3.1 每周几天 (WEEKLY_N_TIMES) 测试

**初始设置：**

- 创建新关键结果，标题为"每周几天测试任务"
- 循环类型：每周几天 (`type=WEEKLY_N_TIMES; count=4`)
- 日期范围：当前日期 ~ 当前日期 +14 天 (跨越两周)
- 量化目标：起始值=0，目标值=80
- 每日目标量：10

**详细测试步骤：**

1. 在 krEdit 页面创建任务
   - 选择"每周几天"循环类型
   - 设置每周完成天数为 4 天
   - 确认系统正确显示"每周需完成 4 天"的提示
   - 检查每日目标量是否正确设置
2. 保存后，在"今日"视图查看任务
   - 检查任务卡片是否显示"每周 4 天"的标记
   - 确认任务进度显示格式是否包含"今日"和"本周"两部分
3. 连续 4 天记录进度 (每天值=10)
   - 第一天记录后，检查"本周：1/4"显示
   - 第二天记录后，检查"本周：2/4"显示
   - 第三天记录后，检查"本周：3/4"显示
   - 第四天记录后，检查"本周：4/4"显示
4. 第五天检查任务状态
   - 确认任务卡片显示"本周已完成"标记
   - 验证是否可以继续记录（应允许超额完成）
   - 如记录第五天，确认不再增加"本周"计数
5. 切换到下周第一天
   - 确认周计数是否重置为"本周：0/4"
   - 验证新一周的记录是否正常工作

**额外测试点：**

- 测试每周天数设置为 1 和 7 等边界值
- 验证跨周边界时系统如何处理剩余未完成的天数
- 测试使用日历切换到下周，然后再返回本周的场景

**预期结果：**

- 任务显示格式："今日：x/10 · 本周：y/4"
- 完成 4 天后，当周其余日期仍显示任务但标记为"本周已完成"
- 下周第一天，周进度重置为"本周：0/4"
- 总进度计算：40/80 = 50%
- 任务卡片颜色根据完成状态变化

## 4. 高级循环测试

### 4.1 每几个周 (N_WEEKS) 测试

**初始设置：**

- 创建新关键结果，标题为"每几个周测试任务"
- 循环类型：每几个周 (`type=N_WEEKS; interval=2; startDate=当前周周一`)
- 日期范围：当前周周一 ~ 当前日期 +28 天 (跨越 4 周)
- 量化目标：起始值=0，目标值=40
- 每日目标量：预期为 20 分/周期

**详细测试步骤：**

1. 在 krEdit 页面创建任务
   - 选择"每几个周"循环类型
   - 设置周期为 2 周
   - 确认开始日期设为当前周的周一
   - 检查系统是否正确计算周期日期
2. 保存后，检查第 1-2 周内的任务显示
   - 确认第 1 周每天都显示任务
   - 确认第 2 周每天都显示任务
   - 任务卡片应包含"第 1 个 2 周期"相关标记
3. 在第 1 周内记录进度
   - 第 1 天记录值=10
   - 第 3 天记录值=10
   - 检查周期内总进度是否达到目标 (20/20)
4. 切换到第 3 周 (新周期)
   - 确认周期进度是否重置 (0/20)
   - 验证任务卡片是否显示"第 2 个 2 周期"标记
5. 记录第 3 周内进度 (值=20)
   - 确认新周期进度更新
   - 验证总目标进度计算

**额外测试点：**

- 测试周期设置为 1、4、12 等不同值的情况
- 验证周期开始日期不是周一时的处理逻辑
- 测试在周期中间修改周期长度的情况

**预期结果：**

- 任务在第 1-2 周 (第一个周期) 的每一天都显示
- 任务在第 3-4 周 (第二个周期) 的每一天都显示
- 第 1 周内记录后，周期进度显示为 20/20(已完成)
- 第 3 周内，进度重置为 0/20
- 记录第 3 周进度后，总进度为：40/40 = 100%
- 任务卡片应显示当前处于哪个周期

## 5. 边界情况测试

### 5.1 跨月份和跨年份测试

**初始设置：**

- 创建新关键结果，标题为"跨月份跨年测试"
- 循环类型：每月几号 (`type=MONTHLY; bymonthday=28,29,30,31`)
- 日期范围：当前年 12 月 20 日 ~ 下一年 2 月 15 日
- 量化目标：起始值=0，目标值=60

**详细测试步骤：**

1. 创建任务时检查月份日期选择
   - 验证可以同时选择 28、29、30、31 日
   - 确认系统是否提示 2 月特殊情况 (无 30、31 日)
2. 保存后，检查以下特定日期：
   - 12 月 28、29、30、31 日 (应全部显示任务)
   - 1 月 28、29、30、31 日 (应全部显示任务)
   - 2 月 28 日 (应显示任务)
   - 2 月 29 日 (闰年显示，非闰年不显示)
   - 2 月 30、31 日 (不应显示任务)
3. 特别测试月末边界
   - 在 12 月 31 日记录进度
   - 确认下一个循环日 (1 月 28 日) 是否正确显示
4. 测试跨月跨年进度计算
   - 记录 12 月和 1 月数据，验证总进度是否连续计算

**额外测试点：**

- 测试闰年与非闰年的 2 月 29 日处理差异
- 验证设置循环日为 31 日时，在小月 (如 4、6、9、11 月) 的处理方式
- 测试时间范围横跨农历新年或其他重要节日的情况

**预期结果：**

- 12 月 28-31 日均应显示任务
- 1 月 28-31 日均应显示任务
- 2 月 28 日应显示任务
- 2 月 29、30、31 日根据是否闰年判断显示情况
- 非本月的日期不能选择 (灰色状态)
- 每日目标量根据实际有效日期数计算
- 跨年时进度计算连续，不受年份变化影响

## 6. 日期区间与循环结合测试

**初始设置：**

- 创建新关键结果，标题为"区间循环结合测试"
- 循环类型：每周几 (`type=WEEKLY; byweekday=MO,WE,FR`)
- 日期范围：当前日期 ~ 当前日期 +10 天 (包含 2 个周一)
- 量化目标：起始值=0，目标值=40

**详细测试步骤：**

1. 创建任务时同时设置循环和日期范围
   - 确认循环设置为周一、周三、周五
   - 明确设置结束日期为当前日期 +10 天
   - 检查系统计算的有效循环日是否正确
2. 保存后检查日期范围内的显示情况
   - 确认范围内的周一、周三、周五是否显示任务
   - 确认范围内的周二、周四、周六、周日不显示任务
3. 在范围边界日期测试：
   - 如果当前日期 +10 天是周一，确认该日任务显示
   - 如果当前日期 +10 天是其他日期，确认该日是否根据规则显示
4. 检查范围外日期
   - 切换到当前日期 +11 天，不应显示任务
   - 切换到当前日期 +14 天 (如为周一)，不应显示任务

**额外测试点：**

- 测试日期范围设置为过去日期的处理方式
- 验证日期范围起始/结束日与循环日重合时的处理
- 测试修改日期范围后所有任务显示的变化

**预期结果：**

- 日期范围内的周一、周三、周五显示任务
- 日期范围外的日期不显示任务，即使是周一、周三、周五
- 每日目标量计算为：40/日期范围内的有效天数
- 任务卡片同时显示循环规则和日期范围信息

## 7. 量化目标与循环结合测试

**初始设置：**

- 创建新关键结果，标题为"量化循环结合测试"
- 循环类型：每周几天 (`type=WEEKLY_N_TIMES; count=3`)
- 日期范围：当前日期 ~ 当前日期 +28 天 (4 周)
- 量化目标：起始值=20，目标值=80
- 自定义每日目标量：5(手动调整)

**详细测试步骤：**

1. 创建任务时设置非零起始值
   - 设置起始值为 20，目标值为 80
   - 确认系统正确计算目标差值为 60
2. 设置循环为每周 3 天
   - 验证系统自动计算的每日目标量 (预期为 5)
   - 使用"调整"按钮尝试修改每日目标量
   - 确认调整后的数值被正确保存
3. 记录每周进度
   - 第一周记录 3 天进度 (每天值=5)
   - 验证系统显示第一周已完成
   - 检查总进度计算是否基于起始值正确计算
4. 继续记录第二周进度
   - 记录 3 天进度 (每天值=5)
   - 验证第二周完成后总进度计算

**额外测试点：**

- 测试起始值大于目标值的情况 (如减重目标)
- 验证修改每日目标量后对现有记录的影响
- 测试每日完成值超过目标量的情况

**预期结果：**

- 每日目标量显示为自定义的 5 分/天
- 第一周完成 3 天后，显示"本周：3/3"(已完成)
- 第二周进度重置为"本周：0/3"
- 总进度计算：(15+15)/(80-20) = 30/60 = 50%
- 进度条颜色随完成度变化

## 8. 实际应用场景测试

### 8.1 健身锻炼计划

**场景描述：**
创建一个健身计划，每周锻炼 3 天，持续 4 周，目标达到 12 次锻炼。

**设置：**

- 关键结果标题："完成 4 周健身计划"
- 循环类型：每周几天 (`type=WEEKLY_N_TIMES; count=3`)
- 日期范围：当前日期 ~ 当前日期 +28 天
- 量化目标：起始值=0，目标值=12
- 每日目标量：1

**详细测试步骤：**

1. 创建健身计划任务
   - 确认任务标题反映实际目标
   - 设置量化单位为"次"
   - 验证每周 3 天的设置是否正确显示
2. 第一周模拟健身记录
   - 周一记录完成 1 次锻炼
   - 周三记录完成 1 次锻炼
   - 周五记录完成 1 次锻炼
   - 确认第一周显示为"本周：3/3"(已完成)
3. 第二周模拟部分完成
   - 周一记录完成 1 次锻炼
   - 周四记录完成 1 次锻炼
   - 确认第二周显示为"本周：2/3"(未完成)
4. 检查总进度显示
   - 验证总进度计算：5/12 ≈ 41.7%
   - 检查进度条颜色是否对应完成比例
5. 测试周末补充记录
   - 在第二周周日补充记录 1 次锻炼
   - 确认第二周变更为"本周：3/3"(已完成)
   - 验证总进度更新为：6/12 = 50%

**额外测试点：**

- 模拟用户提前完成周目标的情况 (如周一至周三连续锻炼 3 天)
- 测试一天内多次记录的处理方式 (如一天锻炼 2 次)
- 验证跨过一周不记录后，下周的进度显示情况

**预期结果：**

- 任务卡片显示"每周 3 天"和总共 12 次的目标
- 第一周完成后，显示进度为 3/12 = 25%
- 第二周部分完成 (2 天) 后，进度为 5/12 ≈ 41.7%
- 第二周补充完成后，进度为 6/12 = 50%
- 任务详情页面应显示每周记录历史和趋势图

### 8.2 阅读计划

**场景描述：**
创建一个阅读计划，每月读完一本书，目标 3 个月读完 3 本书。

**设置：**

- 关键结果标题："完成季度阅读计划"
- 循环类型：每几个月 (`type=N_MONTHS; interval=1; startDate=当月1日`)
- 日期范围：当月 1 日 ~ 3 个月后的最后一天
- 量化目标：起始值=0，目标值=3
- 量化单位：本
- 每周期目标量：1 本

**详细测试步骤：**

1. 创建阅读计划任务
   - 设置量化单位为"本"
   - 确认每月阅读 1 本的设置
   - 验证日期范围正确设置为 3 个月
2. 第一个月阅读记录
   - 在月中 (15 日) 记录完成 0.5 本
   - 在月末 (28 日) 再记录完成 0.5 本
   - 确认第一个月显示为"本月：1/1"(已完成)
3. 检查月度和总体进度
   - 验证月度进度百分比为 100%
   - 确认总进度为 1/3 ≈ 33.3%
4. 第二个月阅读记录
   - 在月初 (5 日) 记录完成 0.3 本
   - 在月中 (15 日) 记录完成 0.4 本
   - 在月末 (25 日) 记录完成 0.3 本
   - 确认第二个月显示为"本月：1/1"(已完成)
5. 更新总体进度检查
   - 验证总进度更新为 2/3 ≈ 66.7%
   - 检查进度条颜色变化

**额外测试点：**

- 测试一个月内多次小量记录的累计效果
- 验证月进度完成后的状态保持
- 检查跳过记录一个月后，下个月的显示情况
- 测试在月末最后一天记录的边界情况

**预期结果：**

- 任务卡片显示"每月 1 本"和总共 3 本的目标
- 第一个月完成后，进度为 1/3 ≈ 33.3%
- 第二个月完成后，进度为 2/3 ≈ 66.7%
- 任务详情页面应显示月度完成趋势
- 任务完成时应有明显的成功提示和动画效果

## 9. 极端情况测试

### 9.1 循环规则变更测试

**初始设置：**

- 关键结果标题："循环规则变更测试"
- 初始循环类型：每天 (`type=DAILY`)
- 日期范围：当前日期 ~ 当前日期 +14 天
- 量化目标：起始值=0，目标值=50

**详细测试步骤：**

1. 创建每天循环任务并记录
   - 记录前 3 天的进度 (每天值=5)
   - 验证总进度为 15/50 = 30%
2. 修改循环规则为每周几
   - 改为每周一三五 (`type=WEEKLY; byweekday=MO,WE,FR`)
   - 观察系统如何处理已记录的数据
   - 检查每日目标量的重新计算
3. 再次修改为无循环
   - 取消所有循环设置
   - 验证系统如何处理已记录的循环数据
   - 检查任务在日历视图的显示变化
4. 记录新的进度数据
   - 在无循环状态下记录进度
   - 确认数据如何叠加到之前的记录

**预期结果：**

- 修改循环规则后，已记录的进度数据应保留
- 每日目标量应根据新规则重新计算
- 循环类型变更后，未来日期的任务显示应遵循新规则
- 总进度计算应始终基于总目标值，不受循环规则变更影响

### 9.2 多类型循环共存测试

**场景描述：**
测试多个不同循环类型的任务同时存在时，系统的整体表现。

**设置：**
同时创建以下任务：

1. 每天任务 (`type=DAILY`)
2. 每周一三五任务 (`type=WEEKLY; byweekday=MO,WE,FR`)
3. 每月 1 日和 15 日任务 (`type=MONTHLY; bymonthday=1,15`)
4. 每周 4 天任务 (`type=WEEKLY_N_TIMES; count=4`)

**测试步骤：**

1. 创建以上四个不同循环类型的任务
2. 在"今日"视图查看任务列表
   - 确认所有符合当日条件的任务全部显示
   - 验证每个任务的循环标识是否正确
3. 使用日历视图逐日检查未来 7 天
   - 验证每天显示的任务是否符合各自的循环规则
   - 检查任务排序逻辑
4. 同时记录多个任务的进度
   - 确认各任务进度独立计算
   - 验证汇总视图是否正确展示所有任务
5. 测试筛选功能
   - 按循环类型筛选任务
   - 验证筛选结果是否准确

**预期结果：**

- 各类型循环任务共存时不互相干扰
- "今日"视图正确显示当天应完成的所有循环任务
- 日历视图准确预览未来日期的任务分布
- 任务列表支持按循环类型进行筛选
- 系统资源占用正常，无明显延迟

### 9.3 长期循环任务测试

**场景描述：**
测试设置为长期 (如一年) 的循环任务在系统中的表现。

**设置：**

- 关键结果标题："年度长期循环任务测试"
- 循环类型：每月 1 日 (`type=MONTHLY; bymonthday=1`)
- 日期范围：当前日期 ~ 当前日期 +365 天
- 量化目标：起始值=0，目标值=120

**测试步骤：**

1. 创建长期循环任务
   - 设置为期一年的日期范围
   - 确认系统能否处理长期循环的计算
2. 检查长期任务的显示方式
   - 查看任务详情页中的循环预览
   - 验证系统是否提供整体规划视图
3. 测试月度视图导航
   - 在日历中查看未来几个月的任务分布
   - 验证跨月导航是否流畅
4. 记录持续进度
   - 模拟连续 3 个月的进度记录
   - 检查长期趋势图表的显示

**预期结果：**

- 系统应能稳定处理长期循环任务
- 提供合适的视图以查看长期任务进度
- 月度导航功能正常工作，支持快速查看未来月份
- 任务详情页面应显示完整的长期进度统计
- 系统性能不受长期任务范围影响

## 10. 错误处理与恢复测试

### 10.1 无效循环设置测试

**场景描述：**
测试用户输入无效循环设置时系统的响应和错误提示。

**测试步骤：**

1. 测试无效的日期选择
   - 尝试设置结束日期早于开始日期
   - 验证系统是否阻止操作并提供错误提示
2. 测试无效的循环配置
   - 设置每周几天 (`WEEKLY_N_TIMES`) 但天数为 0 或超过 7
   - 尝试不选择任何星期几就保存每周几 (`WEEKLY`) 循环
   - 确认系统是否有合适的验证和提示
3. 测试无效的目标设置
   - 尝试将目标值设置为小于起始值
   - 验证系统处理方式 (除非特殊情况如减重目标)
4. 测试极端数值
   - 设置极大的循环间隔 (如 999 天)
   - 设置极大的目标值 (如 99999)
   - 检查系统是否有合理的限制和提示

**预期结果：**

- 所有无效输入应被及时拦截，不允许保存
- 系统提供清晰、友好的错误提示
- 用户界面应高亮显示需要修正的字段
- 修正错误后，保存功能恢复正常

### 10.2 数据同步与恢复测试

**场景描述：**
测试循环任务数据在网络中断后的同步与恢复能力。

**测试步骤：**

1. 创建循环任务并记录部分进度
2. 模拟网络中断
   - 断开设备网络连接
   - 尝试记录新的进度数据
   - 确认用户体验和错误提示
3. 重新连接网络
   - 恢复网络连接
   - 验证离线记录的数据是否成功同步
   - 检查数据一致性
4. 测试多设备同步
   - 在设备 A 上创建并记录循环任务
   - 在设备 B 上登录同一账号
   - 验证循环规则和进度数据是否完整同步

**预期结果：**

- 离线状态下应提供适当的用户提示
- 网络恢复后数据应自动同步
- 多设备间循环规则和进度记录保持一致
- 同步过程应有明确的视觉反馈

## 11. 用户体验测试

### 11.1 循环任务提醒测试

**场景描述：**
测试循环任务的提醒功能。

**测试步骤：**

1. 创建带提醒的循环任务
   - 设置每天循环任务
   - 启用提醒功能并设置提醒时间
2. 检查提醒触发
   - 验证提醒是否按设定时间触发
   - 确认提醒内容是否包含循环信息
3. 测试完成后的提醒行为
   - 完成当天任务后，检查提醒是否自动取消
   - 验证次日提醒是否仍然正常
4. 测试提醒设置修改
   - 更改提醒时间
   - 确认系统是否正确应用到未来所有循环日

**预期结果：**

- 循环任务提醒应按时准确触发
- 提醒通知应包含任务名称和循环信息
- 完成任务后当天提醒自动取消
- 修改提醒设置后应用到所有未来循环日

### 11.2 循环任务可视化测试

**场景描述：**
测试循环任务的可视化展示效果。

**测试步骤：**

1. 检查循环标识的清晰度
   - 验证任务卡片上的循环图标是否醒目
   - 确认不同循环类型是否有区分
2. 测试进度显示的直观性
   - 检查"今日"、"本周"、"本月"等进度指标的展示
   - 验证完成状态的视觉反馈
3. 评估日历视图的循环任务显示
   - 检查循环日期是否在日历上有特殊标记
   - 验证不同循环类型在日历上的区分度
4. 测试统计报表
   - 查看循环任务的完成率统计
   - 评估趋势图的信息传达效果

**预期结果：**

- 循环任务在 UI 中有明显的视觉区分
- 进度显示简洁直观，一目了然
- 日历视图能清晰展示未

<template>
  <view class="flex items-center flex-col h-[100vh]">
    <view
      class="wh-200 bg-blue rounded-[50%] flex flex-none justify-center items-center mx-[20px] mt-[40px] color-white text-50"
      v-for="(item, index) in habitList" :key="index" @click="addRec(item)">
      {{ item.title }}
    </view>
    <view class="mt-[50px]">
      <view class="mb-[10px]" v-for="(item, index) in habits" :key="index">
        <text class="inline-block w-[20px]">{{ item.title }}</text>
        <text class="ml-[20px]">{{ dayjs(item.createTime).format('MM-DD HH:mm:ss') }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">

const habitList = ref([
  {
    title: 'L',
    type: 'l'
  },
  {
    title: 'W',
    type: 'w'
  }
])

const init = async () => {

  const res = await request.post('/okr/getHabits', {})
  // 按照时间倒序
  habits.value = res.data.sort((a: any, b: any) => {
    return new Date(b.createTime).getTime() - new Date(a.createTime).getTime()
  })

  uni.hideLoading()
}
onMounted(init)
const habits = ref([])
const addRec = async (item: any) => {
  uni.showLoading({
    title: '不是吧',
  })
  uni.vibrateShort()
  await request.post('/okr/setHabits', {
    _id: generateUUID(),
    title: item.title,
    type: item.type,
    createTime: new Date().toISOString()
  })
  init()
  // addHabitApi({
  //   title: item.title,
  //   type: item.type
  // }).then(() => {
  //   uni.showToast({
  //     title: '不是吧',
  //     icon: 'error'
  //   })
  //   uni.vibrateShort()
  // })
}
</script>
<style scoped lang="scss">
.pages {
  // height: 100vh;
  background-color: hsl(205deg, 20%, 94%);
  // padding: 10px;
  margin-top: 20rpx;
}

.today-btn {
  positon: fixed;
  right: 50rpx;
  bottom: 50rpx;
  width: 100rpx;
}

.todo-box {
  background-color: #fff;
  padding: 10px;
}

.new-todo-btn {
  position: fixed;
  bottom: 50px;
  right: 50px;
  width: 40px;
  height: 40px;
  background-color: $uni-bg-color-grey;
  border-radius: 50%;
}

.edit-todo-box {
  padding: 10px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  background-color: #fff;

  .operation-panel {
    display: flex;

    .edit-btn {
      margin-left: auto;
      margin-right: 0;
    }
  }
}

.titlee {
  font-size: 30rpx;
  font-weight: bold;
  margin-top: 30rpx;
  margin-bottom: 20rpx;
}

.fixed-placeholder {
  padding-top: calc(var(--status-bar-height) + 80rpx);
}
</style>

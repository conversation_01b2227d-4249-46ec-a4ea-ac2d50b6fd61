# 主题切换功能需求

## 背景
为了提升用户个性化体验，允许用户根据自己的喜好定制应用外观，需要为应用增加主题切换功能。此功能将基于多种预设的颜色主题。

## 需求
### 功能需求
-   应用需支持多种预设的颜色主题（例如：默认、落日、海洋等）。
-   用户可以在设置页面中浏览并选择喜欢的主题。
-   选择主题后，整个应用的界面颜色应立即随之改变，无需重启或刷新页面。
-   用户的选择应被持久化存储，下次打开应用时保持上次选择的主题。

### 非功能需求
-   主题切换过程应流畅，无明显卡顿。
-   新的颜色方案应保证内容的可读性和界面的美观性。

## 技术方案
### 实现思路
核心实现方式是通过 CSS 变量来动态切换主题。

1.  **定义颜色变量**: 在 `src/styles/variables.css` 文件中，利用 `:root` 选择器定义默认主题的颜色变量。
2.  **定义多套主题变量**: 在同一文件中，为每套主题使用一个唯一的 `data-theme` 属性选择器来定义其颜色变量，并覆盖默认值。例如 `[data-theme='sunset']`, `[data-theme='ocean']` 等。
3.  **动态切换**:
    -   通过本地存储 `uni.setStorageSync` 管理当前的主题名称 (如 `theme: 'default'` 或 `theme: 'sunset'`)。
    -   在应用的根组件或 `App.vue` 中，从本地存储读取当前主题名称，并动态地为页面的根元素（如 `page` 或 `body`）添加对应的 `data-theme` 属性。
    -   在设置页面提供主题选择列表，用户点击后更新主题状态，并将其持久化到本地存储。

### 架构设计
```mermaid
graph TD
    A[用户在设置页选择主题] --> B{更新全局状态};
    B --> C[使用 uni.setStorageSync 持久化];
    B --> D[在根元素上设置 data-theme 属性];
    D --> E[CSS根据属性自动应用新主题的颜色变量];
```

### 技术栈与约束
-   **技术**: CSS Custom Properties (CSS 变量)
-   **状态管理**: `uni.setStorageSync`
-   **主要修改文件**:
    -   `src/styles/variables.css`: 定义所有主题的颜色。
    -   `src/pages/setting/setting.vue`: 提供切换 UI。
    -   `App.vue`: 监听主题变化并应用到根元素。
-   **约束**: 必须通过修改 `src/styles/variables.css` 的方式实现，而不是为每个组件编写多套样式。 
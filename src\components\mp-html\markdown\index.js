import MarkdownIt from 'markdown-it'
import { marked } from 'marked'
const md = new MarkdownIt({ breaks: true })
/**
 * 生成 uuid
 */
export function generateUUID() {
  let d = new Date().getTime() // Timestamp
  // let d2 = (performance && performance.now && performance.now() * 1000) || 0 // Time in microseconds since page-load or 0 if unsupported
  let d2 = 0 // Time in microseconds since page-load or 0 if unsupported
  return 'xxxxxxxxxxxxxxxxxxxxxxxx'.replace(/[x]/g, function (c) {
    let r = Math.random() * 16 // random number between 0 and 16
    if (d > 0) {
      // Use timestamp until depleted
      r = (d + r) % 16 | 0
      d = Math.floor(d / 16)
    } else {
      // Use microseconds since page-load if supported
      r = (d2 + r) % 16 | 0
      d2 = Math.floor(d2 / 16)
    }
    return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16)
  })
}
/**
 * 判断是否是 UUID
 */
export const isUUID = (uuid: string) => /^[0-9a-f]+$/i.test(uuid)

/**
 * 解析 YAML 内容
 */
export function parseYAML(yamlString: string) {
  console.log('yamlString', yamlString)
  const yamlContent = yamlString.match(/---\n([\s\S]*?)\n---/)?.[1] || ''
  return yamlContent.split('\n').reduce((acc, line) => {
    const [key, value] = line.split(':').map((s) => s.trim())
    if (key && value) {
      acc[key] = key === 'color' ? `#${value}` : value
    }
    return acc
  }, {} as Record<string, string>)
}

/**
 * 判断是否是对象
 */
export const isObject = (value: any) => {
  // 基本类型和 null 直接返回 false
  if (value === null || typeof value !== 'object') {
    return false
  }

  // Array 和 Function 也属于对象，但不符合我们此处的定义，故需排除
  const type = Object.prototype.toString.call(value)
  return type === '[object Object]'
}
// 获取 url 参数
export const getUrlParams = () => {
  const routes = getCurrentPages() // 获取当前打开过的页面路由数组
  const curPage = routes[routes.length - 1] //获取当前页面路由
  let queryObj = {}
  // #ifdef H5
  queryObj = curPage.$route.query
  // #endif
  // #ifdef APP-PLUS
  queryObj = curPage.$page.options
  // #endif
  for (let key in queryObj) {
    queryObj[key] = decodeURIComponent(queryObj[key])
  }
  return queryObj
}
//获取当前路由、参数
export const getRoute = {
  params() {
    const routes = getCurrentPages() // 获取当前打开过的页面路由数组
    //    console.log('routes', routes)
    // console.info('routes', JSON.stringify(routes))
    const curPage = routes[routes.length - 1] //获取当前页面路由
    //    console.log('curPage', curPage)
    // console.log('curPage', JSON.stringify(curPage))

    let queryObj = {}
    // #ifdef H5
    queryObj = curPage.$route.query
    // #endif
    // #ifdef APP-PLUS
    queryObj = curPage.$page.options
    // #endif
    for (let key in queryObj) {
      queryObj[key] = decodeURIComponent(queryObj[key])
    }
    return queryObj
  },
  path() {
    const routes = getCurrentPages() // 获取当前打开过的页面路由数组
    let curRoute = routes[routes.length - 1].route //获取当前页面路由
    return '/' + curRoute
  },
}

// /**
//  * 处理 api 返回的数据
//  * @param promise api 返回的 promise
//  * @returns 处理后的数据
//  */
// export function handleApi(promise: Promise<any>) {
//   return promise
//     .then((data) => {
//       console.log('===handleApi===', data)
//       syncToServer()
//       if (data === 0) {
//         throw new Error('操作失败')
//       }
//     })
//     .catch((error) => {
//       console.error(error)
//       uni.showToast({
//         title: '操作失败',
//         icon: 'error',
//       })
//     })
// }

export function generateRandomString(length = 5) {
  let result = ''
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const charactersLength = characters.length
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
  }
  return result
}

const frequency = {
  YEARLY: 0,
  MONTHLY: 1,
  WEEKLY: 2,
  DAILY: 3,
  HOURLY: 4,
  MINUTELY: 5,
  SECONDLY: 6,
}

export function parseRRULE(rruleString: string) {
  // 移除 "RRULE:" 前缀
  const paramsString = rruleString.replace('RRULE:', '')
  // 分割字符串以获得所有的键值对
  const params = paramsString.split(';')
  // 初始化一个对象来存储键值对
  const rruleParams: any = {}

  // 遍历每个键值对，解析并添加到结果对象中
  params.forEach((param) => {
    let [key, value] = param.split('=')
    if (key === 'FREQ') {
      value = frequency[value]
    }
    if (key === 'INTERVAL') value = parseInt(value)
    key = key.toLowerCase()
    rruleParams[key] = value
  })

  // 返回解析后的参数对象
  return rruleParams
}

// 十六进制颜色转换为 rgba
export const hexToRGBA = (hex = '#6d8dfe', alpha = 1) => {
  let r = parseInt(hex.slice(1, 3), 16),
    g = parseInt(hex.slice(3, 5), 16),
    b = parseInt(hex.slice(5, 7), 16)
  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}

export const toolCloudSync = {
  isOpen: () => {
    const isCloudSync = uni.getStorageSync('isCloudSync')
    return isCloudSync === '1'
  },
  open: () => {
    uni.setStorageSync('isCloudSync', '1')
  },
  close: () => {
    uni.removeStorageSync('isCloudSync')
  },
}

export function renderMarkdown(content: string): string {
  return md.render(content)
}

/**
 * 使用 marked 解析 Markdown 内容
 * @param content Markdown 文本内容
 * @returns 解析后的 HTML 字符串
 */
export function parseMarkdown(content: string): string {
  try {
    // 配置 marked 选项
    marked.setOptions({
      breaks: true, // 允许换行符转换为 <br>
      gfm: true, // 启用 GitHub 风格的 Markdown
      headerIds: true, // 为标题添加 id 属性
      mangle: false, // 不转义标题中的特殊字符
      sanitize: false, // 不过滤 HTML 标签
      silent: true, // 忽略解析错误
    })

    // 解析 Markdown 内容
    return marked.parse(content)
  } catch (error) {
    console.error('Markdown 解析失败：', error)
    return content // 解析失败时返回原始内容
  }
}

export const toolDebounce = (func: (...args: any[]) => void, delay = 500) => {
  let timeoutId: number | null = null
  return (...args: any[]) => {
    if (timeoutId) clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}
/**
 * 获取网络状态
 * @returns 网络状态 wifi / 4g / 3g / 2g / ethernet(有线网络)/ none
 */
export const toolGetNetworkType = () => {
  return new Promise((resolve) => {
    uni.getNetworkType({
      success: (res) => {
        resolve(res.networkType)
      },
      complete: () => {
        resolve('none')
      },
    })
  })
}

export const toolGlobalData = {
  /**
   * 获取全局变量
   * @param key 传则获取指定 key 的值，不传则获取全部
   */
  get: (key?: string | undefined) => {
    if (key) {
      return getApp().globalData[key]
    } else {
      return getApp().globalData
    }
  },
  set: (key, value) => {
    getApp().globalData[key] = value
  },
}

/**
 * 路由封装
 * 简化页面跳转操作
 */
export const router = {
  /**
   * 保留当前页面，跳转到应用内的某个页面
   * @param url 目标页面路径
   * @param params 页面参数
   */
  push(url: string, params?: Record<string, any>) {
    if (params) {
      const query = Object.keys(params)
        .map(
          (key) =>
            `${key}=${encodeURIComponent(typeof params[key] === 'object' ? JSON.stringify(params[key]) : params[key])}`
        )
        .join('&')
      url = url.includes('?') ? `${url}&${query}` : `${url}?${query}`
    }
    uni.navigateTo({ url })
  },

  /**
   * 关闭当前页面，跳转到应用内的某个页面
   * @param url 目标页面路径
   * @param params 页面参数
   */
  redirect(url: string, params?: Record<string, any>) {
    if (params) {
      const query = Object.keys(params)
        .map(
          (key) =>
            `${key}=${encodeURIComponent(typeof params[key] === 'object' ? JSON.stringify(params[key]) : params[key])}`
        )
        .join('&')
      url = url.includes('?') ? `${url}&${query}` : `${url}?${query}`
    }
    uni.redirectTo({ url })
  },

  /**
   * 关闭所有页面，打开到应用内的某个页面
   * @param url 目标页面路径
   * @param params 页面参数
   */
  reLaunch(url: string, params?: Record<string, any>) {
    if (params) {
      const query = Object.keys(params)
        .map(
          (key) =>
            `${key}=${encodeURIComponent(typeof params[key] === 'object' ? JSON.stringify(params[key]) : params[key])}`
        )
        .join('&')
      url = url.includes('?') ? `${url}&${query}` : `${url}?${query}`
    }
    uni.reLaunch({ url })
  },

  /**
   * 跳转到 tabBar 页面
   * @param url tabBar 页面路径
   */
  switchTab(url: string) {
    uni.switchTab({ url })
  },

  /**
   * 关闭当前页面，返回上一页面或多级页面
   * @param delta 返回的页面数，如果 delta 大于现有页面数，则返回到首页
   */
  back(delta = 1) {
    uni.navigateBack({ delta })
  },
}

# 提交周目标调用提取接口
**入参**
```json
{
  "content": "周目标内容（用户输入的文本）",
  "date": "YYYY-WW（周数格式，如 2023-25）"
}
```

**出参**
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "weekGoals": [
      {
        "id": "goal_1",
        "content": "目标 1 内容",
        "progress": 0,
        "tasks": []
      },
      {
        "id": "goal_2",
        "content": "目标 2 内容",
        "progress": 0,
        "tasks": []
      }
    ],
    "rawContent": "原始周目标内容"
  }
}
```

# 提交日回顾调用接口
**入参**
```json
{
  "weekOverview": {
    "weekGoals": [
      {
        "id": "goal_1",
        "content": "目标 1 内容",
        "progress": 0,
        "tasks": []
      },
      {
        "id": "goal_2",
        "content": "目标 2 内容",
        "progress": 0,
        "tasks": []
      }
    ]
  },
  "dailyTasks": {
    "date": "YYYY-MM-DD",
    "completedTasks": [
      {
        "id": "task_1",
        "content": "任务 1 内容",
        "completed": true
      },
      {
        "id": "task_2",
        "content": "任务 2 内容",
        "completed": true
      }
    ]
  }
}
```

**出参**
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "weekGoals": [
      {
        "id": "goal_1",
        "content": "目标 1 内容",
        "progress": 50,
        "tasks": [
          {
            "id": "task_1",
            "content": "任务 1 内容",
            "completed": true,
            "date": "YYYY-MM-DD"
          }
        ]
      },
      {
        "id": "goal_2",
        "content": "目标 2 内容",
        "progress": 0,
        "tasks": []
      }
    ],
    "analysis": {
      "summary": "今日完成情况总结",
      "progressChange": "+25%",
      "recommendations": "基于完成情况的建议"
    }
  }
}
```
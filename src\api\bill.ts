import db from './database'

/**
 * 添加一条流水
 * @param params 参数
 */
export const addBillApi = async (params: any) => {
  const res = await db.table('bill').add({
    ...params,
  })
  return res
}
/**
 * 更新
 * @param {string} id 更新 id
 * @param {object} params 更新参数
 */
export const updateBillApi = async (id: string, params: API.EditOkr) => {
  return await db.table('bill').update(id, params)
}
// 获取流水列表
export const getBillListApi = async () => {
  const res = await db.table('bill').where().toArray()
  return res
}
/**
 * 获取详情
 * @param {string} _id 查询 id
 */
export const getBillApi = async (_id: string) => {
  const res = await db.table('bill').get(_id)
  return res
}
/**
 * 删除
 * @params 传入 _id 或者 筛选条件
 */
export const delBillApi = async (params: string) => {
  try {
    if (isUUID(params)) {
      await db.table('bill').update(params, { deleteTime: new Date().toISOString() })
    } else {
      await db.table('bill').where(params).modify({ deleteTime: new Date().toISOString() })
    }
  } catch (error) {
    console.error(error)
    throw new Error(String(error))
  }
}

/**
 * 添加一条分类
 * @param params 参数
 */
export const addCategoryApi = async (params: { name: string }) => {
  await db.table('category').add({
    ...params,
  })
}
/**
 * 批量添加分类
 * @param params 参数
 */
export const bulkCategoryApi = async (params: any) => {
  await db.table('category').bulkPut([...params])
}
// 获取分类
export const getCategoryApi = async () => {
  const res = await db.table('category').where().toArray()
  return res
}
/**
 * 更新分类
 * @param {string} id 更新 id
 * @param {object} params 更新参数
 */
export const updateCategoryApi = async (id: string, params: API.EditOkr) => {
  return await db.table('category').update(id, params)
}
/**
 * 删除分类
 * @params 传入 _id 或者 筛选条件
 */
export const delCategoryApi = async (params: string) => {
  try {
    if (isUUID(params)) {
      await db.table('category').update(params, { deleteTime: new Date().toISOString() })
    } else {
      await db.table('category').where(params).modify({ deleteTime: new Date().toISOString() })
    }
  } catch (error) {
    console.error(error)
    throw new Error(String(error))
  }
}

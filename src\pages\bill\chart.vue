<template>
  <view class="bg-[#3eb575] p-4 pb-10 color-white">
    <view class="text-30" @click="openMonth = true">
      {{ dayjs(currentMonth).format('YYYY 年 M 月') }}
      <u-icon name="calendar"></u-icon>
    </view>
    <view class="mt-8 op-70">共支出</view>
    <view class="text-55 mt-2 font-semibold">￥ {{ totalMoney }}</view>
  </view>

  <view class="px-4">
    <view class="mt-10 text-40">支出构成</view>
    <view class="w-full h-400">
      <qiun-data-charts type="ring" :opts="opts" :chartData="chartData" />
    </view>
    <!-- 支出排行 -->
    <view mt-8>
      <view v-for="(item, index) in displayedClassList" :key="index" class="flex items-center h-80"
        @click="goToFlow(item.id)">
        <view class="text-30 w-[80px]">{{ item.name }}</view>
        <view class="flex-1">
          <view :style="{
            width: item.Proportion + '%',
          }" class="h-15 rounded-[10px] bg-[#3eb575]"></view>
        </view>
        <view class="w-[100px] text-right">
          ￥{{ item.value }}
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <view class="text-center color-gray mt-4" @click="toggleClassList">
        {{ classMore ? '收起' : '展开更多' }}
        <u-icon :name="classMore ? 'arrow-up' : 'arrow-down'"></u-icon>
      </view>
    </view>
    <view my-10>
      <u-line color="gray" />
    </view>
    <!-- 每日对比 -->
    <view class="mt-10 mb-20 text-40">每日对比</view>
    <view class="pl-10">
      <view class="relative mb-10 w-full h-500" style="border-bottom: 1px solid #ddd">
        <!-- 动态刻度线 -->
        <view v-for="(tick, i) in yTicks" :key="i" class="w-full bg-[#e8e8e8] h-[1px] absolute"
          :style="{ bottom: `${(100 / (yTicks.length - 1)) * i}%` }">
          <view class="w-60 absolute left--80 top--15 text-right">{{ tick }}</view>
        </view>
        <!-- 柱状条 -->
        <view @click="curDay = i" v-for="(d, i) in dayBills" :key="i" :style="{ left: i * 22 + 'rpx' }"
          class="w-[2%] absolute bottom-0 h-[100%] flex flex-col items-center">
          <u-line :class="curDay === i ? 'op-100' : 'op-0'" class="flex-1" direction="col" border-style="dashed"
            color="#333" />
          <transition name="fade">
            <view :class="curDay === i ? 'op-100' : 'op-40'" class="w-full bg-[#3eb575] transition-opacity duration-200"
              :style="{ height: d.percentage + '%' }">
              <view class="translate-x-[-50%] absolute bottom--35 left-0 t-22">
                {{ i % 5 === 0 ? dayjs(d.date).format('M.D') : '' }}
              </view>
            </view>
          </transition>
        </view>
        <view v-if="curDay !== null" @click="handleDayClick(dayBills[curDay].date)" :style="{
          left: curDay * 22 + 'rpx',
          transform: 'translateX(-50%)',
        }" class="p-2 absolute top--110 bg-black w-250 h-100 flex justify-center rounded-1 z-10">
          <view>
            <view class="color-white t-22"> {{ dayjs(dayBills[curDay].date).format('M 月 D 日') }} 共支出 </view>
            <view class="color-[#3eb575] t-22 font-bold mt-1"> ￥{{ dayBills[curDay].totalAmount || '0.00' }} </view>
          </view>
          <u-icon name="arrow-right" class="color-white t-15 ml-1"></u-icon>
        </view>
      </view>
    </view>

    <view my-10>
      <u-line color="gray" />
    </view>
    <!-- 月度对比 -->
    <view class="mt-10 mb-20 text-40">月度对比</view>
    <view>
      <view class="relative mb-10 w-full h-500" style="border-bottom: 1px solid #ddd">
        <!-- 柱状条 -->
        <view class="absolute bottom-0 w-full h-full flex justify-around items-end">
          <view v-for="(m, i) in lastSixMonths" :key="m.month" @click="curMonthIndex = i"
            class="w-[6%] h-full flex flex-col items-center relative">
            <transition name="fade">
              <view :class="curMonthIndex === i ? 'op-100' : 'op-40'"
                class="absolute bottom-0 w-full bg-[#3eb575] transition-opacity duration-200"
                :style="{ height: m.percentage + '%' }">
                <view class="color-[#3eb575] translate-x-[-50%] absolute top--50 left-[50%] t-18 text-center">
                  ￥{{ m.total.toFixed(2) }}
                </view>
                <view class="translate-x-[-50%] absolute bottom--35 left-[50%] w-50 t-18 text-center">
                  {{ dayjs(m.month).format('M 月') }}
                </view>
              </view>
            </transition>
          </view>
        </view>
      </view>
    </view>
    <view class="text-gray pt-6 pb-2">{{ dayjs(curMonthIndex === null ? currentMonth :
      lastSixMonths[curMonthIndex]?.month).format('M 月') }}支出排行</view>
    <view>
      <uni-swipe-action>
        <uni-swipe-action-item v-for="(item, i) in topTenExpenses" :key="item._id" :right-options="options"
          @click="onClick($event, item)">
          <view class="flex items-center px-4" @click="showBillPop(item._id)">
            <view class="t-30 text-gray w-60">{{ i + 1 }}</view>
            <view class="bg-[#3eb575] mr-4 w-70 h-70 rounded-[50%] color-white flex justify-center items-center">
              {{ categoryMap[item.category] ? categoryMap[item.category][0] : '无' }}
            </view>
            <view class="flex-1 py-4" style="border-bottom: 1px solid #eee">
              <view class="flex justify-between font-medium">
                <text>{{ item.categoryName }}</text>
                <text class="font-semibold">-{{ item.amount.toFixed(2) }}</text>
              </view>
              <view class="mt-1 flex justify-between items-center text-gray text-25">
                <view>{{ item.remark || item.merchants }}</view>
                <view>{{ dayjs(item.date).format('M 月 D 日 HH:mm') }}</view>
              </view>
            </view>
          </view>
        </uni-swipe-action-item>
      </uni-swipe-action>
    </view>
    <view @click="goFlow" class="py-4 flex items-center justify-center color-gray">
      <view>全部排行</view>
      <view>
        <u-icon name="arrow-right"></u-icon>
      </view>
    </view>
  </view>
  <l-month-pop v-model:open="openMonth" v-model:date="currentMonth"></l-month-pop>
  <l-day-bill-pop v-model:open="openDayBill" v-model:date="selectedDate"></l-day-bill-pop>
  <!-- 添加账单编辑弹窗组件 -->
  <l-new-bill :editId="editId" v-model:open="isNewBillOpen" @submit="newBill"></l-new-bill>
</template>

<script setup>
import currency from 'currency.js'

// 账单数据
const bills = ref([])
// 控制分类列表展开/收起
const classMore = ref(false)
// 控制月份选择弹窗
const openMonth = ref(false)
// 分类 id 与名称的映射对象
const categories = ref([])
const openDayBill = ref(false)
const selectedDate = ref('')

// 图表配置选项
const opts = ref({
  rotate: false,
  rotateLock: false,
  dataLabel: true,
  enableScroll: false,
  legend: {
    show: false,
    position: 'right',
    lineHeight: 25,
  },
  title: {
    name: '',
    fontSize: 15,
    color: '#666666',
  },
  subtitle: {
    name: '',
    fontSize: 25,
    color: '#7cb5ec',
  },
  extra: {
    markLine: {
      data: [
        { name: '平均值', value: 100 },
        { name: '最大值', value: 200 },
      ],
    },
    ring: {
      ringWidth: 40,
      activeOpacity: 0.5,
      activeRadius: 10,
      offsetAngle: 0,
      labelWidth: 15,
      border: false,
      borderWidth: 3,
      borderColor: '#FFFFFF',
    },
  },
})

// 图表数据结构
const chartData = ref({
  series: [
    {
      data: [],
    },
  ],
})

// 滑动操作选项
const options = [
  {
    text: '删除',
    key: 'delete',
    style: {
      backgroundColor: '#dd524d',
    },
  },
]
// 删除当前这条账单
const onClick = async (e, item) => {
  if (e.position === 'right' && e.content.key === 'delete') {
    // 删除账单
    try {
      await delBillApi(item._id)
      init() // 重新初始化账单数据
      uni.showToast({
        title: '删除成功',
        icon: 'success',
      })
    } catch (error) {
      uni.showToast({
        title: '删除失败',
        icon: 'none',
      })
    }
  }
}
// 当前选中的日期索引
const curDay = ref(null)
// 当前选中的月份，默认为当前月
const currentMonth = ref(dayjs().format('YYYY-MM'))
// 当前选中的月份索引
const curMonthIndex = ref(null)

// 计算当月总支出
const totalMoney = computed(() => {
  return bills.value.reduce((acc, cur) => currency(acc).add(cur.amount), 0)
})

const goFlow = () => {
  const targetMonth = curMonthIndex.value === null
    ? currentMonth.value
    : lastSixMonths.value[curMonthIndex.value]?.month

  uni.navigateTo({
    url: '/pages/bill/flow?month=' + targetMonth
  })
}

// 使用计算属性获取分类映射
const categoryMap = computed(() => {

  const map = {}
  categories.value.forEach(category => {
    map[category._id] = category.name
  })
  return map
})

// 计算支出构成数据
const classList = computed(() => {
  const categoryAmounts = new Map()
  bills.value.forEach((bill) => {
    if (categoryAmounts.has(bill.category)) {
      categoryAmounts.set(bill.category, currency(categoryAmounts.get(bill.category)).add(bill.amount))
    } else {
      categoryAmounts.set(bill.category, currency(bill.amount))
    }
  })
  const series = []
  for (const [categoryId, value] of categoryAmounts) {
    const percentage = ((value.value / totalMoney.value) * 100).toFixed(2) + '%'
    series.push({
      name: categoryId,
      value: value.value,
      labelText: categoryMap.value[categoryId] + ' ' + percentage
    })
  }
  chartData.value.series[0].data = series

  const sortedCategories = Array.from(categoryAmounts.entries())
    .map(([categoryId, amount]) => ({ categoryId, amount }))
    .sort((a, b) => b.amount - a.amount)
  const maxAmount = sortedCategories.length > 0 ? sortedCategories[0].amount.value : 1

  return sortedCategories.map((item) => ({
    id: item.categoryId,
    name: categoryMap.value[item.categoryId],
    value: item.amount.value.toFixed(2),
    Proportion: ((item.amount.value / maxAmount) * 100).toFixed(2),
  }))
})

// 计算每日账单原始数据
const dayBillsRaw = computed(() => {
  const arr = []
  // 遍历当月每一天
  for (let i = 1; i <= dayjs(currentMonth.value).daysInMonth(); i++) {
    const date = dayjs(currentMonth.value).date(i).format('YYYY-MM-DD')
    const theBills = bills.value.filter((bill) => dayjs(bill.date).format('YYYY-MM-DD') === date)
    const totalAmount = theBills.reduce((acc, cur) => currency(acc).add(cur.amount), 0).value
    arr.push({ date, bills: theBills, totalAmount })
  }
  return arr
})

// 计算日支出最大值，用于确定图表刻度
const maxAmount = computed(() => {
  const rawMax = Math.max(...dayBillsRaw.value.map((item) => item.totalAmount).filter((v) => v > 0))
  if (rawMax <= 0) return 10
  const base = Math.pow(10, Math.floor(Math.log10(rawMax)))
  const ratio = rawMax / base
  let factor

  if (ratio <= 1.2) {
    factor = 1.2
  } else if (ratio <= 1.5) {
    factor = 1.5
  } else if (ratio <= 2) {
    factor = 2
  } else if (ratio <= 3) {
    factor = 3
  } else if (ratio <= 4) {
    factor = 4
  } else if (ratio <= 5) {
    factor = 5
  } else if (ratio <= 6) {
    factor = 6
  } else if (ratio <= 7) {
    factor = 7
  } else if (ratio <= 8) {
    factor = 8
  } else if (ratio <= 9) {
    factor = 9
  } else {
    factor = 10
  }

  return Math.ceil(factor * base)
})

// 计算每日支出数据，包含百分比
const dayBills = computed(() => {
  const mA = maxAmount.value
  return dayBillsRaw.value.map((item) => ({
    ...item,
    percentage: mA ? ((item.totalAmount / mA) * 100).toFixed(2) : 0,
  }))
})

// 计算 Y 轴刻度值
const yTicks = computed(() => {
  const max = maxAmount.value
  if (max <= 0) return [0]

  // 1. 根据 max 设置初始 step
  let step
  if (max <= 600) {
    step = 100
  } else if (max <= 1800) {
    step = 200
  } else if (max < 5000) {
    step = 500
  } else {
    step = 1000
  }

  // 2. 计算初步刻度数
  let count = Math.floor(max / step) + 1
  // 3. 最大刻度数不超过 10
  if (count > 10) {
    count = 10
    step = Math.ceil(max / (count - 1))
  }

  // 4. 生成最终刻度数组
  const arr = []
  for (let i = 0; i < count; i++) {
    arr.push(step * i)
  }
  return arr
})

// 计算最近6个月的支出数据
const lastSixMonths = computed(() => {
  const arr = []


  for (let i = 5; i >= 0; i--) {
    const monthStr = dayjs(currentMonth.value).subtract(i, 'month').format('YYYY-MM')
    const monthBills = allBills.value.filter((b) => dayjs(b.date).isSame(dayjs(monthStr), 'month'))
    console.log('monthBills', monthBills);

    const total = monthBills.reduce((acc, cur) => currency(acc).add(cur.amount), 0).value
    arr.push({ month: monthStr, total: total || 0 })
  }

  const maxVal = Math.max(...arr.map((item) => item.total), 1)
  console.log('lastSixMonths', arr, maxVal)
  return arr.map((it) => ({
    ...it,
    percentage: ((it.total / maxVal) * 100).toFixed(2),
  }))
})

// 根据展开状态显示分类列表
const displayedClassList = computed(() => {
  return classMore.value ? classList.value : classList.value.slice(0, 5)
})

// 切换分类列表展开状态
const toggleClassList = () => {
  classMore.value = !classMore.value
}

// 计算支出排行榜前 10
const topTenExpenses = computed(() => {
  // 如果没有选中月份，则显示当前月的数据
  const targetMonth = curMonthIndex.value === null
    ? currentMonth.value
    : lastSixMonths.value[curMonthIndex.value]?.month

  return allBills.value
    .filter((b) =>
      b.type === 'expense' &&
      dayjs(b.date).format('YYYY-MM') === targetMonth
    )
    .sort((a, b) => b.amount - a.amount)
    .slice(0, 10)
    .map(bill => ({
      ...bill,
      categoryName: categoryMap.value[bill.category] || '未知分类'
    }))
})
const allBills = ref([])
// 初始化数据
const init = async () => {
  // 获取分类列表
  categories.value = await getCategoryApi()

  // 获取并过滤当月账单
  const res = await getBillListApi()
  allBills.value = res
  bills.value = res.filter(
    (bill) => bill.type === 'expense' && dayjs(bill.date).isSame(currentMonth.value, 'month')
  )


}

// 组件挂载时初始化
onMounted(init)

// 监听月份变化，重新获取数据
watch(
  () => currentMonth.value,
  (val) => {
    init()
  }
)

const goToFlow = (category) => {
  uni.navigateTo({
    url: `/pages/bill/flow?month=${currentMonth.value}&category=${category}`
  })
}

const handleDayClick = (date) => {
  selectedDate.value = dayjs(date).format('YYYY-MM-DD')
  openDayBill.value = true
}

// 添加编辑相关变量
const editId = ref('')
const isNewBillOpen = ref(false)

// 添加编辑方法
const showBillPop = (id) => {
  editId.value = id
  isNewBillOpen.value = true
}

// 添加提交回调方法
const newBill = () => {
  isNewBillOpen.value = false
  init()
}
</script>
<style scoped>
.grid-table {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
}

.header {
  font-weight: bold;
}

.row {
  display: contents;
}

.uni-input {
  padding: 5px;
  border: 1px solid #ccc;
}
</style>

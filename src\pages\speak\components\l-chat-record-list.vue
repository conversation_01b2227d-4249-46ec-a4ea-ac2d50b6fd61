<template>
  <div class="chat-record-list">
    <div class="list-header">
      <div class="title">聊天记录</div>
      <div v-if="list.length" class="action-btn" @click="handleClear">
        <i class="fas fa-trash-alt"></i>
        <span>清空</span>
      </div>
    </div>

    <div v-if="loading" class="loading">
      <z-loading color="var(--color-primary)" size="40rpx" />
      <div class="loading-text">加载中...</div>
    </div>

    <div v-else-if="!list.length" class="empty">
      <i class="fas fa-comment-slash empty-icon"></i>
      <div class="empty-text">暂无聊天记录</div>
    </div>

    <div v-else class="record-items">
      <div
        v-for="(item, index) in list"
        :key="item._id"
        class="record-item"
        :class="{ active: activeId === item._id }"
        @click="handleSelect(item)"
      >
        <div class="item-content">
          <div class="item-title">{{ item.title || '无标题对话' }}</div>
          <div class="item-time">{{ formatTime(item.createTime) }}</div>
        </div>
        <div class="item-action" @click.stop="handleDelete(item._id, index)">
          <i class="fas fa-trash-alt"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, computed } from 'vue'
import dayjs from 'dayjs'
import { getChatRecordListApi, delChatRecordApi } from '@/api/chatRecord'

const props = defineProps({
  activeId: {
    type: String,
    default: '',
  },
})

const emits = defineEmits(['select', 'clear'])

const list = ref([])
const loading = ref(true)

// 获取聊天记录列表
const fetchList = async () => {
  loading.value = true
  try {
    const result = await getChatRecordListApi(`deleteTime == ""`)
    list.value = result.sort((a, b) => dayjs(b.createTime).valueOf() - dayjs(a.createTime).valueOf())
  } catch (error) {
    console.error('获取聊天记录失败', error)
  } finally {
    loading.value = false
  }
}

// 选择一条聊天记录
const handleSelect = (item) => {
  emits('select', item)
}

// 删除聊天记录
const handleDelete = async (id, index) => {
  try {
    await delChatRecordApi(id)
    list.value.splice(index, 1)
    uni.showToast({
      title: '删除成功',
      icon: 'success',
    })
  } catch (error) {
    console.error('删除聊天记录失败', error)
    uni.showToast({
      title: '删除失败',
      icon: 'error',
    })
  }
}

// 清空所有聊天记录
const handleClear = () => {
  uni.showModal({
    title: '提示',
    content: '确定要清空所有聊天记录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          // 循环删除所有记录
          const promises = list.value.map((item) => delChatRecordApi(item._id))
          await Promise.all(promises)
          list.value = []
          emits('clear')
          uni.showToast({
            title: '已清空聊天记录',
            icon: 'success',
          })
        } catch (error) {
          console.error('清空聊天记录失败', error)
          uni.showToast({
            title: '操作失败',
            icon: 'error',
          })
        }
      }
    },
  })
}

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return ''
  const date = dayjs(timeString)
  const now = dayjs()

  if (date.isSame(now, 'day')) {
    return date.format('HH:mm')
  } else if (date.isSame(now, 'year')) {
    return date.format('MM-DD HH:mm')
  } else {
    return date.format('YYYY-MM-DD HH:mm')
  }
}

// 初始加载
fetchList()

// 暴露刷新方法
defineExpose({
  refresh: fetchList,
})
</script>

<style lang="scss" scoped>
.chat-record-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--color-bg);

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--color-gray-200);

    .title {
      font-size: 18px;
      font-weight: 600;
      color: var(--color-gray-800);
    }

    .action-btn {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 14px;
      color: var(--color-danger);
      cursor: pointer;
    }
  }

  .loading,
  .empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
    padding: 20px;

    .loading-text,
    .empty-text {
      margin-top: 15px;
      font-size: 15px;
      color: var(--color-gray-600);
    }

    .empty-icon {
      font-size: 40px;
      color: var(--color-gray-400);
    }
  }

  .record-items {
    flex: 1;
    overflow-y: auto;

    .record-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid var(--color-gray-100);
      background-color: var(--color-white);
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: var(--color-gray-50);
      }

      &.active {
        background-color: var(--color-primary-light);

        .item-content .item-title,
        .item-content .item-time,
        .item-action {
          color: white;
        }
      }

      .item-content {
        flex: 1;
        width: 0;
        min-width: 0;

        .item-title {
          font-size: 15px;
          font-weight: 500;
          color: var(--color-gray-800);
          margin-bottom: 6px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .item-time {
          font-size: 13px;
          color: var(--color-gray-500);
        }
      }

      .item-action {
        padding-left: 16px;
        color: var(--color-gray-500);
        cursor: pointer;
        transition: color 0.2s;

        &:hover {
          color: var(--color-danger);
        }
      }
    }
  }
}
</style>

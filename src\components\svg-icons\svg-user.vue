<template>
  <BaseSvgIcon :path="path" :viewBox="viewBox" v-bind="$attrs" />
</template>

<script setup>
import BaseSvgIcon from './BaseSvgIcon.vue'

defineProps({
  viewBox: {
    type: String,
    default: '0 0 1024 1024',
  },
  path: {
    type: String,
    default:
      'M813 799.2c-38.2-93.4-130-159.2-237-159.2h-128c-107 0-198.8 65.8-237 159.2C139.8 724.4 96 623.4 96 512c0-229.8 186.2-416 416-416s416 186.2 416 416c0 111.4-43.8 212.4-115 287.2z m-80.2 65.4c-64 40.2-139.6 63.4-220.8 63.4s-156.8-23.2-221-63.4c14.6-73.4 79.4-128.6 157-128.6h128c77.6 0 142.4 55.2 157 128.6zM512 1024a512 512 0 1 0 0-1024 512 512 0 1 0 0 1024z m0-544a80 80 0 1 1 0-160 80 80 0 1 1 0 160z m-176-80a176 176 0 1 0 352 0 176 176 0 1 0-352 0z',
  },
})
</script>

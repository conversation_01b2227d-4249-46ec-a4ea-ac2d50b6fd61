# OKR 应用产品需求文档 v0605

## 1. 背景与目标

### 1.1 背景分析

目前系统中的关键结果 (KR) 存在两种形式：Todo 类型和量化类型。经过分析，这两种类型在用户体验和功能实现上存在重叠，导致系统逻辑复杂化且限制了功能扩展。

### 1.2 问题陈述

- Todo 类型与量化类型的主要区别仅在于交互方式（打勾 vs 填写进度）
- 当前实现方式增加了系统复杂度和维护成本
- 功能扩展（如循环任务）在双模式下实现困难

### 1.3 解决方案

统一采用量化类型作为关键结果的唯一形式，并在此基础上扩展功能：

- 保留量化类型的所有优势（如循环设置）
- 后期计划为量化类型添加细化任务功能
- 简化系统逻辑，提高开发效率和用户体验

## 2. 功能需求

### 2.1 关键结果编辑页

- 添加根据每日量计算总量的按钮

### 2.2 今天页

- 添加今天完成的 kr
- 点击目标 tag 跳转目标详情
- 重新开发循环功能
  - 不重复
  - 每天
  - [x] 每周几（列出周一到周日）
  - [x] 每月几号（列出 1 号到 31 号）
  - [x] 每隔几天
  - [x] 每周几天
    - 显示：本周 0/2 天 0/100 次
  - [x] 每月几天
    - 显示：本月 0/2 天 0/100 次
  - [x] 每几天（每 3 天，则是这 3 天内总共要完成指定次数）
    - 显示：第 2 天 3 天内：0/100 次
  - [x] 每几个周
    - 显示：第 2 周 3 周内：0/100 次
  - [x] 每几个月
    - 显示：第 2 月 3 月内：0/100 次
- [ ] 显示超前 xx 次，拖慢 xx 次
  - 今日卡片
  - kr 详情
    kr 详情页
- 日完成量

# 如何写需求文档？有哪些方式？需要写哪些要点？版本如何定？如何确定版本内容？

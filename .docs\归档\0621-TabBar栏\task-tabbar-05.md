# 任务：性能优化与最终调优

## 任务信息

- **所属功能模块**: TabBar 栏自定义
- **优先级**: 中
- **预估工时**: 2 人日
- **状态**: 待开发

## 任务描述

针对自定义 TabBar 功能进行性能优化和最终调优，解决可能存在的性能问题和边缘情况。包括组件缓存优化、首屏加载优化、动画性能优化以及兼容性问题修复。确保在各种设备和场景下都有良好的用户体验。

## 技术实现详情

### 1. 组件缓存优化

优化`keep-alive`的使用，改进组件缓存策略：

```vue
<template>
  <div class="container">
    <!-- 内容区域 -->
    <div class="content-area">
      <keep-alive :include="cacheList">
        <component :is="currentComponent" v-if="currentComponent" :key="currentTab" />
        <div v-else class="loading">加载中...</div>
      </keep-alive>
    </div>

    <!-- TabBar区域 -->
    <div class="tabbar-container">
      <tabBar v-model="currentTab" :tabs="tabs" @change="handleTabChange" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, defineAsyncComponent, watch } from 'vue'

// 缓存组件名称列表
const cacheList = ref([])

// 当前选中的tab
const currentTab = ref('okr')

// 最大缓存数量
const MAX_CACHE_SIZE = 3

// 更新缓存列表
const updateCacheList = (tabId) => {
  // 获取组件名称
  const tab = tabs.find((item) => item.id === tabId)
  if (!tab || !tab.component?.name) return

  // 如果已在缓存列表中，调整顺序（最近使用放在前面）
  if (cacheList.value.includes(tab.component.name)) {
    cacheList.value = [tab.component.name, ...cacheList.value.filter((name) => name !== tab.component.name)]
    return
  }

  // 加入缓存列表
  cacheList.value = [tab.component.name, ...cacheList.value]

  // 控制缓存上限
  if (cacheList.value.length > MAX_CACHE_SIZE) {
    cacheList.value = cacheList.value.slice(0, MAX_CACHE_SIZE)
  }
}

// 监听tab变化，更新缓存
watch(currentTab, (newVal) => {
  updateCacheList(newVal)
})
</script>
```

### 2. 首屏加载优化

优化首屏加载速度，减少初始加载时间：

```vue
<script setup>
// 预加载首个tab页面
import OkrList from '@/pages/okr/okrList.vue'

// 其他页面使用异步组件
const Today = defineAsyncComponent({
  loader: () => import('@/pages/okr/today.vue'),
  // 显示加载状态的延迟时间
  delay: 100,
  // 加载出错时显示的组件
  errorComponent: ErrorComponent,
  // 加载时显示的组件
  loadingComponent: LoadingComponent,
})

// 加载占位组件
const LoadingComponent = {
  template: `
    <div class="async-loading">
      <div class="loading-indicator"></div>
      <span>页面加载中...</span>
    </div>
  `,
}

// 错误占位组件
const ErrorComponent = {
  template: `
    <div class="async-error">
      <span>页面加载失败，请重试</span>
      <button @click="reload">重新加载</button>
    </div>
  `,
  methods: {
    reload() {
      location.reload()
    },
  },
}
</script>

<style scoped>
/* 添加Loading和Error组件样式 */
.async-loading,
.async-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--color-gray-500);
}

.loading-indicator {
  width: 24px;
  height: 24px;
  border: 2px solid var(--color-gray-200);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
```

### 3. 动画性能优化

优化页面切换动画的性能，使用硬件加速：

```css
/* 优化过渡动画样式 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease, transform 0.2s ease;
  will-change: opacity, transform; /* 启用硬件加速 */
}

.fade-enter-from {
  opacity: 0;
  transform: translateX(10px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

/* 针对不同设备进行动画优化 */
@media (prefers-reduced-motion: reduce) {
  .fade-enter-active,
  .fade-leave-active {
    transition: none; /* 用户开启减少动画模式时禁用动画 */
  }
}
```

### 4. 内存管理与优化

实现更高级的内存管理策略，避免内存泄漏：

```javascript
// 组件销毁时清理资源
onBeforeUnmount(() => {
  // 清空缓存数据
  cacheList.value = []
  scrollPositions.value = {}
})

// 监测页面切换频率，动态调整缓存策略
const switchFrequency = ref({})
const updateSwitchFrequency = (tabId) => {
  const now = Date.now()
  switchFrequency.value[tabId] = now

  // 分析用户切换模式，动态调整缓存策略
  const frequentTabs = Object.entries(switchFrequency.value)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 3)
    .map((entry) => entry[0])

  // 优先缓存频繁访问的页面
  cacheList.value = frequentTabs
    .map((tabId) => {
      const tab = tabs.find((item) => item.id === tabId)
      return tab?.component?.name
    })
    .filter(Boolean)
}
```

### 5. 兼容性问题解决

处理不同设备和平台上的兼容性问题：

```javascript
// 检测当前运行环境
const platform = uni.getSystemInfoSync().platform
const isIOS = platform === 'ios'
const isAndroid = platform === 'android'
const isH5 = uni.getEnv() === 'H5'

// 针对不同平台调整TabBar样式和行为
onMounted(() => {
  // 根据平台调整样式
  const tabbar = document.querySelector('.custom-tabbar')

  if (isIOS) {
    // iOS特定样式调整
    document.documentElement.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)')
  } else if (isAndroid) {
    // Android特定样式调整
    document.documentElement.style.setProperty('--safe-area-inset-bottom', '0px')
  }

  // 针对H5环境的特殊处理
  if (isH5) {
    // 添加浏览器历史状态管理
    window.addEventListener('popstate', () => {
      parseUrlParams() // 当用户使用浏览器后退按钮时，解析URL参数
    })
  }
})
```

### 6. 异常处理与降级方案

添加异常处理和降级方案，确保在极端情况下也能提供基本功能：

```javascript
// 异常处理封装
const safeExecute = (fn, fallback = null) => {
  try {
    return fn()
  } catch (error) {
    console.error('操作执行失败:', error)
    // 记录错误日志
    // logError(error)
    return fallback
  }
}

// 加载组件时使用安全执行
const loadComponent = (tabId) => {
  return safeExecute(() => {
    const tab = tabs.find((item) => item.id === tabId)
    return tab ? tab.component : null
  }, FallbackComponent) // 提供一个后备组件
}

// 后备组件
const FallbackComponent = {
  template: `
    <div class="fallback-component">
      <div class="fallback-message">内容暂时无法加载</div>
      <button class="fallback-button" @click="retry">重试</button>
    </div>
  `,
  methods: {
    retry() {
      // 触发重新加载
      location.reload()
    },
  },
}
```

## 验收标准

1. 首屏加载时间优化至少提升 30%
2. 页面切换动画流畅，无卡顿现象
3. 内存占用合理，长时间使用不会导致明显的内存泄漏
4. 各种设备和平台上的兼容性问题得到解决
5. 在网络不稳定情况下有适当的降级方案
6. 用户体验整体流畅，无明显的性能问题

## 依赖关系

- 依赖 task-tabbar-04 完成（页面切换机制）
- 这是 TabBar 栏自定义功能的最后一个任务

## 注意事项

1. 优化过程中需持续进行性能测试，对比优化前后的指标变化
2. 使用 Chrome DevTools 等工具分析内存和性能问题
3. 注意不同设备和平台的兼容性测试
4. 优化需在保持功能完整性的前提下进行

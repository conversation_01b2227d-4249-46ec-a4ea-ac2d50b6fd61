# 任务：问答式总结功能集成

- **所属功能模块**: `diary-qa`
- **任务ID**: `task-diary-qa-04`
- **优先级**: 高

## 任务描述
将问答交互UI、AI问答服务和AI总结服务整合起来，实现完整的"问答式日记总结"功能。

## 技术实现详情
1.  **触发流程**:
    - 在日记主页面添加一个"总结"按钮。
    - 用户点击"总结"按钮后，执行以下操作：
        a. 调用问答服务（`task-diary-qa-02`）生成问题。
        b. 使用生成的问题启动 `z-question-answer` 组件（`task-diary-qa-01`）。

2.  **执行问答**:
    - 用户在 `z-question-answer` 组件中完成问答。
    - 组件关闭时，返回包含所有问答记录的 `questions_answers` 数组。

3.  **生成和保存总结**:
    - 获取问答结果后，调用总结服务（`task-diary-qa-03`），传入当天的 `entries` 和 `questions_answers`。
    - 获取服务返回的 `summary` 字符串。
    - 将 `questions_answers` 和 `summary` 更新到当天的 `DiaryContent` 对象中。
    - 将更新后的 `DiaryContent` 序列化并保存回数据库。
    - （可选）可以将生成的总结内容填充到 `z-diary-editor` 中，让用户进行二次编辑和确认。

4.  **状态处理**:
    - 在调用AI服务期间，需要有明确的加载中（loading）状态提示。
    - 对服务可能出现的错误进行捕获和友好提示。

## 验收标准
- 点击"总结"按钮能成功启动问答流程。
- 问答流程结束后，能自动调用总结服务。
- AI生成的总结能被正确保存到数据库。
- 用户能在UI上看到最终生成的总结内容。
- 整个流程中的加载和错误状态都有相应的UI反馈。

## 依赖关系
- `task-diary-entry-04`: 需要当日的日记条目（`entries`）作为输入。
- `task-diary-qa-01`: 需要 `z-question-answer` 组件。
- `task-diary-qa-02`: 需要问答服务。
- `task-diary-qa-03`: 需要总结服务。

## 状态追踪
- 未开始 